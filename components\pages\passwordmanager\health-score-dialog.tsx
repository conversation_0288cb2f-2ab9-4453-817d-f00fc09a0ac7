"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Al<PERSON>Triangle, CheckCircle, Clock, ShieldAlert } from "lucide-react"
import { Progress } from "@/components/ui/progress"
import { useState } from "react"

// Mock数据
const stats = {
  totalPasswords: 15,
  strongPasswords: 8,
  weakPasswords: 3,
  reusedPasswords: 2,
  oldPasswords: 4,
  breachedPasswords: 1,
  securityScore: 78,
}

export function HealthScoreButton() {
  const [showHealthDialog, setShowHealthDialog] = useState(false)

  return (
    <>
      <Button variant="outline" className="flex items-center gap-2" onClick={() => setShowHealthDialog(true)}>
        <div className="flex items-center gap-1">
          <span className="text-sm font-medium">安全评分</span>
          <span className="text-sm font-bold text-primary">{stats.securityScore}%</span>
        </div>
      </Button>

      <Dialog open={showHealthDialog} onOpenChange={setShowHealthDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>密码健康状况</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">安全评分</span>
                <span className="text-xl font-bold text-primary">{stats.securityScore}%</span>
              </div>
              <Progress value={stats.securityScore} className="h-2" />
            </div>

            <div className="space-y-3 pt-2">
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  <span>强密码</span>
                </div>
                <span>
                  {stats.strongPasswords}/{stats.totalPasswords}
                </span>
              </div>

              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center">
                  <AlertTriangle className="h-4 w-4 text-amber-500 mr-2" />
                  <span>弱密码</span>
                </div>
                <span>
                  {stats.weakPasswords}/{stats.totalPasswords}
                </span>
              </div>

              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center">
                  <ShieldAlert className="h-4 w-4 text-red-500 mr-2" />
                  <span>重复使用</span>
                </div>
                <span>
                  {stats.reusedPasswords}/{stats.totalPasswords}
                </span>
              </div>

              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center">
                  <Clock className="h-4 w-4 text-blue-500 mr-2" />
                  <span>过期密码</span>
                </div>
                <span>
                  {stats.oldPasswords}/{stats.totalPasswords}
                </span>
              </div>

              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center">
                  <ShieldAlert className="h-4 w-4 text-red-500 mr-2" />
                  <span>已泄露</span>
                </div>
                <span>
                  {stats.breachedPasswords}/{stats.totalPasswords}
                </span>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
} 
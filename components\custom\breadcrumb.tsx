"use client"

import React, { ReactNode } from "react"
import { useRouter } from "next/navigation"
import {
  <PERSON>readcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
  BreadcrumbEllipsis,
} from "@/components/ui/breadcrumb"
import { Separator } from "@/components/ui/separator"
import { SidebarTrigger, useSidebar } from "../navigation/sidebar"

export type BreadcrumbItem = {
  href?: string
  label: string
  isCurrent?: boolean
}

interface HeaderWithBreadcrumbProps {
  items: BreadcrumbItem[]
  className?: string
  actions?: ReactNode
}

export function HeaderWithBreadcrumb({
                                       items,
                                       className,
                                       actions
                                     }: HeaderWithBreadcrumbProps) {
  const router = useRouter()

  // 处理面包屑显示逻辑
  // 1. 如果项目数量小于等于3个，全部显示
  // 2. 如果大于3个，显示第一个、最后一个，中间用省略号
  const visibleItems = items.length <= 3
      ? items
      : [items[0], { label: '...', href: '#' }, items[items.length - 1]];

  // 处理面包屑点击，使用客户端路由进行导航
  const handleBreadcrumbClick = (href: string, e: React.MouseEvent) => {
    e.preventDefault();
    router.push(href);
  };
  
  return (
      <header className="sticky top-0 z-10 flex h-14 shrink-0 items-center bg-background">
        <div className="flex items-center justify-between gap-2 px-6 w-full transition-all duration-200">
          <div className="flex items-center gap-2 flex-nowrap">
            <SidebarTrigger className="-ml-1" />
            <Separator
                orientation="vertical"
                className="mr-2 data-[orientation=vertical]:h-4"
            />
            <Breadcrumb className="whitespace-nowrap">
              <BreadcrumbList className="flex-nowrap">
                {visibleItems.map((item, index) => (
                    <React.Fragment key={`breadcrumb-item-${index}`}>
                      <BreadcrumbItem className="flex-shrink-0">
                        {item.label === '...' ? (
                            <BreadcrumbEllipsis className="w-6 h-6 flex items-center" />
                        ) : item.isCurrent ? (
                            <BreadcrumbPage className="truncate max-w-[120px] md:max-w-[240px]">{item.label}</BreadcrumbPage>
                        ) : (
                            <BreadcrumbLink 
                              className="truncate max-w-[100px] md:max-w-[200px]" 
                              href={item.href || "#"}
                              onClick={(e) => item.href ? handleBreadcrumbClick(item.href, e) : undefined}
                            >
                              {item.label}
                            </BreadcrumbLink>
                        )}
                      </BreadcrumbItem>
                      {index < visibleItems.length - 1 && (
                        <BreadcrumbSeparator />
                      )}
                    </React.Fragment>
                ))}
              </BreadcrumbList>
            </Breadcrumb>
          </div>

          <div className="flex items-center gap-2">
            {actions}
          </div>
        </div>
      </header>
  )
} 
"use client"

import { useEffect, useState } from "react"
import { Loader2 } from "lucide-react"
import { usePathname, useSearchParams } from "next/navigation"

import { cn } from "@/lib/utils"

/**
 * 页面加载状态组件
 * 用于在页面切换时显示加载效果
 */
export function PageLoader() {
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const [loading, setLoading] = useState(false)
  
  // 监听路由变化，显示加载状态
  useEffect(() => {
    const handleStart = () => setLoading(true)
    const handleComplete = () => {
      setTimeout(() => setLoading(false), 300) // 添加小延迟让动画更流畅
    }

    // 为了在组件挂载后也能触发一次加载状态，通常首次渲染
    let startTimeout: NodeJS.Timeout
    startTimeout = setTimeout(() => {
      handleStart()
      setTimeout(handleComplete, 500)
    }, 0)

    // 监听路由变化
    window.addEventListener("navigationstart", handleStart)
    window.addEventListener("navigationsuccess", handleComplete)
    window.addEventListener("navigationerror", handleComplete)

    return () => {
      clearTimeout(startTimeout)
      window.removeEventListener("navigationstart", handleStart)
      window.removeEventListener("navigationsuccess", handleComplete)
      window.removeEventListener("navigationerror", handleComplete)
    }
  }, [pathname, searchParams])

  if (!loading) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-background/50 backdrop-blur-sm">
      <div className="flex flex-col items-center space-y-4">
        <Loader2 className="h-10 w-10 animate-spin text-primary" />
        <p className="text-sm text-muted-foreground">正在加载页面...</p>
      </div>
    </div>
  )
}

/**
 * 内容骨架屏组件
 * 用于在内容加载时显示占位效果
 */
export function ContentSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn("space-y-4 animate-pulse", className)}>
      <div className="h-8 w-3/4 rounded-md bg-muted"></div>
      <div className="h-4 w-full rounded-md bg-muted"></div>
      <div className="h-4 w-full rounded-md bg-muted"></div>
      <div className="h-4 w-3/4 rounded-md bg-muted"></div>
      <div className="space-y-2">
        <div className="h-64 rounded-md bg-muted"></div>
        <div className="grid grid-cols-3 gap-4">
          <div className="h-20 rounded-md bg-muted"></div>
          <div className="h-20 rounded-md bg-muted"></div>
          <div className="h-20 rounded-md bg-muted"></div>
        </div>
      </div>
    </div>
  )
} 
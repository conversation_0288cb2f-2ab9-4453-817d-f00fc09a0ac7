"use client"

import { Badge } from "@/components/ui/badge"
import { DataTableBadgeProps } from "@/components/common-custom/data-table/types"

/**
 * 数据表格状态徽章组件
 * 根据不同的状态值显示不同样式的徽章
 */
export function DataTableBadge({ 
  status, 
  statusMap,
  className 
}: DataTableBadgeProps) {
  // 如果提供了自定义状态映射，则使用它
  if (statusMap && statusMap[status]) {
    const config = statusMap[status]
    return (
      <Badge 
        variant={config.variant || "default"} 
        className={config.className || className}
      >
        {config.label || status}
      </Badge>
    )
  }

  // 默认状态映射
  switch (status) {
    case "active":
    case "成功":
    case "通过":
    case "已完成":
      return <Badge variant="outline" className={`border-black text-black ${className || ""}`}>激活</Badge>
    case "inactive":
    case "禁用":
    case "失败":
      return <Badge variant="secondary" className={className}>禁用</Badge>
    case "pending":
    case "处理中":
    case "等待":
      return <Badge variant="outline" className={className}>待处理</Badge>
    default:
      return <Badge variant="secondary" className={className}>{status}</Badge>
  }
} 
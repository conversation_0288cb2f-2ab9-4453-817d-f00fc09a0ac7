"use client"

import React from "react"
import { ComponentPreviewContainer } from "@/components/component-detail/preview-container"
import { Breadcrumb } from "@/components/common-custom/breadcrumb"
import { HeaderWithBreadcrumb } from "@/components/project-custom/breadcrumb"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Home, Users, Folder, FileText, PlusCircle, ChevronRight } from "lucide-react"

// 基础面包屑示例代码
const basicBreadcrumbCode = `
import React from "react"
import { Breadcrumb } from "@/components/common-custom/breadcrumb"

function BasicBreadcrumb() {
  const breadcrumbItems = [
    { label: "首页", href: "/" },
    { label: "产品", href: "/products" },
    { label: "类别", href: "/products/categories" },
    { label: "电子产品", href: "/products/categories/electronics", current: true },
  ]

  return (
    <Breadcrumb items={breadcrumbItems} variant="standard" />
  )
}

render(<BasicBreadcrumb />)
`

// 带图标面包屑示例代码
const iconBreadcrumbCode = `
import React from "react"
import { Breadcrumb } from "@/components/common-custom/breadcrumb"
import { Home, Users } from "lucide-react"

function IconBreadcrumb() {
  const breadcrumbItems = [
    { label: "首页", href: "/", icon: <Home className="h-4 w-4" /> },
    { label: "用户管理", href: "/users", icon: <Users className="h-4 w-4" /> },
    { label: "用户详情", href: "/users/profile", current: true },
  ]

  return (
    <Breadcrumb items={breadcrumbItems} variant="standard" />
  )
}

render(<IconBreadcrumb />)
`

// 折叠模式面包屑示例代码
const collapsedBreadcrumbCode = `
import React from "react"
import { Breadcrumb } from "@/components/common-custom/breadcrumb"

function CollapsedBreadcrumb() {
  const breadcrumbItems = [
    { label: "首页", href: "/" },
    { label: "产品", href: "/products" },
    { label: "类别", href: "/products/categories" },
    { label: "电子产品", href: "/products/categories/electronics" },
    { label: "手机", href: "/products/categories/electronics/phones" },
    { label: "智能手机", href: "/products/categories/electronics/phones/smartphones" },
    { label: "iPhone", href: "/products/categories/electronics/phones/smartphones/iphone", current: true },
  ]

  return (
    <Breadcrumb 
      items={breadcrumbItems} 
      variant="collapsed" 
      maxItems={4}
    />
  )
}

render(<CollapsedBreadcrumb />)
`

// 文件路径面包屑示例代码
const fileBreadcrumbCode = `
import React from "react"
import { Breadcrumb } from "@/components/common-custom/breadcrumb"
import { Folder, FileText } from "lucide-react"

function FileBreadcrumb() {
  const breadcrumbItems = [
    { label: "文件", href: "/files", icon: <Folder className="h-4 w-4" /> },
    { label: "文档", href: "/files/documents", icon: <Folder className="h-4 w-4" /> },
    { label: "工作", href: "/files/documents/work", icon: <Folder className="h-4 w-4" /> },
    { label: "报告.docx", href: "/files/documents/work/report.docx", icon: <FileText className="h-4 w-4" />, current: true },
  ]

  return (
    <Breadcrumb items={breadcrumbItems} variant="standard" />
  )
}

render(<FileBreadcrumb />)
`

// 页头面包屑导航示例代码
const headerBreadcrumbCode = `
import React from "react"
import { HeaderWithBreadcrumb } from "@/components/project-custom/breadcrumb"
import { Button } from "@/components/ui/button"
import { PlusCircle } from "lucide-react"

function HeaderBreadcrumb() {
  const breadcrumbItems = [
    { label: "首页", href: "/" },
    { label: "系统管理", href: "/system" },
    { label: "用户管理", isCurrent: true }
  ]

  return (
    <div className="border rounded-lg overflow-hidden">
      <HeaderWithBreadcrumb 
        items={breadcrumbItems}
        actions={
          <Button className="h-9 px-3 rounded-md">
            <PlusCircle className="w-4 h-4 mr-2" /> 
            新增用户
          </Button>
        }
      />
    </div>
  )
}

render(<HeaderBreadcrumb />)
`

// 深度嵌套页头面包屑示例代码
const deepHeaderBreadcrumbCode = `
import React from "react"
import { HeaderWithBreadcrumb } from "@/components/project-custom/breadcrumb"
import { Button } from "@/components/ui/button"

function DeepHeaderBreadcrumb() {
  const breadcrumbItems = [
    { label: "首页", href: "/" },
    { label: "系统管理", href: "/system" },
    { label: "权限管理", href: "/system/permissions" },
    { label: "角色配置", href: "/system/permissions/roles" },
    { label: "管理员角色", isCurrent: true }
  ]

  return (
    <div className="border rounded-lg overflow-hidden">
      <HeaderWithBreadcrumb 
        items={breadcrumbItems}
        actions={
          <Button variant="outline" className="h-9 px-3 rounded-md">
            返回
          </Button>
        }
      />
    </div>
  )
}

render(<DeepHeaderBreadcrumb />)
`

// 自定义分隔符面包屑示例代码
const customSeparatorBreadcrumbCode = `
import React from "react"
import { Breadcrumb } from "@/components/common-custom/breadcrumb"
import { ChevronRight } from "lucide-react"

function CustomSeparatorBreadcrumb() {
  const breadcrumbItems = [
    { label: "首页", href: "/" },
    { label: "系统设置", href: "/settings" },
    { label: "高级选项", href: "/settings/advanced", current: true },
  ]

  return (
    <Breadcrumb 
      items={breadcrumbItems} 
      variant="standard"
      separator={<ChevronRight className="h-4 w-4 opacity-50" />}
    />
  )
}

render(<CustomSeparatorBreadcrumb />)
`

// API文档组件
function BreadcrumbApiDocs() {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="font-medium text-lg mb-2">Breadcrumb</h3>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">items</td>
                <td className="p-2 border">BreadcrumbItem[]</td>
                <td className="p-2 border">[]</td>
                <td className="p-2 border">面包屑项目数组</td>
              </tr>
              <tr>
                <td className="p-2 border">variant</td>
                <td className="p-2 border">&quot;standard&quot; | &quot;collapsed&quot;</td>
                <td className="p-2 border">&quot;standard&quot;</td>
                <td className="p-2 border">面包屑展示模式</td>
              </tr>
              <tr>
                <td className="p-2 border">maxItems</td>
                <td className="p-2 border">number</td>
                <td className="p-2 border">3</td>
                <td className="p-2 border">在折叠模式下显示的最大项数</td>
              </tr>
              <tr>
                <td className="p-2 border">separator</td>
                <td className="p-2 border">React.ReactNode</td>
                <td className="p-2 border">ChevronRight图标</td>
                <td className="p-2 border">分隔符</td>
              </tr>
              <tr>
                <td className="p-2 border">className</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">自定义类名</td>
              </tr>
              <tr>
                <td className="p-2 border">onNavigate</td>
                <td className="p-2 border">(href: string, e: React.MouseEvent) =&gt; void</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">导航回调函数</td>
              </tr>
            </tbody>
          </table>
        </div>
        <div className="mt-4">
          <h3 className="font-medium text-lg mb-2">BreadcrumbItem</h3>
          <div className="overflow-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="bg-muted text-left">
                  <th className="p-2 border">参数</th>
                  <th className="p-2 border">类型</th>
                  <th className="p-2 border">默认值</th>
                  <th className="p-2 border">说明</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td className="p-2 border">href</td>
                  <td className="p-2 border">string</td>
                  <td className="p-2 border">-</td>
                  <td className="p-2 border">跳转链接</td>
                </tr>
                <tr>
                  <td className="p-2 border">label</td>
                  <td className="p-2 border">string</td>
                  <td className="p-2 border">-</td>
                  <td className="p-2 border">显示文字</td>
                </tr>
                <tr>
                  <td className="p-2 border">isCurrent / current</td>
                  <td className="p-2 border">boolean</td>
                  <td className="p-2 border">false</td>
                  <td className="p-2 border">是否为当前项</td>
                </tr>
                <tr>
                  <td className="p-2 border">icon</td>
                  <td className="p-2 border">React.ReactNode</td>
                  <td className="p-2 border">-</td>
                  <td className="p-2 border">图标</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div className="mt-4">
          <h3 className="font-medium text-lg mb-2">HeaderWithBreadcrumb</h3>
          <div className="overflow-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="bg-muted text-left">
                  <th className="p-2 border">参数</th>
                  <th className="p-2 border">类型</th>
                  <th className="p-2 border">默认值</th>
                  <th className="p-2 border">说明</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td className="p-2 border">items</td>
                  <td className="p-2 border">BreadcrumbItem[]</td>
                  <td className="p-2 border">[]</td>
                  <td className="p-2 border">面包屑项目数组</td>
                </tr>
                <tr>
                  <td className="p-2 border">actions</td>
                  <td className="p-2 border">React.ReactNode</td>
                  <td className="p-2 border">-</td>
                  <td className="p-2 border">页头右侧的操作按钮</td>
                </tr>
                <tr>
                  <td className="p-2 border">className</td>
                  <td className="p-2 border">string</td>
                  <td className="p-2 border">-</td>
                  <td className="p-2 border">自定义类名</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function BreadcrumbPreview() {
  const examples = [
    {
      id: "basic-breadcrumb",
      title: "基础面包屑",
      description: "最常用的基础面包屑导航",
      code: basicBreadcrumbCode,
      scope: { Breadcrumb, React }, 
    },
    {
      id: "icon-breadcrumb",
      title: "带图标面包屑",
      description: "包含图标的面包屑导航",
      code: iconBreadcrumbCode,
      scope: { 
        Breadcrumb, 
        Home, 
        Users,
        React 
      },
    },
    {
      id: "collapsed-breadcrumb",
      title: "折叠模式面包屑",
      description: "适用于层级较深的导航路径",
      code: collapsedBreadcrumbCode,
      scope: { Breadcrumb, React },
    },
    {
      id: "file-breadcrumb",
      title: "文件路径面包屑",
      description: "用于文件系统的导航路径",
      code: fileBreadcrumbCode,
      scope: { 
        Breadcrumb, 
        Folder, 
        FileText, 
        React 
      },
    },
    {
      id: "header-breadcrumb",
      title: "页头面包屑导航",
      description: "集成在页头中的面包屑导航",
      code: headerBreadcrumbCode,
      scope: { 
        HeaderWithBreadcrumb, 
        Button, 
        PlusCircle, 
        React 
      },
    },
    {
      id: "deep-header-breadcrumb",
      title: "深度嵌套页头面包屑",
      description: "在页头中显示深层级路径的面包屑",
      code: deepHeaderBreadcrumbCode,
      scope: { HeaderWithBreadcrumb, Button, React },
    },
    {
      id: "custom-separator-breadcrumb",
      title: "自定义分隔符",
      description: "使用自定义分隔符的面包屑",
      code: customSeparatorBreadcrumbCode,
      scope: { 
        Breadcrumb, 
        ChevronRight, 
        React 
      },
    }
  ]

  return (
    <ComponentPreviewContainer
      title="面包屑导航 Breadcrumb"
      description="显示当前页面在系统层级结构中的位置，并能向上返回。"
      whenToUse="当系统层级较多、页面位置需要展示时使用，方便用户了解当前位置和快速导航。适用于具有清晰层级结构的网站，如文件夹、类目、流程中的步骤等多级结构。"
      examples={examples}
      apiDocs={<BreadcrumbApiDocs />}
    />
  )
} 
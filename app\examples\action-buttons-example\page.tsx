"use client"

import React from "react"
import { ComponentPreviewContainer } from "@/components/component-detail/preview-container"
import { allExamples } from "./examples"

// ============================================================================
// API文档组件
// ============================================================================

function ActionButtonsApiDocs() {
  return (
    <div className="space-y-6">
      {/* 主组件API */}
      <div>
        <h3 className="font-medium text-lg mb-2">ActionButtons</h3>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">actions</td>
                <td className="p-2 border">ActionItem[]</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">操作项列表</td>
              </tr>
              <tr>
                <td className="p-2 border">variant</td>
                <td className="p-2 border">"default" | "compact" | "dropdown" | "table" | "floating"</td>
                <td className="p-2 border">"default"</td>
                <td className="p-2 border">显示变体</td>
              </tr>
              <tr>
                <td className="p-2 border">deleteConfirmProps</td>
                <td className="p-2 border">DeleteConfirmProps</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">删除确认配置</td>
              </tr>
              <tr>
                <td className="p-2 border">maxVisibleActions</td>
                <td className="p-2 border">number</td>
                <td className="p-2 border">3</td>
                <td className="p-2 border">最大显示按钮数</td>
              </tr>
              <tr>
                <td className="p-2 border">size</td>
                <td className="p-2 border">"sm" | "lg"</td>
                <td className="p-2 border">"sm"</td>
                <td className="p-2 border">按钮尺寸</td>
              </tr>
              <tr>
                <td className="p-2 border">disabled</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">false</td>
                <td className="p-2 border">是否禁用所有操作</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* 使用指南 */}
      <div>
        <h3 className="font-medium text-lg mb-2">使用指南</h3>
        <div className="space-y-4 text-sm">
          <div>
            <h4 className="font-medium mb-2">基本用法</h4>
            <p className="text-muted-foreground mb-2">
              创建基础的操作按钮组：
            </p>
            <pre className="bg-muted p-3 rounded-md overflow-x-auto">
              <code>{`<ActionButtons
  actions={[
    { id: "edit", label: "编辑", icon: Edit, onClick: handleEdit },
    { id: "delete", label: "删除", destructive: true, onClick: handleDelete }
  ]}
/>`}</code>
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
}

// ============================================================================
// 主预览组件
// ============================================================================

export default function ActionButtonsExamplePage() {
  return (
    <ComponentPreviewContainer
      title="操作按钮 ActionButtons"
      description="提供多种样式的操作按钮组合，支持确认对话框和灵活的布局方式"
      whenToUse="当需要为用户提供一组相关操作时使用；适用于表格行操作、卡片操作、工具栏等场景；支持危险操作确认、异步操作处理等高级功能"
      examples={allExamples}
      apiDocs={<ActionButtonsApiDocs />}
    />
  )
}
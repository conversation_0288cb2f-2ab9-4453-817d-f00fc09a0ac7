"use client"

import React from "react"
import { ComponentPreviewContainer } from "@/components/component-detail/preview-container"
import { allExamples } from "./examples"

// ============================================================================
// API文档组件
// ============================================================================

function ErrorStateApiDocs() {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-3">ErrorCard</h3>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b">
                <th className="text-left py-2 px-3 font-medium">属性</th>
                <th className="text-left py-2 px-3 font-medium">类型</th>
                <th className="text-left py-2 px-3 font-medium">默认值</th>
                <th className="text-left py-2 px-3 font-medium">说明</th>
              </tr>
            </thead>
            <tbody className="text-muted-foreground">
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">icon</td>
                <td className="py-2 px-3">ReactNode</td>
                <td className="py-2 px-3">-</td>
                <td className="py-2 px-3">错误状态图标</td>
              </tr>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">title</td>
                <td className="py-2 px-3">string</td>
                <td className="py-2 px-3">-</td>
                <td className="py-2 px-3">错误标题</td>
              </tr>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">description</td>
                <td className="py-2 px-3">string</td>
                <td className="py-2 px-3">-</td>
                <td className="py-2 px-3">错误描述</td>
              </tr>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">severity</td>
                <td className="py-2 px-3">'low' | 'medium' | 'high'</td>
                <td className="py-2 px-3">'medium'</td>
                <td className="py-2 px-3">错误严重程度</td>
              </tr>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">actions</td>
                <td className="py-2 px-3">ErrorAction[]</td>
                <td className="py-2 px-3">[]</td>
                <td className="py-2 px-3">操作按钮列表</td>
              </tr>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">className</td>
                <td className="py-2 px-3">string</td>
                <td className="py-2 px-3">-</td>
                <td className="py-2 px-3">自定义样式类名</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold mb-3">PageErrorState</h3>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b">
                <th className="text-left py-2 px-3 font-medium">属性</th>
                <th className="text-left py-2 px-3 font-medium">类型</th>
                <th className="text-left py-2 px-3 font-medium">默认值</th>
                <th className="text-left py-2 px-3 font-medium">说明</th>
              </tr>
            </thead>
            <tbody className="text-muted-foreground">
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">icon</td>
                <td className="py-2 px-3">ReactNode</td>
                <td className="py-2 px-3">-</td>
                <td className="py-2 px-3">页面错误图标</td>
              </tr>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">title</td>
                <td className="py-2 px-3">string</td>
                <td className="py-2 px-3">-</td>
                <td className="py-2 px-3">页面错误标题</td>
              </tr>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">description</td>
                <td className="py-2 px-3">string</td>
                <td className="py-2 px-3">-</td>
                <td className="py-2 px-3">页面错误描述</td>
              </tr>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">actions</td>
                <td className="py-2 px-3">ErrorAction[]</td>
                <td className="py-2 px-3">[]</td>
                <td className="py-2 px-3">操作按钮列表</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>


    </div>
  )
}

// ============================================================================
// 页面组件
// ============================================================================

export default function ErrorStateExamplePage() {
  return (
    <ComponentPreviewContainer
      title="错误状态 ErrorState"
      description="用于展示错误信息和提供恢复操作的组件集合，包括页面级错误、卡片错误和内联错误等多种形式"
      whenToUse="当页面加载失败时使用；当网络请求出错时使用；当用户操作失败时提供错误反馈和重试机制"
      examples={allExamples}
      apiDocs={<ErrorStateApiDocs />}
    />
  )
}
﻿"use client"

import { useState } from "react"
import { format } from "date-fns"
import { zhCN } from "date-fns/locale"
import { Mail, Clock, Trash2, UserIcon, MoreVertical } from "lucide-react"

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

// 获取Badge组件支持的变体类型
type BadgeVariant = React.ComponentProps<typeof Badge>["variant"]

interface MemberCardProps {
  member: {
    _id: string
    id?: string // 兼容旧版
    name: string
    email: string
    avatar: string
    status: string
    lastLogin: string
    roleIds: string[]
  }
  statusLabel: string
  statusClass: string
  roleNames: string
  isEditing: boolean
  onRemove: (userId: string) => void
}

export function MemberCard({ 
  member, 
  statusLabel, 
  statusClass, 
  roleNames, 
  isEditing,
  onRemove 
}: MemberCardProps) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  
  const handleDelete = () => {
    // 优先使用_id，若不存在则使用id
    onRemove(member._id || member.id || "")
    setIsDeleteDialogOpen(false)
  }
  
  // 根据状态类获取颜色
  const getBadgeVariant = (): BadgeVariant => {
    if (statusClass.includes("green")) return "success" as BadgeVariant
    if (statusClass.includes("yellow")) return "warning" as BadgeVariant
    if (statusClass.includes("red")) return "destructive"
    return "secondary"
  }
  
  return (
    <Card className="group hover:shadow-md transition-all duration-200 border border-border/30 rounded-xl bg-card/95 ring-1 ring-border/5 h-full">
      <CardContent className="p-4">
        <div className="relative flex flex-col gap-3 h-full">
          <div className="flex justify-between items-start">
            <div className="flex items-start gap-3">
              <Avatar className="size-12 flex-shrink-0">
                <AvatarImage src={member.avatar} alt={member.name} />
                <AvatarFallback className="text-base">{member.name.charAt(0)}</AvatarFallback>
              </Avatar>
              
              <div className="flex flex-col items-start">
                <h3 className="text-base font-semibold">{member.name}</h3>
                <div className="mt-1">
                  <Badge variant={getBadgeVariant()} className="text-xs font-normal">
                    {statusLabel}
                  </Badge>
                </div>
              </div>
            </div>
            
            {isEditing ? (
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 cursor-pointer text-muted-foreground hover:text-foreground hover:bg-accent"
                onClick={() => setIsDeleteDialogOpen(true)}
              >
                <Trash2 className="h-4 w-4" />
                <span className="sr-only">移出团队</span>
              </Button>
            ) : (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-8 w-8 cursor-pointer">
                    <MoreVertical className="h-4 w-4" />
                    <span className="sr-only">操作菜单</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem className="cursor-pointer">
                    查看详情
                  </DropdownMenuItem>
                  <DropdownMenuItem className="cursor-pointer">
                    联系成员
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
          
          <div className="space-y-2 text-xs mt-1">
            <div className="flex items-center">
              <UserIcon className="mr-1.5 h-3.5 w-3.5 text-muted-foreground flex-shrink-0" />
              <span className="truncate text-muted-foreground">{roleNames || "无角色"}</span>
            </div>
            
            <div className="flex items-center truncate">
              <Mail className="mr-1.5 h-3.5 w-3.5 text-muted-foreground flex-shrink-0" />
              <span className="truncate">{member.email}</span>
            </div>
            
            <div className="flex items-center">
              <Clock className="mr-1.5 h-3.5 w-3.5 text-muted-foreground flex-shrink-0" />
              <span className="truncate">登录：{formatDateShort(member.lastLogin)}</span>
            </div>
          </div>
        </div>
      </CardContent>
      
      {/* 删除确认对话框 */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[450px]">
          <DialogHeader className="pb-2">
            <DialogTitle className="text-lg">确认移除</DialogTitle>
            <DialogDescription>
              您确定要将 <span className="font-medium">{member.name}</span> 从团队中移除吗？
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="gap-3 pt-5">
            <Button 
              variant="outline" 
              onClick={() => setIsDeleteDialogOpen(false)}
              className="cursor-pointer"
            >
              取消
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleDelete}
              className="cursor-pointer"
            >
              移除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  )
}

function formatDateShort(dateString: string) {
  return format(new Date(dateString), 'MM-dd HH:mm', { locale: zhCN })
} 
/**
 * API配置文件
 * 包含API基础URL和其他配置信息
 */

// API基础路径
export const API_BASE_URL = "http://127.0.0.1:8413/template";
export const GROUP_API_BASE_URL = `${API_BASE_URL}/api/group`;
export const PERMISSION_API_BASE_URL = `${API_BASE_URL}/api/auth/permission`;

// 请求超时时间(毫秒)
export const REQUEST_TIMEOUT = 10000;

// 响应代码
export const API_RESPONSE_CODE = {
  SUCCESS: "200",
  ERROR: "500",
  UNAUTHORIZED: "401",
  BAD_REQUEST: "400",
  FORBIDDEN: "403",
  NOT_FOUND: "404",
};

// 默认请求头
export const DEFAULT_HEADERS = {
  "Content-Type": "application/json",
}; 

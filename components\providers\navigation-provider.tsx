"use client"

import React, { createContext, useContext, useState, useEffect, useRef } from "react"
import { usePathname, useSearchParams } from "next/navigation"

// 不需要显示加载状态的路径
const EXCLUDED_PATHS = ['/login', '/logout']

// 加载延迟时间（毫秒）
const LOADING_DELAY = 200

// 导航状态上下文
interface NavigationContextType {
  isLoading: boolean
  startLoading: () => void
  stopLoading: () => void
  shouldShowTransition: boolean // 是否应该显示过渡效果
  visitedPaths: Set<string> // 已访问过的路径，仅用于UI展示控制
}

const NavigationContext = createContext<NavigationContextType | undefined>(undefined)

/**
 * 导航状态提供器
 * 提供全局导航加载状态和页面过渡效果
 */
export function NavigationProvider({
  children,
}: {
  children: React.ReactNode
}) {
  const [isLoading, setIsLoading] = useState(false)
  const [shouldShowLoading, setShouldShowLoading] = useState(false)
  const visitedPathsRef = useRef<Set<string>>(new Set())
  const loadingTimerRef = useRef<NodeJS.Timeout | null>(null)
  const pathname = usePathname()

  // 判断当前路径是否需要显示过渡效果
  const shouldShowTransition = !EXCLUDED_PATHS.some(path => pathname.startsWith(path))
  
  // 启动加载状态
  const startLoading = () => {
    // 如果已经有计时器，先清除
    if (loadingTimerRef.current) {
      clearTimeout(loadingTimerRef.current)
      loadingTimerRef.current = null
    }
    
    // 设置延迟显示加载状态的计时器
    loadingTimerRef.current = setTimeout(() => {
      setShouldShowLoading(true)
      setIsLoading(true)
    }, LOADING_DELAY)
  }
  
  // 停止加载状态
  const stopLoading = () => {
    // 如果加载计时器还在运行，清除它（页面在延迟前已加载完成）
    if (loadingTimerRef.current) {
      clearTimeout(loadingTimerRef.current)
      loadingTimerRef.current = null
    }
    
    // 重置加载状态
    setShouldShowLoading(false)
    setIsLoading(false)
  }

  // 当组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (loadingTimerRef.current) {
        clearTimeout(loadingTimerRef.current)
      }
    }
  }, [])

  return (
    <NavigationContext.Provider value={{ 
      isLoading: isLoading && shouldShowLoading, 
      startLoading, 
      stopLoading,
      shouldShowTransition,
      visitedPaths: visitedPathsRef.current
    }}>
      {children}
    </NavigationContext.Provider>
  )
}

/**
 * 使用导航状态的自定义钩子
 */
export function useNavigation() {
  const context = useContext(NavigationContext)
  
  if (context === undefined) {
    throw new Error("useNavigation must be used within a NavigationProvider")
  }
  
  return context
} 
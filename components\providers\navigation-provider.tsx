"use client"

import React, { createContext, useContext, useEffect, useState } from "react"
import { usePathname, useSearchParams } from "next/navigation"

interface NavigationContextType {
  isPageTransition: boolean
  shouldShowTransition: boolean
  visitedPaths: Set<string>
}

const NavigationContext = createContext<NavigationContextType>({
  isPageTransition: false,
  shouldShowTransition: true,
  visitedPaths: new Set(),
})

export function useNavigation() {
  return useContext(NavigationContext)
}

export function NavigationProvider({ children }: { children: React.ReactNode }) {
  const [isPageTransition, setIsPageTransition] = useState(false)
  const [shouldShowTransition, setShouldShowTransition] = useState(true)
  const [visitedPaths, setVisitedPaths] = useState(new Set<string>())
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const currentPath = pathname + searchParams.toString()
  
  // 监听路由变化，设置页面过渡状态
  useEffect(() => {
    const handleStart = () => {
      setIsPageTransition(true)
    }
    const handleComplete = () => {
      // 添加当前路径到已访问路径集合
      setVisitedPaths(prev => {
        const newSet = new Set(prev)
        newSet.add(currentPath)
        return newSet
      })
      
      // 延迟关闭过渡状态，使动画能够完成
      setTimeout(() => {
        setIsPageTransition(false)
      }, 300)
    }
    
    window.addEventListener("navigationstart", handleStart)
    window.addEventListener("navigationsuccess", handleComplete)
    window.addEventListener("navigationerror", handleComplete)
    
    return () => {
      window.removeEventListener("navigationstart", handleStart)
      window.removeEventListener("navigationsuccess", handleComplete)
      window.removeEventListener("navigationerror", handleComplete)
    }
  }, [currentPath])
  
  // 用户可以通过设置localStorage禁用页面过渡效果
  useEffect(() => {
    const disableTransition = localStorage.getItem("disable-page-transition") === "true"
    setShouldShowTransition(!disableTransition)
    
    const handleStorageChange = () => {
      const disableTransition = localStorage.getItem("disable-page-transition") === "true"
      setShouldShowTransition(!disableTransition)
    }
    
    window.addEventListener("storage", handleStorageChange)
    return () => window.removeEventListener("storage", handleStorageChange)
  }, [])
  
  return (
    <NavigationContext.Provider 
      value={{ 
        isPageTransition, 
        shouldShowTransition,
        visitedPaths
      }}
    >
      {children}
    </NavigationContext.Provider>
  )
} 
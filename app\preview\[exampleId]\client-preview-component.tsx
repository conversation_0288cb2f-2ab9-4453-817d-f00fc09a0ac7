"use client";

import React, { useState, useEffect } from "react";
import { ClientPreviewComponentProps } from "./types";

// UI组件
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { 
  Tabs as UiTabs, 
  TabsContent, 
  TabsList, 
  TabsTrigger 
} from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

// 业务组件
import { 
  Timeline, 
  ActivityTimeline, 
  StageTimeline 
} from "@/components/common-custom/timeline";
import { BackButton } from "@/components/common-custom/back-button";
import { Breadcrumb } from "@/components/common-custom/breadcrumb";
import { HeaderWithBreadcrumb } from "@/components/project-custom/breadcrumb";
import { Pagination } from "@/components/common-custom/pagination";
import { 
  Tabs as BusinessTabs, 
  CardTabs, 
  StepTabs 
} from "@/components/common-custom/tabs";
import { 
  LoadingSpinner, 
  LoadingButton,
  FullPageLoader,
  ProgressLoader,
  LoadingContainer
} from "@/components/common-custom/loading";
import { PageLoader } from "@/components/common-custom/page-loader";
import {
  SkeletonText, 
  SkeletonCard, 
  SkeletonTable, 
  SkeletonList,
  SkeletonComponents 
} from "@/components/common-custom/skeleton";
import { ActionButtons, ActionItem } from "@/components/common-custom/action-buttons";
import { 
  Tooltip, 
  IconTooltip, 
  RichTooltip,
  TooltipContainer
} from "@/components/common-custom/tooltip";
import { IconSelector } from "@/components/common-custom/icon-selector";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { useToast } from "@/hooks/use-toast";
import {
  Stepper,
  FAQGroup,
  GroupedList,
  SettingsGroup,
  CollapsibleGroup,
  CollapsibleGroupItem
} from "@/components/common-custom/group-components";
import { TruncateText } from "@/components/common-custom/truncate-text";
import { Calendar } from "@/components/common-custom/calendar";
import { addDays, format, isToday, isSameMonth, startOfMonth, endOfMonth, eachDayOfInterval } from "date-fns";

// TanStack Table
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  getPaginationRowModel,
  getSortedRowModel,
  getFilteredRowModel,
} from "@tanstack/react-table";

// 图标
import {
  Search,
  Mail,
  Plus,
  PlusCircle,
  Loader2,
  Check,
  X,
  ArrowRight,
  ArrowUpDown,
  ChevronDown,
  MoreHorizontal,
  Pencil,
  Save,
  CheckCircle,
  Clock,
  AlertCircle,
  FileText,
  MessageSquare,
  User,
  Users,
  Calendar as CalendarIcon,
  Zap,
  Star,
  Bell,
  Copy,
  Download,
  Upload,
  Settings,
  HelpCircle,
  Info,
  Edit,
  Trash2,
  Eye,
  Share,
  Archive,
  Phone,
  Home,
  RefreshCw,
  Smartphone
} from "lucide-react";
import dynamic from "next/dynamic";

// 引入评分组件
import { Rating, RatingDisplay, RatingStatistics } from "@/components/common-custom/rating";

// 动态导入react-live组件，设置ssr: false以避免水合错误
const LivePreviewWrapper = dynamic(
  () => import("./live-preview"),
  { ssr: false }
);

export default function ClientPreviewComponent({ code }: ClientPreviewComponentProps) {
  const [isClient, setIsClient] = useState(false);
  
  // 构建scope，包含所有需要的组件和hook
  const scope = {
    // React核心
    React,
    // React Hooks
    useState: React.useState,
    useEffect: React.useEffect,
    useCallback: React.useCallback,
    useRef: React.useRef,
    useToast,
    
    // 日期处理函数
    addDays,
    format,
    isToday,
    isSameMonth,
    startOfMonth,
    endOfMonth,
    eachDayOfInterval,
    
    // UI组件
    Button,
    Input,
    Badge,
    Card,
    CardContent,
    Separator,
    Tabs: UiTabs,
    TabsContent,
    TabsList,
    TabsTrigger,
    Checkbox,
    Switch,
    Label,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
    DropdownMenu,
    DropdownMenuCheckboxItem,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
    Avatar,
    AvatarFallback,
    
    // 自定义业务组件
    Timeline, 
    ActivityTimeline, 
    StageTimeline,
    BackButton,
    Breadcrumb,
    HeaderWithBreadcrumb,
    Pagination,
    BusinessTabs,
    CardTabs,
    StepTabs,
    
    // 加载状态组件
    LoadingSpinner,
    LoadingButton,
    FullPageLoader,
    ProgressLoader,
    LoadingContainer,
    PageLoader,
    
    // 骨架屏组件
    SkeletonText,
    SkeletonCard,
    SkeletonTable,
    SkeletonList,
    SkeletonComponents,
    
    // 操作按钮组件
    ActionButtons,
    
    // 提示组件
    Tooltip,
    IconTooltip,
    RichTooltip,
    TooltipContainer,
    
    // 图标选择器组件
    IconSelector,
    
    // 文本截断组件
    TruncateText,
    
    // 日历组件
    Calendar,
    
    // 组件分组组件
    Stepper,
    FAQGroup,
    GroupedList,
    SettingsGroup,
    CollapsibleGroup,
    CollapsibleGroupItem,
    
    // TanStack Table
    getCoreRowModel,
    useReactTable,
    getPaginationRowModel,
    getSortedRowModel,
    getFilteredRowModel,
    flexRender,
    
    // 图标
    Search,
    Mail,
    Plus,
    PlusCircle,
    Loader2,
    Check,
    X,
    ArrowRight,
    ArrowUpDown,
    ChevronDown,
    MoreHorizontal,
    Pencil,
    Save,
    CheckCircle,
    Clock,
    AlertCircle,
    FileText,
    MessageSquare,
    User,
    Users,
    CalendarIcon,
    Zap,
    Star,
    Bell,
    Copy,
    Download,
    Upload,
    Settings,
    HelpCircle,
    Info,
    Smartphone,
    Edit,
    Trash2,
    Eye,
    Share,
    Archive,
    Phone,
    Home,
    RefreshCw,
    
    // 评分组件
    Rating,
    RatingDisplay,
    RatingStatistics,
    
    // 渲染函数
    render: (element: React.ReactNode) => element
  };

  useEffect(() => {
    // 确保在客户端渲染
    setIsClient(true);
  }, []);

  if (!isClient) {
    return null;
  }

  // 只渲染预览组件，没有额外的包装元素
  return <LivePreviewWrapper code={code} scope={scope} />;
} 
# 色彩系统

我们的色彩系统基于Shadcn UI的基础上进行了优化，采用了类似Notion的中性色彩风格，同时添加了柔和的功能色彩。整个系统使用CSS变量定义，便于在明暗主题间平滑过渡。

## 主色调：精简中性色系

采用10个精细的亮度级别，呈现出柔和、专业且不失亲和力的视觉效果。

```css
/* 在globals.css中定义 */
:root {
  /* 中性色调 */
  --slate-50: oklch(0.985 0 0);
  --slate-100: oklch(0.975 0 0);
  --slate-200: oklch(0.927 0 0);
  --slate-300: oklch(0.863 0 0);
  --slate-400: oklch(0.708 0 0);
  --slate-500: oklch(0.556 0 0);
  --slate-600: oklch(0.445 0 0);
  --slate-700: oklch(0.305 0 0);
  --slate-800: oklch(0.205 0 0);
  --slate-900: oklch(0.145 0 0);
  --slate-950: oklch(0.093 0 0);
}
```

## 语义化色彩映射

为确保一致性，我们将基础色彩映射到语义化变量：

```css
:root {
  /* 基础样式变量 */
  --background: oklch(0.985 0 0);
  --foreground: oklch(0.145 0 0);
  
  /* 卡片组件 */
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  
  /* 主要元素 */
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  
  /* 次要元素 */
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  
  /* 弱化元素 */
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  
  /* 边框与表单 */
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  
  /* 圆角 */
  --radius: 0.625rem;
}
```

## 功能色彩系统

功能色彩用于表达不同状态和信息类别：

| 色彩类型 | 应用场景 | 浅色模式 | 深色模式 |
|---------|---------|---------|---------|
| 成功色（绿） | 成功状态、完成操作 | `var(--success-600)` | `var(--success-500)` |
| 警告色（黄） | 警告信息、需注意 | `var(--warning-600)` | `var(--warning-500)` |
| 错误色（红） | 错误状态、危险操作 | `var(--error-600)` | `var(--error-500)` |
| 信息色（蓝） | 提示信息、进行中 | `var(--info-600)` | `var(--info-500)` |

## 暗色模式变量

```css
.dark {
  /* 基础样式变量 */
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  
  /* 卡片组件 */
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  
  /* 主要元素 */
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  
  /* 边框与表单 */
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
}
```

## 色彩层次与对比度

为了确保视觉舒适度和可读性，我们遵循以下色彩层次规则：

1. **文字与背景对比度**：确保达到WCAG 2.1 AA级别标准（正常文本4.5:1，大号文本3:1）
2. **深色模式调整**：深色模式中提高背景亮度，降低文字亮度，减少眩光
3. **强调色使用**：强调色用于引导注意，但不超过页面25%面积
4. **渐变使用**：渐变从基础色到透明，避免强烈色彩冲突

## 色彩无障碍设计

我们的色彩系统遵循以下无障碍原则：

1. **不仅依赖颜色传达信息**：搭配图标、文字或形状区分状态和信息
2. **对比度保障**：文字对比度≥4.5:1
3. **色盲友好**：避免红绿搭配作为唯一区分手段
4. **明暗主题适配**：自动根据系统偏好切换明暗主题

## 实际应用示例

### 基础组件色彩

```jsx
// 卡片组件
<div className="p-6 rounded-lg border border-border bg-card text-card-foreground">
  <h3 className="text-xl font-medium">卡片标题</h3>
  <p className="mt-2 text-muted-foreground">次要文本内容</p>
</div>

// 按钮组件
<button className="bg-primary text-primary-foreground hover:bg-primary/90 px-4 py-2 rounded-md">
  主按钮
</button>
```

### 状态色彩应用

```jsx
// 成功状态
<div className="p-4 rounded-md bg-success-50 border border-success-100 text-success-800 dark:bg-success-950 dark:text-success-400">
  <p>操作成功完成</p>
</div>

// 错误状态
<div className="p-4 rounded-md bg-error-50 border border-error-100 text-error-800 dark:bg-error-950 dark:text-error-400">
  <p>操作失败，请重试</p>
</div>
```

## 色彩命名约定

我们的色彩命名遵循以下约定：

1. **基础色名**: `--{color}-{lightness}`，如`--slate-500`
2. **语义化名称**: `--{role}`，如`--primary`, `--accent`
3. **组件专用色**: `--{component}-{role}`，如`--card-foreground`

## 最佳实践

1. **优先使用语义化变量**：使用`--primary`而不是具体的颜色值
2. **合理使用透明度**：通过`/90`等方式控制透明度
3. **保持一致性**：在整个应用中保持色彩使用的一致性
4. **测试对比度**：确保色彩组合满足无障碍要求

"use client"

import * as React from "react"
import { 
  ColumnDef, 
  flexRender, 
  getCoreRowModel, 
  useReactTable,
  getPaginationRowModel,
} from "@tanstack/react-table"
import { MoreHorizontal } from "lucide-react"

import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Card } from "@/components/ui/card"
import { User } from "../data"

export interface ResponsiveDataTableExampleProps {
  data: User[]
}

export function ResponsiveDataTableExample({ data }: ResponsiveDataTableExampleProps) {
  const columns: ColumnDef<User>[] = [
    {
      accessorKey: "name",
      header: "姓名",
    },
    {
      accessorKey: "email",
      header: "邮箱",
      // 在小屏幕上隐藏
      cell: ({ row }) => <div className="hidden md:block">{row.getValue("email")}</div>,
    },
    {
      accessorKey: "role",
      header: "角色",
      // 在小屏幕上隐藏
      cell: ({ row }) => <div className="hidden sm:block">{row.getValue("role")}</div>,
    },
    {
      accessorKey: "status",
      header: "状态",
      cell: ({ row }) => {
        const status = row.getValue("status") as string
        return (
          <div className="flex items-center">
            <span
              className={`mr-2 h-2 w-2 rounded-full ${
                status === "活跃" ? "bg-green-500" : 
                status === "未活跃" ? "bg-gray-400" : "bg-yellow-500"
              }`}
            />
            {status}
          </div>
        )
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const user = row.original
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">打开菜单</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>操作</DropdownMenuLabel>
              <DropdownMenuItem
                onClick={() => navigator.clipboard.writeText(user.id)}
              >
                复制用户ID
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>查看详情</DropdownMenuItem>
              <DropdownMenuItem className="md:hidden">
                邮箱: {user.email}
              </DropdownMenuItem>
              <DropdownMenuItem className="sm:hidden">
                角色: {user.role}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )
      },
    },
  ]

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    initialState: {
      pagination: {
        pageSize: 5,
      },
    },
  })

  return (
    <div className="space-y-4">
      {/* 桌面视图表格 */}
      <div className="rounded-md border hidden sm:block">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  无数据
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* 移动视图卡片 */}
      <div className="grid gap-4 sm:hidden">
        {data.slice(0, table.getState().pagination.pageSize).map((user) => (
          <Card key={user.id} className="p-4">
            <div className="flex justify-between items-center">
              <div className="font-medium">{user.name}</div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="h-8 w-8 p-0">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem>查看详情</DropdownMenuItem>
                  <DropdownMenuItem>编辑用户</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            <div className="mt-2 text-sm text-muted-foreground">
              {user.email}
            </div>
            <div className="mt-2 flex items-center">
              <span
                className={`mr-2 h-2 w-2 rounded-full ${
                  user.status === "活跃" ? "bg-green-500" : 
                  user.status === "未活跃" ? "bg-gray-400" : "bg-yellow-500"
                }`}
              />
              <span className="text-sm">{user.status}</span>
              <span className="mx-2 text-muted-foreground">•</span>
              <span className="text-sm">{user.role}</span>
            </div>
          </Card>
        ))}
      </div>

      <div className="flex items-center justify-end space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => table.previousPage()}
          disabled={!table.getCanPreviousPage()}
        >
          上一页
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => table.nextPage()}
          disabled={!table.getCanNextPage()}
        >
          下一页
        </Button>
      </div>
    </div>
  )
} 
/**
 * 认证拦截器
 * 处理API请求的认证令牌添加
 */
import { RequestOptionsInit, RequestInstance, request } from '@/lib/request';
import { getToken, getTokenType, removeToken, removeTokenType } from './authService';
import { API_RESPONSE_CODE } from './configApi';

/**
 * 认证拦截器
 * 为请求添加认证令牌
 * @param config 请求配置
 * @returns 更新后的请求配置
 */
export const authInterceptor = (config: RequestOptionsInit): RequestOptionsInit => {
  // 添加token等认证信息
  const token = getToken();
  const tokenType = getTokenType();
  
  if (token) {
    config.headers = {
      ...config.headers,
      'Authorization': `${tokenType} ${token}`
    };
  }
  
  // 如果是开发环境，添加特殊标记以便于调试
  if (process.env.NODE_ENV === 'development') {
    config.headers = {
      ...config.headers,
    };
  }
  
  return config;
};

/**
 * 处理未授权情况
 * 清除token并重定向到登录页
 */
function handleUnauthorized() {
  console.warn('用户未登录或会话已过期');
  
  // 清除localStorage中的token
  removeToken();
  removeTokenType();
  
  // 重定向到登录页
  if (typeof window !== 'undefined' && !window.location.pathname.includes('/login')) {
    // 使用replace而不是href，这样不会在历史记录中留下当前页面
    window.location.replace('/login');
  }
} 

/**
 * 设置认证拦截器
 * 将认证拦截器添加到请求实例中
 * @param requestInstance 请求实例，如果未提供则使用默认实例
 */
export function setupAuthInterceptors(requestInstance: RequestInstance = request) {
  // 检查requestInstance是否有效
  if (!requestInstance || !requestInstance.interceptors) {
    console.error('setupAuthInterceptors: 无效的请求实例, 拦截器未设置');
    return requestInstance;
  }

  // 添加请求拦截器
  requestInstance.interceptors.request.use(authInterceptor);
  
  // 添加响应拦截器处理未授权情况
  requestInstance.interceptors.response.use(
    (response) => {
      // 检查响应数据中的业务状态码
      if (response.data && response.data.code === API_RESPONSE_CODE.UNAUTHORIZED) {
        handleUnauthorized();
        // 创建一个错误，使请求链进入错误处理流程
        const error: any = new Error('用户未登录或登录已过期');
        error.response = response;
        throw error;
      }
      return response;
    },
    (error) => {
      // 处理401未授权错误
      if (error.response && error.response.status === 401) {
        handleUnauthorized();
      }
      
      // 处理业务逻辑错误
      if (error.response && error.response.data) {
        const { code } = error.response.data;
        if (code === API_RESPONSE_CODE.UNAUTHORIZED) {
          handleUnauthorized();
        }
      }
      
      return Promise.reject(error);
    }
  );
  
  return requestInstance;
} 
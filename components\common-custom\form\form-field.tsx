"use client"

import { useCallback } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Slider } from "@/components/ui/slider"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"
import { CalendarIcon, Info, AlertCircle } from "lucide-react"
import { format } from "date-fns"
import { zhCN } from "date-fns/locale"
import { FormFieldProps } from "@/types/form"

/**
 * 表单字段组件
 */
export function FormField({
  label,
  name,
  type = "text",
  placeholder,
  description,
  error,
  required,
  disabled,
  value,
  onChange,
  options,
  min,
  max,
  step,
  rows,
  className,
  children,
}: FormFieldProps) {
  // 处理值变更
  const handleValueChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | Date | string | number | boolean) => {
      if (!onChange) return

      if (e && typeof e === "object" && "target" in e) {
        const target = e.target as HTMLInputElement | HTMLTextAreaElement
        const newValue = type === "checkbox" ? (target as HTMLInputElement).checked : target.value
        onChange(name, newValue)
      } else {
        onChange(name, e)
      }
    },
    [name, onChange, type]
  )

  // 渲染表单控件
  const renderControl = () => {
    switch (type) {
      case "text":
      case "email":
      case "password":
      case "number":
      case "tel":
      case "url":
        return (
          <Input
            id={name}
            name={name}
            type={type}
            placeholder={placeholder}
            value={value as string}
            onChange={handleValueChange}
            required={required}
            disabled={disabled}
            min={min}
            max={max}
            step={step}
            className={error ? "border-destructive" : ""}
          />
        )
      case "textarea":
        return (
          <Textarea
            id={name}
            name={name}
            placeholder={placeholder}
            value={value as string}
            onChange={handleValueChange}
            required={required}
            disabled={disabled}
            rows={rows || 3}
            className={error ? "border-destructive" : ""}
          />
        )
      case "checkbox":
        return (
          <div className="flex items-center space-x-2">
            <Checkbox
              id={name}
              name={name}
              checked={value as boolean}
              onCheckedChange={handleValueChange}
              disabled={disabled}
            />
            <Label htmlFor={name} className="text-sm font-normal">
              {placeholder}
            </Label>
          </div>
        )
      case "radio":
        return (
          <RadioGroup
            value={value as string}
            onValueChange={(val) => handleValueChange(val)}
            disabled={disabled}
          >
            {options?.map((option) => (
              <div key={String(option.value)} className="flex items-center space-x-2">
                <RadioGroupItem value={String(option.value)} id={`${name}-${String(option.value)}`} />
                <Label htmlFor={`${name}-${String(option.value)}`} className="text-sm font-normal">
                  {option.label}
                </Label>
              </div>
            ))}
          </RadioGroup>
        )
      case "select":
        return (
          <Select
            value={String(value)}
            onValueChange={(val) => handleValueChange(val)}
            disabled={disabled}
          >
            <SelectTrigger className={error ? "border-destructive" : ""}>
              <SelectValue placeholder={placeholder} />
            </SelectTrigger>
            <SelectContent>
              {options?.map((option) => (
                <SelectItem key={String(option.value)} value={String(option.value)}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )
      case "switch":
        return (
          <div className="flex items-center space-x-2">
            <Switch
              id={name}
              name={name}
              checked={value as boolean}
              onCheckedChange={handleValueChange}
              disabled={disabled}
            />
            <Label htmlFor={name} className="text-sm font-normal">
              {placeholder}
            </Label>
          </div>
        )
      case "slider":
        return (
          <Slider
            id={name}
            name={name}
            value={[value as number]}
            onValueChange={(vals) => handleValueChange(vals[0])}
            disabled={disabled}
            min={min || 0}
            max={max || 100}
            step={step || 1}
          />
        )
      case "date":
        return (
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !value && "text-muted-foreground",
                  error && "border-destructive"
                )}
                disabled={disabled}
                type="button"
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {value ? format(value as Date, "PPP", { locale: zhCN }) : placeholder || "选择日期"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={value as Date}
                onSelect={handleValueChange}
                disabled={disabled}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        )
      default:
        return children || null
    }
  }

  return (
    <div className={cn("space-y-2 mb-4", className)}>
      {label && (
        <div className="flex items-center">
          <Label
            htmlFor={name}
            className={cn(
              "text-sm font-medium",
              required && "after:content-['*'] after:ml-0.5 after:text-destructive"
            )}
          >
            {label}
          </Label>
          
          {description && (
            <div className="ml-2 relative group">
              <Info className="h-4 w-4 text-muted-foreground cursor-help" />
              <div className="hidden group-hover:block absolute z-10 bottom-full mb-2 p-2 bg-popover border rounded shadow-md text-xs w-48">
                {description}
              </div>
            </div>
          )}
        </div>
      )}
      
      {renderControl()}
      
      {error && (
        <div className="flex items-center text-destructive text-xs">
          <AlertCircle className="h-3 w-3 mr-1" />
          {error}
        </div>
      )}
    </div>
  )
} 
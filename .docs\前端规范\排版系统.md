# 排版系统

排版是用户界面的基础要素，良好的排版系统能提升可读性、可扫描性，并建立清晰的信息层级。我们的排版系统受Notion简洁美学的启发，专注于阅读体验和信息结构。

## 字体家族

我们使用现代无衬线字体作为主要字体，辅以等宽字体用于代码展示。

```css
:root {
  --font-sans: 'Inter var', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
  --font-mono: 'Fira Code', Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
}
```

### 字体应用规则

1. 正文内容使用变量字体（如Inter var）以获得更细腻的字重渐变
2. 确保字体可降级到系统默认字体，保证跨平台一致性
3. 代码块、技术内容使用等宽字体，提高代码可读性

## 文字层级体系

我们采用6级文字层级，从标题到辅助文字，建立清晰的视觉层次结构。

### 标题体系

```css
/* 标题层级 */
.h1 {
  font-size: 2.5rem; /* 40px */
  line-height: 1.4; /* 56px */
  font-weight: 600;
  letter-spacing: -0.02em;
  margin-bottom: 1.5rem;
}

.h2 {
  font-size: 2rem; /* 32px */
  line-height: 1.375; /* 44px */
  font-weight: 600;
  letter-spacing: -0.015em;
  margin-bottom: 1.25rem;
}

.h3 {
  font-size: 1.5rem; /* 24px */
  line-height: 1.333; /* 32px */
  font-weight: 500;
  letter-spacing: -0.01em;
  margin-bottom: 1rem;
}

.h4 {
  font-size: 1.25rem; /* 20px */
  line-height: 1.4; /* 28px */
  font-weight: 500;
  margin-bottom: 0.75rem;
}

.h5 {
  font-size: 1.125rem; /* 18px */
  line-height: 1.333; /* 24px */
  font-weight: 500;
  margin-bottom: 0.5rem;
}
```

### 正文与辅助文字

```css
/* 正文体系 */
.text-base {
  font-size: 1rem; /* 16px */
  line-height: 1.5; /* 24px */
  font-weight: 400;
}

.text-sm {
  font-size: 0.875rem; /* 14px */
  line-height: 1.428; /* 20px */
  font-weight: 400;
}

.text-xs {
  font-size: 0.75rem; /* 12px */
  line-height: 1.333; /* 16px */
  font-weight: 400;
}
```

### 特殊文字样式

```css
/* 强调文本 */
.text-lead {
  font-size: 1.125rem; /* 18px */
  line-height: 1.555; /* 28px */
  color: var(--foreground);
}

/* 引用文本 */
.text-quote {
  border-left: 4px solid var(--border);
  padding-left: 1rem;
  font-style: italic;
  color: var(--foreground-muted);
}

/* 代码文本 */
.text-code {
  font-family: var(--font-mono);
  font-size: 0.9em;
  background: var(--muted);
  padding: 0.2em 0.4em;
  border-radius: 0.25rem;
}
```

## 响应式排版规则

我们的排版系统会根据设备尺寸自动调整，确保在各种屏幕上都有良好的阅读体验。

| 屏幕尺寸 | 正文大小 | 行高 | 标题缩放比例 |
|---------|---------|------|-----------|
| 移动设备 (<640px) | 16px | 1.5 (24px) | 0.875x |
| 平板设备 (640px-1024px) | 16px | 1.5 (24px) | 1x |
| 桌面设备 (>1024px) | 18px | 1.555 (28px) | 1.125x |

### 响应式实现

```css
/* 在媒体查询中调整基础文字大小 */
@media (min-width: 1024px) {
  html {
    font-size: 112.5%; /* 18px */
  }
  
  .text-base {
    line-height: 1.555; /* 28px */
  }
}
```

## 排版间距系统

文本元素之间的间距对可读性至关重要，我们采用基于8px单位的间距系统。

### 垂直间距规则

1. **段落间距**：为段落行高的0.75倍（如24px行高，段落间距为18px）
2. **标题后间距**：标题下方到内容的间距比标题前间距大
3. **相关内容分组**：相关内容间距小于不同内容块间距
4. **区块间距**：不同内容区块之间使用24px或32px间距

```css
/* 垂直间距示例 */
h1, h2, h3, h4, h5 {
  margin-top: 2em; /* 与前内容的间距为字体大小的2倍 */
  margin-bottom: 0.75em; /* 与后内容的间距为字体大小的0.75倍 */
}

p + p {
  margin-top: 0.75em; /* 相邻段落间距 */
}

section + section {
  margin-top: 3rem; /* 48px，区块间距 */
}
```

## 文本对齐与阅读宽度

为了最佳阅读体验，我们对文本长度和对齐方式有以下规范：

1. **最佳阅读宽度**：文本容器宽度控制在65-75字符（约650-750px）
2. **默认对齐**：西文采用左对齐，避免使用两端对齐
3. **长文本处理**：长篇文章使用更宽的行间距（至少为字体大小的1.5倍）
4. **标题与内容对齐**：标题与其内容左对齐，建立清晰的视觉关系

```css
/* 阅读宽度控制 */
.prose {
  max-width: 65ch; /* 约65字符宽度 */
  margin: 0 auto; /* 居中 */
}

/* 长文本样式 */
.prose p {
  line-height: 1.6;
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}

/* 移动设备全宽 */
@media (max-width: 640px) {
  .prose {
    max-width: none;
    padding: 0 1rem;
  }
}
```

## 字体颜色与对比度

我们依据无障碍标准，确保字体颜色具有足够对比度：

1. **主要文本**：使用`var(--foreground)`，与背景对比度≥4.5:1
2. **次要文本**：使用`var(--muted-foreground)`，对比度≥4.5:1
3. **禁用文本**：使用`var(--muted-foreground/60)`，明显区分于常规文本
4. **链接文本**：使用`var(--primary)`或下划线，确保可与普通文本区分

```css
/* 文本颜色示例 */
.text-primary {
  color: var(--foreground);
}

.text-secondary {
  color: var(--muted-foreground);
}

.text-disabled {
  color: color-mix(in srgb, var(--muted-foreground) 60%, transparent);
}

.text-link {
  color: var(--primary);
  text-decoration: underline;
  text-decoration-thickness: 0.1em;
  text-underline-offset: 0.15em;
  transition: text-decoration-color 0.2s ease;
}
```

## 实际应用示例

### 标题和段落

```jsx
<h1 className="text-4xl font-semibold tracking-tight lg:text-5xl">
  页面标题
</h1>

<p className="text-base text-muted-foreground leading-7 mb-6">
  次要说明文字
</p>

<div className="prose prose-slate">
  <p>这是一段长文本内容...</p>
</div>
```

### 表单标签与字段

```jsx
<div className="space-y-2">
  <Label 
    htmlFor="name" 
    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
  >
    用户名
  </Label>
  <Input id="name" className="text-base" />
  <p className="text-xs text-muted-foreground">
    请输入您的用户名，长度为3-20个字符
  </p>
</div>
```

## 最佳实践

### 推荐做法

1. **简化字体变体**：限制字重变化，通常使用3-4种字重
2. **保持一致间距**：在整个UI中保持一致的文本间距规则
3. **使用相对单位**：使用rem和em单位而非固定像素值
4. **确保断词**：长单词在小屏幕上使用`overflow-wrap: break-word`

### 避免事项

1. **避免纯黑文字**：使用深灰色代替#000，减轻视觉疲劳
2. **避免过多行高**：正文行高控制在1.4-1.6之间
3. **避免过多字体混用**：通常整个应用只需1-2种字体家族
4. **避免全大写**：除特定UI元素外，避免使用全大写文本

/**
 * 请求取消令牌
 */
class CancelToken {
  promise: Promise<any>;
  reason?: string;
  
  constructor(executor: (cancel: (reason?: string) => void) => void) {
    let resolvePromise: (reason?: string) => void = () => {};
    
    this.promise = new Promise((resolve) => {
      resolvePromise = resolve;
    });

    executor((reason) => {
      if (this.reason) {
        // 已取消，不再执行
        return;
      }
      this.reason = reason;
      resolvePromise(reason);
    });
  }
  
  static source() {
    let cancel!: (reason?: string) => void;
    const token = new CancelToken((c) => {
      cancel = c;
    });
    return {
      token,
      cancel,
    };
  }
  
  throwIfRequested() {
    if (this.reason) {
      throw new Cancel(this.reason);
    }
  }
}

/**
 * 请求取消异常类
 */
class Cancel {
  message: string;
  
  constructor(message: any = '请求已取消') {
    this.message = String(message || '请求已取消');
  }

  toString() {
    return `Cancel: ${this.message}`;
  }
}

/**
 * 判断是否是取消异常
 */
function isCancel(value: any): boolean {
  return value instanceof Cancel;
}

export { CancelToken, Cancel, isCancel }; 
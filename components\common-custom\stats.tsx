"use client"

import React, { ReactNode } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  TrendingUp,
  TrendingDown,
  LucideIcon,
} from "lucide-react"

// ============================================================================
// 类型定义
// ============================================================================

/**
 * 统计卡片属性
 */
export interface StatsCardProps {
  /**
   * 标题
   */
  title: string

  /**
   * 数值
   */
  value: string | number

  /**
   * 变化量
   */
  change?: number | string

  /**
   * 变化趋势
   */
  trend?: "up" | "down" | "neutral"

  /**
   * 描述
   */
  description?: string

  /**
   * 图标
   */
  icon?: ReactNode | LucideIcon

  /**
   * 无变化是否显示趋势箭头
   */
  showTrendArrow?: boolean

  /**
   * 自定义类名
   */
  className?: string

  /**
   * 点击事件
   */
  onClick?: () => void

  /**
   * 背景渐变
   */
  gradient?: boolean

  /**
   * 自定义颜色
   */
  color?: string
}

/**
 * 数值展示属性
 */
export interface ValueDisplayProps {
  /**
   * 值
   */
  value: number | string

  /**
   * 标签
   */
  label?: string

  /**
   * 前缀
   */
  prefix?: ReactNode

  /**
   * 后缀
   */
  suffix?: ReactNode

  /**
   * 变化量
   */
  change?: number

  /**
   * 是否显示变化趋势
   */
  showTrend?: boolean

  /**
   * 格式化函数
   */
  formatter?: (value: number | string) => string

  /**
   * 是否加载中
   */
  loading?: boolean

  /**
   * 自定义类名
   */
  className?: string
}

/**
 * 百分比环图属性
 */
export interface ProgressCircleProps {
  /**
   * 值(0-100)
   */
  value: number

  /**
   * 大小
   */
  size?: number | string

  /**
   * 线条宽度
   */
  strokeWidth?: number

  /**
   * 标签
   */
  label?: string

  /**
   * 描述
   */
  description?: string

  /**
   * 是否显示值
   */
  showValue?: boolean

  /**
   * 是否显示文本标签
   */
  showLabel?: boolean

  /**
   * 颜色
   */
  color?: string

  /**
   * 自定义前景色
   */
  strokeColor?: string

  /**
   * 自定义背景色
   */
  trailColor?: string

  /**
   * 格式化函数
   */
  formatter?: (value: number) => string

  /**
   * 自定义类名
   */
  className?: string
}

/**
 * 统计数据组属性
 */
export interface StatsGroupProps {
  /**
   * 统计卡片列表
   */
  stats: StatsCardProps[]

  /**
   * 卡片间距
   */
  gap?: number | string

  /**
   * 列数配置
   */
  columns?: {
    default?: number
    sm?: number
    md?: number
    lg?: number
    xl?: number
  }

  /**
   * 自定义类名
   */
  className?: string
}

/**
 * 指标比较属性
 */
export interface MetricCompareProps {
  /**
   * 当前值
   */
  current: number

  /**
   * 对比值
   */
  previous: number

  /**
   * 指标名称
   */
  label: string

  /**
   * 显示百分比
   */
  showPercentage?: boolean

  /**
   * 对比标签
   */
  compareLabel?: string

  /**
   * 上升是否为好
   */
  increaseIsGood?: boolean

  /**
   * 格式化函数
   */
  formatter?: (value: number) => string

  /**
   * 自定义类名
   */
  className?: string
}

/**
 * 数据趋势图属性
 */
export interface TrendChartProps {
  /**
   * 数据
   */
  data: number[]

  /**
   * 标签
   */
  labels?: string[]

  /**
   * 高度
   */
  height?: number | string

  /**
   * 线条颜色
   */
  strokeColor?: string

  /**
   * 填充颜色
   */
  fillColor?: string

  /**
   * 是否填充
   */
  fill?: boolean

  /**
   * 是否平滑曲线
   */
  smooth?: boolean

  /**
   * 是否显示网格线
   */
  showGrid?: boolean

  /**
   * 是否显示工具提示
   */
  showTooltip?: boolean

  /**
   * 线条宽度
   */
  strokeWidth?: number

  /**
   * 自定义类名
   */
  className?: string
}

// ============================================================================
// 组件实现
// ============================================================================

/**
 * 统计卡片组件
 */
export function StatsCard({
  title,
  value,
  change,
  trend = "neutral",
  description,
  icon: Icon,
  showTrendArrow = true,
  className = "",
}: StatsCardProps) {
  const getTrendColor = () => {
    if (trend === "up") return "text-green-600 dark:text-green-400"
    if (trend === "down") return "text-red-600 dark:text-red-400"
    return "text-gray-500 dark:text-gray-400"
  }

  const TrendIcon = trend === "up" ? TrendingUp : trend === "down" ? TrendingDown : null

  // 渲染图标
  const renderIcon = () => {
    if (!Icon) return null

    // 如果是 React 元素，直接返回
    if (React.isValidElement(Icon)) {
      return Icon
    }

    // 如果是组件类型，实例化它
    if (typeof Icon === 'function') {
      const IconComponent = Icon as React.ComponentType<any>
      return <IconComponent className="h-4 w-4 text-muted-foreground" />
    }

    return null
  }

  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {renderIcon()}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold mb-2">{value}</div>
        {(change || description) && (
          <div className={`flex items-center text-xs mb-3 ${getTrendColor()}`}>
            {showTrendArrow && TrendIcon && <TrendIcon className="h-3 w-3 mr-1" />}
            {change && <span>{change} </span>}
            {description && <span className="text-muted-foreground">{description}</span>}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

/**
 * 带进度条的KPI卡片组件
 */
export function KpiStatsCard({
  title,
  value,
  change,
  trend = "neutral",
  period,
  target,
  progress,
  icon: Icon,
}: {
  title: string
  value: string | number
  change?: string
  trend?: "up" | "down" | "neutral"
  period?: string
  target?: string | number
  progress?: number
  icon?: LucideIcon
}) {
  // 渲染图标
  const renderIcon = () => {
    if (!Icon) return null

    // 如果是 React 元素，直接返回
    if (React.isValidElement(Icon)) {
      return Icon
    }

    // 如果是组件类型，实例化它
    if (typeof Icon === 'function') {
      const IconComponent = Icon as React.ComponentType<any>
      return <IconComponent className="h-4 w-4 text-muted-foreground" />
    }

    return null
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {renderIcon()}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold mb-2">{value}</div>
        {change && (
          <div
            className={`flex items-center text-xs mb-3 ${
              trend === "up" ? "text-green-600 dark:text-green-400" : "text-red-600 dark:text-red-400"
            }`}
          >
            {trend === "up" ? (
              <TrendingUp className="h-3 w-3 mr-1" />
            ) : (
              <TrendingDown className="h-3 w-3 mr-1" />
            )}
            {change} {period}
          </div>
        )}
        {target && progress !== undefined && (
          <div className="space-y-2">
            <div className="flex justify-between text-xs">
              <span>目标: {target}</span>
              <span>{progress}%</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>
        )}
      </CardContent>
    </Card>
  )
}

/**
 * 流量来源项组件
 */
export function TrafficSourceItem({
  source,
  visitors,
  percentage,
  color,
}: {
  source: string
  visitors: number
  percentage: number
  color: string
}) {
  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-3">
        <div className={`w-3 h-3 rounded-full ${color}`} />
        <span className="text-sm font-medium">{source}</span>
      </div>
      <div className="text-right">
        <div className="text-sm font-medium">{visitors.toLocaleString()}</div>
        <div className="text-xs text-muted-foreground">{percentage}%</div>
      </div>
    </div>
  )
}

/**
 * 流量来源列表组件
 */
export function TrafficSourcesList({
  sources,
  title = "访问来源",
  description = "网站流量来源分布",
}: {
  sources: Array<{
    source: string
    visitors: number
    percentage: number
    color: string
  }>
  title?: string
  description?: string
}) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {sources.map((item, index) => (
            <TrafficSourceItem
              key={index}
              source={item.source}
              visitors={item.visitors}
              percentage={item.percentage}
              color={item.color}
            />
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

/**
 * 性能指标项组件
 */
export function PerformanceMetricItem({
  metric,
  value,
  status,
  benchmark,
}: {
  metric: string
  value: string
  status: "excellent" | "good" | "warning" | "poor"
  benchmark?: string
}) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "excellent":
        return "bg-green-100 text-green-800 hover:bg-green-100"
      case "good":
        return "bg-blue-100 text-blue-800 hover:bg-blue-100"
      case "warning":
        return "bg-yellow-100 text-yellow-800 hover:bg-yellow-100"
      case "poor":
        return "bg-red-100 text-red-800 hover:bg-red-100"
      default:
        return "bg-gray-100 text-gray-800 hover:bg-gray-100"
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case "excellent":
        return "优秀"
      case "good":
        return "良好"
      case "warning":
        return "警告"
      case "poor":
        return "较差"
      default:
        return "未知"
    }
  }

  return (
    <div className="p-4 border rounded-lg">
      <div className="flex items-center justify-between mb-2">
        <span className="text-sm font-medium">{metric}</span>
        <Badge className={getStatusColor(status)}>{getStatusText(status)}</Badge>
      </div>
      <div className="text-2xl font-bold mb-1">{value}</div>
      {benchmark && <div className="text-xs text-muted-foreground">基准: {benchmark}</div>}
    </div>
  )
}

/**
 * 性能指标组组件
 */
export function PerformanceMetricsGroup({
  metrics,
  title = "性能监控",
  description = "系统关键性能指标监控",
}: {
  metrics: Array<{
    metric: string
    value: string
    status: "excellent" | "good" | "warning" | "poor"
    benchmark?: string
  }>
  title?: string
  description?: string
}) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {metrics.map((metric, index) => (
            <PerformanceMetricItem
              key={index}
              metric={metric.metric}
              value={metric.value}
              status={metric.status}
              benchmark={metric.benchmark}
            />
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

/**
 * 销售数据项组件
 */
export function SalesDataItem({
  label,
  current,
  target,
  growth,
}: {
  label: string
  current: number
  target: number
  growth: number
}) {
  return (
    <div className="space-y-2">
      <div className="flex justify-between text-sm">
        <span>{label}</span>
        <span className="font-medium">
          ¥{current.toLocaleString()} / ¥{target.toLocaleString()}
        </span>
      </div>
      <div className="flex items-center gap-2">
        <Progress value={(current / target) * 100} className="flex-1 h-2" />
        <div
          className={`flex items-center text-xs ${growth > 0 ? "text-green-600 dark:text-green-400" : "text-red-600 dark:text-red-400"}`}
        >
          {growth > 0 ? (
            <TrendingUp className="h-3 w-3 mr-1" />
          ) : (
            <TrendingDown className="h-3 w-3 mr-1" />
          )}
          {Math.abs(growth)}%
        </div>
      </div>
    </div>
  )
}

/**
 * 销售数据列表组件
 */
export function SalesDataList({
  salesData,
  title = "月度销售对比",
  description = "实际销售 vs 目标销售",
}: {
  salesData: Array<{
    month: string
    sales: number
    target: number
    growth: number
  }>
  title?: string
  description?: string
}) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {salesData.map((item, index) => (
            <SalesDataItem
              key={index}
              label={item.month}
              current={item.sales}
              target={item.target}
              growth={item.growth}
            />
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

/**
 * 销售统计概览组件
 */
export function SalesOverview({
  totalSales,
  salesTarget,
  completionRate,
  orderCount,
  averageOrderValue,
}: {
  totalSales: string
  salesTarget: string
  completionRate: number
  orderCount: number
  averageOrderValue: string
}) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>销售统计概览</CardTitle>
        <CardDescription>关键销售指标汇总</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">{totalSales}</div>
              <div className="text-sm text-muted-foreground">总销售额</div>
            </div>
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{salesTarget}</div>
              <div className="text-sm text-muted-foreground">销售目标</div>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm">目标完成率</span>
              <span className="text-sm font-medium">{completionRate}%</span>
            </div>
            <Progress value={completionRate} className="h-2" />
          </div>

          <div className="grid grid-cols-2 gap-4 text-center">
            <div>
              <div className="text-lg font-bold">{orderCount.toLocaleString()}</div>
              <div className="text-xs text-muted-foreground">订单数量</div>
            </div>
            <div>
              <div className="text-lg font-bold">{averageOrderValue}</div>
              <div className="text-xs text-muted-foreground">平均订单价值</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

/**
 * 实时数据卡片组件
 */
export function RealtimeStatsCard({
  title,
  value,
  description,
  icon: Icon,
  color = "green",
}: {
  title: string
  value: string | number
  description?: string
  icon?: LucideIcon
  color?: "green" | "blue" | "purple" | "orange" | "red"
}) {
  const getColorClass = () => {
    switch (color) {
      case "green":
        return "text-green-600 dark:text-green-400"
      case "blue":
        return "text-blue-600 dark:text-blue-400"
      case "purple":
        return "text-purple-600 dark:text-purple-400"
      case "orange":
        return "text-orange-600 dark:text-orange-400"
      case "red":
        return "text-red-600 dark:text-red-400"
      default:
        return "text-green-600 dark:text-green-400"
    }
  }

  const getIconColorClass = () => {
    switch (color) {
      case "green":
        return "text-green-500 dark:text-green-400"
      case "blue":
        return "text-blue-500 dark:text-blue-400"
      case "purple":
        return "text-purple-500 dark:text-purple-400"
      case "orange":
        return "text-orange-500 dark:text-orange-400"
      case "red":
        return "text-red-500 dark:text-red-400"
      default:
        return "text-green-500 dark:text-green-400"
    }
  }

  // 渲染图标
  const renderIcon = () => {
    if (!Icon) return null

    // 如果是 React 元素，直接返回
    if (React.isValidElement(Icon)) {
      return Icon
    }

    // 如果是组件类型，实例化它
    if (typeof Icon === 'function') {
      const IconComponent = Icon as React.ComponentType<any>
      return <IconComponent className={`h-4 w-4 ${getIconColorClass()}`} />
    }

    return null
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {renderIcon()}
      </CardHeader>
      <CardContent>
        <div className={`text-2xl font-bold ${getColorClass()}`}>{value}</div>
        {description && <div className="text-xs text-muted-foreground">{description}</div>}
      </CardContent>
    </Card>
  )
}

/**
 * 统计组件容器
 */
export function StatsContainer({ children }: { children: ReactNode }) {
  return <div className="space-y-8">{children}</div>
} 
{"info": {"_postman_id": "9e2d8f3a-8e6d-4a75-a358-c2a3c987d9e2", "name": "KeelCloud", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "分组管理", "item": [{"name": "分页查询分组", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"code\": \"\",\n    \"name\": \"\",\n    \"moduleCode\": \"\",\n    \"parentId\": null,\n    \"status\": 1,\n    \"pageNum\": 1,\n    \"pageSize\": 10\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/independence/group/page", "host": ["{{baseUrl}}"], "path": ["api", "independence", "group", "page"]}, "description": "分页查询分组信息\n\n**请求参数说明：**\n- code：分组编码（模糊查询）\n- name：分组名称（模糊查询）\n- moduleCode：模块Code\n- parentId：父分组ID\n- status：状态（0-禁用，1-启用）\n- pageNum：页码\n- pageSize：每页条数\n\n**返回结果示例：**\n```json\n{\n    \"code\": 200,\n    \"message\": \"操作成功\",\n    \"data\": {\n        \"total\": 10,\n        \"list\": [\n            {\n                \"id\": 1,\n                \"code\": \"group001\",\n                \"name\": \"测试分组1\",\n                \"description\": \"这是一个测试分组\",\n                \"moduleCode\": \"system\",\n                \"parentId\": null,\n                \"parentName\": null,\n                \"sortOrder\": 0,\n                \"icon\": \"icon-folder\",\n                \"status\": 1,\n                \"statusLabel\": \"启用\",\n                \"createTime\": \"2023-05-10T10:12:30\",\n                \"updateTime\": \"2023-05-10T10:12:30\"\n            }\n        ],\n        \"pageNum\": 1,\n        \"pageSize\": 10,\n        \"pages\": 1\n    }\n}\n```"}, "response": []}, {"name": "查询分组列表", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"code\": \"\",\n    \"name\": \"\",\n    \"moduleCode\": \"\",\n    \"parentId\": null,\n    \"status\": 1\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/independence/group/list", "host": ["{{baseUrl}}"], "path": ["api", "independence", "group", "list"]}, "description": "查询分组列表信息\n\n**请求参数说明：**\n- code：分组编码（模糊查询）\n- name：分组名称（模糊查询）\n- moduleCode：模块Code\n- parentId：父分组ID\n- status：状态（0-禁用，1-启用）\n\n**返回结果示例：**\n```json\n{\n    \"code\": 200,\n    \"message\": \"操作成功\",\n    \"data\": [\n        {\n            \"id\": 1,\n            \"code\": \"group001\",\n            \"name\": \"测试分组1\",\n            \"description\": \"这是一个测试分组\",\n            \"moduleCode\": \"system\",\n            \"parentId\": null,\n            \"parentName\": null,\n            \"sortOrder\": 0,\n            \"icon\": \"icon-folder\",\n            \"status\": 1,\n            \"statusLabel\": \"启用\",\n            \"createTime\": \"2023-05-10T10:12:30\",\n            \"updateTime\": \"2023-05-10T10:12:30\"\n        }\n    ]\n}\n```"}, "response": []}, {"name": "根据ID查询分组", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/independence/group/get?id=1", "host": ["{{baseUrl}}"], "path": ["api", "independence", "group", "get"], "query": [{"key": "id", "value": "1", "description": "分组ID"}]}, "description": "根据ID查询分组详情\n\n**请求参数说明：**\n- id：分组ID\n\n**返回结果示例：**\n```json\n{\n    \"code\": 200,\n    \"message\": \"操作成功\",\n    \"data\": {\n        \"id\": 1,\n        \"code\": \"group001\",\n        \"name\": \"测试分组1\",\n        \"description\": \"这是一个测试分组\",\n        \"moduleCode\": \"system\",\n        \"parentId\": null,\n        \"parentName\": null,\n        \"sortOrder\": 0,\n        \"icon\": \"icon-folder\",\n        \"status\": 1,\n        \"statusLabel\": \"启用\",\n        \"createTime\": \"2023-05-10T10:12:30\",\n        \"updateTime\": \"2023-05-10T10:12:30\"\n    }\n}\n```"}, "response": []}, {"name": "创建分组", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"code\": \"group001\",\n    \"name\": \"测试分组1\",\n    \"description\": \"这是一个测试分组\",\n    \"moduleCode\": \"system\",\n    \"parentId\": null,\n    \"sortOrder\": 0,\n    \"icon\": \"icon-folder\",\n    \"status\": 1\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/independence/group/create", "host": ["{{baseUrl}}"], "path": ["api", "independence", "group", "create"]}, "description": "创建分组\n\n**请求参数说明：**\n- code：分组编码（必填，唯一）\n- name：分组名称（必填）\n- description：分组描述\n- moduleCode：模块Code\n- parentId：父分组ID\n- sortOrder：排序序号\n- icon：分组图标URL\n- status：状态（0-禁用，1-启用）\n\n**返回结果示例：**\n```json\n{\n    \"code\": 200,\n    \"message\": \"操作成功\",\n    \"data\": 1\n}\n```"}, "response": []}, {"name": "更新分组", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"id\": 1,\n    \"code\": \"group001\",\n    \"name\": \"测试分组1-更新\",\n    \"description\": \"这是一个测试分组-更新\",\n    \"moduleCode\": \"system\",\n    \"parentId\": null,\n    \"sortOrder\": 0,\n    \"icon\": \"icon-folder\",\n    \"status\": 1\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/independence/group/update", "host": ["{{baseUrl}}"], "path": ["api", "independence", "group", "update"]}, "description": "更新分组\n\n**请求参数说明：**\n- id：分组ID（必填）\n- code：分组编码（必填，唯一）\n- name：分组名称（必填）\n- description：分组描述\n- moduleCode：模块Code\n- parentId：父分组ID\n- sortOrder：排序序号\n- icon：分组图标URL\n- status：状态（0-禁用，1-启用）\n\n**返回结果示例：**\n```json\n{\n    \"code\": 200,\n    \"message\": \"操作成功\",\n    \"data\": true\n}\n```"}, "response": []}, {"name": "删除分组", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/independence/group/delete?id=1", "host": ["{{baseUrl}}"], "path": ["api", "independence", "group", "delete"], "query": [{"key": "id", "value": "1", "description": "分组ID"}]}, "description": "删除分组\n\n**请求参数说明：**\n- id：分组ID\n\n**返回结果示例：**\n```json\n{\n    \"code\": 200,\n    \"message\": \"操作成功\",\n    \"data\": true\n}\n```"}, "response": []}]}, {"name": "角色管理", "item": [{"name": "分页查询角色", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"roleName\": \"\",\n    \"roleCode\": \"\",\n    \"teamCode\": \"\",\n    \"status\": 1,\n    \"pageNum\": 1,\n    \"pageSize\": 10\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/auth/role/page", "host": ["{{baseUrl}}"], "path": ["api", "auth", "role", "page"]}, "description": "分页查询角色信息\n\n**请求参数说明：**\n- roleName：角色名称（模糊查询）\n- roleCode：角色编码（模糊查询）\n- teamCode：团队编码\n- status：状态（0-禁用，1-启用）\n- pageNum：页码\n- pageSize：每页条数\n\n**返回结果示例：**\n```json\n{\n    \"code\": 200,\n    \"message\": \"操作成功\",\n    \"data\": {\n        \"total\": 10,\n        \"list\": [\n            {\n                \"id\": 1,\n                \"teamCode\": \"team001\",\n                \"teamName\": \"测试团队\",\n                \"roleName\": \"管理员\",\n                \"roleCode\": \"admin\",\n                \"description\": \"系统管理员角色\",\n                \"status\": 1,\n                \"statusLabel\": \"启用\",\n                \"permissionCount\": 15,\n                \"createTime\": \"2023-05-10T10:12:30\",\n                \"updateTime\": \"2023-05-10T10:12:30\"\n            }\n        ],\n        \"pageNum\": 1,\n        \"pageSize\": 10,\n        \"pages\": 1\n    }\n}\n```"}, "response": []}, {"name": "查询角色列表", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"roleName\": \"\",\n    \"roleCode\": \"\",\n    \"teamCode\": \"\",\n    \"status\": 1\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/auth/role/listAll", "host": ["{{baseUrl}}"], "path": ["api", "auth", "role", "listAll"]}, "description": "查询角色列表信息\n\n**请求参数说明：**\n- roleName：角色名称（模糊查询）\n- roleCode：角色编码（模糊查询）\n- teamCode：团队编码\n- status：状态（0-禁用，1-启用）\n\n**返回结果示例：**\n```json\n{\n    \"code\": 200,\n    \"message\": \"操作成功\",\n    \"data\": [\n        {\n            \"id\": 1,\n            \"teamCode\": \"team001\",\n            \"teamName\": \"测试团队\",\n            \"roleName\": \"管理员\",\n            \"roleCode\": \"admin\",\n            \"description\": \"系统管理员角色\",\n            \"status\": 1,\n            \"statusLabel\": \"启用\",\n            \"permissionCount\": 15,\n            \"createTime\": \"2023-05-10T10:12:30\",\n            \"updateTime\": \"2023-05-10T10:12:30\"\n        }\n    ]\n}\n```"}, "response": []}, {"name": "根据ID查询角色", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/auth/role/getById?id=1", "host": ["{{baseUrl}}"], "path": ["api", "auth", "role", "getById"], "query": [{"key": "id", "value": "1", "description": "角色ID"}]}, "description": "根据ID查询角色详情\n\n**请求参数说明：**\n- id：角色ID\n\n**返回结果示例：**\n```json\n{\n    \"code\": 200,\n    \"message\": \"操作成功\",\n    \"data\": {\n        \"id\": 1,\n        \"teamCode\": \"team001\",\n        \"teamName\": \"测试团队\",\n        \"roleName\": \"管理员\",\n        \"roleCode\": \"admin\",\n        \"description\": \"系统管理员角色\",\n        \"status\": 1,\n        \"statusLabel\": \"启用\",\n        \"permissionCount\": 15,\n        \"createTime\": \"2023-05-10T10:12:30\",\n        \"updateTime\": \"2023-05-10T10:12:30\"\n    }\n}\n```"}, "response": []}, {"name": "根据编码查询角色", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/auth/role/getByCode?roleCode=admin", "host": ["{{baseUrl}}"], "path": ["api", "auth", "role", "getByCode"], "query": [{"key": "roleCode", "value": "admin", "description": "角色编码"}]}, "description": "根据编码查询角色详情\n\n**请求参数说明：**\n- roleCode：角色编码\n\n**返回结果示例：**\n```json\n{\n    \"code\": 200,\n    \"message\": \"操作成功\",\n    \"data\": {\n        \"id\": 1,\n        \"teamCode\": \"team001\",\n        \"teamName\": \"测试团队\",\n        \"roleName\": \"管理员\",\n        \"roleCode\": \"admin\",\n        \"description\": \"系统管理员角色\",\n        \"status\": 1,\n        \"statusLabel\": \"启用\",\n        \"permissionCount\": 15,\n        \"createTime\": \"2023-05-10T10:12:30\",\n        \"updateTime\": \"2023-05-10T10:12:30\"\n    }\n}\n```"}, "response": []}, {"name": "创建角色", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"teamCode\": \"team001\",\n    \"roleName\": \"管理员\",\n    \"roleCode\": \"admin\",\n    \"description\": \"系统管理员角色\",\n    \"status\": 1\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/auth/role/create", "host": ["{{baseUrl}}"], "path": ["api", "auth", "role", "create"]}, "description": "创建角色\n\n**请求参数说明：**\n- teamCode：团队编码（必填）\n- roleName：角色名称（必填）\n- roleCode：角色编码（必填，唯一）\n- description：角色描述\n- status：状态（0-禁用，1-启用）\n\n**返回结果示例：**\n```json\n{\n    \"code\": 200,\n    \"message\": \"操作成功\",\n    \"data\": 1\n}\n```"}, "response": []}, {"name": "更新角色", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"id\": 1,\n    \"teamCode\": \"team001\",\n    \"roleName\": \"管理员-更新\",\n    \"roleCode\": \"admin\",\n    \"description\": \"系统管理员角色-更新\",\n    \"status\": 1\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/auth/role/update", "host": ["{{baseUrl}}"], "path": ["api", "auth", "role", "update"]}, "description": "更新角色\n\n**请求参数说明：**\n- id：角色ID（必填）\n- teamCode：团队编码（必填）\n- roleName：角色名称（必填）\n- roleCode：角色编码（必填，唯一）\n- description：角色描述\n- status：状态（0-禁用，1-启用）\n\n**返回结果示例：**\n```json\n{\n    \"code\": 200,\n    \"message\": \"操作成功\",\n    \"data\": true\n}\n```"}, "response": []}, {"name": "删除角色", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/auth/role/delete?id=1", "host": ["{{baseUrl}}"], "path": ["api", "auth", "role", "delete"], "query": [{"key": "id", "value": "1", "description": "角色ID"}]}, "description": "删除角色\n\n**请求参数说明：**\n- id：角色ID\n\n**返回结果示例：**\n```json\n{\n    \"code\": 200,\n    \"message\": \"操作成功\",\n    \"data\": true\n}\n```"}, "response": []}, {"name": "获取团队下的角色选项", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/auth/role/options?teamCode=team001", "host": ["{{baseUrl}}"], "path": ["api", "auth", "role", "options"], "query": [{"key": "teamCode", "value": "team001", "description": "团队编码"}]}, "description": "获取团队下的角色选项列表\n\n**请求参数说明：**\n- teamCode：团队编码\n\n**返回结果示例：**\n```json\n{\n    \"code\": 200,\n    \"message\": \"操作成功\",\n    \"data\": [\n        {\n            \"label\": \"管理员\",\n            \"value\": \"admin\"\n        },\n        {\n            \"label\": \"普通用户\",\n            \"value\": \"user\"\n        }\n    ]\n}\n```"}, "response": []}, {"name": "为角色分配权限", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"roleCode\": \"admin\",\n    \"permissionCodes\": [\"system:user:view\", \"system:user:edit\", \"system:role:view\"]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/auth/role/assignPermissions", "host": ["{{baseUrl}}"], "path": ["api", "auth", "role", "assignPermissions"]}, "description": "为角色分配权限\n\n**请求参数说明：**\n- roleCode：角色编码（必填）\n- permissionCodes：权限编码列表（必填）\n\n**返回结果示例：**\n```json\n{\n    \"code\": 200,\n    \"message\": \"操作成功\",\n    \"data\": true\n}\n```"}, "response": []}, {"name": "获取角色的权限编码列表", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/auth/role/permissions?roleCode=admin", "host": ["{{baseUrl}}"], "path": ["api", "auth", "role", "permissions"], "query": [{"key": "roleCode", "value": "admin", "description": "角色编码"}]}, "description": "获取角色的权限编码列表\n\n**请求参数说明：**\n- roleCode：角色编码\n\n**返回结果示例：**\n```json\n{\n    \"code\": 200,\n    \"message\": \"操作成功\",\n    \"data\": [\n        \"system:user:view\",\n        \"system:user:edit\",\n        \"system:role:view\"\n    ]\n}\n```"}, "response": []}]}, {"name": "用户角色关联", "item": [{"name": "为用户分配角色", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"userId\": 1,\n    \"teamCode\": \"team001\",\n    \"roleCodes\": [\"admin\", \"user\"]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/auth/user-role/assign", "host": ["{{baseUrl}}"], "path": ["api", "auth", "user-role", "assign"]}, "description": "为用户分配角色\n\n**请求参数说明：**\n- userId：用户ID（必填）\n- teamCode：团队编码（必填）\n- roleCodes：角色编码列表（必填）\n\n**返回结果示例：**\n```json\n{\n    \"code\": 200,\n    \"message\": \"操作成功\",\n    \"data\": true\n}\n```"}, "response": []}, {"name": "获取用户在团队中的角色列表", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/auth/user-role/list?userId=1&teamCode=team001", "host": ["{{baseUrl}}"], "path": ["api", "auth", "user-role", "list"], "query": [{"key": "userId", "value": "1", "description": "用户ID"}, {"key": "teamCode", "value": "team001", "description": "团队编码"}]}, "description": "获取用户在团队中的角色列表\n\n**请求参数说明：**\n- userId：用户ID\n- teamCode：团队编码\n\n**返回结果示例：**\n```json\n{\n    \"code\": 200,\n    \"message\": \"操作成功\",\n    \"data\": [\n        \"admin\",\n        \"user\"\n    ]\n}\n```"}, "response": []}, {"name": "获取用户角色详情", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/auth/user-role/detail?userId=1&teamCode=team001", "host": ["{{baseUrl}}"], "path": ["api", "auth", "user-role", "detail"], "query": [{"key": "userId", "value": "1", "description": "用户ID"}, {"key": "teamCode", "value": "team001", "description": "团队编码"}]}, "description": "获取用户角色详情\n\n**请求参数说明：**\n- userId：用户ID\n- teamCode：团队编码\n\n**返回结果示例：**\n```json\n{\n    \"code\": 200,\n    \"message\": \"操作成功\",\n    \"data\": {\n        \"userId\": 1,\n        \"userName\": \"张三\",\n        \"teamCode\": \"team001\",\n        \"teamName\": \"测试团队\",\n        \"roles\": [\n            {\n                \"roleCode\": \"admin\",\n                \"roleName\": \"管理员\",\n                \"createTime\": \"2023-05-10T10:12:30\"\n            },\n            {\n                \"roleCode\": \"user\",\n                \"roleName\": \"普通用户\",\n                \"createTime\": \"2023-05-10T10:12:30\"\n            }\n        ]\n    }\n}\n```"}, "response": []}, {"name": "移除用户的所有角色", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/auth/user-role/remove?userId=1&teamCode=team001", "host": ["{{baseUrl}}"], "path": ["api", "auth", "user-role", "remove"], "query": [{"key": "userId", "value": "1", "description": "用户ID"}, {"key": "teamCode", "value": "team001", "description": "团队编码"}]}, "description": "移除用户的所有角色\n\n**请求参数说明：**\n- userId：用户ID\n- teamCode：团队编码\n\n**返回结果示例：**\n```json\n{\n    \"code\": 200,\n    \"message\": \"操作成功\",\n    \"data\": true\n}\n```"}, "response": []}, {"name": "检查用户是否拥有指定角色", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/auth/user-role/hasRole?userId=1&teamCode=team001&roleCode=admin", "host": ["{{baseUrl}}"], "path": ["api", "auth", "user-role", "hasRole"], "query": [{"key": "userId", "value": "1", "description": "用户ID"}, {"key": "teamCode", "value": "team001", "description": "团队编码"}, {"key": "roleCode", "value": "admin", "description": "角色编码"}]}, "description": "检查用户是否拥有指定角色\n\n**请求参数说明：**\n- userId：用户ID\n- teamCode：团队编码\n- roleCode：角色编码\n\n**返回结果示例：**\n```json\n{\n    \"code\": 200,\n    \"message\": \"操作成功\",\n    \"data\": true\n}\n```"}, "response": []}]}]}
"use client"

import { useState, ReactNode } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { Separator } from "@/components/ui/separator"
import { cn } from "@/lib/utils"
import {
  ChevronDown,
  ChevronRight,
  Check,
  Circle,
  Clock,
  LucideIcon
} from "lucide-react"
// ============================================================================
// 类型定义
// ============================================================================

/**
 * 步骤状态类型
 */
export type StepStatus = "pending" | "current" | "completed"

/**
 * 步骤项属性
 */
export interface StepItemProps {
  title: string
  description?: string
  status?: StepStatus
  icon?: ReactNode
  isLast?: boolean
  onContinue?: () => void
  className?: string
}

/**
 * 步骤项数据
 */
export interface StepItem {
  id: number
  title: string
  description?: string
  icon?: ReactNode
  status?: StepStatus
}

/**
 * 步骤条属性
 */
export interface StepperProps {
  steps: StepItem[]
  currentStep: number
  onStepChange?: (step: number) => void
  className?: string
}

/**
 * FAQ项属性
 */
export interface FAQItemProps {
  question: string
  answer: string
  isOpen: boolean
  onToggle: () => void
  className?: string
}

/**
 * FAQ项数据
 */
export interface FAQItem {
  id: number
  question: string
  answer: string
  category?: string
}

/**
 * FAQ组属性
 */
export interface FAQGroupProps {
  faqs: FAQItem[]
  title?: string
  description?: string
  searchable?: boolean
  categorized?: boolean
  className?: string
}

/**
 * 分组列表项
 */
export interface GroupedListItem {
  id: string | number
  title: string
  description?: string
  icon?: ReactNode | LucideIcon
  badge?: string
  href?: string
  onClick?: () => void
  disabled?: boolean
  meta?: Record<string, any>
}

/**
 * 分组列表属性
 */
export interface GroupedListProps {
  title?: string
  description?: string
  items: GroupedListItem[]
  showDividers?: boolean
  compact?: boolean
  className?: string
}

/**
 * 设置项属性
 */
export interface SettingsItemProps {
  title: string
  description?: string
  icon?: ReactNode | LucideIcon
  action?: ReactNode
  disabled?: boolean
  className?: string
}

/**
 * 设置组属性
 */
export interface SettingsGroupProps {
  title?: string
  description?: string
  items: SettingsItemProps[]
  className?: string
}

/**
 * 可折叠组项属性
 */
export interface CollapsibleGroupItemProps {
  title: string
  content: ReactNode
  icon?: ReactNode | LucideIcon
  badge?: string
  defaultOpen?: boolean
  disabled?: boolean
  className?: string
}

/**
 * 可折叠组属性
 */
export interface CollapsibleGroupProps {
  title?: string
  description?: string
  items: CollapsibleGroupItemProps[]
  allowMultiple?: boolean
  className?: string
}

// ============================================================================
// 组件实现
// ============================================================================

/**
 * 步骤条项组件
 */
export function StepItem({
  title,
  description,
  status = "pending",
  icon,
  isLast = false,
  onContinue,
  className
}: StepItemProps) {
  return (
    <div className={cn("flex items-start gap-4", className)}>
      <div className="flex flex-col items-center">
        <div
          className={cn(
            "flex items-center justify-center w-8 h-8 rounded-full border-2",
            status === "completed"
              ? "bg-primary border-primary text-primary-foreground"
              : status === "current"
                ? "border-primary text-primary"
                : "border-muted-foreground text-muted-foreground"
          )}
        >
          {status === "completed" ? (
            <Check className="h-4 w-4" />
          ) : status === "current" ? (
            icon || <Clock className="h-4 w-4" />
          ) : (
            <Circle className="h-4 w-4" />
          )}
        </div>
        {!isLast && (
          <div className={cn("w-0.5 h-8 mt-2", status === "completed" ? "bg-primary" : "bg-muted")} />
        )}
      </div>
      <div className="flex-1 pb-8">
        <h4 className={cn("font-medium", status === "current" ? "text-primary" : "")}>{title}</h4>
        {description && <p className="text-sm text-muted-foreground">{description}</p>}
        {status === "current" && onContinue && (
          <Button size="sm" className="mt-2 cursor-pointer" onClick={onContinue}>
            继续
          </Button>
        )}
      </div>
    </div>
  )
}

/**
 * 步骤条组件
 */
export function Stepper({
  steps,
  currentStep,
  onStepChange,
  className
}: StepperProps) {
  return (
    <div className={cn("space-y-4", className)}>
      {steps.map((step, index) => {
        const status = 
          step.id < currentStep ? "completed" : 
          step.id === currentStep ? "current" : "pending"
        
        return (
          <StepItem
            key={step.id}
            title={step.title}
            description={step.description}
            status={status}
            icon={step.icon}
            isLast={index === steps.length - 1}
            onContinue={status === "current" && onStepChange ? () => onStepChange(step.id + 1) : undefined}
          />
        )
      })}
    </div>
  )
}

/**
 * FAQ项组件
 */
export function FAQItem({
  question,
  answer,
  isOpen,
  onToggle,
  className
}: FAQItemProps) {
  return (
    <Collapsible open={isOpen} onOpenChange={onToggle} className={className}>
      <CollapsibleTrigger asChild>
        <Button
          variant="ghost"
          className="w-full justify-between p-4 h-auto text-left cursor-pointer hover:bg-muted/50"
        >
          <span className="font-medium">{question}</span>
          {isOpen ? (
            <ChevronDown className="h-4 w-4" />
          ) : (
            <ChevronRight className="h-4 w-4" />
          )}
        </Button>
      </CollapsibleTrigger>
      <CollapsibleContent className="px-4 pb-4">
        <p className="text-sm text-muted-foreground leading-relaxed">{answer}</p>
      </CollapsibleContent>
    </Collapsible>
  )
}

/**
 * FAQ分组组件
 */
export function FAQGroup({
  title,
  description,
  icon,
  items,
  className
}: FAQGroupProps) {
  const [openItems, setOpenItems] = useState<string[]>([])

  const toggleItem = (id: string) => {
    setOpenItems((prev) => 
      prev.includes(id) 
        ? prev.filter((item) => item !== id) 
        : [...prev, id]
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        {title && (
          <CardTitle className="flex items-center gap-2">
            {icon}
            {title}
          </CardTitle>
        )}
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent className="space-y-2">
        {items.map((item, index) => (
          <FAQItem
            key={item.id || index}
            question={item.question}
            answer={item.answer}
            isOpen={openItems.includes(item.id || `faq-${index}`)}
            onToggle={() => toggleItem(item.id || `faq-${index}`)}
          />
        ))}
      </CardContent>
    </Card>
  )
}

/**
 * 分组列表项组件
 */
export function GroupedListItem({
  name,
  count,
  status,
  className
}: GroupedListItem) {
  const getStatusVariant = () => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 hover:bg-green-100"
      case "pending":
        return "bg-yellow-100 text-yellow-800 hover:bg-yellow-100"
      case "inactive":
      default:
        return "bg-gray-100 text-gray-800 hover:bg-gray-100"
    }
  }

  const getStatusText = () => {
    switch (status) {
      case "active":
        return "正常"
      case "pending":
        return "待处理"
      case "inactive":
      default:
        return "禁用"
    }
  }

  return (
    <div className={cn("flex items-center justify-between", className)}>
      <div className="flex items-center gap-2">
        <span className="text-sm">{name}</span>
        {count > 0 && (
          <Badge variant="secondary" className="text-xs">
            {count}
          </Badge>
        )}
      </div>
      <Badge
        variant={
          status === "active" ? "default" : status === "pending" ? "secondary" : "outline"
        }
        className={getStatusVariant()}
      >
        {getStatusText()}
      </Badge>
    </div>
  )
}

/**
 * 分组列表组件
 */
export function GroupedList({
  title,
  icon,
  items,
  className
}: GroupedListProps) {
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-base">
          {icon}
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {items.map((item, index) => (
            <GroupedListItem
              key={index}
              name={item.name}
              count={item.count}
              status={item.status}
            />
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

/**
 * 设置项组件
 */
export function SettingsItem({
  label,
  description,
  onClick,
  className
}: SettingsItemProps) {
  return (
    <button
      onClick={onClick}
      className={cn(
        "w-full flex items-center justify-between p-3 rounded-lg hover:bg-muted/50 transition-colors cursor-pointer text-left",
        className
      )}
    >
      <div>
        <div className="font-medium text-sm">{label}</div>
        {description && <div className="text-xs text-muted-foreground">{description}</div>}
      </div>
      <ChevronRight className="h-4 w-4 text-muted-foreground" />
    </button>
  )
}

/**
 * 设置分组组件
 */
export function SettingsGroup({
  title,
  icon,
  items,
  showSeparator = true,
  isLast = false,
  className
}: SettingsGroupProps) {
  return (
    <div className={className}>
      <div className="flex items-center gap-2 mb-3">
        {icon}
        <h4 className="font-medium">{title}</h4>
      </div>
      <div className="space-y-2 ml-6">
        {items.map((item, index) => (
          <SettingsItem
            key={index}
            label={item.label}
            description={item.description}
            onClick={item.onClick}
          />
        ))}
      </div>
      {showSeparator && !isLast && <Separator className="mt-4" />}
    </div>
  )
}

/**
 * 可折叠分组项组件
 */
export function CollapsibleGroupItem({
  title,
  children,
  className
}: CollapsibleGroupItemProps) {
  return (
    <div className={cn("flex items-center justify-between", className)}>
      <div>
        <div className="font-medium">{title}</div>
        {children}
      </div>
    </div>
  )
}

/**
 * 可折叠分组组件
 */
export function CollapsibleGroup({
  title,
  icon,
  isOpen,
  onToggle,
  children,
  className
}: CollapsibleGroupProps) {
  return (
    <Card className={className}>
      <Collapsible open={isOpen} onOpenChange={onToggle}>
        <CollapsibleTrigger asChild>
          <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {icon}
                <CardTitle className="text-base">{title}</CardTitle>
              </div>
              {isOpen ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </div>
          </CardHeader>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <CardContent>
            <div className="space-y-4">
              {children}
            </div>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  )
} 
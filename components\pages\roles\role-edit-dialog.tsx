"use client"

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Loader2, Save } from "lucide-react";
import { toast } from "sonner";
import { Role } from "@/types/models";
import { updateRoleRequest } from "@/services/api/roleRequestApi";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from "@/components/ui/dialog";
import { nanoid } from "nanoid";

export default function RoleEditDialog() {
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [currentRole, setCurrentRole] = useState<Role | null>(null);
  
  // 表单状态
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    code: ""
  });
  
  // 监听自定义事件以打开弹窗
  useEffect(() => {
    const handleOpenDialog = (event: CustomEvent<{ role: Role }>) => {
      const { role } = event.detail;
      setCurrentRole(role);
      setFormData({
        name: role.name,
        description: role.description || "",
        code: role.code || ""
      });
      setOpen(true);
    };

    // 添加事件监听器
    window.addEventListener('open-edit-role-dialog', handleOpenDialog as EventListener);

    // 清理事件监听器
    return () => {
      window.removeEventListener('open-edit-role-dialog', handleOpenDialog as EventListener);
    };
  }, []);
  
  // 处理表单输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  // 生成角色编码
  const generateRoleCode = () => {
    // 基于当前时间戳和随机字符生成唯一编码
    const timestamp = Date.now().toString(36).slice(-4);
    const random = nanoid(4).toUpperCase();
    const newCode = `ROLE_${timestamp}${random}`;
    
    setFormData(prev => ({
      ...prev,
      code: newCode
    }));
  };
  
  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!currentRole) return;
    
    if (!formData.name.trim()) {
      toast.error("角色名称不能为空");
      return;
    }
    
    setIsSaving(true);
    
    try {
      await updateRoleRequest(currentRole._id, {
        name: formData.name.trim(),
        description: formData.description.trim(),
        code: formData.code.trim() || undefined
      });
      
      toast.success("角色更新成功");
      
      // 更新成功后关闭弹窗并刷新页面
      setOpen(false);
      router.refresh();
    } catch (error) {
      console.error("更新角色失败", error);
      toast.error("更新角色失败，请稍后重试");
    } finally {
      setIsSaving(false);
    }
  };
  
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>编辑角色</DialogTitle>
          <DialogDescription>
            修改角色信息，设置角色名称和描述
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="name">
              角色名称 <span className="text-destructive">*</span>
            </Label>
            <Input
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              placeholder="请输入角色名称"
              required
              className="border border-input/30"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="code">角色编码</Label>
            <div className="flex gap-2">
              <Input
                id="code"
                name="code"
                value={formData.code}
                onChange={handleInputChange}
                placeholder="请输入角色编码"
                className="border border-input/30"
                readOnly={currentRole?.code ? true : false}
              />
              {!currentRole?.code && (
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={generateRoleCode}
                  className="cursor-pointer shrink-0"
                >
                  生成
                </Button>
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              角色编码用于系统内部标识，只能在创建时设置
            </p>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="description">角色描述</Label>
            <Textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              placeholder="请输入角色描述"
              rows={4}
              className="border border-input/30"
            />
          </div>
          
          <DialogFooter>
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => setOpen(false)}
              disabled={isSaving}
              className="cursor-pointer"
            >
              取消
            </Button>
            <Button 
              type="submit"
              disabled={isSaving || !formData.name.trim()}
              className="cursor-pointer"
            >
              {isSaving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  保存中...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  保存修改
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
} 
"use client";

import React from "react";
import dynamic from "next/dynamic";

// 动态导入客户端组件，设置ssr: false
const ClientPreviewComponent = dynamic(
  () => import("./client-preview-component"),
  {
    ssr: false,
    loading: () => null
  }
);

interface PreviewClientProps {
  code: string;
  title: string;
}

export default function PreviewClient({ code }: PreviewClientProps) {
  // 直接渲染组件，没有任何包装元素
  return <ClientPreviewComponent code={code} />;
} 
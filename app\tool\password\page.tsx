"use client"


import {AuthScreen} from "@/components/pages/passwordmanager/auth-screen";
import {HeaderWithBreadcrumb} from "@/components/custom/breadcrumb";

export default function PasswordManagerPage() {

    // 面包屑数据
    const breadcrumbItems = [
        { label: "工具" },
        { label: "密码管理器", isCurrent: true }
    ]

    return (
        <div className="flex flex-col min-h-screen">
            <HeaderWithBreadcrumb items={breadcrumbItems}/>
            <main className="flex-1">
                <div className="container mx-auto px-6 py-6 max-w-7xl">
                    <AuthScreen />
                </div>
            </main>
        </div>
    )
} 

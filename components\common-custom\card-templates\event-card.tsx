"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Calendar, Clock, MapPin, Users } from "lucide-react"

/**
 * 活动卡片组件
 * 用于展示活动信息
 */
export function EventCard({
  data
}: {
  data: {
    title: string
    description?: string
    date?: string
    time?: string
    location?: string
    image?: string
    attendees?: number
    categories?: string[]
  }
}) {
  return (
    <Card className="overflow-hidden transition-all hover:shadow-md">
      {data.image && (
        <div className="h-40 bg-muted">
          <img 
            src={data.image} 
            alt={data.title} 
            className="w-full h-full object-cover"
          />
        </div>
      )}
      <CardHeader className="pt-4 pb-2">
        <div className="space-y-1">
          <CardTitle className="line-clamp-2">{data.title}</Card<PERSON>itle>
          {data.categories && data.categories.length > 0 && (
            <div className="flex gap-1 flex-wrap">
              {data.categories.map((category, index) => (
                <Badge key={index} variant="secondary" className="text-xs px-2 py-0">
                  {category}
                </Badge>
              ))}
            </div>
          )}
          {data.description && (
            <CardDescription className="line-clamp-2 mt-1">
              {data.description}
            </CardDescription>
          )}
        </div>
      </CardHeader>
      <CardContent className="pb-2 pt-0">
        <div className="space-y-2 text-sm text-muted-foreground">
          {(data.date || data.time) && (
            <div className="flex items-start">
              <Calendar className="h-4 w-4 mr-2 mt-0.5" />
              <div>
                {data.date}
                {data.date && data.time && <> · </>}
                {data.time && (
                  <span className="flex items-center">
                    <Clock className="h-3 w-3 mr-1 inline" />
                    {data.time}
                  </span>
                )}
              </div>
            </div>
          )}
          {data.location && (
            <div className="flex items-start">
              <MapPin className="h-4 w-4 mr-2 mt-0.5" />
              <div>{data.location}</div>
            </div>
          )}
          {data.attendees !== undefined && data.attendees > 0 && (
            <div className="flex items-center">
              <Users className="h-4 w-4 mr-2" />
              <div>{data.attendees} 人参加</div>
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter>
        <Button className="w-full">查看详情</Button>
      </CardFooter>
    </Card>
  );
} 
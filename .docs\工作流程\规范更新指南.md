# 规范更新指南

本文档规定了如何识别、记录和更新优秀的规范性设计，确保docs下的文档持续改进。

## 📋 更新原则

根据核心指导规范要求：**遇到优秀的规范性设计要更新docs下对应的文档**

### 更新触发条件
1. **发现更优秀的实践**：在开发过程中发现更好的解决方案
2. **技术栈升级**：技术栈更新带来的新的最佳实践
3. **用户反馈**：基于用户使用反馈的改进
4. **行业标准变化**：行业标准或最佳实践的变化

## 🔍 优秀规范识别标准

### 技术层面
- **性能提升**：显著提高系统性能
- **代码质量**：提高代码可读性、可维护性
- **开发效率**：减少开发时间和复杂度
- **错误减少**：减少常见错误和bug

### 用户体验层面
- **易用性提升**：提高用户操作的便利性
- **一致性改进**：增强界面和交互的一致性
- **无障碍性**：改善无障碍访问体验
- **响应性能**：提升界面响应速度

### 维护层面
- **文档完善**：提供更清晰的文档和示例
- **测试覆盖**：增加测试覆盖率和质量
- **扩展性**：提高系统的可扩展性
- **兼容性**：改善跨平台兼容性

## 📝 更新流程

### 1. 识别阶段
```markdown
## 优秀实践识别记录

### 发现信息
- **发现时间**：YYYY-MM-DD
- **发现人员**：[姓名]
- **发现场景**：[具体场景描述]
- **相关任务**：[关联的任务ID]

### 实践描述
- **实践内容**：[详细描述优秀实践]
- **技术细节**：[技术实现细节]
- **适用范围**：[适用的场景和条件]

### 优势分析
- **性能提升**：[具体的性能改进]
- **质量提升**：[代码质量改进]
- **效率提升**：[开发效率改进]
- **其他优势**：[其他方面的改进]
```

### 2. 评估阶段
使用MCP工具进行评估：

```javascript
// 1. 使用sequential-thinking分析实践价值
sequentialthinking_sequential_thinking({
  thought: "分析这个优秀实践的价值和适用性",
  nextThoughtNeeded: true,
  thoughtNumber: 1,
  totalThoughts: 3
})

// 2. 使用memory记录评估结果
create_entities_memory({
  entities: [{
    name: "excellent-practice-evaluation",
    entityType: "evaluation",
    observations: ["实践价值", "适用范围", "更新建议"]
  }]
})

// 3. 使用feedback-enhanced确认更新计划
collect_feedback_feedback_enhanced({
  work_summary: "## 规范更新计划\n\n详细的更新计划..."
})
```

### 3. 更新阶段

#### 确定更新范围
- **影响的文档**：列出需要更新的具体文档
- **更新类型**：新增/修改/删除
- **影响范围**：通用/前端/项目特定

#### 执行更新
```javascript
// 1. 备份原有文档
read_file_filesystem({
  path: "docs/target-document.md"
})

// 2. 使用str-replace-editor更新文档
str_replace_editor({
  command: "str_replace",
  path: "docs/target-document.md",
  old_str: "原有内容",
  new_str: "更新后内容"
})

// 3. 记录更新历史
create_entities_memory({
  entities: [{
    name: "document-update-history",
    entityType: "update_record",
    observations: ["更新时间", "更新内容", "更新原因"]
  }]
})
```

### 4. 验证阶段
- **内容验证**：确保更新内容的准确性
- **格式验证**：确保文档格式的一致性
- **链接验证**：确保文档间链接的有效性
- **实践验证**：在实际项目中验证更新的有效性

## 📋 更新记录模板

### 文档更新记录
```markdown
## 更新记录

### [YYYY-MM-DD] - 版本 X.X
**更新类型**：新增/修改/删除
**更新人员**：[姓名]
**更新原因**：[更新的具体原因]

#### 更新内容
- [具体的更新内容1]
- [具体的更新内容2]
- [具体的更新内容3]

#### 影响范围
- **文档影响**：[影响的其他文档]
- **实践影响**：[对开发实践的影响]
- **工具影响**：[对工具使用的影响]

#### 验证结果
- [ ] 内容准确性验证通过
- [ ] 格式一致性验证通过
- [ ] 链接有效性验证通过
- [ ] 实践有效性验证通过
```

## 🗂️ 文档分类更新指南

### 通用规范文档更新
适用于前后端通用的规范更新：
- **AI协作规范**：新的协作模式或工具使用方法
- **MCP工具指南**：新工具集成或使用技巧
- **API接口规范**：通用的API设计模式

### 前端规范文档更新
适用于前端特定的规范更新：
- **设计哲学**：新的设计理念或原则
- **组件开发规范**：新的组件模式或最佳实践
- **样式规范**：新的CSS技巧或设计系统更新

### 项目规范文档更新
适用于项目特定的规范更新：
- **实施指南**：项目特定的开发流程改进
- **配置规范**：项目配置的优化方案

## 🔄 持续改进机制

### 定期评审
- **月度评审**：每月评审一次文档更新情况
- **季度总结**：每季度总结规范改进成果
- **年度规划**：每年制定规范改进计划

### 反馈收集
- **开发者反馈**：收集开发团队的使用反馈
- **用户反馈**：收集最终用户的体验反馈
- **社区反馈**：关注开源社区的最佳实践

### 质量保证
- **同行评议**：重要更新需要同行评议
- **实践验证**：在实际项目中验证更新效果
- **文档审核**：确保文档质量和一致性

## 📋 检查清单

### 更新前检查
- [ ] 确认优秀实践的价值和适用性
- [ ] 确定需要更新的具体文档
- [ ] 评估更新的影响范围
- [ ] 制定详细的更新计划

### 更新中检查
- [ ] 备份原有文档内容
- [ ] 按照模板格式进行更新
- [ ] 保持文档间的一致性
- [ ] 更新相关的链接和引用

### 更新后检查
- [ ] 验证更新内容的准确性
- [ ] 检查文档格式的一致性
- [ ] 测试相关链接的有效性
- [ ] 在实际项目中验证效果

## 🚀 最佳实践

1. **及时更新**：发现优秀实践后及时记录和更新
2. **详细记录**：完整记录更新的原因和过程
3. **影响评估**：充分评估更新对其他文档的影响
4. **验证确认**：确保更新后的文档在实际使用中有效
5. **持续改进**：基于使用反馈持续优化规范文档

通过遵循这个更新指南，可以确保docs下的文档始终保持最新和最优的状态。

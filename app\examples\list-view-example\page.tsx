"use client"

import React from "react"
import { ComponentPreviewContainer } from "@/components/component-detail/preview-container"
import { 
  MessageList, 
  NotificationList,
  CommentList,
  UserList,
  ListViewControls,
  LoadMoreButton
} from "@/components/common-custom/list-view"
import { 
  Search,
  Filter,
  MessageCircle,
  Share2,
  Clock,
  Star,
  Eye,
  ThumbsUp,
  Calendar,
  MapPin,
  Check,
  X
} from "lucide-react"

// 消息列表代码示例
const messageListCode = `
import React from "react"
import { MessageList } from "@/components/common-custom/list-view"

function MessageListExample() {
  const messages = [
    {
      id: 1,
      sender: "张三",
      avatar: "/placeholder-user.jpg",
      subject: "关于项目进度的讨论",
      preview: "我们需要加快进度，希望能在本周五之前完成第一阶段的开发工作...",
      time: "10:30",
      unread: true,
      important: true
    },
    {
      id: 2,
      sender: "李四",
      avatar: "/placeholder-user.jpg",
      subject: "设计稿确认",
      preview: "请查看最新的设计稿，并给出您的反馈意见。我们需要尽快确定最终方案。",
      time: "昨天",
      unread: false,
      important: false
    },
    {
      id: 3,
      sender: "王五",
      avatar: "/placeholder-user.jpg",
      subject: "会议安排",
      preview: "下周一上午10点将举行项目进展汇报会，请做好准备并准时参加。",
      time: "星期二",
      unread: false,
      important: false
    }
  ]
  
  return (
    <MessageList 
      title="消息列表"
      description="您的最新消息和对话"
      messages={messages}
      onMessageAction={(message) => console.log("消息操作:", message)}
    />
  )
}

render(<MessageListExample />)
`

// 通知列表代码示例
const notificationListCode = `
import React from "react"
import { NotificationList } from "@/components/common-custom/list-view"

function NotificationListExample() {
  const notifications = [
    {
      id: 1,
      type: "system",
      title: "系统升级通知",
      message: "系统将于今晚22:00-24:00进行升级维护，请提前保存相关工作。",
      time: "10分钟前",
      read: false
    },
    {
      id: 2,
      type: "security",
      title: "安全提醒",
      message: "您的账户刚刚在新设备上登录，如非本人操作，请立即修改密码。",
      time: "1小时前",
      read: true
    },
    {
      id: 3,
      type: "order",
      title: "订单状态更新",
      message: "您的订单#12345已发货，预计3-5天送达。",
      time: "昨天",
      read: true
    }
  ]
  
  return (
    <NotificationList
      notifications={notifications}
      title="通知中心"
      description="系统通知和重要提醒"
      onNotificationClick={(notification) => console.log("点击通知:", notification)}
    />
  )
}

render(<NotificationListExample />)
`

// 评论列表代码示例
const commentListCode = `
import React from "react"
import { CommentList } from "@/components/common-custom/list-view"

function CommentListExample() {
  const comments = [
    {
      id: 1,
      author: "张三",
      avatar: "/placeholder-user.jpg",
      content: "这个设计方案非常棒，我很喜欢整体的布局和色彩搭配。",
      time: "2小时前",
      likes: 12,
      replies: 3,
      rating: 5
    },
    {
      id: 2,
      author: "李四",
      avatar: "/placeholder-user.jpg",
      content: "我认为导航栏的交互可以再优化一下，目前的用户体验不是很流畅。",
      time: "昨天",
      likes: 5,
      replies: 2,
      rating: 3
    }
  ]
  
  return (
    <CommentList
      title="评论列表"
      description="最新评论和反馈"
      comments={comments}
      onCommentLike={(comment) => console.log("点赞评论:", comment)}
      onCommentReply={(comment) => console.log("回复评论:", comment)}
      onCommentShare={(comment) => console.log("分享评论:", comment)}
    />
  )
}

render(<CommentListExample />)
`

// 用户列表代码示例
const userListCode = `
import React from "react"
import { UserList } from "@/components/common-custom/list-view"

function UserListExample() {
  const users = [
    {
      id: 1,
      name: "张三",
      avatar: "/placeholder-user.jpg",
      role: "前端开发工程师",
      department: "技术部",
      location: "北京",
      joinDate: "2022-05-15",
      status: "online",
      projects: 8
    },
    {
      id: 2,
      name: "李四",
      avatar: "/placeholder-user.jpg",
      role: "产品经理",
      department: "产品部",
      location: "上海",
      joinDate: "2021-10-20",
      status: "busy",
      projects: 12
    }
  ]
  
  return (
    <UserList
      title="用户列表"
      description="系统用户和成员"
      users={users}
      onUserMessage={(user) => console.log("发送消息给用户:", user)}
      onUserDetails={(user) => console.log("查看用户详情:", user)}
    />
  )
}

render(<UserListExample />)
`

// 列表视图控件代码示例
const listViewControlsCode = `
import React, { useState } from "react"
import { ListViewControls } from "@/components/common-custom/list-view"

function ListViewControlsExample() {
  const [searchValue, setSearchValue] = useState("")
  const [currentView, setCurrentView] = useState("grid")
  
  const viewTypes = [
    { value: "grid", label: "网格视图" },
    { value: "list", label: "列表视图" },
    { value: "table", label: "表格视图" }
  ]
  
  const filters = [
    {
      id: "status",
      label: "状态",
      type: "select",
      options: [
        { label: "全部", value: "all" },
        { label: "活跃", value: "active" },
        { label: "已禁用", value: "disabled" }
      ],
      defaultValue: "all"
    },
    {
      id: "type",
      label: "类型",
      type: "radio",
      options: [
        { label: "全部", value: "all" },
        { label: "公开", value: "public" },
        { label: "私有", value: "private" }
      ],
      defaultValue: "all"
    },
    {
      id: "dateRange",
      label: "创建日期",
      type: "dateRange",
      placeholder: "选择日期范围"
    }
  ]
  
  return (
    <div className="w-full">
      <ListViewControls
        viewTypes={viewTypes}
        currentView={currentView}
        onViewChange={setCurrentView}
        searchValue={searchValue}
        onSearchChange={setSearchValue}
        filters={filters}
        onFilterChange={(id, value) => console.log("筛选器变更:", id, value)}
        onFilterApply={() => console.log("应用筛选器")}
        onFilterReset={() => console.log("重置筛选器")}
      />
      
      <div className="mt-4 p-6 border rounded-md">
        <div className="text-center text-muted-foreground">
          {currentView === "grid" ? "网格视图内容" : currentView === "list" ? "列表视图内容" : "表格视图内容"}
          {searchValue && <div className="mt-2">搜索: {searchValue}</div>}
        </div>
      </div>
    </div>
  )
}

render(<ListViewControlsExample />)
`

// 加载更多按钮代码示例
const loadMoreButtonCode = `
import React, { useState } from "react"
import { LoadMoreButton } from "@/components/common-custom/list-view"

function LoadMoreButtonExample() {
  const [loading, setLoading] = useState(false)
  
  const handleLoadMore = () => {
    setLoading(true)
    // 模拟加载数据
    setTimeout(() => {
      setLoading(false)
    }, 1500)
  }
  
  return (
    <div className="text-center">
      <div className="mb-4 p-4 border rounded-md">
        <p>列表内容会显示在这里...</p>
      </div>
      <LoadMoreButton
        onClick={handleLoadMore}
        loading={loading}
      />
    </div>
  )
}

render(<LoadMoreButtonExample />)
`

// API文档组件
function ListViewApiDocs() {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="font-medium text-lg mb-2">MessageList</h3>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">messages</td>
                <td className="p-2 border">MessageItemProps[]</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">消息数据数组</td>
              </tr>
              <tr>
                <td className="p-2 border">title</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">"消息列表"</td>
                <td className="p-2 border">列表标题</td>
              </tr>
              <tr>
                <td className="p-2 border">description</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">"您的最新消息和对话"</td>
                <td className="p-2 border">列表描述</td>
              </tr>
              <tr>
                <td className="p-2 border">onMessageAction</td>
                <td className="p-2 border">(message: MessageItemProps) =&gt; void</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">消息操作回调</td>
              </tr>
              <tr>
                <td className="p-2 border">className</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">自定义类名</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div>
        <h3 className="font-medium text-lg mb-2">NotificationList</h3>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">notifications</td>
                <td className="p-2 border">NotificationItemProps[]</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">通知数据数组</td>
              </tr>
              <tr>
                <td className="p-2 border">title</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">"通知中心"</td>
                <td className="p-2 border">列表标题</td>
              </tr>
              <tr>
                <td className="p-2 border">description</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">"系统通知和重要提醒"</td>
                <td className="p-2 border">列表描述</td>
              </tr>
              <tr>
                <td className="p-2 border">onNotificationClick</td>
                <td className="p-2 border">(notification: NotificationItemProps) =&gt; void</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">通知点击回调</td>
              </tr>
              <tr>
                <td className="p-2 border">className</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">自定义类名</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      
      <div>
        <h3 className="font-medium text-lg mb-2">ListViewControls</h3>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">viewTypes</td>
                <td className="p-2 border">Array&lt;{`{value: string, label: string}`}&gt;</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">视图类型选项</td>
              </tr>
              <tr>
                <td className="p-2 border">currentView</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">当前选中的视图类型</td>
              </tr>
              <tr>
                <td className="p-2 border">onViewChange</td>
                <td className="p-2 border">(viewId: string) =&gt; void</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">视图类型变更回调</td>
              </tr>
              <tr>
                <td className="p-2 border">searchValue</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">""</td>
                <td className="p-2 border">搜索框值</td>
              </tr>
              <tr>
                <td className="p-2 border">onSearchChange</td>
                <td className="p-2 border">(value: string) =&gt; void</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">搜索值变更回调</td>
              </tr>
              <tr>
                <td className="p-2 border">filters</td>
                <td className="p-2 border">FilterConfig[]</td>
                <td className="p-2 border">[]</td>
                <td className="p-2 border">筛选器配置</td>
              </tr>
              <tr>
                <td className="p-2 border">onFilterChange</td>
                <td className="p-2 border">(id: string, value: any) =&gt; void</td>
                <td className="p-2 border">() =&gt; {`{}`}</td>
                <td className="p-2 border">筛选器变更回调</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

export default function ListViewPreview() {
  const examples = [
    {
      id: "message-list",
      title: "消息列表",
      description: "用于展示用户收到的消息列表",
      code: messageListCode,
      scope: { 
        React, 
        MessageList 
      },
    },
    {
      id: "notification-list",
      title: "通知列表",
      description: "用于展示系统通知和用户提醒",
      code: notificationListCode,
      scope: { 
        React, 
        NotificationList 
      },
    },
    {
      id: "comment-list",
      title: "评论列表",
      description: "用于展示用户评论和反馈",
      code: commentListCode,
      scope: { 
        React, 
        CommentList 
      },
    },
    {
      id: "user-list",
      title: "用户列表",
      description: "用于展示系统用户和团队成员",
      code: userListCode,
      scope: { 
        React, 
        UserList 
      },
    },
    {
      id: "list-view-controls",
      title: "列表视图控件",
      description: "提供搜索、筛选和视图切换功能",
      code: listViewControlsCode,
      scope: { 
        React, 
        ListViewControls,
        useState: React.useState
      },
    },
    {
      id: "load-more-button",
      title: "加载更多按钮",
      description: "用于分页加载更多列表数据",
      code: loadMoreButtonCode,
      scope: { 
        React, 
        LoadMoreButton,
        useState: React.useState
      },
    },
  ];

  return (
    <ComponentPreviewContainer
      title="列表视图 ListView"
      description="用于展示各种类型列表数据的组件集合，包括消息列表、通知列表、评论列表、用户列表等。"
      whenToUse="当需要以列表形式展示结构化数据时使用，适用于消息中心、通知中心、评论系统、用户管理等场景。列表视图组件提供了丰富的展示方式和交互功能，支持搜索、筛选、分页等操作。"
      examples={examples}
      apiDocs={<ListViewApiDocs />}
    />
  );
} 
 
 
 

 
/**
 * 模态框组件示例代码集合
 * 
 * 按照新规范管理示例代码，避免在页面文件中硬编码
 */

import React from "react"
import { Modal, AlertDialogModal, FormModal, InfoModal, ModalTrigger } from "@/components/common-custom/modal"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { AlertTriangle, Info, CheckCircle, XCircle } from "lucide-react"

// ============================================================================
// 基础模态框示例
// ============================================================================

export const basicModalExample = {
  id: "basic-modal",
  title: "基础模态框",
  description: "展示基本的模态框功能，包含标题、内容和操作按钮",
  code: `
import React, { useState } from "react";
import { Modal } from "@/components/common-custom/modal";
import { Button } from "@/components/ui/button";

function BasicModalExample() {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleConfirm = async () => {
    setIsLoading(true);
    // 模拟异步操作
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsLoading(false);
    setIsOpen(false);
    console.log("操作已确认");
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-wrap gap-4">
        <Button onClick={() => setIsOpen(true)}>
          打开基础模态框
        </Button>
      </div>

      <Modal
        open={isOpen}
        onOpenChange={setIsOpen}
        title="基础模态框"
        description="这是一个基础的模态框示例，展示了标题、描述和操作按钮。"
        confirmButton={{
          label: "确认",
          loading: isLoading,
          onClick: handleConfirm,
        }}
        cancelButton={{
          label: "取消",
          variant: "outline",
          onClick: () => setIsOpen(false),
        }}
      >
        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">
            这里是模态框的主要内容区域。您可以在这里放置任何需要的内容，
            比如表单、信息展示、确认提示等。
          </p>
          <div className="p-4 bg-muted rounded-lg">
            <h4 className="font-medium mb-2">注意事项</h4>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• 模态框会阻止用户与背景内容的交互</li>
              <li>• 可以通过ESC键或点击遮罩层关闭</li>
              <li>• 支持多种尺寸和自定义样式</li>
            </ul>
          </div>
        </div>
      </Modal>
    </div>
  );
}

render(<BasicModalExample />);
  `,
  scope: { Modal, Button, React, useState: React.useState },
}

// ============================================================================
// 警告对话框示例
// ============================================================================

export const alertDialogExample = {
  id: "alert-dialog",
  title: "警告对话框",
  description: "展示不同类型的警告对话框，用于确认危险操作",
  code: `
import React, { useState } from "react";
import { AlertDialogModal } from "@/components/common-custom/modal";
import { Button } from "@/components/ui/button";
import { AlertTriangle, Info, CheckCircle, XCircle } from "lucide-react";

function AlertDialogExample() {
  const [warningOpen, setWarningOpen] = useState(false);
  const [dangerOpen, setDangerOpen] = useState(false);
  const [infoOpen, setInfoOpen] = useState(false);
  const [successOpen, setSuccessOpen] = useState(false);

  const handleDelete = () => {
    console.log("删除操作已确认");
    setDangerOpen(false);
  };

  const handleWarning = () => {
    console.log("警告操作已确认");
    setWarningOpen(false);
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-wrap gap-4">
        <Button 
          variant="outline" 
          onClick={() => setWarningOpen(true)}
        >
          警告对话框
        </Button>
        
        <Button 
          variant="destructive" 
          onClick={() => setDangerOpen(true)}
        >
          危险操作
        </Button>
        
        <Button 
          variant="secondary" 
          onClick={() => setInfoOpen(true)}
        >
          信息提示
        </Button>
        
        <Button 
          variant="default" 
          onClick={() => setSuccessOpen(true)}
        >
          成功提示
        </Button>
      </div>

      {/* 警告对话框 */}
      <AlertDialogModal
        open={warningOpen}
        onOpenChange={setWarningOpen}
        type="warning"
        icon={AlertTriangle}
        title="确认操作"
        description="此操作可能会影响系统性能，请确认是否继续？"
        confirmButton={{
          label: "继续",
          variant: "default",
          onClick: handleWarning,
        }}
        cancelButton={{
          label: "取消",
          variant: "outline",
        }}
      />

      {/* 危险操作对话框 */}
      <AlertDialogModal
        open={dangerOpen}
        onOpenChange={setDangerOpen}
        type="danger"
        icon={XCircle}
        title="删除确认"
        description="此操作不可撤销，确定要删除这些数据吗？"
        confirmButton={{
          label: "删除",
          variant: "destructive",
          onClick: handleDelete,
        }}
        cancelButton={{
          label: "取消",
          variant: "outline",
        }}
      />

      {/* 信息提示对话框 */}
      <AlertDialogModal
        open={infoOpen}
        onOpenChange={setInfoOpen}
        type="info"
        icon={Info}
        title="系统信息"
        description="系统将在今晚进行维护，预计持续2小时。"
        confirmButton={{
          label: "知道了",
          variant: "default",
        }}
      />

      {/* 成功提示对话框 */}
      <AlertDialogModal
        open={successOpen}
        onOpenChange={setSuccessOpen}
        type="success"
        icon={CheckCircle}
        title="操作成功"
        description="您的设置已成功保存！"
        confirmButton={{
          label: "确定",
          variant: "default",
        }}
      />
    </div>
  );
}

render(<AlertDialogExample />);
  `,
  scope: { AlertDialogModal, Button, AlertTriangle, Info, CheckCircle, XCircle, React, useState: React.useState },
}

// ============================================================================
// 表单模态框示例
// ============================================================================

export const formModalExample = {
  id: "form-modal",
  title: "表单模态框",
  description: "展示包含表单的模态框，用于数据输入和编辑",
  code: `
import React, { useState } from "react";
import { FormModal } from "@/components/common-custom/modal";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";

function FormModalExample() {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    message: "",
  });

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    
    // 模拟表单提交
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log("表单数据:", formData);
    setIsLoading(false);
    setIsOpen(false);
    
    // 重置表单
    setFormData({ name: "", email: "", message: "" });
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="space-y-4">
      <Button onClick={() => setIsOpen(true)}>
        打开表单模态框
      </Button>

      <FormModal
        open={isOpen}
        onOpenChange={setIsOpen}
        title="联系我们"
        description="请填写以下信息，我们会尽快与您联系。"
        onSubmit={handleSubmit}
        submitButton={{
          label: "提交",
          loading: isLoading,
        }}
        cancelButton={{
          label: "取消",
          variant: "outline",
        }}
        size="lg"
      >
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">姓名 *</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              placeholder="请输入您的姓名"
              required
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="email">邮箱 *</Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange("email", e.target.value)}
              placeholder="请输入您的邮箱"
              required
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="message">留言</Label>
            <Textarea
              id="message"
              value={formData.message}
              onChange={(e) => handleInputChange("message", e.target.value)}
              placeholder="请输入您的留言..."
              rows={4}
            />
          </div>
        </div>
      </FormModal>
    </div>
  );
}

render(<FormModalExample />);
  `,
  scope: { FormModal, Button, Input, Label, Textarea, React, useState: React.useState },
}

// ============================================================================
// 统一导出所有示例
// ============================================================================

export const allExamples = [
  basicModalExample,
  alertDialogExample,
  formModalExample,
]

// ============================================================================
// 按类别导出示例
// ============================================================================

export const basicExamples = [basicModalExample]
export const advancedExamples = [alertDialogExample, formModalExample]

// ============================================================================
// 示例元数据
// ============================================================================

export const exampleMetadata = {
  total: allExamples.length,
  categories: {
    basic: basicExamples.length,
    advanced: advancedExamples.length,
  },
  tags: ["modal", "dialog", "popup", "form", "alert", "confirmation"],
  lastUpdated: "2024-01-01",
}

"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Eye, UserX, Loader2 } from "lucide-react";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { formatDate } from "@/lib/utils";
import { removeUserFromRoleRequest } from "@/services/api/roleRequestApi";

interface User {
  _id: string;
  name: string;
  avatar?: string;
  email?: string;
  department?: string;
  status: "active" | "inactive";
  joinedAt?: string;
}

interface RoleUsersProps {
  roleId: string;
  users: User[];
  readOnly?: boolean;
  onUsersChange?: () => void;
}

export default function RoleUsers({ roleId, users, readOnly = false, onUsersChange }: RoleUsersProps) {
  const router = useRouter();
  const [isRemoving, setIsRemoving] = useState(false);
  const [userToRemove, setUserToRemove] = useState<User | null>(null);

  // 查看用户详情
  const handleViewUser = (userId: string) => {
    router.push(`/members/${userId}`);
  };

  // 移除用户
  const handleRemoveUser = async () => {
    if (!userToRemove) return;

    setIsRemoving(true);
    try {
      await removeUserFromRoleRequest(roleId, userToRemove._id);
      toast.success(`已从角色中移除用户 ${userToRemove.name}`);
      if (onUsersChange) {
        onUsersChange();
      }
    } catch (error) {
      console.error("移除用户失败", error);
      toast.error("移除用户失败，请稍后重试");
    } finally {
      setIsRemoving(false);
      setUserToRemove(null);
    }
  };

  // 生成头像文本
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .substring(0, 2);
  };

  if (users.length === 0) {
    return (
      <div className="flex items-center justify-center h-[200px] text-center">
        <div className="space-y-2">
          <p className="text-muted-foreground">该角色暂无关联用户</p>
        </div>
      </div>
    );
  }

  return (
    <div className="border rounded-md">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>用户</TableHead>
            <TableHead className="hidden md:table-cell">部门</TableHead>
            <TableHead className="hidden sm:table-cell">状态</TableHead>
            <TableHead className="hidden lg:table-cell">加入时间</TableHead>
            <TableHead className="text-right">操作</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {users.map((user) => (
            <TableRow key={user._id}>
              <TableCell>
                <div className="flex items-center gap-3">
                  <Avatar className="h-8 w-8">
                    {user.avatar ? (
                      <AvatarImage src={user.avatar} alt={user.name} />
                    ) : null}
                    <AvatarFallback>{getInitials(user.name)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium">{user.name}</p>
                    {user.email && (
                      <p className="text-xs text-muted-foreground">{user.email}</p>
                    )}
                  </div>
                </div>
              </TableCell>
              <TableCell className="hidden md:table-cell">
                {user.department || "-"}
              </TableCell>
              <TableCell className="hidden sm:table-cell">
                <Badge
                  variant={user.status === "active" ? "default" : "secondary"}
                  className="px-2 py-1"
                >
                  {user.status === "active" ? "活跃" : "未激活"}
                </Badge>
              </TableCell>
              <TableCell className="hidden lg:table-cell text-muted-foreground">
                {user.joinedAt ? formatDate(user.joinedAt) : "-"}
              </TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 cursor-pointer"
                    onClick={() => handleViewUser(user._id)}
                  >
                    <Eye className="h-4 w-4" />
                    <span className="sr-only">查看</span>
                  </Button>
                  
                  {!readOnly && (
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 cursor-pointer text-destructive"
                          onClick={() => setUserToRemove(user)}
                        >
                          <UserX className="h-4 w-4" />
                          <span className="sr-only">移除</span>
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>移除用户</AlertDialogTitle>
                          <AlertDialogDescription>
                            确定要将用户 <span className="font-medium">{user.name}</span> 从该角色中移除吗？移除后该用户将失去此角色的所有权限。
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel className="cursor-pointer">
                            取消
                          </AlertDialogCancel>
                          <AlertDialogAction
                            className="cursor-pointer bg-destructive hover:bg-destructive/90"
                            onClick={handleRemoveUser}
                            disabled={isRemoving}
                          >
                            {isRemoving ? (
                              <>
                                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                移除中...
                              </>
                            ) : (
                              "确认移除"
                            )}
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  )}
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
} 
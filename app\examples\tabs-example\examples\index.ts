/**
 * 标签页组件示例代码集合
 * 
 * 按照新规范管理示例代码，避免在页面文件中硬编码
 */

import React from "react"
import { Tabs, CardTabs, StepTabs } from "@/components/common-custom/tabs"
import { User, Settings, Bell, Shield, Home, FileText, Calendar } from "lucide-react"

// ============================================================================
// 基础标签页示例
// ============================================================================

export const basicTabsExample = {
  id: "basic-tabs",
  title: "基础标签页",
  description: "基本的标签页功能，支持不同变体和尺寸",
  code: `
import React, { useState } from "react";
import { Tabs } from "@/components/common-custom/tabs";
import { User, Settings, Bell } from "lucide-react";

function BasicTabsExample() {
  const [activeTab, setActiveTab] = useState("profile");
  
  const tabs = [
    {
      id: "profile",
      title: "个人资料",
      icon: User,
      content: (
        <div className="p-4">
          <h3 className="text-lg font-medium mb-2">个人资料</h3>
          <p className="text-muted-foreground">
            在这里管理您的个人信息和偏好设置。
          </p>
        </div>
      ),
    },
    {
      id: "settings",
      title: "设置",
      icon: Settings,
      content: (
        <div className="p-4">
          <h3 className="text-lg font-medium mb-2">系统设置</h3>
          <p className="text-muted-foreground">
            配置应用程序的各种设置选项。
          </p>
        </div>
      ),
    },
    {
      id: "notifications",
      title: "通知",
      icon: Bell,
      badge: 3,
      content: (
        <div className="p-4">
          <h3 className="text-lg font-medium mb-2">通知中心</h3>
          <p className="text-muted-foreground">
            查看和管理您的通知设置。
          </p>
        </div>
      ),
    },
  ];
  
  return (
    <div className="space-y-6">
      <div>
        <h4 className="text-sm font-medium mb-2">默认样式</h4>
        <Tabs
          tabs={tabs}
          value={activeTab}
          onValueChange={setActiveTab}
        />
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">下划线样式</h4>
        <Tabs
          tabs={tabs}
          variant="underline"
          defaultValue="profile"
        />
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">药丸样式</h4>
        <Tabs
          tabs={tabs}
          variant="pills"
          defaultValue="profile"
        />
      </div>
    </div>
  );
}

render(<BasicTabsExample />);
  `,
  scope: { Tabs, User, Settings, Bell, React, useState: React.useState },
}

// ============================================================================
// 卡片标签页示例
// ============================================================================

export const cardTabsExample = {
  id: "card-tabs",
  title: "卡片标签页",
  description: "带有卡片容器的标签页，适用于内容展示",
  code: `
import React from "react";
import { CardTabs } from "@/components/common-custom/tabs";
import { Home, FileText, Calendar, Settings } from "lucide-react";

function CardTabsExample() {
  const tabs = [
    {
      id: "overview",
      title: "概览",
      icon: Home,
      content: (
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="p-4 border rounded-md">
              <h4 className="font-medium">总用户数</h4>
              <p className="text-2xl font-bold text-blue-600">1,234</p>
            </div>
            <div className="p-4 border rounded-md">
              <h4 className="font-medium">活跃用户</h4>
              <p className="text-2xl font-bold text-green-600">856</p>
            </div>
          </div>
        </div>
      ),
    },
    {
      id: "documents",
      title: "文档",
      icon: FileText,
      badge: "新",
      content: (
        <div className="space-y-3">
          <div className="p-3 border rounded-md">
            <h4 className="font-medium">用户手册</h4>
            <p className="text-sm text-muted-foreground">最后更新：2024-01-15</p>
          </div>
          <div className="p-3 border rounded-md">
            <h4 className="font-medium">API文档</h4>
            <p className="text-sm text-muted-foreground">最后更新：2024-01-10</p>
          </div>
        </div>
      ),
    },
    {
      id: "schedule",
      title: "日程",
      icon: Calendar,
      content: (
        <div className="space-y-3">
          <div className="p-3 border-l-4 border-blue-500 bg-blue-50">
            <h4 className="font-medium">团队会议</h4>
            <p className="text-sm text-muted-foreground">今天 14:00 - 15:00</p>
          </div>
          <div className="p-3 border-l-4 border-green-500 bg-green-50">
            <h4 className="font-medium">项目评审</h4>
            <p className="text-sm text-muted-foreground">明天 10:00 - 11:30</p>
          </div>
        </div>
      ),
    },
  ];
  
  return (
    <CardTabs
      title="工作台"
      description="管理您的工作内容和日程安排"
      tabs={tabs}
      defaultValue="overview"
      actions={
        <button className="text-sm text-blue-600 hover:text-blue-800">
          查看全部
        </button>
      }
    />
  );
}

render(<CardTabsExample />);
  `,
  scope: { CardTabs, Home, FileText, Calendar, Settings, React },
}

// ============================================================================
// 步骤标签页示例
// ============================================================================

export const stepTabsExample = {
  id: "step-tabs",
  title: "步骤标签页",
  description: "引导用户完成多步骤流程的标签页",
  code: `
import React, { useState } from "react";
import { StepTabs } from "@/components/common-custom/tabs";
import { User, Shield, Bell, Settings } from "lucide-react";

function StepTabsExample() {
  const [currentStep, setCurrentStep] = useState("basic-info");
  
  const steps = [
    {
      id: "basic-info",
      title: "基本信息",
      icon: User,
      content: (
        <div className="space-y-4">
          <h3 className="text-lg font-medium">填写基本信息</h3>
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium mb-1">姓名</label>
              <input 
                type="text" 
                className="w-full p-2 border rounded-md" 
                placeholder="请输入您的姓名"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">邮箱</label>
              <input 
                type="email" 
                className="w-full p-2 border rounded-md" 
                placeholder="请输入您的邮箱"
              />
            </div>
          </div>
        </div>
      ),
    },
    {
      id: "security",
      title: "安全设置",
      icon: Shield,
      content: (
        <div className="space-y-4">
          <h3 className="text-lg font-medium">设置安全选项</h3>
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <input type="checkbox" id="two-factor" />
              <label htmlFor="two-factor" className="text-sm">启用双因素认证</label>
            </div>
            <div className="flex items-center space-x-2">
              <input type="checkbox" id="login-alerts" />
              <label htmlFor="login-alerts" className="text-sm">登录提醒</label>
            </div>
          </div>
        </div>
      ),
    },
    {
      id: "notifications",
      title: "通知偏好",
      icon: Bell,
      content: (
        <div className="space-y-4">
          <h3 className="text-lg font-medium">选择通知偏好</h3>
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <input type="checkbox" id="email-notifications" defaultChecked />
              <label htmlFor="email-notifications" className="text-sm">邮件通知</label>
            </div>
            <div className="flex items-center space-x-2">
              <input type="checkbox" id="push-notifications" />
              <label htmlFor="push-notifications" className="text-sm">推送通知</label>
            </div>
          </div>
        </div>
      ),
    },
    {
      id: "complete",
      title: "完成",
      icon: Settings,
      content: (
        <div className="text-center space-y-4">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
            <Settings className="w-8 h-8 text-green-600" />
          </div>
          <h3 className="text-lg font-medium">设置完成</h3>
          <p className="text-muted-foreground">
            您的账户已成功设置，现在可以开始使用了。
          </p>
        </div>
      ),
    },
  ];
  
  const handleNext = async (currentIndex) => {
    // 这里可以添加验证逻辑
    console.log(\`验证第 \${currentIndex + 1} 步\`);
    return true; // 返回 true 允许继续，false 阻止
  };
  
  const handleComplete = () => {
    console.log("设置流程完成");
    alert("设置完成！");
  };
  
  return (
    <StepTabs
      tabs={steps}
      value={currentStep}
      onValueChange={setCurrentStep}
      onNext={handleNext}
      onComplete={handleComplete}
      backButtonText="上一步"
      nextButtonText="下一步"
      completeButtonText="完成设置"
    />
  );
}

render(<StepTabsExample />);
  `,
  scope: { StepTabs, User, Shield, Bell, Settings, React, useState: React.useState },
}

// ============================================================================
// 统一导出所有示例
// ============================================================================

export const allExamples = [
  basicTabsExample,
  cardTabsExample,
  stepTabsExample,
]

// ============================================================================
// 按类别导出示例
// ============================================================================

export const basicExamples = [basicTabsExample]
export const advancedExamples = [cardTabsExample, stepTabsExample]

// ============================================================================
// 示例元数据
// ============================================================================

export const exampleMetadata = {
  total: allExamples.length,
  categories: {
    basic: basicExamples.length,
    advanced: advancedExamples.length,
  },
  tags: ["tabs", "navigation", "steps", "card", "workflow"],
  lastUpdated: "2024-01-01",
}

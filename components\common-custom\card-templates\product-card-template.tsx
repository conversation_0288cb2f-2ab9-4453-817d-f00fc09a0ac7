"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ShoppingCart, Star } from "lucide-react"

/**
 * 产品卡片模板
 */
export function ProductCardTemplate({
  data
}: {
  data: {
    name: string
    price: number
    originalPrice?: number
    rating?: number
    reviewCount?: number
    image?: string
    inStock?: boolean
    features?: string[]
  }
}) {
  return (
    <Card className="overflow-hidden transition-all hover:shadow-md h-full flex flex-col">
      {data.image && (
        <div className="aspect-video">
          <img src={data.image} alt={data.name} className="w-full h-full object-cover" />
        </div>
      )}
      <CardHeader className="space-y-1 pt-4 px-4">
        <div className="flex items-start justify-between">
          <CardTitle className="text-lg line-clamp-2">{data.name}</<PERSON><PERSON><PERSON>le>
          {data.inStock !== undefined && (
            <Badge variant={data.inStock ? "default" : "outline"}>
              {data.inStock ? '有货' : '缺货'}
            </Badge>
          )}
        </div>
        {data.rating !== undefined && (
          <div className="flex items-center gap-1">
            {Array(5).fill(0).map((_, i) => (
              <Star key={i} className={`h-4 w-4 ${i < Math.floor(data.rating || 0) ? 'fill-yellow-400 text-yellow-400' : 'text-muted-foreground'}`} />
            ))}
            {data.reviewCount ? <span className="text-xs ml-1 text-muted-foreground">({data.reviewCount})</span> : null}
          </div>
        )}
      </CardHeader>
      <CardContent className="px-4 py-2 flex-grow">
        {data.features && data.features.length > 0 && (
          <ul className="text-sm space-y-1 text-muted-foreground">
            {data.features.map((feature, index) => (
              <li key={index} className="list-disc list-inside">
                {feature}
              </li>
            ))}
          </ul>
        )}
      </CardContent>
      <CardFooter className="p-4 flex-shrink-0">
        <div className="w-full space-y-3">
          <div className="flex items-center justify-between">
            <div className="font-bold text-lg">
              ¥{data.price.toLocaleString()}
              {data.originalPrice && (
                <span className="text-muted-foreground text-sm ml-2 line-through">
                  ¥{data.originalPrice.toLocaleString()}
                </span>
              )}
            </div>
          </div>
          <Button className="w-full">
            <ShoppingCart className="h-4 w-4 mr-2" />
            加入购物车
          </Button>
        </div>
      </CardFooter>
    </Card>
  )
} 
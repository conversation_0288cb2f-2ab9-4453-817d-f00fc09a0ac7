import UserManagement from "@/components/pages/users/user-management";
import { HeaderWithBreadcrumb } from "@/components/custom/breadcrumb";
import UserEditDialog from "@/components/pages/users/user-edit-dialog";

export default function UsersPage() {
  // 面包屑数据
  const breadcrumbItems = [
    { label: "设置", href: "/settings" },
    { label: "用户管理", active: true }
  ]

  return (
    <div className="flex flex-col min-h-screen">
      <HeaderWithBreadcrumb items={breadcrumbItems}/>
      <main className="flex-1">
        <div className="container mx-auto px-6 py-6 max-w-7xl">
          <UserManagement />
        </div>
      </main>
      
      {/* 用户编辑弹窗 */}
      <UserEditDialog />
    </div>
  )
} 
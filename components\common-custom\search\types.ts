/**
 * 搜索组件类型定义文件
 * 
 * 按照新规范，复杂组件的类型定义应该在组件目录内管理
 */

import { ReactNode, KeyboardEvent, ChangeEvent } from "react"
import { LucideIcon } from "lucide-react"

// ============================================================================
// 基础类型定义
// ============================================================================

/**
 * 搜索项类型
 */
export interface SearchItem<T = any> {
  /**
   * 唯一标识符
   */
  id: string | number
  
  /**
   * 项目标题
   */
  title: string
  
  /**
   * 项目描述
   */
  description?: string
  
  /**
   * 项目图标
   * 支持React节点或Lucide图标组件
   */
  icon?: React.ComponentType<{ className?: string }> | ReactNode
  
  /**
   * 标签列表
   */
  tags?: string[]
  
  /**
   * 高亮匹配的文本
   */
  matches?: {
    field: string
    indices: Array<[number, number]>
  }[]
  
  /**
   * 关联数据
   */
  data?: T
  
  /**
   * 排序权重
   */
  weight?: number
  
  /**
   * 项目类型
   */
  type?: string
  
  /**
   * 项目URL
   */
  url?: string
}

/**
 * 搜索过滤器类型
 */
export interface SearchFilter {
  /**
   * 过滤器ID
   */
  id: string
  
  /**
   * 过滤器标签
   */
  label: string
  
  /**
   * 过滤器值
   */
  value: string | boolean | number | null
  
  /**
   * 过滤器类型
   */
  type: "text" | "select" | "checkbox" | "date" | "number"
  
  /**
   * 下拉选项（用于select类型）
   */
  options?: { label: string; value: string }[]
  
  /**
   * 是否必填
   */
  required?: boolean
  
  /**
   * 占位符文本
   */
  placeholder?: string
}

/**
 * 搜索结果分组
 */
export interface SearchResultGroup<T = any> {
  /**
   * 分组标题
   */
  title: string
  
  /**
   * 分组描述
   */
  description?: string
  
  /**
   * 分组图标
   */
  icon?: React.ComponentType<{ className?: string }> | ReactNode
  
  /**
   * 分组项目列表
   */
  items: SearchItem<T>[]
  
  /**
   * 分组类型
   */
  type?: string
  
  /**
   * 是否可折叠
   */
  collapsible?: boolean
  
  /**
   * 默认是否展开
   */
  defaultExpanded?: boolean
}

/**
 * 搜索建议类型
 */
export interface SearchSuggestion {
  /**
   * 建议ID
   */
  id: string
  
  /**
   * 建议文本
   */
  text: string
  
  /**
   * 建议类型
   */
  type: "query" | "filter" | "recent" | "popular"
  
  /**
   * 建议图标
   */
  icon?: LucideIcon
  
  /**
   * 使用次数
   */
  count?: number
  
  /**
   * 最后使用时间
   */
  lastUsed?: Date
}

// ============================================================================
// 事件处理类型定义
// ============================================================================

/**
 * 搜索事件处理器
 */
export type SearchHandler = (query: string, filters?: SearchFilter[]) => void

/**
 * 搜索项选择事件处理器
 */
export type SearchItemSelectHandler<T = any> = (item: SearchItem<T>) => void

/**
 * 搜索过滤器变更事件处理器
 */
export type SearchFilterChangeHandler = (filters: SearchFilter[]) => void

/**
 * 搜索建议选择事件处理器
 */
export type SearchSuggestionSelectHandler = (suggestion: SearchSuggestion) => void

/**
 * 键盘事件处理器
 */
export type SearchKeyboardHandler = (event: KeyboardEvent<HTMLInputElement>) => void

/**
 * 输入变更事件处理器
 */
export type SearchInputChangeHandler = (event: ChangeEvent<HTMLInputElement>) => void

// ============================================================================
// 组件属性类型定义
// ============================================================================

/**
 * 搜索输入组件属性
 */
export interface SearchInputProps {
  /**
   * 搜索值
   */
  value?: string
  
  /**
   * 占位符文本
   * @default "搜索..."
   */
  placeholder?: string
  
  /**
   * 是否禁用
   * @default false
   */
  disabled?: boolean
  
  /**
   * 是否加载中
   * @default false
   */
  loading?: boolean
  
  /**
   * 输入框尺寸
   * @default "md"
   */
  size?: "sm" | "md" | "lg"
  
  /**
   * 自定义类名
   */
  className?: string
  
  /**
   * 是否显示清除按钮
   * @default true
   */
  clearable?: boolean
  
  /**
   * 是否自动聚焦
   * @default false
   */
  autoFocus?: boolean
  
  // 事件处理器
  onChange?: SearchInputChangeHandler
  onSearch?: SearchHandler
  onKeyDown?: SearchKeyboardHandler
  onFocus?: () => void
  onBlur?: () => void
  onClear?: () => void
}

/**
 * 搜索结果组件属性
 */
export interface SearchResultsProps<T = any> {
  /**
   * 搜索结果列表
   */
  results: SearchItem<T>[] | SearchResultGroup<T>[]
  
  /**
   * 是否加载中
   * @default false
   */
  loading?: boolean
  
  /**
   * 是否为空状态
   */
  empty?: boolean
  
  /**
   * 空状态文本
   * @default "暂无搜索结果"
   */
  emptyText?: string
  
  /**
   * 最大显示数量
   */
  maxResults?: number
  
  /**
   * 是否显示更多按钮
   * @default false
   */
  showMore?: boolean
  
  /**
   * 自定义类名
   */
  className?: string
  
  // 事件处理器
  onItemSelect?: SearchItemSelectHandler<T>
  onShowMore?: () => void
}

/**
 * 搜索建议组件属性
 */
export interface SearchSuggestionsProps {
  /**
   * 建议列表
   */
  suggestions: SearchSuggestion[]
  
  /**
   * 是否显示
   * @default false
   */
  visible?: boolean
  
  /**
   * 最大显示数量
   * @default 10
   */
  maxSuggestions?: number
  
  /**
   * 自定义类名
   */
  className?: string
  
  // 事件处理器
  onSelect?: SearchSuggestionSelectHandler
  onClear?: () => void
}

/**
 * 高级搜索组件属性
 */
export interface AdvancedSearchProps {
  /**
   * 过滤器列表
   */
  filters: SearchFilter[]
  
  /**
   * 是否显示
   * @default false
   */
  visible?: boolean
  
  /**
   * 自定义类名
   */
  className?: string
  
  // 事件处理器
  onChange?: SearchFilterChangeHandler
  onReset?: () => void
  onApply?: (filters: SearchFilter[]) => void
}

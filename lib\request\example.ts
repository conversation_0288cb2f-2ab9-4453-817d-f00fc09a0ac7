/**
 * 这是一个HTTP请求工具的使用示例
 */
import {request, extend, CancelToken, isCancel, RequestInstance, ResponseError} from './index';
import type { RequestResponse } from './types';

/**
 * 用户接口
 */
interface User {
  id: number;
  name: string;
  email: string;
}

/**
 * 基本请求示例
 */
export async function basicRequestExample() {
  // GET 请求
  try {
    const users = await request.get<User[]>('/api/users');
    console.log('用户列表:', users);
  } catch (error) {
    console.error('获取用户列表失败:', error);
  }

  // 带参数的 GET 请求
  try {
    const user = await request.get<User>('/api/users', { params: { id: 1 } });
    console.log('用户信息:', user);
  } catch (error) {
    console.error('获取用户信息失败:', error);
  }

  // 使用简化的请求方法 - 直接传递数据作为第二个参数
  try {
    // POST请求 - 直接传递数据对象，将自动设置Content-Type为application/json
    const newUser = await request.post<User>('/api/users', {
      name: '<PERSON>',
      email: '<EMAIL>'
    });
    console.log('创建用户成功:', newUser);
    
    // PUT请求 - 同样直接传递数据对象
    const updatedUser = await request.put<User>('/api/users/1', {
      name: 'John Updated',
      email: '<EMAIL>'
    });
    console.log('更新用户成功:', updatedUser);
    
    // PATCH请求 - 用于部分更新
    const partialUpdatedUser = await request.patch<User>('/api/users/1', {
      email: '<EMAIL>'
    });
    console.log('部分更新用户成功:', partialUpdatedUser);
  } catch (error) {
    console.error('请求失败:', error);
  }

  // 使用完整参数的写法
  try {
    const newUser = await request.post<User>('/api/users', { name: 'Jane', email: '<EMAIL>' }, {
      timeout: 5000,
      headers: {
        'X-Custom-Header': 'custom-value'
      }
    });
    console.log('创建用户成功:', newUser);
  } catch (error) {
    console.error('创建用户失败:', error);
  }

  // DELETE 请求
  try {
    await request.delete('/api/users', { id: 1 }); // 直接传ID将转为params
    console.log('删除用户成功');
  } catch (error) {
    console.error('删除用户失败:', error);
  }

  // 获取完整响应
  try {
    const result = await request.get<User>('/api/users/1', {
      getResponse: true
    });
    // 使用类型断言确保结果符合 RequestResponse 接口
    const typedResult = result as unknown as RequestResponse<User>;
    console.log('用户数据:', typedResult.data);
    console.log('状态码:', typedResult.response.status);
    console.log('响应头:', typedResult.response.headers);
  } catch (error) {
    console.error('请求失败:', error);
  }
}

/**
 * 带状态管理的请求示例
 */
export async function requestWithStatusExample() {
  // 显示加载中状态的请求
  try {
    const users = await request.get<User[]>('/api/users', {
      showLoading: true,
      loadingText: '加载用户列表中...'
    });
    console.log('用户列表:', users);
  } catch (error) {
    console.error('获取用户列表失败:', error);
  }
  
  // 显示成功消息的请求
  try {
    const newUser = await request.post<User>(
      '/api/users', 
      { name: 'Alice', email: '<EMAIL>' },
      {
        showSuccessMessage: true,
        successMessage: '用户创建成功！'
      }
    );
    console.log('创建用户成功:', newUser);
  } catch (error) {
    console.error('创建用户失败:', error);
  }
  
  // 自定义错误消息的请求
  try {
    await request.get('/api/non-existent', {
      showErrorMessage: true,
      errorMessage: '获取资源失败，请检查URL是否正确'
    });
  } catch (error) {
    // 错误消息已由请求库处理，这里不需要再处理
    console.log('请求已处理错误');
  }
}

/**
 * 自定义实例示例
 */
export function customInstanceExample() {
  // 创建自定义实例
  const api = extend({
    baseURL: '/api/v1',  // 使用axios的baseURL
    timeout: 5000,
    headers: {
      'Content-Type': 'application/json',
      'X-API-KEY': 'your-api-key'
    },
    requestStatusConfig: {
      defaultShowLoading: true,    // 默认显示加载状态
      defaultShowSuccessMessage: false,
      defaultShowErrorMessage: true
    },
    errorConfig: {
      errorHandler: (error) => {
        console.error('API请求错误:', error);
      }
    }
  });

  // 使用自定义实例
  api.get<User[]>('/users').then(users => {
    console.log('用户列表:', users);
  });

  // 添加拦截器
  api.interceptors.request.use((config) => {
    console.log('请求拦截器:', config);
    
    // 添加 token
    config.headers = {
      ...config.headers,
      'Authorization': `Bearer token-example`
    };
    
    return config;
  });

  api.interceptors.response.use((response) => {
    console.log('响应拦截器:', response);
    
    // 可以在这里处理响应数据
    return response;
  });

  return api;
}

/**
 * 请求取消示例
 */
export function cancelRequestExample() {
  // 使用CancelToken
  const { token, cancel } = CancelToken.source();
  
  const fetchPromise = request.get('/api/long-request', {
    cancelToken: token as any // 类型转换，解决类型不兼容问题
  }).catch(error => {
    if (isCancel(error)) {
      console.log('请求已被取消:', error.message);
      return { canceled: true };
    }
    throw error;
  });
  
  // 3秒后取消请求
  setTimeout(() => {
    cancel('用户取消了请求');
  }, 3000);
  
  return fetchPromise;
}

/**
 * 使用 AbortController 取消请求
 */
export function abortControllerExample() {
  // 使用 AbortController (现代浏览器支持)
  const controller = new AbortController();
  const { signal } = controller;
  
  const fetchPromise = request.get('/api/long-request', {
    signal
  }).catch(error => {
    console.log('请求错误:', error);
    if (error.name === 'AbortError') {
      console.log('请求已被中止');
      return { aborted: true };
    }
    throw error;
  });
  
  // 3秒后中止请求
  setTimeout(() => {
    controller.abort();
  }, 3000);
  
  return fetchPromise;
}

/**
 * 错误处理示例
 */
export function errorHandlingExample() {
  // 创建自定义错误处理的实例
  const api = extend({
    errorConfig: {
      // 错误抛出函数
      errorThrower: (res) => {
        const { success, data, errorCode, errorMessage, showType } = res;
        if (!success) {
          const error: any = new Error(errorMessage);
          error.name = 'BizError';
          error.data = data;
          error.info = { errorCode, errorMessage, showType, data };
          throw error;
        }
      },
      // 错误处理函数
      errorHandler: (error: ResponseError) => {
        if (error.name === 'BizError') {
          // 业务错误
          const errorInfo = error.info;
          if (errorInfo) {
            const { errorMessage, errorCode } = errorInfo;
            console.error(`业务错误: ${errorCode} - ${errorMessage}`);
          }
        } else if (error.response) {
          // HTTP 错误
          console.error(`HTTP错误: ${error.response.status}`);
        } else {
          // 其他错误
          console.error(`请求错误: ${error.msg}`);
        }
      }
    }
  });

  // 发起会导致业务错误的请求
  api.get('/api/error-endpoint').catch(error => {
    console.log('错误已被 errorHandler 处理，无需在这里再处理');
  });

  // 跳过错误处理
  api.get('/api/error-endpoint', {
    skipErrorHandler: true
  }).catch(error => {
    console.log('需要在这里手动处理错误:', error);
  });

  return api;
}

/**
 * TypeScript泛型使用示例
 */
export function typedRequestExample() {
  interface CreateUserRequest {
    name: string;
    email: string;
    role?: string;
  }
  
  interface UserResponse {
    id: number;
    name: string;
    email: string;
    role: string;
    createdAt: string;
  }
  
  // 使用泛型指定响应类型和请求数据类型
  async function createUser(userData: CreateUserRequest): Promise<UserResponse> {
    return request.post<UserResponse>(
      '/api/users', 
      userData,
      {
        showLoading: true,
        loadingText: '创建用户中...',
        showSuccessMessage: true,
        successMessage: '用户创建成功'
      }
    );
  }
  
  // 调用函数
  createUser({
    name: 'New User',
    email: '<EMAIL>',
    role: 'admin'
  }).then(user => {
    console.log('创建的用户:', user);
  }).catch(error => {
    console.error('创建用户失败:', error);
  });
}

/**
 * 测试直接调用request函数
 */
export async function directRequestExample() {
  try {
    // 直接调用request，提供完整参数
    const response1 = await request('/api/users', {
      method: 'get',
      params: { role: 'admin' }
    });
    console.log('直接请求示例1:', response1);
    
    // 直接调用request，使用数据参数
    const response2 = await request('/api/users', { id: 1 }, { method: 'get' });
    console.log('直接请求示例2:', response2);
    
    // POST请求
    const response3 = await request('/api/users', {
      name: 'John Doe',
      email: '<EMAIL>'
    }, {
      method: 'post'
    });
    console.log('直接请求示例3:', response3);
    
    // 使用完整选项
    const response4 = await request('/api/users', null, {
      method: 'get',
      params: { id: 1 },
      timeout: 5000,
      headers: {
        'X-Custom-Header': 'custom-value'
      }
    });
    console.log('直接请求示例4:', response4);
    
  } catch (error) {
    console.error('直接请求失败:', error);
  }
}

/**
 * 完整示例
 */
export async function fullExample() {
  // 基本请求
  await basicRequestExample();
  
  // 带状态管理的请求
  await requestWithStatusExample();
  
  // 自定义实例
  const api = customInstanceExample();
  
  // 取消请求
  const cancelResult = await cancelRequestExample();
  console.log('取消结果:', cancelResult);
  
  // 错误处理
  errorHandlingExample();
  
  // 类型安全的请求
  typedRequestExample();
  
  // 测试直接调用request函数
  await directRequestExample();
  
  return '示例执行完成';
} 
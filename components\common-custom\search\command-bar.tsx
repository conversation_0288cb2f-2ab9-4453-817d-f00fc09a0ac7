"use client"

import { ReactNode } from "react"
import { 
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator
} from "@/components/ui/command"
import { cn } from "@/lib/utils"
import type { CommandBarProps } from "@/types/search"

/**
 * 命令栏组件
 * 提供类似VSCode命令面板的体验，支持键盘导航和命令执行
 */
export function CommandBar({
  open,
  onOpenChange,
  placeholder = "输入命令...",
  commands,
  filter,
  className,
}: CommandBarProps) {
  // 过滤命令
  const filteredCommands = filter 
    ? commands.filter(command => filter(command.name, ""))
    : commands
  
  // 渲染图标
  const renderIcon = (icon: any): ReactNode => {
    if (!icon) return null
    
    // 如果是函数组件（包括 Lucide 图标）
    if (typeof icon === "function") {
      try {
        const IconComponent = icon
        return <IconComponent className="mr-2 h-4 w-4" />
      } catch (e) {
        console.error("Failed to render icon:", e)
        return null
      }
    }
    
    // 如果已经是 ReactNode（如 JSX 元素）
    return icon
  }

  return (
    <CommandDialog open={open} onOpenChange={onOpenChange}>
      <CommandInput placeholder={placeholder} />
      <CommandList className={cn("max-h-[60vh]", className)}>
        <CommandEmpty>未找到匹配的命令</CommandEmpty>
        <CommandGroup>
          {filteredCommands.map((command, index) => (
            <CommandItem
              key={index}
              onSelect={() => {
                command.onSelect()
                onOpenChange?.(false)
              }}
            >
              {renderIcon(command.icon)}
              <span>{command.name}</span>
              {command.shortcut && (
                <kbd className="ml-auto pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground">
                  {command.shortcut}
                </kbd>
              )}
            </CommandItem>
          ))}
        </CommandGroup>
      </CommandList>
    </CommandDialog>
  )
} 
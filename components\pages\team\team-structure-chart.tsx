"use client"

import { useEffect, useRef } from "react"
import { useRouter } from "next/navigation"

interface TeamMember {
  id: string
  name: string
}

interface TeamData {
  id: string
  name: string
  leader?: TeamMember
  memberCount: number
  subTeamCount: number
}

interface SubTeam {
  id: string
  name: string
  memberCount: number
  subTeamCount: number
  level: number
}

interface TeamStructureChartProps {
  team: TeamData
  subTeams: SubTeam[]
}

export function TeamStructureChart({ team, subTeams }: TeamStructureChartProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const router = useRouter()

  useEffect(() => {
    if (!containerRef.current) return

    // 清除之前的内容
    containerRef.current.innerHTML = ""

    // 创建团队结构图
    const container = document.createElement("div")
    container.className = "flex flex-col items-center"

    // 主团队
    const mainTeam = document.createElement("div")
    mainTeam.className = "flex flex-col items-center"

    const mainTeamIcon = document.createElement("div")
    mainTeamIcon.className = "flex h-16 w-16 items-center justify-center rounded-full bg-primary/10 mb-2 cursor-pointer"
    mainTeamIcon.innerHTML = `
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" class="text-primary">
        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
        <line x1="3" y1="9" x2="21" y2="9"></line>
        <line x1="9" y1="21" x2="9" y2="9"></line>
      </svg>
    `
    mainTeamIcon.addEventListener("click", () => {
      // 当前已经在团队页面，不需要导航
    })

    const mainTeamInfo = document.createElement("div")
    mainTeamInfo.className = "text-center"
    mainTeamInfo.innerHTML = `
      <div class="font-semibold">${team.name}</div>
      <div class="text-sm text-muted-foreground">${team.memberCount} 成员</div>
    `

    mainTeam.appendChild(mainTeamIcon)
    mainTeam.appendChild(mainTeamInfo)
    container.appendChild(mainTeam)

    // 连接线
    if (subTeams.length > 0) {
      const connector = document.createElement("div")
      connector.className = "h-8 w-0.5 bg-border my-2"
      container.appendChild(connector)
    }

    // 子团队
    if (subTeams.length > 0) {
      const subTeamsContainer = document.createElement("div")
      subTeamsContainer.className = "flex justify-center gap-8"

      subTeams.forEach((subTeam) => {
        const subTeamEl = document.createElement("div")
        subTeamEl.className = "flex flex-col items-center"

        const subTeamIcon = document.createElement("div")
        subTeamIcon.className =
          "flex h-12 w-12 items-center justify-center rounded-full bg-primary/10 mb-2 cursor-pointer hover:bg-primary/20 transition-colors"
        subTeamIcon.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" class="text-primary">
            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
            <line x1="3" y1="9" x2="21" y2="9"></line>
            <line x1="9" y1="21" x2="9" y2="9"></line>
          </svg>
        `
        subTeamIcon.addEventListener("click", () => {
          router.push(`/teams/${subTeam.id}`)
        })

        const subTeamInfo = document.createElement("div")
        subTeamInfo.className = "text-center cursor-pointer"
        subTeamInfo.innerHTML = `
          <div class="font-medium">${subTeam.name}</div>
          <div class="text-xs text-muted-foreground">${subTeam.memberCount} 成员</div>
        `
        subTeamInfo.addEventListener("click", () => {
          router.push(`/teams/${subTeam.id}`)
        })

        subTeamEl.appendChild(subTeamIcon)
        subTeamEl.appendChild(subTeamInfo)
        subTeamsContainer.appendChild(subTeamEl)
      })

      container.appendChild(subTeamsContainer)
    }

    containerRef.current.appendChild(container)
  }, [team, subTeams, router])

  return <div ref={containerRef} className="p-4 min-h-[200px]"></div>
}

"use client"

import React from "react"
import { ComponentPreviewContainer } from "@/components/component-detail/preview-container"
import { PageHeader } from "@/components/common-custom/page-header"
import { allExamples } from "./examples"

export default function PageHeaderPreview() {
  return (
    <ComponentPreviewContainer
      title="页头组件 PageHeader"
      description="功能完整的页头组件，支持多种布局模式，集成导航、搜索、操作和用户功能"
      whenToUse="用于应用顶部导航栏，提供统一的页面头部体验。支持标准、简洁、仪表板和移动端等多种布局模式。"
      examples={allExamples}
      apiDocs={<PageHeaderApiDocs />}
    />
  );
}

function PageHeaderApiDocs() {
  return (
    <div className="space-y-8">
      {/* 主组件API */}
      <div>
        <h3 className="font-medium text-lg mb-4">PageHeader</h3>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-3 border font-medium">参数</th>
                <th className="p-3 border font-medium">类型</th>
                <th className="p-3 border font-medium">默认值</th>
                <th className="p-3 border font-medium">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-3 border">variant</td>
                <td className="p-3 border">"standard" | "simple" | "dashboard" | "mobile"</td>
                <td className="p-3 border">"standard"</td>
                <td className="p-3 border">布局变体</td>
              </tr>
              <tr>
                <td className="p-3 border">logo</td>
                <td className="p-3 border">LogoConfig</td>
                <td className="p-3 border">-</td>
                <td className="p-3 border">Logo配置</td>
              </tr>
              <tr>
                <td className="p-3 border">navigation</td>
                <td className="p-3 border">NavigationConfig</td>
                <td className="p-3 border">-</td>
                <td className="p-3 border">导航配置</td>
              </tr>
              <tr>
                <td className="p-3 border">search</td>
                <td className="p-3 border">SearchConfig</td>
                <td className="p-3 border">-</td>
                <td className="p-3 border">搜索配置</td>
              </tr>
              <tr>
                <td className="p-3 border">actions</td>
                <td className="p-3 border">ActionsConfig</td>
                <td className="p-3 border">-</td>
                <td className="p-3 border">操作区域配置</td>
              </tr>
              <tr>
                <td className="p-3 border">user</td>
                <td className="p-3 border">UserConfig</td>
                <td className="p-3 border">-</td>
                <td className="p-3 border">用户区域配置</td>
              </tr>
              <tr>
                <td className="p-3 border">height</td>
                <td className="p-3 border">"sm" | "md" | "lg"</td>
                <td className="p-3 border">"md"</td>
                <td className="p-3 border">页头高度</td>
              </tr>
              <tr>
                <td className="p-3 border">sticky</td>
                <td className="p-3 border">boolean</td>
                <td className="p-3 border">true</td>
                <td className="p-3 border">是否固定定位</td>
              </tr>
              <tr>
                <td className="p-3 border">bordered</td>
                <td className="p-3 border">boolean</td>
                <td className="p-3 border">true</td>
                <td className="p-3 border">是否显示边框</td>
              </tr>
              <tr>
                <td className="p-3 border">className</td>
                <td className="p-3 border">string</td>
                <td className="p-3 border">-</td>
                <td className="p-3 border">自定义类名</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* LogoConfig */}
      <div>
        <h3 className="font-medium text-lg mb-4">LogoConfig</h3>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-3 border font-medium">参数</th>
                <th className="p-3 border font-medium">类型</th>
                <th className="p-3 border font-medium">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-3 border">src</td>
                <td className="p-3 border">string</td>
                <td className="p-3 border">Logo图片地址</td>
              </tr>
              <tr>
                <td className="p-3 border">alt</td>
                <td className="p-3 border">string</td>
                <td className="p-3 border">图片alt文本</td>
              </tr>
              <tr>
                <td className="p-3 border">href</td>
                <td className="p-3 border">string</td>
                <td className="p-3 border">Logo链接</td>
              </tr>
              <tr>
                <td className="p-3 border">text</td>
                <td className="p-3 border">string</td>
                <td className="p-3 border">Logo文本</td>
              </tr>
              <tr>
                <td className="p-3 border">showText</td>
                <td className="p-3 border">boolean</td>
                <td className="p-3 border">是否显示文本</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* NavigationConfig */}
      <div>
        <h3 className="font-medium text-lg mb-4">NavigationConfig</h3>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-3 border font-medium">参数</th>
                <th className="p-3 border font-medium">类型</th>
                <th className="p-3 border font-medium">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-3 border">items</td>
                <td className="p-3 border">NavigationItem[]</td>
                <td className="p-3 border">导航项列表</td>
              </tr>
              <tr>
                <td className="p-3 border">showBreadcrumb</td>
                <td className="p-3 border">boolean</td>
                <td className="p-3 border">是否显示面包屑</td>
              </tr>
              <tr>
                <td className="p-3 border">breadcrumbItems</td>
                <td className="p-3 border">BreadcrumbItem[]</td>
                <td className="p-3 border">面包屑项列表</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* SearchConfig */}
      <div>
        <h3 className="font-medium text-lg mb-4">SearchConfig</h3>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-3 border font-medium">参数</th>
                <th className="p-3 border font-medium">类型</th>
                <th className="p-3 border font-medium">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-3 border">enabled</td>
                <td className="p-3 border">boolean</td>
                <td className="p-3 border">是否启用搜索</td>
              </tr>
              <tr>
                <td className="p-3 border">placeholder</td>
                <td className="p-3 border">string</td>
                <td className="p-3 border">搜索占位符</td>
              </tr>
              <tr>
                <td className="p-3 border">onSearch</td>
                <td className="p-3 border">(query: string) => void</td>
                <td className="p-3 border">搜索回调</td>
              </tr>
              <tr>
                <td className="p-3 border">suggestions</td>
                <td className="p-3 border">SearchSuggestion[]</td>
                <td className="p-3 border">搜索建议</td>
              </tr>
              <tr>
                <td className="p-3 border">showShortcut</td>
                <td className="p-3 border">boolean</td>
                <td className="p-3 border">是否显示快捷键</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* ActionsConfig */}
      <div>
        <h3 className="font-medium text-lg mb-4">ActionsConfig</h3>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-3 border font-medium">参数</th>
                <th className="p-3 border font-medium">类型</th>
                <th className="p-3 border font-medium">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-3 border">items</td>
                <td className="p-3 border">ActionItem[]</td>
                <td className="p-3 border">操作项列表</td>
              </tr>
              <tr>
                <td className="p-3 border">showThemeToggle</td>
                <td className="p-3 border">boolean</td>
                <td className="p-3 border">是否显示主题切换</td>
              </tr>
              <tr>
                <td className="p-3 border">showNotifications</td>
                <td className="p-3 border">boolean</td>
                <td className="p-3 border">是否显示通知</td>
              </tr>
              <tr>
                <td className="p-3 border">notificationCount</td>
                <td className="p-3 border">number</td>
                <td className="p-3 border">通知数量</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* UserConfig */}
      <div>
        <h3 className="font-medium text-lg mb-4">UserConfig</h3>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-3 border font-medium">参数</th>
                <th className="p-3 border font-medium">类型</th>
                <th className="p-3 border font-medium">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-3 border">name</td>
                <td className="p-3 border">string</td>
                <td className="p-3 border">用户名</td>
              </tr>
              <tr>
                <td className="p-3 border">avatar</td>
                <td className="p-3 border">string</td>
                <td className="p-3 border">用户头像</td>
              </tr>
              <tr>
                <td className="p-3 border">email</td>
                <td className="p-3 border">string</td>
                <td className="p-3 border">用户邮箱</td>
              </tr>
              <tr>
                <td className="p-3 border">role</td>
                <td className="p-3 border">string</td>
                <td className="p-3 border">用户角色</td>
              </tr>
              <tr>
                <td className="p-3 border">menuItems</td>
                <td className="p-3 border">UserMenuItem[]</td>
                <td className="p-3 border">用户菜单项</td>
              </tr>
              <tr>
                <td className="p-3 border">onSignOut</td>
                <td className="p-3 border">() => void</td>
                <td className="p-3 border">登出回调</td>
              </tr>
              <tr>
                <td className="p-3 border">showUserInfo</td>
                <td className="p-3 border">boolean</td>
                <td className="p-3 border">是否显示用户信息</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

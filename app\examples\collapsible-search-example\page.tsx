"use client"

import React from "react"
import { ComponentPreviewContainer } from "@/components/component-detail/preview-container"
import { CollapsibleSearch } from "@/components/common-custom/search/collapsible-search"

const examples = [
  {
    id: "basic-collapsible-search",
    title: "基础可折叠搜索",
    description: "默认收起状态的搜索框",
    code: `
import React, { useState } from "react";
import { CollapsibleSearch } from "@/components/common-custom/search/collapsible-search";

function BasicCollapsibleSearch() {
  const [value, setValue] = useState("");

  return (
    <div className="flex items-center gap-4 p-4">
      <span className="text-sm text-muted-foreground">搜索：</span>
      <CollapsibleSearch
        value={value}
        onChange={setValue}
        placeholder="搜索内容..."
        onSearch={(query) => console.log("搜索:", query)}
        size="sm"
      />
    </div>
  );
}

render(<BasicCollapsibleSearch />);
    `,
    scope: { CollapsibleSearch, React },
  },
  {
    id: "expanded-collapsible-search",
    title: "默认展开搜索",
    description: "默认展开状态的搜索框",
    code: `
import React, { useState } from "react";
import { CollapsibleSearch } from "@/components/common-custom/search/collapsible-search";

function ExpandedCollapsibleSearch() {
  const [value, setValue] = useState("");

  return (
    <div className="flex items-center gap-4 p-4">
      <span className="text-sm text-muted-foreground">搜索：</span>
      <CollapsibleSearch
        value={value}
        onChange={setValue}
        placeholder="搜索内容..."
        onSearch={(query) => console.log("搜索:", query)}
        defaultExpanded={true}
        size="md"
      />
    </div>
  );
}

render(<ExpandedCollapsibleSearch />);
    `,
    scope: { CollapsibleSearch, React },
  },
  {
    id: "directions-collapsible-search",
    title: "展开方向",
    description: "展示向左和向右展开的可折叠搜索框",
    code: `
import React, { useState } from "react";
import { CollapsibleSearch } from "@/components/common-custom/search/collapsible-search";

function DirectionsCollapsibleSearch() {
  const [value1, setValue1] = useState("");
  const [value2, setValue2] = useState("");

  return (
    <div className="space-y-6 p-4">
      <div className="flex items-center gap-4">
        <span className="text-sm text-muted-foreground w-20">向右展开：</span>
        <CollapsibleSearch
          value={value1}
          onChange={setValue1}
          placeholder="向右展开搜索..."
          expandDirection="right"
          size="md"
        />
      </div>
      <div className="flex items-center gap-4 justify-end">
        <CollapsibleSearch
          value={value2}
          onChange={setValue2}
          placeholder="向左展开搜索..."
          expandDirection="left"
          size="md"
        />
        <span className="text-sm text-muted-foreground w-20">：向左展开</span>
      </div>
    </div>
  );
}

render(<DirectionsCollapsibleSearch />);
    `,
    scope: { CollapsibleSearch, React },
  },
  {
    id: "sizes-collapsible-search",
    title: "不同尺寸",
    description: "展示不同尺寸的可折叠搜索框",
    code: `
import React, { useState } from "react";
import { CollapsibleSearch } from "@/components/common-custom/search/collapsible-search";

function SizesCollapsibleSearch() {
  const [value1, setValue1] = useState("");
  const [value2, setValue2] = useState("");
  const [value3, setValue3] = useState("");

  return (
    <div className="space-y-4 p-4">
      <div className="flex items-center gap-4">
        <span className="text-sm text-muted-foreground w-16">小尺寸：</span>
        <CollapsibleSearch
          value={value1}
          onChange={setValue1}
          placeholder="小尺寸搜索..."
          size="sm"
        />
      </div>
      <div className="flex items-center gap-4">
        <span className="text-sm text-muted-foreground w-16">中尺寸：</span>
        <CollapsibleSearch
          value={value2}
          onChange={setValue2}
          placeholder="中尺寸搜索..."
          size="md"
        />
      </div>
      <div className="flex items-center gap-4">
        <span className="text-sm text-muted-foreground w-16">大尺寸：</span>
        <CollapsibleSearch
          value={value3}
          onChange={setValue3}
          placeholder="大尺寸搜索..."
          size="lg"
        />
      </div>
    </div>
  );
}

render(<SizesCollapsibleSearch />);
    `,
    scope: { CollapsibleSearch, React },
  },
]

export default function CollapsibleSearchPreview() {
  return (
    <ComponentPreviewContainer
      title="可折叠搜索框 CollapsibleSearch"
      description="支持展开收起的搜索框组件，默认收起状态节省空间，点击展开进行搜索"
      whenToUse="适用于空间有限的界面，如导航栏、工具栏等场景。提供紧凑的搜索功能。"
      examples={examples}
      apiDocs={<CollapsibleSearchApiDocs />}
    />
  );
}

function CollapsibleSearchApiDocs() {
  return (
    <div className="space-y-8">
      <div>
        <h3 className="font-medium text-lg mb-4">CollapsibleSearch</h3>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-3 border font-medium">参数</th>
                <th className="p-3 border font-medium">类型</th>
                <th className="p-3 border font-medium">默认值</th>
                <th className="p-3 border font-medium">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-3 border">value</td>
                <td className="p-3 border">string</td>
                <td className="p-3 border">""</td>
                <td className="p-3 border">搜索值</td>
              </tr>
              <tr>
                <td className="p-3 border">onChange</td>
                <td className="p-3 border">(value: string) => void</td>
                <td className="p-3 border">-</td>
                <td className="p-3 border">值变化回调</td>
              </tr>
              <tr>
                <td className="p-3 border">onSearch</td>
                <td className="p-3 border">(value: string) => void</td>
                <td className="p-3 border">-</td>
                <td className="p-3 border">搜索提交回调</td>
              </tr>
              <tr>
                <td className="p-3 border">placeholder</td>
                <td className="p-3 border">string</td>
                <td className="p-3 border">"搜索..."</td>
                <td className="p-3 border">占位符</td>
              </tr>
              <tr>
                <td className="p-3 border">defaultExpanded</td>
                <td className="p-3 border">boolean</td>
                <td className="p-3 border">false</td>
                <td className="p-3 border">是否默认展开</td>
              </tr>
              <tr>
                <td className="p-3 border">disabled</td>
                <td className="p-3 border">boolean</td>
                <td className="p-3 border">false</td>
                <td className="p-3 border">是否禁用</td>
              </tr>
              <tr>
                <td className="p-3 border">size</td>
                <td className="p-3 border">"sm" | "md" | "lg"</td>
                <td className="p-3 border">"md"</td>
                <td className="p-3 border">尺寸</td>
              </tr>
              <tr>
                <td className="p-3 border">className</td>
                <td className="p-3 border">string</td>
                <td className="p-3 border">-</td>
                <td className="p-3 border">自定义类名</td>
              </tr>
              <tr>
                <td className="p-3 border">onExpandedChange</td>
                <td className="p-3 border">(expanded: boolean) => void</td>
                <td className="p-3 border">-</td>
                <td className="p-3 border">展开状态变化回调</td>
              </tr>
              <tr>
                <td className="p-3 border">expandDirection</td>
                <td className="p-3 border">"left" | "right"</td>
                <td className="p-3 border">"right"</td>
                <td className="p-3 border">展开方向</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div>
        <h3 className="font-medium text-lg mb-4">键盘操作</h3>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-3 border font-medium">按键</th>
                <th className="p-3 border font-medium">功能</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-3 border">Enter</td>
                <td className="p-3 border">提交搜索并收起</td>
              </tr>
              <tr>
                <td className="p-3 border">Escape</td>
                <td className="p-3 border">收起搜索框</td>
              </tr>
              <tr>
                <td className="p-3 border">点击外部</td>
                <td className="p-3 border">失焦时自动收起</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

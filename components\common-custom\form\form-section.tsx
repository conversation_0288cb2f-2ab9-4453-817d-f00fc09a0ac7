"use client"

import { ReactNode } from "react"
import { cn } from "@/lib/utils"
import { FormSectionProps } from "@/types/form"

/**
 * 表单分节组件
 */
export function FormSection({ title, description, children, className }: FormSectionProps) {
  return (
    <div className={cn("space-y-4 mb-6", className)}>
      {title && (
        <div className="space-y-1">
          <h3 className="text-lg font-medium">{title}</h3>
          {description && <p className="text-sm text-muted-foreground">{description}</p>}
        </div>
      )}
      <div className="space-y-4">{children}</div>
    </div>
  )
} 
/**
 * API类型定义
 * 包含API请求和响应的基础类型
 */

// 响应类型
export interface ApiResponse<T = unknown> {
  code: string | number;
  msg: string;
  data: T;
}

// 分页参数
export interface PaginationParams {
  page?: number;
  size?: number;
  keyword?: string;
  sort?: string;
  order?: 'asc' | 'desc';
}

// 角色分页查询参数
export interface RolePaginationParams {
  roleName?: string;
  roleCode?: string;
  teamCode?: string;
  status?: number;
  pageNum: number;
  pageSize: number;
}

// 分页响应结果
export interface PageResult<T> {
  total: number;
  list: T[];
  pageNum: number;
  pageSize: number;
  pages: number;
}
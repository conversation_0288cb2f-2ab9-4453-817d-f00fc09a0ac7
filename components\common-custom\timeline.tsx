"use client"

import { ReactNode } from "react"
import { cn } from "@/lib/utils"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Check, Clock, AlertCircle, Calendar, LucideIcon } from "lucide-react"
// ============================================================================
// 类型定义
// ============================================================================

/**
 * 时间线项属性
 */
export interface TimelineItem {
  /**
   * 唯一ID
   */
  id: string | number

  /**
   * 标题
   */
  title: ReactNode

  /**
   * 描述
   */
  description?: ReactNode

  /**
   * 时间
   */
  time?: string

  /**
   * 图标
   */
  icon?: LucideIcon | ReactNode

  /**
   * 状态
   */
  status?: "pending" | "current" | "completed" | "error"

  /**
   * 颜色
   */
  color?: string

  /**
   * 是否可点击
   */
  clickable?: boolean

  /**
   * 点击事件
   */
  onClick?: () => void

  /**
   * 自定义类名
   */
  className?: string

  /**
   * 自定义内容
   */
  content?: ReactNode

  /**
   * 子项
   */
  children?: TimelineItem[]
}

/**
 * 时间轴属性
 */
export interface TimelineProps {
  /**
   * 时间线项列表
   */
  items: TimelineItem[]

  /**
   * 显示模式
   */
  mode?: "vertical" | "horizontal"

  /**
   * 是否反向显示
   */
  reverse?: boolean

  /**
   * 对齐方式
   */
  align?: "left" | "center" | "right"

  /**
   * 连接线样式
   */
  connectorStyle?: "solid" | "dashed" | "dotted"

  /**
   * 连接线颜色
   */
  connectorColor?: string

  /**
   * 圆点大小
   */
  dotSize?: "sm" | "md" | "lg"

  /**
   * 圆点形状
   */
  dotShape?: "circle" | "square"

  /**
   * 是否可折叠
   */
  collapsible?: boolean

  /**
   * 自定义类名
   */
  className?: string

  /**
   * 自定义渲染函数
   */
  renderItem?: (item: TimelineItem, index: number) => ReactNode

  /**
   * 项点击事件
   */
  onItemClick?: (item: TimelineItem, index: number) => void
}

/**
 * 活动时间轴属性
 */
export interface ActivityTimelineProps {
  /**
   * 活动列表
   */
  activities: Array<{
    id: string | number
    user: {
      name: string
      avatar?: string
    }
    action: string
    target?: string
    time: string
    type?: "create" | "update" | "delete" | "comment" | "status"
    details?: ReactNode
  }>

  /**
   * 是否显示头像
   */
  showAvatar?: boolean

  /**
   * 是否显示时间
   */
  showTime?: boolean

  /**
   * 是否分组显示
   */
  grouped?: boolean

  /**
   * 自定义类名
   */
  className?: string

  /**
   * 活动点击事件
   */
  onActivityClick?: (activity: any) => void
}

/**
 * 阶段时间轴属性
 */
export interface StageTimelineProps {
  /**
   * 阶段列表
   */
  stages: Array<{
    id: string | number
    title: string
    description?: string
    status: "pending" | "current" | "completed" | "error"
    startTime?: string
    endTime?: string
    duration?: string
    progress?: number
    details?: ReactNode
  }>

  /**
   * 是否显示进度
   */
  showProgress?: boolean

  /**
   * 是否显示时间
   */
  showTime?: boolean

  /**
   * 自定义类名
   */
  className?: string

  /**
   * 阶段点击事件
   */
  onStageClick?: (stage: any) => void
}

// ============================================================================
// 组件实现
// ============================================================================

/**
 * 基础时间轴组件
 */
export function Timeline({
  items,
  mode = "vertical",
  reverse = false,
  align = "left",
  connectorStyle = "solid",
  connectorColor,
  dotSize = "md",
  dotShape = "circle",
  collapsible = false,
  className,
  renderItem,
}: TimelineProps) {
  const timelineItems = reverse ? [...items].reverse() : items

  const getConnectorClassName = () => {
    const baseClass = "w-0.5"
    const styleClass = connectorStyle === "dashed" 
      ? "border-l border-dashed" 
      : connectorStyle === "dotted" 
        ? "border-l border-dotted" 
        : "bg-border"
    
    return cn(baseClass, styleClass)
  }

  const getDotSizeClassName = (itemDotSize?: "sm" | "md" | "lg") => {
    const size = itemDotSize || dotSize
    switch (size) {
      case "sm": return "w-4 h-4"
      case "lg": return "w-10 h-10"
      default: return "w-8 h-8"
    }
  }

  const getDotShapeClassName = () => {
    return dotShape === "square" ? "rounded-md" : "rounded-full"
  }

  const getStatusColor = (status?: string) => {
    switch (status) {
      case "success": return "bg-green-500"
      case "warning": return "bg-yellow-500"
      case "error": return "bg-red-500"
      case "primary": return "bg-primary"
      default: return "bg-gray-300"
    }
  }

  return (
    <div className={cn(
      "relative",
      mode === "horizontal" && "flex",
      className
    )}>
      {timelineItems.map((item, index) => {
        const isLast = index === timelineItems.length - 1
        
        return (
          <div 
            key={item.id} 
            className={cn(
              "relative",
              mode === "vertical" ? "flex gap-4 pb-6" : "flex-1",
              isLast && mode === "vertical" && "pb-0"
            )}
          >
            {renderItem ? (
              renderItem(item, index)
            ) : (
              <>
                <div className="flex flex-col items-center">
                  <div
                    className={cn(
                      "flex items-center justify-center",
                      getDotSizeClassName(item.dotSize),
                      getDotShapeClassName(),
                      getStatusColor(item.status)
                    )}
                  >
                    {item.icon && typeof item.icon === 'function' 
                      ? item.icon({}) 
                      : item.icon}
                  </div>
                  {!isLast && item.showConnector !== false && (
                    <div
                      className={cn(
                        "h-6 mt-2",
                        getConnectorClassName()
                      )}
                      style={connectorColor ? { borderColor: connectorColor } : undefined}
                    />
                  )}
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-1">
                    <h4 className="font-medium">{item.title}</h4>
                    {item.time && (
                      <span className="text-sm text-muted-foreground">{item.time}</span>
                    )}
                  </div>
                  {item.description && (
                    <p className="text-sm text-muted-foreground mb-2">{item.description}</p>
                  )}
                  {item.content}
                </div>
              </>
            )}
          </div>
        )
      })}
    </div>
  )
}

/**
 * 活动时间轴组件
 */
export function ActivityTimeline({
  activities,
  mode = "vertical",
  showAvatar = true,
  dotSize = "md",
  showConnector = true,
  className,
  onLoadMore,
  hasMore = false,
}: ActivityTimelineProps) {
  return (
    <div className={cn("relative", className)}>
      {activities.map((activity, index) => {
        const isLast = index === activities.length - 1
        
        return (
          <div key={activity.id} className="flex gap-4 pb-4 last:pb-0">
            <div className="flex flex-col items-center">
              {showAvatar && activity.user ? (
                <Avatar className="h-8 w-8">
                  <AvatarFallback className="text-xs">
                    {activity.user.avatar || activity.user.name.charAt(0)}
                  </AvatarFallback>
                </Avatar>
              ) : activity.icon ? (
                <div className={cn(
                  "flex items-center justify-center rounded-full",
                  dotSize === "sm" ? "w-6 h-6" : dotSize === "lg" ? "w-10 h-10" : "w-8 h-8",
                  activity.status === "success" ? "bg-green-100 text-green-600" :
                  activity.status === "warning" ? "bg-yellow-100 text-yellow-600" :
                  activity.status === "error" ? "bg-red-100 text-red-600" :
                  activity.status === "primary" ? "bg-primary/10 text-primary" :
                  "bg-gray-100 text-gray-600"
                )}>
                  {typeof activity.icon === 'function' ? activity.icon({}) : activity.icon}
                </div>
              ) : null}
              {!isLast && showConnector && (
                <div className="w-0.5 h-4 mt-2 bg-gray-200" />
              )}
            </div>
            <div className="flex-1">
              <div className="flex items-center justify-between mb-1">
                <div className="flex items-center gap-2">
                  {activity.user && (
                    <span className="font-medium text-sm">{activity.user.name}</span>
                  )}
                  <span className="text-sm text-muted-foreground">{activity.action}</span>
                  {activity.target && (
                    <span className="font-medium text-sm">{activity.target}</span>
                  )}
                </div>
                <span className="text-xs text-muted-foreground">{activity.time}</span>
              </div>
              {activity.content}
            </div>
          </div>
        )
      })}
      
      {onLoadMore && (
        <div className="text-center mt-4">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={onLoadMore}
            disabled={!hasMore}
          >
            {hasMore ? "查看更多活动" : "没有更多活动"}
          </Button>
        </div>
      )}
    </div>
  )
}

/**
 * 阶段时间轴组件
 */
export function StageTimeline({
  stages,
  currentStage,
  onChange,
  mode = "vertical",
  className,
  readonly = false,
  compact = false,
}: StageTimelineProps) {
  const getStageIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <Check className="h-3 w-3 text-white" />
      case "current":
        return <Clock className="h-3 w-3 text-white" />
      case "error":
        return <AlertCircle className="h-3 w-3 text-white" />
      default:
        return null
    }
  }

  const getStageColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-500"
      case "current":
        return "bg-blue-500"
      case "error":
        return "bg-red-500"
      default:
        return "bg-gray-300"
    }
  }

  const handleStageClick = (stageId: string | number) => {
    if (!readonly && onChange) {
      onChange(stageId)
    }
  }

  return (
    <div className={cn(
      "relative",
      mode === "horizontal" && "flex items-center justify-between",
      className
    )}>
      {stages.map((stage, index) => {
        const isLast = index === stages.length - 1
        const isCurrent = stage.id === currentStage || stage.status === "current"
        const isCompleted = stage.status === "completed"
        
        return (
          <div 
            key={stage.id} 
            className={cn(
              "relative",
              mode === "vertical" ? "flex gap-4 pb-6" : "flex-1 flex-col items-center",
              isLast && mode === "vertical" && "pb-0",
              !readonly && "cursor-pointer",
              isCurrent && "text-primary"
            )}
            onClick={() => handleStageClick(stage.id)}
          >
            <div className={cn(
              mode === "vertical" ? "flex flex-col items-center" : "w-full flex-col items-center"
            )}>
              <div
                className={cn(
                  "flex items-center justify-center rounded-full",
                  compact ? "w-6 h-6" : "w-8 h-8",
                  getStageColor(stage.status),
                  isCurrent && "ring-2 ring-offset-2 ring-primary"
                )}
              >
                {stage.icon 
                  ? (typeof stage.icon === 'function' ? stage.icon({}) : stage.icon) 
                  : getStageIcon(stage.status)}
              </div>
              
              {!isLast && mode === "vertical" && (
                <div
                  className={cn(
                    "w-0.5 h-6 mt-2",
                    isCompleted ? "bg-green-500" : "bg-gray-200"
                  )}
                />
              )}
              
              {!isLast && mode === "horizontal" && (
                <div className="absolute top-1/2 left-[calc(50%+16px)] right-0 h-0.5 -translate-y-1/2">
                  <div
                    className={cn(
                      "h-full w-full",
                      isCompleted ? "bg-green-500" : "bg-gray-200"
                    )}
                  />
                </div>
              )}
            </div>
            
            <div className={cn(
              "flex-1",
              mode === "horizontal" && "text-center mt-2"
            )}>
              <div className={cn(
                mode === "vertical" ? "flex items-center justify-between mb-1" : "mb-1"
              )}>
                <h4 className={cn(
                  "font-medium",
                  isCurrent && "text-primary"
                )}>
                  {stage.name}
                </h4>
                {stage.time && mode === "vertical" && (
                  <div className="flex items-center gap-2">
                    <Calendar className="h-3 w-3 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">{stage.time}</span>
                  </div>
                )}
              </div>
              
              {!compact && stage.description && (
                <p className="text-sm text-muted-foreground">
                  {stage.description}
                </p>
              )}
              
              {stage.time && mode === "horizontal" && (
                <div className="text-xs text-muted-foreground mt-1">
                  {stage.time}
                </div>
              )}
            </div>
          </div>
        )
      })}
    </div>
  )
}

/**
 * 时间轴容器组件，用于包装时间轴相关组件
 */
export function TimelineContainer({ children }: { children: ReactNode }) {
  return (
    <div className="space-y-8">
      {children}
    </div>
  )
} 
"use client";

import React from "react";
import { LiveProvider, LiveError, LivePreview } from "react-live";
import { LivePreviewProps } from "./types";

/**
 * 转换代码，处理require语句和处理可能的语法错误
 */
const transformCode = (code: string) => {
  // 移除require语句
  let transformedCode = code.replace(/require\(['"](.*)['"]\)/g, "{}");
  
  // 保留React导入，但注释掉其他导入
  transformedCode = transformedCode.replace(/import\s+React\s+from\s+['"]react['"]/g, "// React already available");
  transformedCode = transformedCode.replace(/import\s+{([^}]*?)}\s+from\s+['"]react['"]/g, "// React hooks already available");
  
  // 处理其他导入语句
  transformedCode = transformedCode.replace(/import\s+.*\s+from\s+['"]((?!react).+)['"]/g, "// import from $1");
  
  // 确保代码结尾有分号
  if (!transformedCode.trim().endsWith(";") && !transformedCode.trim().endsWith("}")) {
    transformedCode = transformedCode.trim() + ";";
  }

  // 移除所有引用require的代码，以防止运行时错误
  transformedCode = transformedCode.replace(/.*require.*/g, "// require removed");
  
  return transformedCode;
};

export default function LivePreviewWrapper({ code, scope }: LivePreviewProps) {
  // 提取render函数调用部分
  const hasRenderCall = code.includes("render(");
  
  // 合并基础scope和自定义scope
  const combinedScope = {
    ...scope,
    // 添加渲染函数，将组件直接返回，不包装
    render: (Component: React.ReactNode) => Component
  };

  return (
    <LiveProvider 
      code={code} 
      scope={combinedScope}
      transformCode={transformCode}
      noInline={hasRenderCall}
    >
      <LivePreview />
      <LiveError className="text-red-500 mt-2 text-sm" />
    </LiveProvider>
  );
} 
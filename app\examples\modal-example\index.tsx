"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Modal, AlertDialogModal, FormModal, InfoModal } from '@/components/common-custom/modal'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Check, Info, AlertTriangle } from 'lucide-react'

export function ModalExample() {
  const [showBasicModal, setShowBasicModal] = useState(false)
  const [showLargeModal, setShowLargeModal] = useState(false)
  const [showFullScreenModal, setShowFullScreenModal] = useState(false)
  const [showAlertDialog, setShowAlertDialog] = useState(false)
  const [showFormModal, setShowFormModal] = useState(false)
  const [showInfoModal, setShowInfoModal] = useState(false)

  return (
    <div className='w-full space-y-8'>
      <div>
        <h3 className="text-lg font-medium mb-4">基本模态框</h3>
        <div className='flex flex-wrap gap-4'>
          <Button onClick={() => setShowBasicModal(true)}>
            基础模态框
          </Button>
          <Button onClick={() => setShowLargeModal(true)} variant='outline'>
            大尺寸模态框
          </Button>
          <Button onClick={() => setShowFullScreenModal(true)} variant='secondary'>
            全屏模态框
          </Button>
        </div>
      </div>
      
      <div>
        <h3 className="text-lg font-medium mb-4">特殊模态框</h3>
        <div className='flex flex-wrap gap-4'>
          <Button onClick={() => setShowAlertDialog(true)} variant="destructive">
            <AlertTriangle className="w-4 h-4 mr-2" />
            警告对话框
          </Button>
          <Button onClick={() => setShowFormModal(true)} variant="default">
            <Check className="w-4 h-4 mr-2" />
            表单模态框
          </Button>
          <Button onClick={() => setShowInfoModal(true)} variant="outline">
            <Info className="w-4 h-4 mr-2" />
            信息模态框
          </Button>
        </div>
      </div>

      <Modal
        open={showBasicModal}
        onOpenChange={(open) => setShowBasicModal(open)}
        title='基础模态框'
      >
        <div className='py-4'>
          <p>这是一个基础的模态框示例，用于展示简单的内容或者确认信息。</p>
        </div>
      </Modal>

      <Modal
        open={showLargeModal}
        onOpenChange={(open) => setShowLargeModal(open)}
        title='大尺寸模态框'
        maxWidth='sm:max-w-[600px]'
      >
        <div className='py-4'>
          <p>这是一个大尺寸的模态框示例，适合展示更多的内容或复杂表单。</p>
          <div className='h-60 bg-accent/20 mt-4 rounded flex items-center justify-center'>
            <span className='text-muted-foreground'>内容区域</span>
          </div>
        </div>
      </Modal>

      <Modal
        open={showFullScreenModal}
        onOpenChange={(open) => setShowFullScreenModal(open)}
        title='全屏模态框'
        maxWidth='sm:max-w-full h-screen'
        className='h-screen max-h-screen'
      >
        <div className='py-4'>
          <p>这是一个全屏的模态框示例，适合需要最大工作区域的复杂内容。</p>
          <div className='h-80 bg-accent/20 mt-4 rounded flex items-center justify-center'>
            <span className='text-muted-foreground'>内容区域</span>
          </div>
        </div>
      </Modal>
      
      {/* 警告对话框 */}
      <AlertDialogModal
        open={showAlertDialog}
        onOpenChange={setShowAlertDialog}
        title="确认删除"
        description="您确定要删除此项吗？此操作不可撤销。"
        confirmText="删除"
        cancelText="取消"
        onConfirm={() => {
          console.log("确认删除");
          setShowAlertDialog(false);
        }}
      />
      
      {/* 表单模态框 */}
      <FormModal
        open={showFormModal}
        onOpenChange={setShowFormModal}
        title="编辑用户信息"
        description="请填写以下表单以更新用户信息"
        onSubmit={() => console.log("表单已提交")}
      >
        <div className="py-4 space-y-4">
          <div className="space-y-2">
            <label htmlFor="name" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
              姓名
            </label>
            <Input id="name" placeholder="请输入姓名" />
          </div>
          <div className="space-y-2">
            <label htmlFor="email" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
              邮箱
            </label>
            <Input id="email" type="email" placeholder="请输入邮箱" />
          </div>
          <div className="space-y-2">
            <label htmlFor="bio" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
              简介
            </label>
            <Textarea id="bio" placeholder="请输入个人简介" />
          </div>
        </div>
      </FormModal>
      
      {/* 信息模态框 */}
      <InfoModal
        open={showInfoModal}
        onOpenChange={setShowInfoModal}
        title="操作成功"
        description="您的操作已经成功完成"
        closeText="我知道了"
      >
        <div className="py-6 flex items-center justify-center text-center">
          <div className="rounded-full bg-green-100 p-3 dark:bg-green-900/30">
            <Check className="h-8 w-8 text-green-600 dark:text-green-400" />
          </div>
        </div>
      </InfoModal>
    </div>
  )
} 
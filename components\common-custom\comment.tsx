"use client"

import { useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { MessageCircle, MoreHorizontal, Reply, ThumbsUp } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
// ============================================================================
// 类型定义
// ============================================================================

/**
 * 回复类型
 */
export interface CommentReply {
  /**
   * 回复ID
   */
  id: string | number

  /**
   * 回复作者
   */
  author: string

  /**
   * 回复作者头像
   */
  avatar?: string

  /**
   * 回复内容
   */
  content: string

  /**
   * 回复时间
   */
  time: string

  /**
   * 点赞数
   */
  likes?: number

  /**
   * 是否已点赞
   */
  liked?: boolean
}

/**
 * 评论类型
 */
export interface Comment {
  /**
   * 评论ID
   */
  id: string | number

  /**
   * 评论作者
   */
  author: string

  /**
   * 评论作者头像
   */
  avatar?: string

  /**
   * 评论内容
   */
  content: string

  /**
   * 评论时间
   */
  time: string

  /**
   * 点赞数
   */
  likes?: number

  /**
   * 是否已点赞
   */
  liked?: boolean

  /**
   * 回复列表
   */
  replies?: CommentReply[]

  /**
   * 评论状态
   */
  status?: "published" | "pending" | "hidden"

  /**
   * 评论标签
   */
  tags?: string[]

  /**
   * 是否置顶
   */
  pinned?: boolean

  /**
   * 评论等级
   */
  level?: number
}

/**
 * 评论输入框属性
 */
export interface CommentInputProps {
  /**
   * 占位符文本
   */
  placeholder?: string

  /**
   * 是否显示头像
   */
  showAvatar?: boolean

  /**
   * 用户头像
   */
  userAvatar?: string

  /**
   * 是否禁用
   */
  disabled?: boolean

  /**
   * 最大字符数
   */
  maxLength?: number

  /**
   * 是否显示字符计数
   */
  showCharCount?: boolean

  /**
   * 提交按钮文本
   */
  submitText?: string

  /**
   * 取消按钮文本
   */
  cancelText?: string

  /**
   * 是否显示取消按钮
   */
  showCancelButton?: boolean

  /**
   * 提交回调
   */
  onSubmit?: (content: string) => void

  /**
   * 取消回调
   */
  onCancel?: () => void

  /**
   * 自定义类名
   */
  className?: string
}

/**
 * 评论列表属性
 */
export interface CommentListProps {
  /**
   * 评论列表
   */
  comments?: Comment[]

  /**
   * 标题
   */
  title?: string

  /**
   * 空状态文本
   */
  emptyText?: string

  /**
   * 是否显示回复
   */
  showReplies?: boolean

  /**
   * 是否可回复
   */
  allowReply?: boolean

  /**
   * 是否可点赞
   */
  allowLike?: boolean

  /**
   * 是否可删除
   */
  allowDelete?: boolean

  /**
   * 是否可编辑
   */
  allowEdit?: boolean

  /**
   * 最大嵌套层级
   */
  maxNestLevel?: number

  /**
   * 评论点击回调
   */
  onCommentClick?: (comment: Comment) => void

  /**
   * 回复回调
   */
  onReply?: (commentId: string | number) => void

  /**
   * 点赞回调
   */
  onLike?: (commentId: string | number) => void

  /**
   * 删除回调
   */
  onDelete?: (comment: Comment) => void

  /**
   * 编辑回调
   */
  onEdit?: (comment: Comment, content: string) => void

  /**
   * 提交回复回调
   */
  onSubmitReply?: (commentId: string | number, content: string) => void

  /**
   * 更多操作
   */
  moreActions?: React.ReactNode

  /**
   * 自定义类名
   */
  className?: string
}

/**
 * 评论系统属性
 */
export interface CommentSystemProps {
  /**
   * 评论列表
   */
  comments: Comment[]

  /**
   * 是否显示输入框
   */
  showInput?: boolean

  /**
   * 输入框属性
   */
  inputProps?: Omit<CommentInputProps, 'onSubmit'>

  /**
   * 列表属性
   */
  listProps?: Omit<CommentListProps, 'comments'>

  /**
   * 标题
   */
  title?: string

  /**
   * 评论总数
   */
  total?: number

  /**
   * 是否显示统计
   */
  showStats?: boolean

  /**
   * 排序方式
   */
  sortBy?: "time" | "likes" | "replies"

  /**
   * 排序顺序
   */
  sortOrder?: "asc" | "desc"

  /**
   * 新增评论回调
   */
  onAddComment?: (content: string) => void

  /**
   * 回复评论回调
   */
  onReplyComment?: (comment: Comment, content: string) => void

  /**
   * 点赞评论回调
   */
  onLikeComment?: (comment: Comment) => void

  /**
   * 删除评论回调
   */
  onDeleteComment?: (comment: Comment) => void

  /**
   * 编辑评论回调
   */
  onEditComment?: (comment: Comment, content: string) => void

  /**
   * 自定义类名
   */
  className?: string
}

// ============================================================================
// 组件实现
// ============================================================================

/**
 * 评论输入框组件
 */
export function CommentInput({
  placeholder = "写下你的评论...",
  buttonText = "发表评论",
  hint = "支持 Markdown 语法",
  disabled = false,
  className = "",
  onSubmit,
  avatar,
}: CommentInputProps) {
  const [text, setText] = useState("")

  const handleSubmit = () => {
    if (text.trim() && onSubmit) {
      onSubmit(text)
      setText("")
    }
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-sm">发表评论</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-3">
          {avatar && (
            <Avatar className="h-8 w-8">
              <AvatarImage src={avatar.src || ""} />
              <AvatarFallback>{avatar.fallback}</AvatarFallback>
            </Avatar>
          )}
          <div className="flex-1">
            <Textarea
              placeholder={placeholder}
              value={text}
              onChange={(e) => setText(e.target.value)}
              className="min-h-[80px]"
              disabled={disabled}
            />
          </div>
        </div>
        <div className="flex justify-between items-center">
          <div className="text-xs text-muted-foreground">{hint}</div>
          <Button onClick={handleSubmit} disabled={disabled || !text.trim()}>
            {buttonText}
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

/**
 * 评论列表组件
 */
export function CommentList({
  comments = [],
  className = "",
  emptyText = "暂无评论",
  title,
  onLike,
  onReply,
  onSubmitReply,
  moreActions,
}: CommentListProps) {
  const [replyTo, setReplyTo] = useState<string | number | null>(null)
  const [replyText, setReplyText] = useState("")

  const handleReply = (commentId: string | number) => {
    if (onReply) {
      onReply(commentId)
    } else {
      setReplyTo(replyTo === commentId ? null : commentId)
    }
  }

  const handleSubmitReply = (commentId: string | number) => {
    if (replyText.trim() && onSubmitReply) {
      onSubmitReply(commentId, replyText)
      setReplyText("")
      setReplyTo(null)
    }
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-sm flex items-center gap-2">
          {title || (
            <>
              <MessageCircle className="h-4 w-4" />
              评论列表 ({comments.length})
            </>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {comments.length === 0 ? (
          <div className="text-center py-6 text-muted-foreground">{emptyText}</div>
        ) : (
          <div className="space-y-6">
            {comments.map((comment) => (
              <div key={comment.id} className="space-y-4">
                {/* 主评论 */}
                <div className="flex gap-3">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={comment.avatar || "/placeholder.svg"} />
                    <AvatarFallback>{comment.author[0]}</AvatarFallback>
                  </Avatar>
                  <div className="flex-1 space-y-2">
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-sm">{comment.author}</span>
                      {comment.isAuthor && (
                        <Badge variant="secondary" className="text-xs">
                          {comment.authorBadgeText || "作者"}
                        </Badge>
                      )}
                      <span className="text-xs text-muted-foreground">{comment.time}</span>
                    </div>
                    <p className="text-sm leading-relaxed">{comment.content}</p>
                    <div className="flex items-center gap-4">
                      <Button 
                        variant={comment.isLiked ? "secondary" : "ghost"} 
                        size="sm" 
                        className="h-8 px-2 text-xs"
                        onClick={() => onLike?.(comment.id)}
                      >
                        <ThumbsUp className="h-3 w-3 mr-1" />
                        {comment.likes || 0}
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 px-2 text-xs"
                        onClick={() => handleReply(comment.id)}
                      >
                        <Reply className="h-3 w-3 mr-1" />
                        回复
                      </Button>
                      {moreActions && moreActions.length > 0 && (
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-3 w-3" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            {moreActions.map((action, index) => (
                              <DropdownMenuItem 
                                key={index}
                                onClick={() => action.onClick(comment.id)}
                              >
                                {action.label}
                              </DropdownMenuItem>
                            ))}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      )}
                    </div>

                    {/* 回复输入框 */}
                    {replyTo === comment.id && (
                      <div className="space-y-2 pt-2">
                        <Textarea
                          placeholder={`回复 @${comment.author}...`}
                          value={replyText}
                          onChange={(e) => setReplyText(e.target.value)}
                          className="min-h-[60px] text-sm"
                        />
                        <div className="flex gap-2">
                          <Button size="sm" onClick={() => handleSubmitReply(comment.id)} disabled={!replyText.trim()}>
                            回复
                          </Button>
                          <Button variant="outline" size="sm" onClick={() => setReplyTo(null)}>
                            取消
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* 回复列表 */}
                {comment.replies && comment.replies.length > 0 && (
                  <div className="ml-11 space-y-4 border-l-2 border-muted pl-4">
                    {comment.replies.map((reply) => (
                      <div key={reply.id} className="flex gap-3">
                        <Avatar className="h-6 w-6">
                          <AvatarImage src={reply.avatar || "/placeholder.svg"} />
                          <AvatarFallback className="text-xs">{reply.author[0]}</AvatarFallback>
                        </Avatar>
                        <div className="flex-1 space-y-1">
                          <div className="flex items-center gap-2">
                            <span className="font-medium text-sm">{reply.author}</span>
                            <span className="text-xs text-muted-foreground">{reply.time}</span>
                          </div>
                          <p className="text-sm leading-relaxed">{reply.content}</p>
                          <div className="flex items-center gap-4">
                            <Button 
                              variant={reply.isLiked ? "secondary" : "ghost"} 
                              size="sm" 
                              className="h-6 px-2 text-xs"
                              onClick={() => onLike?.(reply.id, true)}
                            >
                              <ThumbsUp className="h-3 w-3 mr-1" />
                              {reply.likes || 0}
                            </Button>
                            <Button 
                              variant="ghost" 
                              size="sm" 
                              className="h-6 px-2 text-xs"
                              onClick={() => handleReply(comment.id)}
                            >
                              <Reply className="h-3 w-3 mr-1" />
                              回复
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

/**
 * 评论系统组件，集成了评论输入框和评论列表
 */
export function CommentSystem({
  comments,
  showInput = true,
  inputProps = {},
  listProps = {},
  onSubmitComment,
  onSubmitReply,
  className = "",
}: CommentSystemProps) {
  return (
    <div className={`space-y-6 ${className}`}>
      {showInput && (
        <CommentInput
          {...inputProps}
          onSubmit={onSubmitComment}
        />
      )}
      <CommentList
        comments={comments}
        {...listProps}
        onSubmitReply={onSubmitReply}
      />
    </div>
  )
}

/**
 * 评论组件的主要导出
 * 用户可以直接使用 CommentSystem，或者分别使用 CommentInput 和 CommentList
 */
export const Comment = CommentSystem 
/**
 * 卡片模板组件示例代码集合
 * 
 * 按照新规范管理示例代码，避免在页面文件中硬编码
 */

import React from "react"
import { 
  BlogCard,
  ProductCardTemplate,
  EventCard,
  DashboardCard,
  TestimonialCard
} from "@/components/common-custom/card-templates"
import { ThumbsUp } from "lucide-react"

// ============================================================================
// 博客卡片示例
// ============================================================================

export const blogCardExample = {
  id: "blog-card",
  title: "博客卡片",
  description: "展示博客文章信息的卡片组件",
  code: `
import React from "react";
import { BlogCard } from "@/components/common-custom/card-templates";

function BlogCardExample() {
  const blogData = {
    title: "构建现代化React应用：最佳实践与技巧",
    excerpt: "学习如何使用最新的React特性和模式构建高性能、可维护的前端应用程序。",
    author: {
      name: "张伟",
      avatar: "/placeholder-user.jpg",
    },
    publishDate: "2023-11-20",
    readTime: "5分钟",
    category: "前端开发",
    image: "/placeholder.jpg",
    stats: {
      views: 1420,
      likes: 238,
      comments: 32
    }
  };
  
  return (
    <div className="w-full max-w-sm mx-auto">
      <BlogCard
        title={blogData.title}
        excerpt={blogData.excerpt}
        author={blogData.author}
        publishDate={blogData.publishDate}
        readTime={blogData.readTime}
        category={blogData.category}
        image={blogData.image}
        stats={blogData.stats}
      />
    </div>
  );
}

render(<BlogCardExample />);
  `,
  scope: { BlogCard, React },
}

// ============================================================================
// 产品卡片示例
// ============================================================================

export const productCardExample = {
  id: "product-card",
  title: "产品卡片",
  description: "展示产品信息的卡片组件",
  code: `
import React from "react";
import { ProductCardTemplate } from "@/components/common-custom/card-templates";

function ProductCardExample() {
  const productData = {
    name: "专业设计师办公椅",
    price: 1299,
    originalPrice: 1599,
    rating: 4.8,
    reviewCount: 124,
    image: "/placeholder.jpg",
    inStock: true,
    features: ["人体工学设计", "高质量网面靠背", "多角度调节", "静音滚轮"]
  };
  
  return (
    <div className="w-full max-w-sm mx-auto">
      <ProductCardTemplate data={productData} />
    </div>
  );
}

render(<ProductCardExample />);
  `,
  scope: { ProductCardTemplate, React },
}

// ============================================================================
// 活动卡片示例
// ============================================================================

export const eventCardExample = {
  id: "event-card",
  title: "活动卡片",
  description: "展示活动信息的卡片组件",
  code: `
import React from "react";
import { EventCard } from "@/components/common-custom/card-templates";

function EventCardExample() {
  const eventData = {
    title: "2023技术创新峰会",
    description: "加入我们的年度峰会，探讨最新的技术趋势和创新解决方案。",
    date: "2023-12-15",
    time: "09:00 - 18:00",
    location: "上海国际会议中心",
    image: "/placeholder.jpg",
    attendees: 356,
    categories: ["技术", "创新", "峰会"]
  };
  
  return (
    <div className="w-full max-w-sm mx-auto">
      <EventCard data={eventData} />
    </div>
  );
}

render(<EventCardExample />);
  `,
  scope: { EventCard, React },
}

// ============================================================================
// 仪表盘卡片示例
// ============================================================================

export const dashboardCardExample = {
  id: "dashboard-card",
  title: "仪表盘卡片",
  description: "展示统计数据的卡片组件",
  code: `
import React from "react";
import { DashboardCard } from "@/components/common-custom/card-templates";
import { ThumbsUp } from "lucide-react";

function DashboardCardExample() {
  const dashboardData = {
    title: "月度销售额",
    value: "￥128,560",
    change: 12.5,
    trend: "increase" as const,
    description: "较上月增长12.5%",
    icon: <ThumbsUp className="h-4 w-4" />
  };
  
  return (
    <div className="w-full max-w-sm mx-auto">
      <DashboardCard data={dashboardData} />
    </div>
  );
}

render(<DashboardCardExample />);
  `,
  scope: { DashboardCard, ThumbsUp, React },
}

// ============================================================================
// 推荐卡片示例
// ============================================================================

export const testimonialCardExample = {
  id: "testimonial-card",
  title: "推荐卡片",
  description: "展示用户推荐的卡片组件",
  code: `
import React from "react";
import { TestimonialCard } from "@/components/common-custom/card-templates";

function TestimonialCardExample() {
  const testimonialData = {
    quote: "这个产品彻底改变了我们的工作流程，提高了团队效率，节省了大量时间。",
    author: {
      name: "李明",
      position: "技术总监",
      company: "未来科技有限公司",
      avatar: "/placeholder-user.jpg",
    },
    rating: 5
  };
  
  return (
    <div className="w-full max-w-sm mx-auto">
      <TestimonialCard data={testimonialData} />
    </div>
  );
}

render(<TestimonialCardExample />);
  `,
  scope: { TestimonialCard, React },
}

// ============================================================================
// 统一导出所有示例
// ============================================================================

export const allExamples = [
  blogCardExample,
  productCardExample,
  eventCardExample,
  dashboardCardExample,
  testimonialCardExample,
]

// ============================================================================
// 按类别导出示例
// ============================================================================

export const basicExamples = [blogCardExample, productCardExample]
export const advancedExamples = [eventCardExample, dashboardCardExample, testimonialCardExample]
export const contentExamples = [blogCardExample, eventCardExample, testimonialCardExample]
export const commercialExamples = [productCardExample, dashboardCardExample]

// ============================================================================
// 示例元数据
// ============================================================================

export const exampleMetadata = {
  total: allExamples.length,
  categories: {
    basic: basicExamples.length,
    advanced: advancedExamples.length,
    content: contentExamples.length,
    commercial: commercialExamples.length,
  },
  tags: ["card", "template", "layout", "ui", "content", "dashboard", "blog", "product"],
  lastUpdated: "2024-01-01",
  description: "卡片模板组件示例集合，展示了多种内容展示场景的实现方式",
}

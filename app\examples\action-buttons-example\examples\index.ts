/**
 * 操作按钮组件示例代码集合
 * 
 * 按照新规范管理示例代码，避免在页面文件中硬编码
 */

import React from "react"
import { ActionButtons } from "@/components/common-custom/action-buttons"
import { Edit, Trash, Eye, Copy, Download, Share, Settings, MoreHorizontal, RefreshCw } from "lucide-react"

// ============================================================================
// 基础操作按钮示例
// ============================================================================

export const basicExample = {
  id: "basic-action-buttons",
  title: "基础操作按钮",
  description: "基本的操作按钮组合",
  code: `
import React from "react";
import { ActionButtons } from "@/components/common-custom/action-buttons";
import { Edit, Trash, Eye } from "lucide-react";

function BasicActionButtonsExample() {
  const actions = [
    {
      id: "view",
      label: "查看",
      icon: React.createElement(Eye, { className: "h-4 w-4" }),
      onClick: () => console.log("查看操作"),
    },
    {
      id: "edit",
      label: "编辑",
      icon: React.createElement(Edit, { className: "h-4 w-4" }),
      onClick: () => console.log("编辑操作"),
    },
    {
      id: "delete",
      label: "删除",
      icon: React.createElement(Trash, { className: "h-4 w-4" }),
      destructive: true,
      onClick: () => console.log("删除操作"),
    },
  ];

  return (
    <div className="space-y-4">
      <div>
        <h4 className="text-sm font-medium mb-2">默认样式</h4>
        <ActionButtons
          actions={actions}
          deleteConfirmProps={{
            title: "确认删除",
            description: "删除后无法恢复，请谨慎操作",
          }}
        />
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">紧凑样式</h4>
        <ActionButtons
          variant="compact"
          actions={actions}
          deleteConfirmProps={{
            title: "确认删除",
            description: "删除后无法恢复，请谨慎操作",
          }}
        />
      </div>
    </div>
  );
}

render(<BasicActionButtonsExample />);
  `,
  scope: {
    React,
    ActionButtons,
    Edit,
    Trash,
    Eye
  },
}

// ============================================================================
// 下拉菜单示例
// ============================================================================

export const dropdownExample = {
  id: "dropdown-action-buttons",
  title: "下拉菜单操作",
  description: "使用下拉菜单组织多个操作",
  code: `
import React from "react";
import { ActionButtons } from "@/components/common-custom/action-buttons";
import { Edit, Trash, Eye, Copy, Download, Share, Settings } from "lucide-react";

function DropdownActionButtonsExample() {
  const actions = [
    {
      id: "view",
      label: "查看详情",
      icon: React.createElement(Eye, { className: "h-4 w-4" }),
      onClick: () => console.log("查看详情"),
      shortcut: "⌘V",
    },
    {
      id: "edit",
      label: "编辑",
      icon: React.createElement(Edit, { className: "h-4 w-4" }),
      onClick: () => console.log("编辑"),
      shortcut: "⌘E",
    },
    {
      id: "copy",
      label: "复制",
      icon: React.createElement(Copy, { className: "h-4 w-4" }),
      onClick: () => console.log("复制"),
      shortcut: "⌘C",
    },
    {
      id: "download",
      label: "下载",
      icon: React.createElement(Download, { className: "h-4 w-4" }),
      onClick: () => console.log("下载"),
    },
    {
      id: "share",
      label: "分享",
      icon: React.createElement(Share, { className: "h-4 w-4" }),
      onClick: () => console.log("分享"),
    },
    {
      id: "settings",
      label: "设置",
      icon: React.createElement(Settings, { className: "h-4 w-4" }),
      onClick: () => console.log("设置"),
    },
    {
      id: "delete",
      label: "删除",
      icon: React.createElement(Trash, { className: "h-4 w-4" }),
      destructive: true,
      onClick: () => console.log("删除"),
    },
  ];

  return (
    <div className="space-y-4">
      <div>
        <h4 className="text-sm font-medium mb-2">下拉菜单</h4>
        <ActionButtons
          variant="dropdown"
          actions={actions}
          deleteConfirmProps={{
            title: "确认删除项目",
            description: "删除后将无法恢复，相关数据也会被清除",
            confirmText: "确认删除",
            cancelText: "取消",
          }}
        />
      </div>
    </div>
  );
}

render(<DropdownActionButtonsExample />);
  `,
  scope: {
    React,
    ActionButtons,
    Edit,
    Trash,
    Eye,
    Copy,
    Download,
    Share,
    Settings
  },
}

// ============================================================================
// 表格操作示例
// ============================================================================

export const tableExample = {
  id: "table-action-buttons",
  title: "表格行操作",
  description: "适用于表格行的操作按钮组合",
  code: `
import React from "react";
import { ActionButtons } from "@/components/common-custom/action-buttons";
import { Edit, Trash, Eye, Copy, Download } from "lucide-react";

function TableActionButtonsExample() {
  const actions = [
    {
      id: "view",
      label: "查看",
      icon: React.createElement(Eye, { className: "h-4 w-4" }),
      onClick: () => console.log("查看"),
    },
    {
      id: "edit",
      label: "编辑",
      icon: React.createElement(Edit, { className: "h-4 w-4" }),
      onClick: () => console.log("编辑"),
    },
    {
      id: "copy",
      label: "复制",
      icon: React.createElement(Copy, { className: "h-4 w-4" }),
      onClick: () => console.log("复制"),
    },
    {
      id: "download",
      label: "下载",
      icon: React.createElement(Download, { className: "h-4 w-4" }),
      onClick: () => console.log("下载"),
    },
    {
      id: "delete",
      label: "删除",
      icon: React.createElement(Trash, { className: "h-4 w-4" }),
      destructive: true,
      onClick: () => console.log("删除"),
    },
  ];

  return (
    <div className="space-y-4">
      <div>
        <h4 className="text-sm font-medium mb-2">表格行操作</h4>
        <div className="border rounded-lg">
          <table className="w-full">
            <thead>
              <tr className="border-b">
                <th className="text-left p-3">名称</th>
                <th className="text-left p-3">状态</th>
                <th className="text-left p-3">创建时间</th>
                <th className="text-right p-3">操作</th>
              </tr>
            </thead>
            <tbody>
              {[1, 2, 3].map((item) => (
                <tr key={item} className="border-b last:border-b-0">
                  <td className="p-3">项目 {item}</td>
                  <td className="p-3">
                    <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
                      活跃
                    </span>
                  </td>
                  <td className="p-3">2024-01-0{item}</td>
                  <td className="p-3">
                    <div className="flex justify-end">
                      <ActionButtons
                        variant="table"
                        actions={actions}
                        maxVisibleActions={2}
                        deleteConfirmProps={{
                          title: \`确认删除项目 \${item}\`,
                          description: "删除后无法恢复",
                        }}
                      />
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

render(<TableActionButtonsExample />);
  `,
  scope: {
    React,
    ActionButtons,
    Edit,
    Trash,
    Eye,
    Copy,
    Download
  },
}

// ============================================================================
// 高级功能示例
// ============================================================================

export const advancedExample = {
  id: "advanced-action-buttons",
  title: "高级功能",
  description: "展示加载状态、禁用状态等高级功能",
  code: `
import React, { useState } from "react";
import { ActionButtons } from "@/components/common-custom/action-buttons";
import { Edit, Trash, Eye, Download, RefreshCw } from "lucide-react";

function AdvancedActionButtonsExample() {
  const [loading, setLoading] = useState(false);
  const [disabled, setDisabled] = useState(false);
  
  const handleAsyncAction = async (actionName) => {
    setLoading(true);
    console.log(\`开始执行: \${actionName}\`);
    
    // 模拟异步操作
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log(\`完成执行: \${actionName}\`);
    setLoading(false);
  };

  const actions = [
    {
      id: "view",
      label: "查看",
      icon: React.createElement(Eye, { className: "h-4 w-4" }),
      onClick: () => handleAsyncAction("查看"),
      disabled: disabled,
    },
    {
      id: "edit",
      label: "编辑",
      icon: React.createElement(Edit, { className: "h-4 w-4" }),
      onClick: () => handleAsyncAction("编辑"),
      description: "编辑当前项目的详细信息",
    },
    {
      id: "download",
      label: "下载",
      icon: React.createElement(Download, { className: "h-4 w-4" }),
      onClick: () => handleAsyncAction("下载"),
      loading: loading,
    },
    {
      id: "refresh",
      label: "刷新",
      icon: React.createElement(RefreshCw, { className: "h-4 w-4" }),
      onClick: () => handleAsyncAction("刷新"),
      variant: "outline" as const,
    },
    {
      id: "delete",
      label: "删除",
      icon: React.createElement(Trash, { className: "h-4 w-4" }),
      destructive: true,
      onClick: () => handleAsyncAction("删除"),
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex gap-4">
        <button
          onClick={() => setDisabled(!disabled)}
          className="px-3 py-1 bg-gray-100 rounded text-sm"
        >
          {disabled ? "启用操作" : "禁用操作"}
        </button>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">异步操作示例</h4>
        <ActionButtons
          actions={actions}
          onBeforeAction={async (action) => {
            console.log(\`准备执行: \${action.label}\`);
            return true;
          }}
          onAfterAction={(action) => {
            console.log(\`操作完成: \${action.label}\`);
          }}
          deleteConfirmProps={{
            title: "确认删除",
            description: "此操作将永久删除数据，无法恢复",
          }}
        />
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">紧凑模式（显示标签）</h4>
        <ActionButtons
          variant="compact"
          actions={actions.slice(0, 3)}
          showLabels={true}
        />
      </div>
    </div>
  );
}

render(<AdvancedActionButtonsExample />);
  `,
  scope: {
    React,
    ActionButtons,
    Edit,
    Trash,
    Eye,
    Download,
    RefreshCw,
    useState: React.useState
  },
}

// ============================================================================
// 统一导出所有示例
// ============================================================================

export const allExamples = [
  basicExample,
  dropdownExample,
  tableExample,
  advancedExample,
]

// ============================================================================
// 按类别导出示例
// ============================================================================

export const basicExamples = [basicExample]
export const layoutExamples = [dropdownExample, tableExample]
export const advancedExamples = [advancedExample]

// ============================================================================
// 示例元数据
// ============================================================================

export const exampleMetadata = {
  total: allExamples.length,
  categories: {
    basic: basicExamples.length,
    layout: layoutExamples.length,
    advanced: advancedExamples.length,
  },
  tags: ["actions", "buttons", "dropdown", "table", "async"],
  lastUpdated: "2024-01-01",
}

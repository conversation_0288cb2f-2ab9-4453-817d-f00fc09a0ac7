import { PermissionNode, PermissionType, PermissionGroup, RolePermission } from "@/types/permission";

/**
 * 权限树模拟数据
 */
export const permissionTreeMock: PermissionNode[] = [
  {
    id: "group_system",
    name: "系统管理",
    type: PermissionType.Directory,
    path: "/settings",
    icon: "settings",
    order: 1,
    enabled: true,
    children: [
      {
        id: "menu_user",
        name: "用户管理",
        type: PermissionType.Menu,
        path: "/members",
        icon: "users",
        parentId: "group_system",
        order: 1,
        enabled: true,
        children: [
          {
            id: "btn_user_add",
            name: "新增用户",
            type: PermissionType.Button,
            permissionId: "user:add",
            parentId: "menu_user",
            order: 1,
            enabled: true,
            children: []
          },
          {
            id: "btn_user_edit",
            name: "编辑用户",
            type: PermissionType.Button,
            permissionId: "user:edit",
            parentId: "menu_user",
            order: 2,
            enabled: true,
            children: []
          },
          {
            id: "btn_user_delete",
            name: "删除用户",
            type: PermissionType.Button,
            permissionId: "user:delete",
            parentId: "menu_user",
            order: 3,
            enabled: true,
            children: []
          },
          {
            id: "btn_user_view",
            name: "查看用户",
            type: PermissionType.Button,
            permissionId: "user:view",
            parentId: "menu_user",
            order: 4,
            enabled: true,
            children: []
          }
        ]
      },
      {
        id: "menu_role",
        name: "角色管理",
        type: PermissionType.Menu,
        path: "/settings/roles",
        icon: "shield",
        parentId: "group_system",
        order: 2,
        enabled: true,
        children: [
          {
            id: "btn_role_add",
            name: "新增角色",
            type: PermissionType.Button,
            permissionId: "role:add",
            parentId: "menu_role",
            order: 1,
            enabled: true,
            children: []
          },
          {
            id: "btn_role_edit",
            name: "编辑角色",
            type: PermissionType.Button,
            permissionId: "role:edit",
            parentId: "menu_role",
            order: 2,
            enabled: true,
            children: []
          },
          {
            id: "btn_role_delete",
            name: "删除角色",
            type: PermissionType.Button,
            permissionId: "role:delete",
            parentId: "menu_role",
            order: 3,
            enabled: true,
            children: []
          },
          {
            id: "btn_role_permission",
            name: "权限设置",
            type: PermissionType.Button,
            permissionId: "role:permission",
            parentId: "menu_role",
            order: 4,
            enabled: true,
            children: []
          }
        ]
      },
      {
        id: "menu_permission",
        name: "权限设置",
        type: PermissionType.Menu,
        path: "/settings/permission",
        icon: "key",
        parentId: "group_system",
        order: 3,
        enabled: true,
        children: [
          {
            id: "btn_permission_add",
            name: "新增权限",
            type: PermissionType.Button,
            permissionId: "permission:add",
            parentId: "menu_permission",
            order: 1,
            enabled: true,
            children: []
          },
          {
            id: "btn_permission_edit",
            name: "编辑权限",
            type: PermissionType.Button,
            permissionId: "permission:edit",
            parentId: "menu_permission",
            order: 2,
            enabled: true,
            children: []
          },
          {
            id: "btn_permission_delete",
            name: "删除权限",
            type: PermissionType.Button,
            permissionId: "permission:delete",
            parentId: "menu_permission",
            order: 3,
            enabled: true,
            children: []
          }
        ]
      }
    ]
  },
  {
    id: "group_project",
    name: "项目管理",
    type: PermissionType.Directory,
    path: "/projects",
    icon: "folder",
    order: 2,
    enabled: true,
    children: [
      {
        id: "menu_project_list",
        name: "项目列表",
        type: PermissionType.Menu,
        path: "/projects",
        icon: "list",
        parentId: "group_project",
        order: 1,
        enabled: true,
        children: [
          {
            id: "btn_project_add",
            name: "新增项目",
            type: PermissionType.Button,
            permissionId: "project:add",
            parentId: "menu_project_list",
            order: 1,
            enabled: true,
            children: []
          },
          {
            id: "btn_project_edit",
            name: "编辑项目",
            type: PermissionType.Button,
            permissionId: "project:edit",
            parentId: "menu_project_list",
            order: 2,
            enabled: true,
            children: []
          },
          {
            id: "btn_project_delete",
            name: "删除项目",
            type: PermissionType.Button,
            permissionId: "project:delete",
            parentId: "menu_project_list",
            order: 3,
            enabled: true,
            children: []
          }
        ]
      },
      {
        id: "menu_team",
        name: "团队管理",
        type: PermissionType.Menu,
        path: "/teams",
        icon: "users",
        parentId: "group_project",
        order: 2,
        enabled: true,
        children: [
          {
            id: "btn_team_add",
            name: "新增团队",
            type: PermissionType.Button,
            permissionId: "team:add",
            parentId: "menu_team",
            order: 1,
            enabled: true,
            children: []
          },
          {
            id: "btn_team_edit",
            name: "编辑团队",
            type: PermissionType.Button,
            permissionId: "team:edit",
            parentId: "menu_team",
            order: 2,
            enabled: true,
            children: []
          },
          {
            id: "btn_team_delete",
            name: "删除团队",
            type: PermissionType.Button,
            permissionId: "team:delete",
            parentId: "menu_team",
            order: 3,
            enabled: true,
            children: []
          }
        ]
      }
    ]
  },
  {
    id: "group_tools",
    name: "工具管理",
    type: PermissionType.Directory,
    path: "/tool",
    icon: "tool",
    order: 3,
    enabled: true,
    children: [
      {
        id: "menu_password",
        name: "密码管理",
        type: PermissionType.Menu,
        path: "/tool/password",
        icon: "key",
        parentId: "group_tools",
        order: 1,
        enabled: true,
        children: [
          {
            id: "btn_password_add",
            name: "新增密码",
            type: PermissionType.Button,
            permissionId: "password:add",
            parentId: "menu_password",
            order: 1,
            enabled: true,
            children: []
          },
          {
            id: "btn_password_edit",
            name: "编辑密码",
            type: PermissionType.Button,
            permissionId: "password:edit",
            parentId: "menu_password",
            order: 2,
            enabled: true,
            children: []
          },
          {
            id: "btn_password_delete",
            name: "删除密码",
            type: PermissionType.Button,
            permissionId: "password:delete",
            parentId: "menu_password",
            order: 3,
            enabled: true,
            children: []
          }
        ]
      },
      {
        id: "menu_notice",
        name: "通知配置",
        type: PermissionType.Menu,
        path: "/tool/noticeconfig",
        icon: "bell",
        parentId: "group_tools",
        order: 2,
        enabled: true,
        children: [
          {
            id: "btn_notice_add",
            name: "新增配置",
            type: PermissionType.Button,
            permissionId: "notice:add",
            parentId: "menu_notice",
            order: 1,
            enabled: true,
            children: []
          },
          {
            id: "btn_notice_edit",
            name: "编辑配置",
            type: PermissionType.Button,
            permissionId: "notice:edit",
            parentId: "menu_notice",
            order: 2,
            enabled: true,
            children: []
          },
          {
            id: "btn_notice_delete",
            name: "删除配置",
            type: PermissionType.Button,
            permissionId: "notice:delete",
            parentId: "menu_notice",
            order: 3,
            enabled: true,
            children: []
          }
        ]
      }
    ]
  }
]; 

/**
 * 权限组模拟数据
 */
export const permissionGroupsMock: PermissionGroup[] = [
  {
    id: "1",
    code: "system",
    name: "系统管理",
    description: "系统管理相关权限",
    icon: "settings",
    order: 1
  },
  {
    id: "2",
    code: "project",
    name: "项目管理",
    description: "项目管理相关权限",
    icon: "folder",
    order: 2
  },
  {
    id: "3",
    code: "personal",
    name: "个人中心",
    description: "个人中心相关权限",
    icon: "user",
    order: 3
  },
  {
    id: "4",
    code: "report",
    name: "报表管理",
    description: "报表和数据分析相关权限",
    icon: "bar-chart",
    order: 4
  },
  {
    id: "5",
    code: "workflow",
    name: "工作流程",
    description: "工作流程相关权限",
    icon: "git-branch",
    order: 5
  }
];

/**
 * 按权限组分类的权限树
 */
export const getPermissionTreeByGroup = (groupCode: string): PermissionNode[] => {
  // 根据权限组代码过滤权限树
  const allNodes: PermissionNode[] = [];
  
  const filterNodesByGroup = (nodes: PermissionNode[], groupCode: string): PermissionNode[] => {
    return nodes.filter(node => {
      // 检查当前节点是否属于指定组
      const isInGroup = node.groupCode === groupCode;
      
      // 递归检查子节点
      let filteredChildren: PermissionNode[] = [];
      if (node.children && node.children.length > 0) {
        filteredChildren = filterNodesByGroup(node.children, groupCode);
      }
      
      // 如果当前节点属于指定组或者它的子节点中有属于指定组的，则保留
      if (isInGroup || filteredChildren.length > 0) {
        // 深拷贝节点，并只保留符合条件的子节点
        const newNode = { ...node };
        if (filteredChildren.length > 0) {
          newNode.children = filteredChildren;
        }
        return true;
      }
      
      return false;
    });
  };
  
  // 开始递归过滤
  switch (groupCode) {
    case "system":
      return permissionTreeMock.filter(node => node.id === "group_system");
    case "project":
      return permissionTreeMock.filter(node => node.id === "group_project");
    case "personal":
      // 假设个人中心权限在系统管理中，我们需要提取出来
      return [
        {
          id: "group_personal",
          name: "个人中心",
          type: PermissionType.Directory,
          path: "/profile",
          icon: "user",
          order: 3,
          enabled: true,
          children: [
            {
              id: "menu_profile",
              name: "个人资料",
              type: PermissionType.Menu,
              permissionId: "profile:view",
              path: "/profile",
              icon: "user",
              order: 1,
              enabled: true,
              children: []
            },
            {
              id: "menu_account",
              name: "账号安全",
              type: PermissionType.Menu,
              permissionId: "account:security",
              path: "/profile/security",
              icon: "shield",
              order: 2,
              enabled: true,
              children: [
                {
                  id: "btn_change_password",
                  name: "修改密码",
                  type: PermissionType.Button,
                  permissionId: "account:change_password",
                  order: 1,
                  enabled: true,
                  children: []
                }
              ]
            }
          ]
        }
      ];
    case "report":
      return [
        {
          id: "group_report",
          name: "报表管理",
          type: PermissionType.Directory,
          path: "/reports",
          icon: "bar-chart",
          order: 4,
          enabled: true,
          children: [
            {
              id: "menu_data_analysis",
              name: "数据分析",
              type: PermissionType.Menu,
              permissionId: "report:analysis",
              path: "/reports/analysis",
              icon: "pie-chart",
              order: 1,
              enabled: true,
              children: []
            },
            {
              id: "menu_reports",
              name: "报表中心",
              type: PermissionType.Menu,
              permissionId: "report:center",
              path: "/reports/center",
              icon: "file-text",
              order: 2,
              enabled: true,
              children: [
                {
                  id: "btn_export_report",
                  name: "导出报表",
                  type: PermissionType.Button,
                  permissionId: "report:export",
                  order: 1,
                  enabled: true,
                  children: []
                }
              ]
            }
          ]
        }
      ];
    case "workflow":
      return [
        {
          id: "group_workflow",
          name: "工作流程",
          type: PermissionType.Directory,
          path: "/workflow",
          icon: "git-branch",
          order: 5,
          enabled: true,
          children: [
            {
              id: "menu_approval",
              name: "审批流程",
              type: PermissionType.Menu,
              permissionId: "workflow:approval",
              path: "/workflow/approval",
              icon: "check-circle",
              order: 1,
              enabled: true,
              children: [
                {
                  id: "btn_create_workflow",
                  name: "创建流程",
                  type: PermissionType.Button,
                  permissionId: "workflow:create",
                  order: 1,
                  enabled: true,
                  children: []
                },
                {
                  id: "btn_approve",
                  name: "审批",
                  type: PermissionType.Button,
                  permissionId: "workflow:approve",
                  order: 2,
                  enabled: true,
                  children: []
                }
              ]
            }
          ]
        }
      ];
    default:
      return permissionTreeMock;
  }
};

/**
 * 角色权限关联模拟数据
 */
export const rolePermissionsMock: RolePermission[] = [
  { roleCode: "admin", permissionCode: "user:view", createTime: "2023-10-01 10:00:00" },
  { roleCode: "admin", permissionCode: "user:add", createTime: "2023-10-01 10:00:00" },
  { roleCode: "admin", permissionCode: "user:edit", createTime: "2023-10-01 10:00:00" },
  { roleCode: "admin", permissionCode: "user:delete", createTime: "2023-10-01 10:00:00" },
  { roleCode: "admin", permissionCode: "role:view", createTime: "2023-10-01 10:00:00" },
  { roleCode: "admin", permissionCode: "role:add", createTime: "2023-10-01 10:00:00" },
  { roleCode: "admin", permissionCode: "role:edit", createTime: "2023-10-01 10:00:00" },
  { roleCode: "admin", permissionCode: "role:delete", createTime: "2023-10-01 10:00:00" },
  { roleCode: "admin", permissionCode: "role:permission", createTime: "2023-10-01 10:00:00" },
  { roleCode: "editor", permissionCode: "user:view", createTime: "2023-10-01 10:00:00" },
  { roleCode: "editor", permissionCode: "project:view", createTime: "2023-10-01 10:00:00" },
  { roleCode: "editor", permissionCode: "project:edit", createTime: "2023-10-01 10:00:00" }
];

/**
 * 获取角色的权限编码列表
 */
export const getRolePermissionsMock = (roleCode: string): string[] => {
  return rolePermissionsMock
    .filter(item => item.roleCode === roleCode)
    .map(item => item.permissionCode);
}; 
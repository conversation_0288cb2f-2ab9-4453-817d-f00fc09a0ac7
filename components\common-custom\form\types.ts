import { ReactNode, ComponentProps } from "react"
import { LucideIcon } from "lucide-react"
import { Card } from "@/components/ui/card"

/**
 * 表单字段选项
 */
export interface FormFieldOption {
  label: string
  value: string | number | boolean
  disabled?: boolean
  description?: string
}

/**
 * 表单字段属性
 */
export interface FormFieldProps {
  /**
   * 字段名
   */
  name: string
  
  /**
   * 字段标签
   */
  label?: ReactNode
  
  /**
   * 字段类型
   */
  type?: "text" | "email" | "password" | "number" | "tel" | "url" | "textarea" | "select" | "radio" | "checkbox" | "switch" | "slider" | "date" | "custom"
  
  /**
   * 占位符
   */
  placeholder?: string
  
  /**
   * 描述文本
   */
  description?: ReactNode
  
  /**
   * 错误信息
   */
  error?: string
  
  /**
   * 是否必填
   */
  required?: boolean
  
  /**
   * 是否禁用
   */
  disabled?: boolean
  
  /**
   * 是否只读
   */
  readOnly?: boolean
  
  /**
   * 字段值
   */
  value?: any
  
  /**
   * 默认值
   */
  defaultValue?: any
  
  /**
   * 选项列表（用于select、radio、checkbox）
   */
  options?: FormFieldOption[]
  
  /**
   * 最小值（用于number、slider）
   */
  min?: number
  
  /**
   * 最大值（用于number、slider）
   */
  max?: number
  
  /**
   * 步长（用于number、slider）
   */
  step?: number
  
  /**
   * 最小长度
   */
  minLength?: number
  
  /**
   * 最大长度
   */
  maxLength?: number
  
  /**
   * 验证模式
   */
  pattern?: string
  
  /**
   * 自定义验证函数
   */
  validate?: (value: any) => string | undefined
  
  /**
   * 值变化回调
   */
  onChange?: (value: any) => void
  
  /**
   * 失焦回调
   */
  onBlur?: () => void
  
  /**
   * 获得焦点回调
   */
  onFocus?: () => void
  
  /**
   * 自定义渲染函数
   */
  render?: (props: any) => ReactNode
  
  /**
   * 自定义类名
   */
  className?: string
  
  /**
   * 字段图标
   */
  icon?: LucideIcon
  
  /**
   * 字段前缀
   */
  prefix?: ReactNode
  
  /**
   * 字段后缀
   */
  suffix?: ReactNode
  
  /**
   * 字段提示
   */
  tooltip?: string
  
  /**
   * 字段尺寸
   */
  size?: "sm" | "md" | "lg"
  
  /**
   * 字段变体
   */
  variant?: "default" | "outline" | "filled"
}

/**
 * 表单分组属性
 */
export interface FormSectionProps {
  /**
   * 分组标题
   */
  title?: ReactNode
  
  /**
   * 分组描述
   */
  description?: ReactNode
  
  /**
   * 分组图标
   */
  icon?: LucideIcon
  
  /**
   * 是否可折叠
   */
  collapsible?: boolean
  
  /**
   * 默认是否展开
   */
  defaultOpen?: boolean
  
  /**
   * 分组字段
   */
  fields?: FormFieldProps[]
  
  /**
   * 子内容
   */
  children?: ReactNode
  
  /**
   * 自定义类名
   */
  className?: string
}

/**
 * 表单配置
 */
export interface FormConfig {
  /**
   * 表单标题
   */
  title?: ReactNode
  
  /**
   * 表单描述
   */
  description?: ReactNode
  
  /**
   * 表单字段
   */
  fields: FormFieldProps[]
  
  /**
   * 表单分组
   */
  sections?: FormSectionProps[]
  
  /**
   * 表单布局
   */
  layout?: "vertical" | "horizontal" | "inline"
  
  /**
   * 表单尺寸
   */
  size?: "sm" | "md" | "lg"
  
  /**
   * 是否显示必填标记
   */
  showRequiredMark?: boolean
  
  /**
   * 是否显示重置按钮
   */
  showResetButton?: boolean
  
  /**
   * 提交按钮文本
   */
  submitText?: string
  
  /**
   * 重置按钮文本
   */
  resetText?: string
  
  /**
   * 是否禁用表单
   */
  disabled?: boolean
  
  /**
   * 是否只读
   */
  readOnly?: boolean
  
  /**
   * 表单提交回调
   */
  onSubmit?: (values: Record<string, any>) => void | Promise<void>
  
  /**
   * 表单重置回调
   */
  onReset?: () => void
  
  /**
   * 表单值变化回调
   */
  onChange?: (values: Record<string, any>) => void
  
  /**
   * 表单验证回调
   */
  onValidate?: (values: Record<string, any>) => Record<string, string> | undefined
  
  /**
   * 自定义类名
   */
  className?: string
}

/**
 * 表单属性
 */
export interface FormProps extends Omit<ComponentProps<"form">, "onSubmit" | "onReset"> {
  /**
   * 表单配置
   */
  config?: FormConfig
  
  /**
   * 表单初始值
   */
  initialValues?: Record<string, any>
  
  /**
   * 表单验证模式
   */
  mode?: "onChange" | "onBlur" | "onSubmit"
  
  /**
   * 是否重新验证
   */
  reValidateMode?: "onChange" | "onBlur" | "onSubmit"
  
  /**
   * 表单提交状态
   */
  loading?: boolean
  
  /**
   * 表单错误
   */
  error?: string
  
  /**
   * 表单成功消息
   */
  success?: string
}

/**
 * 表单对话框属性
 */
export interface FormDialogProps {
  /**
   * 是否打开
   */
  open: boolean
  
  /**
   * 关闭回调
   */
  onClose: () => void
  
  /**
   * 对话框标题
   */
  title: ReactNode
  
  /**
   * 对话框描述
   */
  description?: ReactNode
  
  /**
   * 表单配置
   */
  formConfig: FormConfig
  
  /**
   * 表单初始值
   */
  initialValues?: Record<string, any>
  
  /**
   * 确认按钮文本
   */
  confirmText?: string
  
  /**
   * 取消按钮文本
   */
  cancelText?: string
  
  /**
   * 是否显示取消按钮
   */
  showCancelButton?: boolean
  
  /**
   * 对话框尺寸
   */
  size?: "sm" | "md" | "lg" | "xl"
  
  /**
   * 提交回调
   */
  onSubmit: (values: Record<string, any>) => void | Promise<void>
  
  /**
   * 自定义类名
   */
  className?: string
}

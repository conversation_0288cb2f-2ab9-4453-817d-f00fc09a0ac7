# 项目文档使用说明

欢迎来到 web-template-demo 项目文档中心。本文档集合为项目开发提供全面的规范和指导。

## 📚 文档目录结构

### 🎯 核心指导
项目开发的核心指导原则和快速参考：
- [核心指导规范](./核心指导规范.md) - 50字总结性指导规范
- [开发规则速查](./开发规则速查.md) - 核心开发规则与检查清单
- [使用说明](./使用说明.md) - 本文档，项目文档导航

### 🌐 通用规范
适用于前后端开发的所有场景：
- [AI协作规范](../通用规范/AI协作规范.md) - 多维思考与AI协作规则
- [MCP工具使用指南](../通用规范/MCP工具使用指南.md) - MCP工具集使用规范
- [API接口规范](../通用规范/API接口规范.md) - 通用API设计与实现规范

### 🎨 前端规范
专门针对前端开发的规范：
- [设计哲学](../前端规范/设计哲学.md) - 前端设计理念与原则
- [组件开发规范](../前端规范/组件开发规范.md) - React组件开发标准
- [色彩系统](../前端规范/色彩系统.md) - 色彩规范与使用指南
- [排版系统](../前端规范/排版系统.md) - 字体与排版规范
- [布局网格](../前端规范/布局网格.md) - 网格系统与布局规范

### 🚀 项目规范
针对当前web-template-demo项目的规范：
- [实施指南](../项目规范/实施指南.md) - 项目实施与开发指南
- [请求响应规范](../项目规范/请求响应规范.md) - 项目特定的请求处理规范

### 🛠️ 工作流程
项目管理和规范维护的流程指南：
- [任务拆分模板](../工作流程/任务拆分模板.md) - 任务拆分和记录的标准模板
- [规范更新指南](../工作流程/规范更新指南.md) - 优秀规范识别和文档更新流程

### 📋 项目总结
- [项目总结](../项目总结.md) - 文档整合工作总结

## 🚀 快速开始

### 根据角色选择文档
1. **新手开发者**：
   - 从 [核心指导规范](./核心指导规范.md) 开始
   - 阅读 [设计哲学](../前端规范/设计哲学.md)
   - 学习 [实施指南](../项目规范/实施指南.md)

2. **前端开发者**：
   - 重点阅读 [组件开发规范](../前端规范/组件开发规范.md)
   - 掌握 [MCP工具使用指南](../通用规范/MCP工具使用指南.md)
   - 参考前端规范文档

3. **后端开发者**：
   - 重点阅读 [API接口规范](../通用规范/API接口规范.md)
   - 掌握 [MCP工具使用指南](../通用规范/MCP工具使用指南.md)

4. **AI协作者**：
   - 必读 [AI协作规范](../通用规范/AI协作规范.md)
   - 掌握 [MCP工具使用指南](../通用规范/MCP工具使用指南.md)

### 日常开发参考
- **快速检查**：使用 [开发规则速查](./开发规则速查.md)
- **核心原则**：参考 [核心指导规范](./核心指导规范.md)
- **问题解决**：遵循MCP工具使用规范

## 📝 文档维护

本文档集基于项目实际需求整合而成，采用中文目录分类：
- **核心指导**：项目开发的核心指导原则
- **通用规范**：前后端通用规范
- **前端规范**：前端专用规范  
- **项目规范**：项目特定规范
- **工作流程**：流程管理和规范维护

定期更新以保持与项目发展同步。如有疑问或建议，请通过项目协作流程提出。

## 🎯 核心原则

**交流使用中文，遵循docs文档指导，合理使用MCP工具；任务需拆分记录为独立文档；发现优秀规范及时更新对应文档。**

1. **中文交流**：所有协作交流使用中文
2. **文档指导**：严格遵循docs下的规范文档
3. **MCP优先**：合理使用MCP工具提高效率
4. **任务拆分**：每个任务独立记录文档
5. **持续改进**：优秀规范及时更新文档

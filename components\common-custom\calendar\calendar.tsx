"use client"

import React, { useMemo, useCallback } from "react"
import { Calendar as CalendarPrimitive } from "@/components/ui/calendar"
import { cn } from "@/lib/utils"
import { CalendarProps, CalendarMode, CalendarValue, CalendarEvent } from "./types"
import { CalendarDay } from "./calendar-day"
import { CalendarLegend } from "./calendar-legend"

/**
 * 日历组件
 * 
 * 基于 react-day-picker 的增强日历组件，支持事件显示、多种选择模式、图例等功能
 * 
 * @example
 * ```tsx
 * // 基础用法
 * <Calendar 
 *   mode="single" 
 *   value={date} 
 *   handlers={{ onSelect: setDate }} 
 * />
 * 
 * // 带事件的日历
 * <Calendar 
 *   mode="single"
 *   value={date}
 *   events={events}
 *   handlers={{ 
 *     onSelect: setDate,
 *     onEventClick: handleEventClick 
 *   }}
 *   features={{ showEvents: true, showLegend: true }}
 *   legends={legends}
 * />
 * ```
 */
export function Calendar<T extends CalendarMode = "single">({
  mode = "single" as T,
  value,
  defaultValue,
  events = [],
  legends = [],
  handlers = {},
  style = {},
  features = {},
  dayPickerProps = {},
  renderDay,
  renderEvent,
  renderLegend,
}: CalendarProps<T>) {
  
  // 解构配置项并设置默认值
  const {
    showEvents = true,
    showLegend = false,
    legendPosition = "bottom",
    showSelectedInfo = true,
    showEventDetails = true,
    maxEventsPerDay = 3,
    enableEventTooltip = true,
  } = features
  
  const {
    className,
    calendarClassName,
    eventClassName,
    legendClassName,
    showBorder = true,
    showShadow = false,
  } = style
  
  const {
    onSelect,
    onEventClick,
    onDateClick,
    onMonthChange,
  } = handlers
  
  // 获取指定日期的事件
  const getEventsForDate = useCallback((date: Date): CalendarEvent[] => {
    if (!showEvents || !events.length || !date) return []

    return events.filter(event =>
      event.date &&
      event.date.getDate() === date.getDate() &&
      event.date.getMonth() === date.getMonth() &&
      event.date.getFullYear() === date.getFullYear()
    )
  }, [events, showEvents])
  
  // 处理日期选择
  const handleSelect = useCallback((selectedValue: any) => {
    if (onSelect) {
      onSelect(selectedValue as CalendarValue<T>)
    }
  }, [onSelect])
  
  // 处理事件点击
  const handleEventClick = useCallback((event: CalendarEvent, date: Date) => {
    if (onEventClick) {
      onEventClick(event, date)
    }
  }, [onEventClick])
  
  // 处理日期点击
  const handleDateClick = useCallback((date: Date) => {
    if (onDateClick) {
      onDateClick(date)
    }
  }, [onDateClick])
  
  // 自定义日期组件
  const dayComponent = useMemo(() => {
    if (!showEvents || !events.length) return undefined
    
    return {
      Day: ({ date, ...props }: any) => {
        const dayEvents = getEventsForDate(date)
        
        if (renderDay) {
          return renderDay(date, dayEvents)
        }
        
        return (
          <CalendarDay
            date={date}
            events={dayEvents}
            maxEvents={maxEventsPerDay}
            onClick={handleDateClick}
            onEventClick={handleEventClick}
            className={eventClassName}
            renderEvent={renderEvent}
            {...props}
          />
        )
      }
    }
  }, [
    showEvents, 
    events, 
    getEventsForDate, 
    renderDay, 
    maxEventsPerDay, 
    handleDateClick, 
    handleEventClick, 
    eventClassName, 
    renderEvent
  ])
  
  // 构建日历容器样式 - 参考 Ant Design 5.x 设计
  const containerClassName = cn(
    "calendar-container bg-background",
    {
      "border border-border rounded-lg": showBorder,
      "shadow-sm": showShadow,
    },
    className
  )

  // 构建日历样式 - 优化内边距和字体
  const calendarStyles = cn(
    "p-4 font-sans",
    "[&_.rdp-head_cell]:text-sm [&_.rdp-head_cell]:font-medium [&_.rdp-head_cell]:text-muted-foreground [&_.rdp-head_cell]:pb-2",
    "[&_.rdp-cell]:p-0",
    "[&_.rdp-button]:w-8 [&_.rdp-button]:h-8 [&_.rdp-button]:text-sm [&_.rdp-button]:font-normal",
    "[&_.rdp-button]:hover:bg-accent [&_.rdp-button]:hover:text-accent-foreground",
    "[&_.rdp-button]:focus-visible:ring-2 [&_.rdp-button]:focus-visible:ring-ring [&_.rdp-button]:focus-visible:ring-offset-2",
    "[&_.rdp-day_selected]:bg-primary [&_.rdp-day_selected]:text-primary-foreground [&_.rdp-day_selected]:hover:bg-primary/90",
    "[&_.rdp-day_today]:bg-accent [&_.rdp-day_today]:text-accent-foreground [&_.rdp-day_today]:font-medium",
    "[&_.rdp-day_outside]:text-muted-foreground/50",
    "[&_.rdp-day_disabled]:text-muted-foreground/30 [&_.rdp-day_disabled]:cursor-not-allowed",
    "[&_.rdp-nav_button]:w-8 [&_.rdp-nav_button]:h-8 [&_.rdp-nav_button]:hover:bg-accent",
    "[&_.rdp-caption]:flex [&_.rdp-caption]:justify-center [&_.rdp-caption]:items-center [&_.rdp-caption]:mb-4",
    "[&_.rdp-caption_label]:text-base [&_.rdp-caption_label]:font-medium",
    calendarClassName
  )
  
  // 渲染日历组件
  const renderCalendar = () => {
    const commonProps = {
      className: calendarStyles,
      components: dayComponent,
      onMonthChange,
      ...dayPickerProps,
    }
    
    switch (mode) {
      case "single":
        return (
          <CalendarPrimitive
            mode="single"
            selected={value as Date}
            onSelect={handleSelect}
            {...commonProps}
          />
        )
      case "multiple":
        return (
          <CalendarPrimitive
            mode="multiple"
            selected={value as Date[]}
            onSelect={handleSelect}
            {...commonProps}
          />
        )
      case "range":
        return (
          <CalendarPrimitive
            mode="range"
            selected={value as { from: Date; to?: Date }}
            onSelect={handleSelect}
            {...commonProps}
          />
        )
      default:
        return (
          <CalendarPrimitive
            mode="single"
            selected={value as Date}
            onSelect={handleSelect}
            {...commonProps}
          />
        )
    }
  }
  
  // 渲染图例
  const renderLegendComponent = () => {
    if (!showLegend || !legends.length) return null
    
    return (
      <CalendarLegend
        legends={legends}
        position={legendPosition}
        className={legendClassName}
        renderItem={renderLegend}
      />
    )
  }
  
  // 根据图例位置决定布局
  const isVerticalLegend = legendPosition === "left" || legendPosition === "right"
  const isTopLegend = legendPosition === "top"
  const isBottomLegend = legendPosition === "bottom"
  const isLeftLegend = legendPosition === "left"
  
  return (
    <div className={containerClassName}>
      {isTopLegend && renderLegendComponent()}
      
      <div className={cn(
        "calendar-content",
        {
          "flex gap-4": isVerticalLegend,
          "flex-row-reverse": isLeftLegend,
        }
      )}>
        {isLeftLegend && renderLegendComponent()}
        
        <div className="calendar-wrapper flex-1">
          {renderCalendar()}
        </div>
        
        {legendPosition === "right" && renderLegendComponent()}
      </div>
      
      {isBottomLegend && renderLegendComponent()}
    </div>
  )
}

Calendar.displayName = "Calendar"

---
description:
globs:
alwaysApply: true
---
# API和数据服务规则

## 服务层结构
- `services/api` - API请求接口目录
  - 命名规则为 `xxxRequestApi.ts`，如 `userRequestApi.ts`
  - 内部方法命名为 `xxxRequest`，如 `getUserRequest`
- `services/mock` - 模拟数据目录
  - 命名规则为 `xxxMock.ts`，如 `userMock.ts`
  - 一旦接口更换为真实接口，应删除对应的mock数据和mock接口

## API请求规范
1. 所有API请求必须包含错误处理逻辑
2. 在UI上提供适当的加载状态和错误提示
3. 使用TypeScript类型定义API接口参数和返回值
4. API接口不要创建index文件，需要使用时直接从目标方法导入

## 类型定义
- API请求和响应的类型定义放在 `types` 目录下
- 使用接口（interface）定义API请求和响应的数据结构
- 为复杂的嵌套数据结构创建专门的类型定义

## 示例代码
```typescript
// services/api/userRequestApi.ts
import { request } from '@/lib/request';
import { User, CreateUserParams } from '@/types/user';

// 获取用户列表
export const getUsersRequest = async () => {
  try {
    const response = await request.get<User[]>('/api/users');
    return response.data;
  } catch (error) {
    // 错误处理
    console.error('获取用户列表失败:', error);
    throw error;
  }
};

// 创建用户
export const createUserRequest = async (params: CreateUserParams) => {
  try {
    const response = await request.post<User>('/api/users', {
      data: params
    });
    return response.data;
  } catch (error) {
    // 错误处理
    console.error('创建用户失败:', error);
    throw error;
  }
};
```

```typescript
// services/mock/userMock.ts
import { User } from '@/types/user';

export const usersMock: User[] = [
  {
    id: '1',
    name: '张三',
    email: '<EMAIL>',
    role: 'admin'
  },
  {
    id: '2',
    name: '李四',
    email: '<EMAIL>',
    role: 'user'
  }
];
```

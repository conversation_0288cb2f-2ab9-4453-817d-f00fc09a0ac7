"use client"

import React, { ReactNode } from "react"
import { cn } from "@/lib/utils"
import { Tabs as ShadcnTabs, <PERSON><PERSON>Content, <PERSON>bsList, <PERSON>bsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Plus, X, LucideIcon } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card"

// ============================================================================
// 类型定义 - 单文件组件类型定义直接在组件文件内
// ============================================================================

/**
 * 标签页项属性
 */
export interface TabItem {
  /**
   * 标签ID
   */
  id: string

  /**
   * 标签标题
   */
  title: ReactNode

  /**
   * 标签内容
   */
  content: ReactNode

  /**
   * 标签图标
   */
  icon?: LucideIcon | ReactNode

  /**
   * 是否禁用
   * @default false
   */
  disabled?: boolean

  /**
   * 徽章内容
   */
  badge?: ReactNode | number | string

  /**
   * 徽章变体
   * @default "default"
   */
  badgeVariant?: "default" | "secondary" | "outline" | "destructive"

  /**
   * 自定义类名
   */
  className?: string

  /**
   * 描述
   */
  description?: ReactNode
}

/**
 * 标签页属性
 */
export interface TabsProps {
  /**
   * 标签项列表
   */
  tabs: TabItem[]

  /**
   * 默认激活的标签ID
   */
  defaultValue?: string

  /**
   * 当前激活的标签ID（受控模式）
   */
  value?: string

  /**
   * 标签变更回调
   */
  onValueChange?: (value: string) => void

  /**
   * 触发方式
   * @default "automatic"
   */
  activationMode?: "automatic" | "manual"

  /**
   * 方向
   * @default "horizontal"
   */
  orientation?: "horizontal" | "vertical"

  /**
   * 排列方式
   * @default "justify"
   */
  alignment?: "start" | "center" | "end" | "justify"

  /**
   * 大小
   * @default "md"
   */
  size?: "sm" | "md" | "lg"

  /**
   * 变体
   * @default "default"
   */
  variant?: "default" | "outline" | "underline" | "pills"

  /**
   * 是否可关闭标签
   * @default false
   */
  closable?: boolean

  /**
   * 关闭标签回调
   */
  onClose?: (id: string) => void

  /**
   * 是否可增加标签
   * @default false
   */
  addable?: boolean

  /**
   * 添加标签回调
   */
  onAdd?: () => void

  /**
   * 添加按钮文本
   * @default "添加标签"
   */
  addButtonText?: ReactNode

  /**
   * 自定义类名
   */
  className?: string

  /**
   * 内容样式类名
   */
  contentClassName?: string
}

/**
 * 卡片标签页属性
 */
export interface CardTabsProps extends Omit<TabsProps, 'variant'> {
  /**
   * 卡片标题
   */
  title?: ReactNode

  /**
   * 卡片描述
   */
  description?: ReactNode

  /**
   * 卡片操作
   */
  actions?: ReactNode

  /**
   * 是否显示分割线
   * @default true
   */
  showDivider?: boolean

  /**
   * 卡片内边距
   * @default true
   */
  cardPadding?: boolean

  /**
   * 卡片外边框
   * @default true
   */
  cardBorder?: boolean

  /**
   * 卡片圆角
   * @default true
   */
  cardRounded?: boolean
}

/**
 * 步骤标签页属性
 */
export interface StepTabsProps extends Omit<TabsProps, 'orientation' | 'variant'> {
  /**
   * 是否可返回
   * @default true
   */
  backable?: boolean

  /**
   * 返回按钮文本
   * @default "上一步"
   */
  backButtonText?: string

  /**
   * 下一步按钮文本
   * @default "下一步"
   */
  nextButtonText?: string

  /**
   * 完成按钮文本
   * @default "完成"
   */
  completeButtonText?: string

  /**
   * 返回回调
   */
  onBack?: () => void

  /**
   * 下一步回调
   */
  onNext?: (currentIndex: number) => boolean | Promise<boolean>

  /**
   * 完成回调
   */
  onComplete?: () => void

  /**
   * 步骤变更验证
   */
  onStepChange?: (fromIndex: number, toIndex: number) => boolean | Promise<boolean>
}

/**
 * 自定义标签页组件
 */
export function Tabs({
  tabs,
  defaultValue,
  value,
  onValueChange,
  activationMode,
  orientation = "horizontal",
  alignment = "justify",
  size = "md",
  variant = "default",
  closable = false,
  onClose,
  addable = false,
  onAdd,
  addButtonText = "添加标签",
  className,
  contentClassName,
}: TabsProps) {
  // 确定使用的默认值
  const defaultTab = defaultValue || (tabs.length > 0 ? tabs[0].id : "")
  
  // 根据尺寸定义样式
  const sizeStyles = {
    sm: "text-xs p-1",
    md: "",
    lg: "text-lg p-3"
  }
  
  // 根据变体定义样式
  const variantStyles = {
    default: "",
    outline: "border rounded-md",
    underline: "border-b",
    pills: "bg-muted p-1 rounded-md"
  }
  
  // 根据对齐方式定义样式
  const alignmentStyles = {
    start: "justify-start",
    center: "justify-center",
    end: "justify-end",
    justify: "grid"
  }

  return (
    <ShadcnTabs
      defaultValue={value || defaultTab}
      value={value}
      onValueChange={onValueChange}
      className={className}
      orientation={orientation}
    >
      <TabsList 
        className={cn(
          orientation === "vertical" ? "flex-col h-auto" : "w-full",
          alignment === "justify" ? `grid-cols-${tabs.length + (addable ? 1 : 0)}` : "",
          alignmentStyles[alignment],
          variantStyles[variant],
          sizeStyles[size]
        )}
      >
        {tabs.map((tab) => (
          <TabsTrigger
            key={tab.id}
            value={tab.id}
            disabled={tab.disabled}
            className={cn("flex items-center gap-2", tab.className)}
          >
            {typeof tab.icon === 'function' 
              ? React.createElement(tab.icon as React.FC)
              : tab.icon}
            {tab.title}
            {tab.badge && (
              <Badge variant={tab.badgeVariant || "secondary"} className="ml-1">
                {tab.badge}
              </Badge>
            )}
            {closable && onClose && (
              <X 
                className="h-3 w-3 ml-1 opacity-60 hover:opacity-100 cursor-pointer" 
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                  onClose(tab.id);
                }}
                aria-label="关闭标签页"
              />
            )}
          </TabsTrigger>
        ))}
        
        {addable && onAdd && (
          <Button 
            variant="ghost" 
            size="sm" 
            className="flex items-center gap-1"
            onClick={onAdd}
          >
            <Plus className="h-4 w-4" />
            {addButtonText}
          </Button>
        )}
      </TabsList>

      {tabs.map((tab) => (
        <TabsContent 
          key={tab.id} 
          value={tab.id} 
          className={cn("mt-4", contentClassName)}
        >
          {tab.content}
          {tab.description && (
            <p className="text-sm text-muted-foreground mt-1">{tab.description}</p>
          )}
        </TabsContent>
      ))}
    </ShadcnTabs>
  )
}

/**
 * 卡片标签页组件
 */
export function CardTabs({
  title,
  description,
  actions,
  showDivider = true,
  cardPadding = true,
  cardBorder = true,
  cardRounded = true,
  contentClassName,
  ...props
}: CardTabsProps) {
  return (
    <Card className={cn(
      !cardBorder && "border-0",
      !cardRounded && "rounded-none"
    )}>
      {(title || description || actions) && (
        <CardHeader className={cardPadding ? "" : "p-0"}>
          <div className="flex justify-between items-start">
            <div>
              {title && <CardTitle>{title}</CardTitle>}
              {description && <CardDescription>{description}</CardDescription>}
            </div>
            {actions && <div>{actions}</div>}
          </div>
        </CardHeader>
      )}
      
      <CardContent className={cn(
        cardPadding ? "" : "p-0",
        !title && !description && !actions && "pt-6"
      )}>
        <Tabs 
          contentClassName={cn(
            contentClassName,
            showDivider && "border-t pt-4 mt-4"
          )}
          {...props} 
        />
      </CardContent>
    </Card>
  )
}

/**
 * 步骤标签页组件
 */
export function StepTabs({
  tabs,
  defaultValue,
  value,
  onValueChange,
  backable = true,
  backButtonText = "上一步",
  nextButtonText = "下一步",
  completeButtonText = "完成",
  onBack,
  onNext,
  onComplete,
  onStepChange,
  ...props
}: StepTabsProps) {
  const [activeTab, setActiveTab] = React.useState(value || defaultValue || (tabs.length > 0 ? tabs[0].id : ""))
  
  // 获取当前步骤索引
  const currentIndex = tabs.findIndex(tab => tab.id === activeTab)
  
  // 处理下一步
  const handleNext = async () => {
    if (currentIndex < tabs.length - 1) {
      // 如果有验证函数，先验证
      if (onNext) {
        const canProceed = await onNext(currentIndex)
        if (!canProceed) return
      }
      
      // 如果有步骤变更验证函数，先验证
      if (onStepChange) {
        const canChange = await onStepChange(currentIndex, currentIndex + 1)
        if (!canChange) return
      }
      
      // 设置下一步
      const nextTab = tabs[currentIndex + 1].id
      setActiveTab(nextTab)
      if (onValueChange) onValueChange(nextTab)
    } else if (onComplete) {
      // 最后一步，调用完成回调
      onComplete()
    }
  }
  
  // 处理上一步
  const handleBack = () => {
    if (currentIndex > 0) {
      // 如果有步骤变更验证函数，先验证
      if (onStepChange) {
        const canChange = onStepChange(currentIndex, currentIndex - 1)
        if (!canChange) return
      }
      
      // 设置上一步
      const prevTab = tabs[currentIndex - 1].id
      setActiveTab(prevTab)
      if (onValueChange) onValueChange(prevTab)
      
      // 如果有自定义返回函数，调用
      if (onBack) onBack()
    }
  }
  
  return (
    <div className="space-y-4">
      <Tabs 
        tabs={tabs}
        value={activeTab}
        onValueChange={(newValue) => {
          setActiveTab(newValue)
          if (onValueChange) onValueChange(newValue)
        }}
        {...props}
      />
      
      <div className="flex justify-between pt-4 border-t">
        {backable && currentIndex > 0 ? (
          <Button variant="outline" onClick={handleBack}>
            {backButtonText}
          </Button>
        ) : <div />}
        
        <Button onClick={handleNext}>
          {currentIndex < tabs.length - 1 ? nextButtonText : completeButtonText}
        </Button>
      </div>
    </div>
  )
}

// ============================================================================
// 类型导出 - 类型已在定义时导出
// ============================================================================

// ============================================================================
// 常量导出
// ============================================================================

/**
 * 支持的标签页尺寸
 */
export const TABS_SIZES = ['sm', 'md', 'lg'] as const

/**
 * 支持的标签页变体
 */
export const TABS_VARIANTS = ['default', 'outline', 'underline', 'pills'] as const

/**
 * 支持的排列方式
 */
export const TABS_ALIGNMENTS = ['start', 'center', 'end', 'justify'] as const

/**
 * 支持的方向
 */
export const TABS_ORIENTATIONS = ['horizontal', 'vertical'] as const

/**
 * 支持的激活模式
 */
export const TABS_ACTIVATION_MODES = ['automatic', 'manual'] as const

/**
 * 支持的徽章变体
 */
export const TABS_BADGE_VARIANTS = ['default', 'secondary', 'outline', 'destructive'] as const

/**
 * 默认标签页配置
 */
export const DEFAULT_TABS_CONFIG = {
  orientation: 'horizontal' as const,
  alignment: 'justify' as const,
  size: 'md' as const,
  variant: 'default' as const,
  activationMode: 'automatic' as const,
  closable: false,
  addable: false,
  addButtonText: '添加标签',
} satisfies Partial<TabsProps>

/**
 * 默认卡片标签页配置
 */
export const DEFAULT_CARD_TABS_CONFIG = {
  showDivider: true,
  cardPadding: true,
  cardBorder: true,
  cardRounded: true,
} satisfies Partial<CardTabsProps>

/**
 * 默认步骤标签页配置
 */
export const DEFAULT_STEP_TABS_CONFIG = {
  backable: true,
  backButtonText: '上一步',
  nextButtonText: '下一步',
  completeButtonText: '完成',
} satisfies Partial<StepTabsProps>
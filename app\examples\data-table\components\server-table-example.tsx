"use client"

import * as React from "react"
import { 
  ColumnDef, 
  ColumnFiltersState,
  SortingState,
  flexRender, 
  getCoreRowModel, 
  useReactTable,
  PaginationState,
} from "@tanstack/react-table"
import { ArrowUpDown, Loader2 } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { User } from "../data"

// 模拟API请求
async function fetchData(
  page: number,
  pageSize: number,
  sorting: SortingState,
  filters: ColumnFiltersState
): Promise<{ data: User[], totalCount: number }> {
  // 模拟服务器延迟
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  // 从 localStorage 获取数据或使用默认数据
  const mockData: User[] = [
    {
      id: "u-001",
      name: "张三",
      email: "zhang<PERSON>@example.com",
      role: "管理员",
      status: "活跃",
      lastLogin: "2023-12-01 10:30:00",
    },
    {
      id: "u-002",
      name: "李四",
      email: "<EMAIL>",
      role: "用户",
      status: "活跃",
      lastLogin: "2023-12-03 14:20:00",
    },
    {
      id: "u-003",
      name: "王五",
      email: "<EMAIL>",
      role: "编辑",
      status: "未活跃",
      lastLogin: "2023-11-28 09:45:00",
    },
    {
      id: "u-004",
      name: "赵六",
      email: "<EMAIL>",
      role: "用户",
      status: "待处理",
      lastLogin: "2023-12-05 16:10:00",
    },
    {
      id: "u-005",
      name: "钱七",
      email: "<EMAIL>",
      role: "编辑",
      status: "活跃",
      lastLogin: "2023-12-04 11:25:00",
    },
    {
      id: "u-006",
      name: "孙八",
      email: "<EMAIL>",
      role: "用户",
      status: "活跃",
      lastLogin: "2023-12-02 08:15:00",
    },
    {
      id: "u-007",
      name: "周九",
      email: "<EMAIL>",
      role: "管理员",
      status: "未活跃",
      lastLogin: "2023-11-30 13:40:00",
    },
    {
      id: "u-008",
      name: "吴十",
      email: "<EMAIL>",
      role: "用户",
      status: "活跃",
      lastLogin: "2023-12-01 15:50:00",
    },
    {
      id: "u-009",
      name: "郑十一",
      email: "<EMAIL>",
      role: "编辑",
      status: "待处理",
      lastLogin: "2023-12-03 09:20:00",
    },
    {
      id: "u-010",
      name: "王十二",
      email: "<EMAIL>",
      role: "用户",
      status: "活跃",
      lastLogin: "2023-12-04 14:30:00",
    },
  ]
  
  // 应用筛选
  let filteredData = [...mockData]
  
  filters.forEach(filter => {
    const { id, value } = filter
    if (value) {
      filteredData = filteredData.filter(item => 
        String(item[id as keyof User]).toLowerCase().includes(String(value).toLowerCase())
      )
    }
  })
  
  // 应用排序
  if (sorting.length > 0) {
    const { id, desc } = sorting[0]
    filteredData.sort((a, b) => {
      const aValue = a[id as keyof User]
      const bValue = b[id as keyof User]
      
      if (aValue < bValue) return desc ? 1 : -1
      if (aValue > bValue) return desc ? -1 : 1
      return 0
    })
  }
  
  // 计算总数
  const totalCount = filteredData.length
  
  // 分页
  const start = page * pageSize
  const paginatedData = filteredData.slice(start, start + pageSize)
  
  return {
    data: paginatedData,
    totalCount
  }
}

export function ServerDataTableExample() {
  const [data, setData] = React.useState<User[]>([])
  const [totalCount, setTotalCount] = React.useState(0)
  const [isLoading, setIsLoading] = React.useState(false)
  
  // 表格状态
  const [sorting, setSorting] = React.useState<SortingState>([])
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [pagination, setPagination] = React.useState<PaginationState>({
    pageIndex: 0,
    pageSize: 5,
  })
  
  const columns: ColumnDef<User>[] = [
    {
      accessorKey: "name",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            姓名
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        )
      },
    },
    {
      accessorKey: "email",
      header: "邮箱",
    },
    {
      accessorKey: "role",
      header: "角色",
    },
    {
      accessorKey: "status",
      header: "状态",
      cell: ({ row }) => {
        const status = row.getValue("status") as string
        return (
          <div className="flex items-center">
            <span
              className={`mr-2 h-2 w-2 rounded-full ${
                status === "活跃" ? "bg-green-500" : 
                status === "未活跃" ? "bg-gray-400" : "bg-yellow-500"
              }`}
            />
            {status}
          </div>
        )
      },
    },
    {
      accessorKey: "lastLogin",
      header: "最后登录",
    },
  ]
  
  // 加载数据
  const fetchTableData = React.useCallback(async () => {
    setIsLoading(true)
    try {
      const result = await fetchData(
        pagination.pageIndex,
        pagination.pageSize,
        sorting,
        columnFilters
      )
      setData(result.data)
      setTotalCount(result.totalCount)
    } catch (error) {
      console.error("Error fetching data:", error)
    } finally {
      setIsLoading(false)
    }
  }, [pagination.pageIndex, pagination.pageSize, sorting, columnFilters])
  
  // 当依赖项变化时重新获取数据
  React.useEffect(() => {
    fetchTableData()
  }, [fetchTableData])
  
  const table = useReactTable({
    data,
    columns,
    pageCount: Math.ceil(totalCount / pagination.pageSize),
    state: {
      sorting,
      columnFilters,
      pagination,
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
    manualSorting: true,
    manualFiltering: true,
  })
  
  return (
    <div className="space-y-4">
      <div className="flex items-center py-4">
        <Input
          placeholder="按姓名筛选..."
          value={(table.getColumn("name")?.getFilterValue() as string) ?? ""}
          onChange={(event) =>
            table.getColumn("name")?.setFilterValue(event.target.value)
          }
          className="max-w-sm"
        />
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  <div className="flex justify-center items-center">
                    <Loader2 className="h-6 w-6 animate-spin mr-2" />
                    加载中...
                  </div>
                </TableCell>
              </TableRow>
            ) : data.length > 0 ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  无数据
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-between space-x-2 py-4">
        <div className="text-sm text-muted-foreground">
          共 {totalCount} 条记录，当前显示第 {pagination.pageIndex + 1} 页
        </div>
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage() || isLoading}
          >
            上一页
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage() || isLoading}
          >
            下一页
          </Button>
        </div>
      </div>
    </div>
  )
} 
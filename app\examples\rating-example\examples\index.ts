/**
 * 评分组件示例代码集合
 * 
 * 按照新规范管理示例代码，避免在页面文件中硬编码
 */

import React from "react"
import { Rating, RatingDisplay, RatingStatistics } from "@/components/common-custom/rating"

// ============================================================================
// 基础评分示例
// ============================================================================

export const basicExample = {
  id: "basic-rating",
  title: "基础评分",
  description: "可交互的星级评分组件，支持不同尺寸和图标类型",
  code: `
import React, { useState } from "react";
import { Rating } from "@/components/common-custom/rating";

function BasicRatingExample() {
  const [value, setValue] = useState(3);
  
  return (
    <div className="space-y-6">
      <div>
        <h4 className="text-sm font-medium mb-2">基本用法</h4>
        <Rating
          value={value}
          onChange={setValue}
          max={5}
        />
        <p className="mt-2 text-sm text-muted-foreground">
          当前评分：{value}
        </p>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">只读模式</h4>
        <Rating
          value={4}
          readonly
          max={5}
        />
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">心形图标</h4>
        <Rating
          value={value}
          onChange={setValue}
          max={5}
          icon="heart"
          highlightColor="text-red-500"
        />
      </div>
    </div>
  );
}

render(<BasicRatingExample />);
  `,
  scope: { Rating, React, useState: React.useState },
}

// ============================================================================
// 尺寸示例
// ============================================================================

export const sizeExample = {
  id: "size-rating",
  title: "不同尺寸",
  description: "展示不同尺寸的评分组件",
  code: `
import React, { useState } from "react";
import { Rating } from "@/components/common-custom/rating";

function SizeRatingExample() {
  const [value, setValue] = useState(4);
  
  return (
    <div className="space-y-4">
      <div>
        <h4 className="text-sm font-medium mb-2">小尺寸 (sm)</h4>
        <Rating
          value={value}
          onChange={setValue}
          size="sm"
          max={5}
        />
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">中尺寸 (md)</h4>
        <Rating
          value={value}
          onChange={setValue}
          size="md"
          max={5}
        />
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">大尺寸 (lg)</h4>
        <Rating
          value={value}
          onChange={setValue}
          size="lg"
          max={5}
        />
      </div>
    </div>
  );
}

render(<SizeRatingExample />);
  `,
  scope: { Rating, React, useState: React.useState },
}

// ============================================================================
// 半星评分示例
// ============================================================================

export const halfStarExample = {
  id: "half-star-rating",
  title: "半星评分",
  description: "支持更精细的半星评分功能",
  code: `
import React, { useState } from "react";
import { Rating } from "@/components/common-custom/rating";

function HalfStarRatingExample() {
  const [value, setValue] = useState(2.5);
  
  return (
    <div className="space-y-4">
      <div>
        <h4 className="text-sm font-medium mb-2">半星模式</h4>
        <Rating
          value={value}
          onChange={setValue}
          max={5}
          allowHalf
        />
        <p className="mt-2 text-sm text-muted-foreground">
          当前评分：{value}
        </p>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">预设半星值</h4>
        <div className="space-y-2">
          <Rating value={1.5} readonly allowHalf />
          <Rating value={2.5} readonly allowHalf />
          <Rating value={3.5} readonly allowHalf />
          <Rating value={4.5} readonly allowHalf />
        </div>
      </div>
    </div>
  );
}

render(<HalfStarRatingExample />);
  `,
  scope: { Rating, React, useState: React.useState },
}

// ============================================================================
// 评分展示示例
// ============================================================================

export const displayExample = {
  id: "rating-display",
  title: "评分展示",
  description: "用于展示评分结果的只读组件",
  code: `
import React from "react";
import { RatingDisplay } from "@/components/common-custom/rating";

function RatingDisplayExample() {
  return (
    <div className="space-y-6">
      <div>
        <h4 className="text-sm font-medium mb-2">基本展示</h4>
        <RatingDisplay
          value={4.5}
          max={5}
        />
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">显示评分值</h4>
        <div className="space-y-3">
          <div className="flex items-center gap-4">
            <RatingDisplay
              value={4.5}
              max={5}
              showValue
              valueFormat="decimal"
            />
            <span className="text-sm text-muted-foreground">小数形式</span>
          </div>
          
          <div className="flex items-center gap-4">
            <RatingDisplay
              value={4.5}
              max={5}
              showValue
              valueFormat="fraction"
            />
            <span className="text-sm text-muted-foreground">分数形式</span>
          </div>
          
          <div className="flex items-center gap-4">
            <RatingDisplay
              value={4.5}
              max={5}
              showValue
              valueFormat="percent"
            />
            <span className="text-sm text-muted-foreground">百分比形式</span>
          </div>
        </div>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">带评分数量</h4>
        <RatingDisplay
          value={4.5}
          max={5}
          showValue
          count={126}
        />
      </div>
    </div>
  );
}

render(<RatingDisplayExample />);
  `,
  scope: { RatingDisplay, React },
}

// ============================================================================
// 评分统计示例
// ============================================================================

export const statisticsExample = {
  id: "rating-statistics",
  title: "评分统计",
  description: "显示评分分布情况的统计图表",
  code: `
import React from "react";
import { RatingStatistics } from "@/components/common-custom/rating";

function RatingStatisticsExample() {
  return (
    <div className="space-y-6">
      <div>
        <h4 className="text-sm font-medium mb-2">评分分布统计</h4>
        <RatingStatistics
          distribution={[85, 32, 15, 8, 5]}
          average={4.2}
          count={145}
        />
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">自定义颜色</h4>
        <RatingStatistics
          distribution={[45, 28, 12, 6, 3]}
          average={4.1}
          count={94}
          highlightColor="bg-blue-500"
          backgroundColor="bg-gray-100"
        />
      </div>
    </div>
  );
}

render(<RatingStatisticsExample />);
  `,
  scope: { RatingStatistics, React },
}

// ============================================================================
// 综合示例
// ============================================================================

export const comprehensiveExample = {
  id: "comprehensive-rating",
  title: "综合示例",
  description: "展示评分组件的综合使用场景",
  code: `
import React, { useState } from "react";
import { Rating, RatingDisplay, RatingStatistics } from "@/components/common-custom/rating";

function ComprehensiveRatingExample() {
  const [userRating, setUserRating] = useState(0);
  
  return (
    <div className="space-y-8">
      <div className="border rounded-lg p-4">
        <h4 className="text-sm font-medium mb-3">产品评价</h4>
        
        <div className="space-y-4">
          <div>
            <p className="text-sm text-muted-foreground mb-2">您对此产品的评价：</p>
            <Rating
              value={userRating}
              onChange={setUserRating}
              max={5}
              size="lg"
            />
            {userRating > 0 && (
              <p className="mt-2 text-sm text-green-600">
                感谢您的 {userRating} 星评价！
              </p>
            )}
          </div>
          
          <div className="border-t pt-4">
            <p className="text-sm font-medium mb-2">当前产品评分：</p>
            <RatingDisplay
              value={4.3}
              max={5}
              showValue
              count={89}
              size="md"
            />
          </div>
        </div>
      </div>
      
      <div className="border rounded-lg p-4">
        <h4 className="text-sm font-medium mb-3">评分分布</h4>
        <RatingStatistics
          distribution={[45, 28, 12, 3, 1]}
          average={4.3}
          count={89}
        />
      </div>
    </div>
  );
}

render(<ComprehensiveRatingExample />);
  `,
  scope: { Rating, RatingDisplay, RatingStatistics, React, useState: React.useState },
}

// ============================================================================
// 统一导出所有示例
// ============================================================================

export const allExamples = [
  basicExample,
  sizeExample,
  halfStarExample,
  displayExample,
  statisticsExample,
  comprehensiveExample,
]

// ============================================================================
// 按类别导出示例
// ============================================================================

export const basicExamples = [basicExample, sizeExample]
export const advancedExamples = [halfStarExample, displayExample, statisticsExample]
export const comprehensiveExamples = [comprehensiveExample]

// ============================================================================
// 示例元数据
// ============================================================================

export const exampleMetadata = {
  total: allExamples.length,
  categories: {
    basic: basicExamples.length,
    advanced: advancedExamples.length,
    comprehensive: comprehensiveExamples.length,
  },
  tags: ["rating", "star", "feedback", "interactive", "display"],
  lastUpdated: "2024-01-01",
}

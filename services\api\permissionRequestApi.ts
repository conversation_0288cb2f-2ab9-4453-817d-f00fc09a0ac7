/**
 * 权限API请求
 * 提供权限相关的API调用接口
 */
import { type PermissionNode, PermissionType, PermissionGroup } from "@/types/permission";
import { PERMISSION_API_BASE_URL } from "./configApi";
import apiClient from "./requestApi";
import { ApiResponse } from "@/types/api";
import { nanoid } from 'nanoid';
import { processArrayResponse, processResponse } from "@/lib/request";
import { toast } from "sonner";

/**
 * 获取权限树形结构
 * @param groupCode 可选的群组代码
 * @returns 权限节点树结构
 */
export async function fetchPermissionTree(groupCode?: string): Promise<PermissionNode[]> {
  const response = await apiClient.get<ApiResponse<PermissionNode[]>>(`${PERMISSION_API_BASE_URL}/tree`, {
    params: groupCode ? { groupCode } : undefined
  });
  return processArrayResponse(response, {
    errorMessage: "获取权限树失败",
    showErrorToast: true
  });
}

/**
 * 获取指定权限组的权限树
 * @param groupCode 权限组代码
 * @returns 权限树数据
 */
export const fetchPermissionTreeByGroup = async (groupCode: string): Promise<PermissionNode[]> => {
  try {
    const response = await apiClient.get<ApiResponse<PermissionNode[]>>(
      `${PERMISSION_API_BASE_URL}/tree`,
      { params: { groupCode } }
    );

    return processArrayResponse(response, {
      errorMessage: `获取权限组[${groupCode}]的权限树失败`,
      showErrorToast: true
    });
  } catch (error) {
    console.error(`获取权限组[${groupCode}]的权限树失败`, error);
    toast.error("获取权限树失败");
    return [];
  }
};

/**
 * 为新权限节点预处理数据
 * @param node 权限节点
 * @param groupCode 可选的群组代码
 * @returns 处理后的权限节点
 */
function preprocessPermissionNode(node: PermissionNode, groupCode?: string): PermissionNode {
  // 如果节点没有ID，生成一个临时ID（前端生成的临时ID以'temp-'开头）
  if (!node.id || node.id.startsWith('temp-')) {
    // 使用类型前缀加6位nanoid生成ID
    const typePrefix = node.type.toLowerCase();
    node.id = `${typePrefix}_${nanoid(6)}`;
  }
  
  // 如果有groupCode参数且节点没有groupCode，则添加
  if (groupCode && !node.groupCode) {
    node.groupCode = groupCode;
  }
  
  // 递归处理子节点
  if (node.children && node.children.length > 0) {
    node.children = node.children.map(child => {
      // 如果子节点没有parentId，设置为当前节点的id
      if (!child.parentId) {
        child.parentId = node.id;
      }
      return preprocessPermissionNode(child, groupCode);
    });
  }
  
  return node;
}

/**
 * 将权限树拉平为列表（不包含children）
 * @param permissions 权限节点数组
 * @returns 拉平后的权限节点数组
 */
function flattenPermissionTree(permissions: PermissionNode[]): Omit<PermissionNode, 'children'>[] {
  const result: Omit<PermissionNode, 'children'>[] = [];
  
  const flatten = (nodes: PermissionNode[], parentId?: string) => {
    for (const node of nodes) {
      // 创建一个不包含children的节点副本
      const { children, ...nodeWithoutChildren } = node;
      
      // 确保有parentId
      if (parentId) {
        nodeWithoutChildren.parentId = parentId;
      }
      
      // 添加到结果数组
      result.push(nodeWithoutChildren);
      
      // 递归处理子节点
      if (children && children.length > 0) {
        flatten(children, node.id);
      }
    }
  };
  
  flatten(permissions);
  return result;
}

/**
 * 全量替换权限数据
 * @param permissions 权限节点数组
 * @param groupCode 可选的群组代码
 * @returns 是否成功
 */
export async function createPermissions(permissions: PermissionNode[], groupCode?: string): Promise<ApiResponse<boolean>> {
  // 预处理权限节点
  const processedPermissions = permissions.map(node => preprocessPermissionNode(node, groupCode));
  
  // 将权限树拉平，去除children
  const flattenedPermissions = flattenPermissionTree(processedPermissions);
  
  return apiClient.post<ApiResponse<boolean>>(`${PERMISSION_API_BASE_URL}/create`, flattenedPermissions);
}

/**
 * 创建新的权限节点
 * @param type 权限类型
 * @param parentId 可选的父节点ID
 * @param groupCode 可选的群组代码
 * @returns 新的权限节点
 */
export function createNewPermissionNode(type: PermissionType, parentId?: string, groupCode?: string): PermissionNode {
  // 使用类型前缀加6位nanoid生成ID
  const typePrefix = type.toLowerCase();
  const id = `${typePrefix}_${nanoid(6)}`;
  
  // 根据类型提供默认值
  const node: PermissionNode = {
    id,
    name: getDefaultNameByType(type),
    type,
    children: [],
    enabled: true,
    parentId,
    order: 0,
    groupCode,
    openInNewTab: false // 默认不在新页面打开
  };
  
  // 根据类型添加额外属性
  if (type === PermissionType.Menu || type === PermissionType.Directory) {
    node.icon = 'setting';
    node.path = getDefaultPathByType(type);
    node.hideInMenu = false;
    
    // 添加组件路径
    if (type === PermissionType.Menu) {
      node.componentPath = `/pages/${getRandomPathSegment()}`;
    }
  }
  
  return node;
}

/**
 * 生成随机路径段
 * @returns 随机路径段
 */
function getRandomPathSegment(): string {
  return `page-${nanoid(4)}`;
}

/**
 * 根据类型获取默认名称
 * @param type 权限类型
 * @returns 默认名称
 */
function getDefaultNameByType(type: PermissionType): string {
  switch (type) {
    case PermissionType.Directory:
      return '新建目录';
    case PermissionType.Menu:
      return '新建菜单';
    case PermissionType.Button:
      return '新建按钮';
    default:
      return '新建权限';
  }
}

/**
 * 根据类型获取默认路径
 * @param type 权限类型
 * @returns 默认路径
 */
function getDefaultPathByType(type: PermissionType): string {
  switch (type) {
    case PermissionType.Directory:
      return '/directory-' + nanoid(4);
    case PermissionType.Menu:
      return '/menu-' + nanoid(4);
    default:
      return '';
  }
}
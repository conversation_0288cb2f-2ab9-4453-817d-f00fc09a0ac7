"use client"

import * as React from "react"
import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { CalendarIcon, X, Filter, ChevronDown } from "lucide-react"
import { format } from "date-fns"
import { Card } from "@/components/ui/card"

export interface FilterOption {
  label: string
  value: string
}

export function FilterExample() {
  // 筛选条件状态
  const [filters, setFilters] = useState({
    status: "",
    category: [],
    dateRange: { from: undefined, to: undefined },
    price: { min: "", max: "" },
    search: "",
  })

  // 状态选项
  const statusOptions: FilterOption[] = [
    { label: "全部", value: "" },
    { label: "活跃", value: "active" },
    { label: "非活跃", value: "inactive" },
    { label: "已归档", value: "archived" },
  ]

  // 分类选项
  const categoryOptions: FilterOption[] = [
    { label: "电子产品", value: "electronics" },
    { label: "服装", value: "clothing" },
    { label: "家居", value: "home" },
    { label: "食品", value: "food" },
    { label: "玩具", value: "toys" },
  ]

  // 处理筛选条件变更
  const handleFilterChange = (filterKey: string, value: any) => {
    setFilters((prev) => ({
      ...prev,
      [filterKey]: value,
    }))
    console.log(`Filter ${filterKey} changed to: `, value)
  }

  // 处理重置筛选条件
  const handleResetFilters = () => {
    setFilters({
      status: "",
      category: [],
      dateRange: { from: undefined, to: undefined },
      price: { min: "", max: "" },
      search: "",
    })
    console.log("Filters reset")
  }

  // 渲染活跃筛选器徽章
  const renderActiveBadges = () => {
    const badges = []

    if (filters.status) {
      const statusOption = statusOptions.find(opt => opt.value === filters.status)
      badges.push(
        <Badge key="status" variant="secondary" className="gap-1">
          状态: {statusOption?.label || filters.status}
          <X className="w-3 h-3 cursor-pointer" onClick={() => handleFilterChange("status", "")} />
        </Badge>
      )
    }

    if (filters.category.length > 0) {
      badges.push(
        <Badge key="category" variant="secondary" className="gap-1">
          分类: {filters.category.length}项已选
          <X className="w-3 h-3 cursor-pointer" onClick={() => handleFilterChange("category", [])} />
        </Badge>
      )
    }

    if (filters.dateRange.from || filters.dateRange.to) {
      badges.push(
        <Badge key="dateRange" variant="secondary" className="gap-1">
          日期范围
          <X className="w-3 h-3 cursor-pointer" onClick={() => handleFilterChange("dateRange", { from: undefined, to: undefined })} />
        </Badge>
      )
    }

    if (filters.price.min || filters.price.max) {
      badges.push(
        <Badge key="price" variant="secondary" className="gap-1">
          价格: {filters.price.min || '0'} - {filters.price.max || '∞'}
          <X className="w-3 h-3 cursor-pointer" onClick={() => handleFilterChange("price", { min: "", max: "" })} />
        </Badge>
      )
    }

    if (filters.search) {
      badges.push(
        <Badge key="search" variant="secondary" className="gap-1">
          搜索: {filters.search}
          <X className="w-3 h-3 cursor-pointer" onClick={() => handleFilterChange("search", "")} />
        </Badge>
      )
    }

    return badges
  }

  return (
    <div className="space-y-6">
      <Card className="p-4">
        <div className="flex flex-col gap-6">
          {/* 搜索栏 */}
          <div className="flex gap-2">
            <div className="flex-1">
              <Input 
                placeholder="搜索..." 
                value={filters.search}
                onChange={(e) => handleFilterChange("search", e.target.value)}
              />
            </div>
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="gap-1">
                  <Filter className="h-4 w-4" />
                  筛选
                  <ChevronDown className="h-4 w-4" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80 p-4" align="end">
                <div className="grid gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="status">状态</Label>
                    <Select
                      value={filters.status}
                      onValueChange={(value) => handleFilterChange("status", value)}
                    >
                      <SelectTrigger id="status">
                        <SelectValue placeholder="选择状态" />
                      </SelectTrigger>
                      <SelectContent>
                        {statusOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value === "" ? "none" : option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label>日期范围</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant={"outline"}
                          className="w-full justify-start text-left font-normal"
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {filters.dateRange.from ? (
                            filters.dateRange.to ? (
                              <>
                                {format(filters.dateRange.from, "yyyy-MM-dd")} -{" "}
                                {format(filters.dateRange.to, "yyyy-MM-dd")}
                              </>
                            ) : (
                              format(filters.dateRange.from, "yyyy-MM-dd")
                            )
                          ) : (
                            "选择日期范围"
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="range"
                          selected={filters.dateRange}
                          onSelect={(range) => handleFilterChange("dateRange", range)}
                          numberOfMonths={2}
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                  
                  <div className="space-y-2">
                    <Label>价格范围</Label>
                    <div className="flex items-center gap-2">
                      <Input
                        type="number"
                        placeholder="最小"
                        value={filters.price.min}
                        onChange={(e) => 
                          handleFilterChange("price", { ...filters.price, min: e.target.value })
                        }
                      />
                      <span>-</span>
                      <Input
                        type="number"
                        placeholder="最大"
                        value={filters.price.max}
                        onChange={(e) => 
                          handleFilterChange("price", { ...filters.price, max: e.target.value })
                        }
                      />
                    </div>
                  </div>
                  
                  <Button onClick={handleResetFilters} variant="outline" className="mt-2">
                    重置筛选
                  </Button>
                </div>
              </PopoverContent>
            </Popover>
          </div>
          
          {/* 活跃筛选器 */}
          {renderActiveBadges().length > 0 && (
            <div className="flex flex-wrap gap-2">
              {renderActiveBadges()}
              <Badge 
                variant="outline" 
                className="cursor-pointer" 
                onClick={handleResetFilters}
              >
                清除全部
              </Badge>
            </div>
          )}
        </div>
      </Card>
      
      {/* 筛选结果示例 */}
      <Card className="p-4">
        <div className="text-center py-8">
          <h3 className="text-lg font-medium mb-2">筛选条件已应用</h3>
          <pre className="text-sm bg-muted p-4 rounded-md text-left overflow-auto">
            {JSON.stringify(filters, null, 2)}
          </pre>
        </div>
      </Card>
    </div>
  )
} 
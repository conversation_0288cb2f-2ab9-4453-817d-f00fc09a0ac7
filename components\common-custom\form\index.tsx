"use client"

/**
 * 表单组件集合
 *
 * 按照新规范重新导出所有表单相关组件和类型
 */

import React from "react"
import { Form } from "./form"

// ============================================================================
// 主组件导出
// ============================================================================

export { Form } from "./form"

// ============================================================================
// 子组件导出
// ============================================================================

export { FormField } from "./form-field"
export { FormSection } from "./form-section"

// ============================================================================
// 扩展组件导出
// ============================================================================

/**
 * 表单对话框组件属性
 */
export interface FormDialogProps<T extends Record<string, any> = Record<string, any>> {
  /**
   * 是否打开对话框
   */
  open: boolean

  /**
   * 对话框打开状态变化回调
   */
  onOpenChange: (open: boolean) => void

  /**
   * 对话框最大宽度
   * @default "md"
   */
  maxWidth?: "sm" | "md" | "lg" | "xl" | "2xl"

  /**
   * 表单标题
   */
  title?: string

  /**
   * 表单描述
   */
  description?: string

  /**
   * 表单字段配置
   */
  fields: any[]

  /**
   * 表单初始值
   */
  defaultValues?: Partial<T>

  /**
   * 表单提交回调
   */
  onSubmit: (data: T) => void | Promise<void>

  /**
   * 表单取消回调
   */
  onCancel?: () => void

  /**
   * 是否加载中
   * @default false
   */
  loading?: boolean

  /**
   * 自定义类名
   */
  className?: string
}

/**
 * 表单对话框组件
 *
 * 基于 Form 组件的对话框封装
 */
export function FormDialog<T extends Record<string, any> = Record<string, any>>(props: FormDialogProps<T>) {
  return (
    <Form
      {...props}
      isDialog={true}
      dialogOpen={props.open}
      onDialogOpenChange={props.onOpenChange}
      dialogMaxWidth={props.maxWidth}
    />
  )
}

// ============================================================================
// 类型导出
// ============================================================================

// 从本地types文件导出所有类型（需要创建）
// export * from "./types"

// 从本地types文件导出所有类型
export * from "./types"

// ============================================================================
// 常量导出
// ============================================================================

/**
 * 支持的表单字段类型
 */
export const FORM_FIELD_TYPES = [
  'text', 'email', 'password', 'number', 'tel', 'url',
  'textarea', 'select', 'checkbox', 'radio', 'switch',
  'date', 'time', 'datetime', 'file', 'hidden'
] as const

/**
 * 支持的表单布局
 */
export const FORM_LAYOUTS = ['vertical', 'horizontal', 'inline'] as const

/**
 * 支持的表单尺寸
 */
export const FORM_SIZES = ['sm', 'md', 'lg'] as const

/**
 * 支持的验证规则类型
 */
export const VALIDATION_RULE_TYPES = ['required', 'min', 'max', 'pattern', 'custom'] as const

/**
 * 默认表单配置
 */
export const DEFAULT_FORM_CONFIG = {
  layout: 'vertical' as const,
  size: 'md' as const,
  showRequiredMark: true,
  validateOnChange: true,
  validateOnBlur: true,
} as const

/**
 * 默认表单字段配置
 */
export const DEFAULT_FORM_FIELD_CONFIG = {
  type: 'text' as const,
  size: 'md' as const,
  disabled: false,
  required: false,
  clearable: false,
} as const

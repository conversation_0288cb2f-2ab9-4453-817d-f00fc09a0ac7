# 组件清单

本文档提供了当前项目中所有组件的详细清单，按照组件分类进行组织，包含组件的功能描述、使用场景和维护状态。

## 📋 组件分类说明

### 分类体系
- **UI基础组件 (ui/)**：基于shadcn/ui的基础UI组件，提供基础交互能力
- **通用自定义组件 (common-custom/)**：业务组件，无项目特定依赖，可跨项目复用
- **项目特定组件 (project-custom/)**：集成项目特定依赖的组件
- **系统组件 (component-detail/)**：组件预览和文档系统

### 维护状态说明
- 🟢 **稳定**：功能完整，文档齐全，可直接使用
- 🟡 **开发中**：基本功能完成，文档或示例待完善
- 🔴 **待优化**：需要重构或功能增强

---

## 🎨 UI基础组件 (components/ui/)

基于shadcn/ui构建的基础UI组件，提供标准的用户界面元素。

| 组件名称 | 文件路径 | 功能描述 | 使用场景 | 状态 |
|---------|---------|---------|---------|------|
| Accordion | `ui/accordion.tsx` | 可折叠内容面板 | 展示分组信息，FAQ页面 | 🟢 |
| Alert | `ui/alert.tsx` | 警告提示组件 | 系统消息，错误提示 | 🟢 |
| AlertDialog | `ui/alert-dialog.tsx` | 确认对话框 | 删除确认，重要操作提示 | 🟢 |
| Avatar | `ui/avatar.tsx` | 用户头像组件 | 用户信息展示 | 🟢 |
| Badge | `ui/badge.tsx` | 标记徽章 | 状态标识，数量提示 | 🟢 |
| Breadcrumb | `ui/breadcrumb.tsx` | 面包屑导航 | 页面路径导航 | 🟢 |
| Button | `ui/button.tsx` | 按钮组件 | 用户交互，表单提交 | 🟢 |
| Calendar | `ui/calendar.tsx` | 日历组件 | 日期选择，事件展示 | 🟢 |
| Card | `ui/card.tsx` | 卡片容器 | 内容分组，信息展示 | 🟢 |
| Carousel | `ui/carousel.tsx` | 轮播图组件 | 图片展示，内容滑动 | 🟢 |
| Chart | `ui/chart.tsx` | 图表组件 | 数据可视化 | 🟢 |
| Checkbox | `ui/checkbox.tsx` | 复选框 | 多选操作，设置开关 | 🟢 |
| Collapsible | `ui/collapsible.tsx` | 可折叠容器 | 内容收起展开 | 🟢 |
| Command | `ui/command.tsx` | 命令面板 | 快捷操作，搜索功能 | 🟢 |
| ContextMenu | `ui/context-menu.tsx` | 右键菜单 | 上下文操作 | 🟢 |
| Dialog | `ui/dialog.tsx` | 对话框 | 模态窗口，表单弹窗 | 🟢 |
| Drawer | `ui/drawer.tsx` | 抽屉组件 | 侧边面板，移动端菜单 | 🟢 |
| DropdownMenu | `ui/dropdown-menu.tsx` | 下拉菜单 | 操作菜单，选项列表 | 🟢 |
| Form | `ui/form.tsx` | 表单组件 | 数据输入，表单验证 | 🟢 |
| HoverCard | `ui/hover-card.tsx` | 悬停卡片 | 信息预览，详情展示 | 🟢 |
| Input | `ui/input.tsx` | 输入框 | 文本输入，数据录入 | 🟢 |
| InputOTP | `ui/input-otp.tsx` | 验证码输入 | 身份验证，安全验证 | 🟢 |
| Label | `ui/label.tsx` | 标签组件 | 表单标签，字段说明 | 🟢 |
| Menubar | `ui/menubar.tsx` | 菜单栏 | 应用菜单，导航栏 | 🟢 |
| NavigationMenu | `ui/navigation-menu.tsx` | 导航菜单 | 网站导航，页面跳转 | 🟢 |
| Pagination | `ui/pagination.tsx` | 分页组件 | 数据分页，页面导航 | 🟢 |
| Popover | `ui/popover.tsx` | 弹出层 | 提示信息，操作面板 | 🟢 |
| Progress | `ui/progress.tsx` | 进度条 | 加载进度，任务进度 | 🟢 |
| RadioGroup | `ui/radio-group.tsx` | 单选组 | 单选操作，选项选择 | 🟢 |
| ScrollArea | `ui/scroll-area.tsx` | 滚动区域 | 内容滚动，长列表 | 🟢 |
| Select | `ui/select.tsx` | 选择器 | 下拉选择，选项选择 | 🟢 |
| Separator | `ui/separator.tsx` | 分隔线 | 内容分割，视觉分组 | 🟢 |
| Sheet | `ui/sheet.tsx` | 侧边栏 | 侧边面板，设置面板 | 🟢 |
| Sidebar | `ui/sidebar.tsx` | 侧边栏导航 | 应用导航，菜单展示 | 🟢 |
| Skeleton | `ui/skeleton.tsx` | 骨架屏 | 加载占位，内容预览 | 🟢 |
| Slider | `ui/slider.tsx` | 滑块组件 | 数值选择，范围调节 | 🟢 |
| Switch | `ui/switch.tsx` | 开关组件 | 设置开关，状态切换 | 🟢 |
| Table | `ui/table.tsx` | 表格组件 | 数据展示，列表显示 | 🟢 |
| Tabs | `ui/tabs.tsx` | 标签页 | 内容分组，页面切换 | 🟢 |
| Textarea | `ui/textarea.tsx` | 多行输入 | 长文本输入，评论输入 | 🟢 |
| Toast | `ui/toast.tsx` | 消息提示 | 操作反馈，状态通知 | 🟢 |
| Toggle | `ui/toggle.tsx` | 切换按钮 | 状态切换，选项开关 | 🟢 |
| ToggleGroup | `ui/toggle-group.tsx` | 切换组 | 多选切换，选项组 | 🟢 |
| Tooltip | `ui/tooltip.tsx` | 工具提示 | 帮助信息，操作说明 | 🟢 |

---

## 🔧 通用自定义组件 (components/common-custom/)

业务导向的自定义组件，无项目特定依赖，可在不同项目间复用。

### 📊 数据展示组件

| 组件名称 | 文件路径 | 功能描述 | 使用场景 | 状态 |
|---------|---------|---------|---------|------|
| AdvancedDataTable | `common-custom/advanced-data-table.tsx` | 高级数据表格 | 复杂数据展示，管理后台 | 🟢 |
| DataTable | `common-custom/data-table.tsx` | 基础数据表格 | 简单数据列表，信息展示 | 🟢 |
| DataTable (复杂) | `common-custom/data-table/` | 数据表格组件集 | 功能完整的表格系统 | 🟢 |
| ListView | `common-custom/list-view.tsx` | 列表视图组件 | 消息列表，通知列表，用户列表 | 🟢 |
| Timeline | `common-custom/timeline.tsx` | 时间轴组件 | 流程展示，历史记录 | 🟢 |
| Stats | `common-custom/stats.tsx` | 统计数据组件 | 数据概览，仪表板 | 🟢 |

### 🎨 卡片模板组件

| 组件名称 | 文件路径 | 功能描述 | 使用场景 | 状态 |
|---------|---------|---------|---------|------|
| BlogCard | `common-custom/card-templates/blog-card.tsx` | 博客卡片 | 文章展示，内容列表 | 🟢 |
| DashboardCard | `common-custom/card-templates/dashboard-card.tsx` | 仪表板卡片 | 数据概览，统计展示 | 🟢 |
| EventCard | `common-custom/card-templates/event-card.tsx` | 事件卡片 | 活动展示，事件列表 | 🟢 |
| InfoCard | `common-custom/card-templates/info-card.tsx` | 信息卡片 | 信息展示，内容预览 | 🟢 |
| ProductCard | `common-custom/card-templates/product-card.tsx` | 产品卡片 | 商品展示，产品列表 | 🟢 |
| StatCard | `common-custom/card-templates/stat-card.tsx` | 统计卡片 | 数据统计，指标展示 | 🟢 |
| TestimonialCard | `common-custom/card-templates/testimonial-card.tsx` | 推荐卡片 | 用户评价，推荐展示 | 🟢 |
| UserCard | `common-custom/card-templates/user-card.tsx` | 用户卡片 | 用户信息，团队展示 | 🟢 |

### 📝 表单与输入组件

| 组件名称 | 文件路径 | 功能描述 | 使用场景 | 状态 |
|---------|---------|---------|---------|------|
| Form (复杂) | `common-custom/form/` | 表单组件集 | 复杂表单，数据录入 | 🟢 |
| FileUpload | `common-custom/file-upload.tsx` | 文件上传组件 | 文件上传，附件管理 | 🟢 |
| Filter | `common-custom/filter.tsx` | 筛选器组件 | 数据筛选，条件过滤 | 🟢 |
| Search (复杂) | `common-custom/search/` | 搜索组件集 | 全局搜索，高级搜索 | 🟢 |
| Rating | `common-custom/rating.tsx` | 评分组件 | 评价打分，质量评估 | 🟢 |

### 🔄 状态与反馈组件

| 组件名称 | 文件路径 | 功能描述 | 使用场景 | 状态 |
|---------|---------|---------|---------|------|
| Loading | `common-custom/loading.tsx` | 加载状态 | 数据加载，操作等待 | 🟢 |
| PageLoading | `common-custom/page-loading.tsx` | 页面加载 | 页面切换，路由加载 | 🟢 |
| PageLoader | `common-custom/page-loader.tsx` | 页面加载器 | 全屏加载，页面初始化 | 🟢 |
| GlobalLoading | `common-custom/global-loading.tsx` | 全局加载 | 应用级加载，系统操作 | 🟢 |
| EmptyState | `common-custom/empty-state.tsx` | 空状态 | 无数据展示，空列表 | 🟢 |
| ErrorState | `common-custom/error-state.tsx` | 错误状态 | 错误页面，异常处理 | 🟢 |
| Skeleton | `common-custom/skeleton.tsx` | 骨架屏 | 内容加载，占位显示 | 🟢 |
| StatusBadge | `common-custom/status-badge.tsx` | 状态徽章 | 状态标识，进度标记 | 🟢 |

### 🧭 导航与交互组件

| 组件名称 | 文件路径 | 功能描述 | 使用场景 | 状态 |
|---------|---------|---------|---------|------|
| BackButton | `common-custom/back-button.tsx` | 返回按钮 | 页面返回，导航回退 | 🟢 |
| Breadcrumb | `common-custom/breadcrumb.tsx` | 面包屑导航 | 路径导航，层级展示 | 🟢 |
| Pagination | `common-custom/pagination.tsx` | 分页组件 | 数据分页，列表导航 | 🟢 |
| ActionButtons | `common-custom/action-buttons.tsx` | 操作按钮组 | 批量操作，快捷操作 | 🟢 |
| Menu | `common-custom/menu.tsx` | 菜单组件 | 导航菜单，操作菜单 | 🟢 |

### 🎯 功能组件

| 组件名称 | 文件路径 | 功能描述 | 使用场景 | 状态 |
|---------|---------|---------|---------|------|
| Modal (复杂) | `common-custom/modal/` | 模态框组件集 | 弹窗操作，表单对话框 | 🟢 |
| Calendar (复杂) | `common-custom/calendar/` | 日历组件集 | 事件管理，日程安排 | 🟢 |
| Dashboard (复杂) | `common-custom/dashboard/` | 仪表板组件集 | 数据看板，管理面板 | 🟢 |
| Comment | `common-custom/comment.tsx` | 评论系统 | 用户评论，反馈收集 | 🟢 |
| Notification | `common-custom/notification.tsx` | 通知组件 | 消息通知，系统提醒 | 🟢 |
| IconSelector | `common-custom/icon-selector.tsx` | 图标选择器 | 图标选择，界面配置 | 🟢 |
| Tooltip | `common-custom/tooltip.tsx` | 工具提示 | 帮助信息，操作指导 | 🟢 |
| TruncateText | `common-custom/truncate-text.tsx` | 文本截断 | 长文本处理，内容省略 | 🟢 |
| Tag | `common-custom/tag.tsx` | 标签组件 | 分类标签，关键词标记 | 🟢 |
| Tabs | `common-custom/tabs.tsx` | 标签页 | 内容分组，页面切换 | 🟢 |
| GroupComponents | `common-custom/group-components.tsx` | 组件分组 | 组件组织，功能分类 | 🟢 |
| PageHeader | `common-custom/page-header.tsx` | 页头组件 | 应用顶部导航栏，多种布局模式 | 🟢 |
| CollapsibleSearch | `common-custom/search/collapsible-search.tsx` | 可折叠搜索框 | 支持展开收起的搜索功能 | 🟢 |
| IconButtonWithBadge | `common-custom/icon-button-with-badge.tsx` | 带徽标图标按钮 | 支持右上角徽标的图标按钮 | 🟢 |

---

## 🎯 项目特定组件 (components/project-custom/)

集成项目特定依赖的组件，基于common-custom组件构建。

| 组件名称 | 文件路径 | 功能描述 | 集成依赖 | 状态 |
|---------|---------|---------|---------|------|
| PageLoading | `project-custom/page-loading/` | 页面加载指示器 | navigation-provider, sidebar | 🟢 |
| Notification | `project-custom/notification/` | 通知系统 | use-toast hook | 🟢 |
| Breadcrumb | `project-custom/breadcrumb/` | 面包屑导航 | next/navigation | 🟢 |


---

## 🛠️ 系统组件 (components/component-detail/)

组件预览和文档系统相关组件。

| 组件名称 | 文件路径 | 功能描述 | 使用场景 | 状态 |
|---------|---------|---------|---------|------|
| PreviewContainer | `component-detail/preview-container.tsx` | 预览容器 | 组件展示，文档系统 | 🟢 |
| Preview | `component-detail/preview.tsx` | 代码预览 | 代码展示，实时预览 | 🟢 |
| Detail | `component-detail/detail.tsx` | 详情组件 | 组件详情，API文档 | 🟢 |
| Layout | `component-detail/layout.tsx` | 布局组件 | 预览布局，页面结构 | 🟢 |

---

## 📈 组件统计

- **UI基础组件**：42个 (100%稳定)
- **通用自定义组件**：48个 (100%稳定)
- **项目特定组件**：3个 (100%稳定)
- **系统组件**：4个 (100%稳定)
- **总计**：97个组件

## 🔗 相关文档

- [组件开发规范](./component-development-specification.md)
- [实施指南](./implementation-guide.md)
- [MCP工具使用规范](./MCP工具使用规范.md)

---

## 🎯 页头组件实现完成

基于当前组件库的分析，页头组件已成功实现：

### 实际分类
**通用自定义组件 (common-custom/)**
- 页头组件实现为独立的通用组件，无项目特定依赖
- 通过配置参数支持不同的项目需求
- 可在不同项目间复用

### 可复用的现有组件
- `BackButton` - 返回按钮功能
- `Breadcrumb` - 路径导航
- `ActionButtons` - 操作按钮组
- `Menu` - 菜单功能
- `Search` - 搜索功能
- `Avatar` - 用户头像
- `Badge` - 状态标识
- `Button` - 基础按钮

### 已实现功能模块
1. **Logo区域**：支持图片Logo和文字Logo
2. **导航区域**：主导航菜单和面包屑导航
3. **搜索区域**：全局搜索功能
4. **操作区域**：通知、主题切换、自定义操作
5. **用户区域**：用户信息和下拉菜单

### 支持的布局模式
- **标准模式**：完整功能的页头布局
- **简洁模式**：适用于内容页面
- **仪表板模式**：突出搜索功能
- **移动端模式**：适配移动设备

### 集成状态
- ✅ **导航菜单集成**：已添加到"导航与布局"分类中
- ✅ **搜索功能优化**：导航菜单顶部已集成组件搜索功能
- ✅ **预览页面完整**：提供6种不同使用场景的示例
- ✅ **组件复用优化**：使用项目中已封装的独立组件
- ✅ **界面优化**：减少边框明显度，优化预览尺寸
- ✅ **问题修复**：修复重复key、搜索框展开方向、面包屑间距、菜单分割线等问题
- ✅ **搜索优化**：支持向左/向右展开，搜索框集成到右侧按钮组

---

## 📋 组件依赖关系图

```mermaid
graph TD
    A[UI基础组件] --> B[通用自定义组件]
    B --> C[项目特定组件]

    A1[Button] --> B1[BackButton]
    A2[Card] --> B2[StatCard]
    A3[Table] --> B3[DataTable]
    A4[Input] --> B4[Search]

    B1 --> C1[PageHeader]
    B2 --> C1
    B4 --> C1

    D[Hooks] --> C
    E[Providers] --> C
    F[Utils] --> B
```

---

## 🔄 维护指南

### 组件状态更新流程
1. **新增组件**：更新本清单，添加组件信息
2. **功能更新**：修改功能描述和使用场景
3. **状态变更**：更新维护状态标识
4. **废弃组件**：标记为废弃，提供替代方案

### 使用MCP工具维护清单
```javascript
// 1. 使用memory工具记录组件变更
create_entities_memory({
  entities: [{
    name: "component-inventory-update",
    entityType: "maintenance_record",
    observations: ["新增页头组件", "更新组件清单", "完善文档结构"]
  }]
})

// 2. 使用filesystem工具批量检查组件状态
search_files_filesystem({
  path: "components",
  pattern: "*.tsx",
  excludePatterns: ["node_modules", "dist"]
})

// 3. 使用feedback-enhanced确认重要变更
collect_feedback_feedback_enhanced({
  work_summary: "组件清单更新完成，请确认变更内容"
})
```

---

*最后更新：2025-06-28*

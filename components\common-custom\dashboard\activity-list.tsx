"use client"

import { <PERSON>actN<PERSON> } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Bell } from "lucide-react"
import { ActivityItem } from "@/types/dashboard-templates"

/**
 * 活动列表组件
 */
export function ActivityList({ 
  activities, 
  title 
}: { 
  activities: ActivityItem[], 
  title?: ReactNode 
}) {
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>{title || "最近活动"}</CardTitle>
          <Button variant="ghost" size="sm" className="cursor-pointer">
            <Bell className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities.map((activity, index) => (
            <div key={index} className="flex gap-3">
              <Avatar className="h-8 w-8">
                <AvatarFallback className="text-xs">{activity.avatarFallback || activity.user.charAt(0)}</AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <p className="text-sm">
                  <span className="font-medium">{activity.user}</span> {activity.action}{" "}
                  {activity.target && <span className="font-medium">{activity.target}</span>}
                </p>
                <p className="text-xs text-muted-foreground">{activity.time}</p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
} 
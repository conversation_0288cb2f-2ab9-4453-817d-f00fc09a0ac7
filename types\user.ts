// 用户类型定义
export interface User {
  id: number;
  account: string; // 账号，原username
  nickname: string; // 用户名，原realName
  email?: string; // 邮箱
  mobile?: string; // 手机号，原phone
  avatarUrl?: string; // 用户头像，原avatar
  status: number;
  statusLabel?: string; // 状态文本
  lastLoginTime?: string;
  createTime?: string; // 创建时间
  updateTime?: string; // 最近一次更新时间
  departmentId?: number;
}

// 分页响应类型
export interface PageResponse<T> {
  total: number;
  list: T[];
  pageNum: number;
  pageSize: number;
  pages: number;
}

// 通用响应类型
export interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}

// 用户分页响应类型
export type UserPageResponse = ApiResponse<PageResponse<User>>;

// 用户详情响应类型
export type UserDetailResponse = ApiResponse<User>;

// 操作结果响应类型
export type OperationResponse = ApiResponse<boolean | number>;

// 创建用户请求参数
export interface CreateUserParams {
  account: string; // 账号
  nickname: string; // 用户名
  password: string;
  email?: string; // 邮箱
  mobile?: string; // 手机号
  status?: number;
  avatarUrl?: string; // 用户头像
}

// 更新用户请求参数
export interface UpdateUserParams {
  id: number;
  nickname?: string;
  email?: string;
  mobile?: string;
  status?: number;
  avatarUrl?: string;
  departmentId?: number;
}

// 用户查询参数
export interface UserQueryParams {
  account?: string;
  nickname?: string;
  mobile?: string;
  email?: string;
  status?: number;
  pageNum: number;
  pageSize: number;
}

// 用户查询结果
export interface UserQueryResult {
  total: number;
  page: number;
  pageSize: number;
  data: User[];
} 
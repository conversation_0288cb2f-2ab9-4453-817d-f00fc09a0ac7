/**
 * 组件预览页面模板
 * 
 * 这是组件预览页面的标准模板，展示了如何创建完整的组件文档和示例
 */

"use client"

import React from "react"
import { ComponentPreviewContainer } from "@/components/component-detail/preview-container"
import { Button } from "@/components/ui/button"
import { Star, Heart, Settings, User } from "lucide-react"
import { allExamples } from "./examples"

// ============================================================================
// API文档组件
// ============================================================================

function ExampleComponentApiDocs() {
  return (
    <div className="space-y-6">
      {/* 主组件API */}
      <div>
        <h3 className="font-medium text-lg mb-2">ExampleComponent</h3>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">title</td>
                <td className="p-2 border">string | ReactNode</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">组件标题，支持字符串或ReactNode</td>
              </tr>
              <tr>
                <td className="p-2 border">description</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">组件描述</td>
              </tr>
              <tr>
                <td className="p-2 border">size</td>
                <td className="p-2 border">"sm" | "md" | "lg"</td>
                <td className="p-2 border">"md"</td>
                <td className="p-2 border">组件尺寸</td>
              </tr>
              <tr>
                <td className="p-2 border">variant</td>
                <td className="p-2 border">"default" | "primary" | "secondary" | "destructive"</td>
                <td className="p-2 border">"default"</td>
                <td className="p-2 border">组件变体样式</td>
              </tr>
              <tr>
                <td className="p-2 border">disabled</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">false</td>
                <td className="p-2 border">是否禁用组件</td>
              </tr>
              <tr>
                <td className="p-2 border">showIcon</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">true</td>
                <td className="p-2 border">是否显示图标</td>
              </tr>
              <tr>
                <td className="p-2 border">icon</td>
                <td className="p-2 border">LucideIcon</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">自定义图标组件</td>
              </tr>
              <tr>
                <td className="p-2 border">className</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">自定义CSS类名</td>
              </tr>
              <tr>
                <td className="p-2 border">children</td>
                <td className="p-2 border">ReactNode</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">子元素内容</td>
              </tr>
              <tr>
                <td className="p-2 border">onClick</td>
                <td className="p-2 border">(value: string, event: MouseEvent) =&gt; void</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">点击事件回调函数</td>
              </tr>
              <tr>
                <td className="p-2 border">meta</td>
                <td className="p-2 border">Record&lt;string, any&gt;</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">自定义元数据</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* 类型定义说明 */}
      <div>
        <h3 className="font-medium text-lg mb-2">类型定义</h3>
        <div className="space-y-4">
          <div>
            <h4 className="font-medium mb-2">ComponentProps</h4>
            <p className="text-sm text-muted-foreground mb-2">
              组件的完整属性接口，包含所有可配置的选项。
            </p>
            <pre className="bg-muted p-3 rounded-md text-sm overflow-x-auto">
              <code>{`interface ComponentProps {
  title: string | ReactNode
  description?: string
  size?: "sm" | "md" | "lg"
  variant?: "default" | "primary" | "secondary" | "destructive"
  disabled?: boolean
  showIcon?: boolean
  icon?: LucideIcon
  className?: string
  children?: ReactNode
  onClick?: (value: string, event: MouseEvent<HTMLButtonElement>) => void
  meta?: Record<string, any>
}`}</code>
            </pre>
          </div>
        </div>
      </div>

      {/* 使用指南 */}
      <div>
        <h3 className="font-medium text-lg mb-2">使用指南</h3>
        <div className="space-y-4 text-sm">
          <div>
            <h4 className="font-medium mb-2">基本用法</h4>
            <p className="text-muted-foreground mb-2">
              最简单的使用方式，只需要提供必要的属性：
            </p>
            <pre className="bg-muted p-3 rounded-md overflow-x-auto">
              <code>{`<ExampleComponent title="示例按钮" />`}</code>
            </pre>
          </div>
          
          <div>
            <h4 className="font-medium mb-2">自定义样式</h4>
            <p className="text-muted-foreground mb-2">
              通过size和variant属性控制组件的外观：
            </p>
            <pre className="bg-muted p-3 rounded-md overflow-x-auto">
              <code>{`<ExampleComponent 
  title="大型主要按钮"
  size="lg"
  variant="primary"
/>`}</code>
            </pre>
          </div>
          
          <div>
            <h4 className="font-medium mb-2">事件处理</h4>
            <p className="text-muted-foreground mb-2">
              通过onClick属性处理用户交互：
            </p>
            <pre className="bg-muted p-3 rounded-md overflow-x-auto">
              <code>{`<ExampleComponent 
  title="点击我"
  onClick={(value, event) => {
    console.log('点击了:', value)
  }}
/>`}</code>
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
}

// ============================================================================
// 主预览组件
// ============================================================================

export default function ExampleComponentPreview() {
  return (
    <ComponentPreviewContainer
      title="示例组件 ExampleComponent"
      description="这是一个示例组件，展示了单文件组件的标准实现方式，支持多种尺寸、变体和交互功能"
      whenToUse="当需要一个可配置的交互式按钮组件时使用；当需要统一的组件样式和行为时使用；当需要支持图标、多种尺寸和变体的按钮时使用"
      examples={allExamples}
      apiDocs={<ExampleComponentApiDocs />}
    />
  );
}

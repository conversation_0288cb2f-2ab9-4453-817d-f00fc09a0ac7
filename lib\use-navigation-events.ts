import { useEffect, useState } from "react"
import { usePathname, useSearchParams } from "next/navigation"

/**
 * 监听导航事件的自定义钩子
 * @returns 当前导航状态
 */
export function useNavigationEvents() {
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const [isNavigating, setIsNavigating] = useState(false)

  useEffect(() => {
    const handleStart = () => {
      setIsNavigating(true)
    }

    const handleComplete = () => {
      // 添加延迟使过渡效果更自然
      setTimeout(() => {
        setIsNavigating(false)
      }, 300)
    }

    // 使用 next/router 事件API
    window.addEventListener("navigationstart", handleStart)
    window.addEventListener("navigationsuccess", handleComplete)
    window.addEventListener("navigationerror", handleComplete)

    return () => {
      window.removeEventListener("navigationstart", handleStart)
      window.removeEventListener("navigationsuccess", handleComplete)
      window.removeEventListener("navigationerror", handleComplete)
    }
  }, [])

  // 路由变化时也触发加载状态
  useEffect(() => {
    setIsNavigating(true)
    const timer = setTimeout(() => {
      setIsNavigating(false)
    }, 500)

    return () => clearTimeout(timer)
  }, [pathname, searchParams])

  return { isNavigating }
} 
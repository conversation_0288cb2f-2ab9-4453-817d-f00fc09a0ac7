"use client"

import { useState } from "react"
import { Search, Plus, Wand2 } from "lucide-react"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { PasswordList } from "./password-list"
import { CreatePasswordDialog } from "./create-password-dialog"
import { PasswordGeneratorDialog } from "./password-generator-dialog"
import { HealthScoreButton } from "./health-score-dialog"
import {useToast} from "@/components/ui/use-toast";

export function PasswordDashboard() {
  const [searchQuery, setSearchQuery] = useState("")
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isGeneratorDialogOpen, setIsGeneratorDialogOpen] = useState(false)
  const [refreshKey, setRefreshKey] = useState(0)
  const { toast } = useToast()

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value)
  }

  const handleQuickCreate = () => {
    setIsCreateDialogOpen(true)
  }

  const handleOpenGenerator = () => {
    setIsGeneratorDialogOpen(true)
  }

  // 刷新密码列表
  const refreshPasswordList = () => {
    setRefreshKey(prev => prev + 1)
  }

  const handleCreateSuccess = () => {
    setIsCreateDialogOpen(false)
    toast({
      title: "密码已创建",
      description: "您的新密码已安全存储",
    })
    refreshPasswordList()
  }
  
  const handleEditSuccess = () => {
    toast({
      title: "密码已更新",
      description: "您的密码已成功更新",
    })
    refreshPasswordList()
  }
  
  const handleDeleteSuccess = () => {
    toast({
      title: "密码已删除",
      description: "您的密码已成功删除",
    })
    refreshPasswordList()
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 gap-4">
        <div className="col-span-1">
          <div className="flex items-center space-x-2 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input placeholder="搜索密码..." value={searchQuery} onChange={handleSearch} className="pl-10" />
            </div>
            <Button onClick={handleQuickCreate} variant="default">
              <Plus className="mr-2 h-4 w-4" />
              创建
            </Button>
            <Button onClick={handleOpenGenerator} variant="outline">
              <Wand2 className="mr-2 h-4 w-4" />
              生成器
            </Button>
          </div>
        </div>
      </div>

      <div className="flex justify-between items-center">
        <Tabs defaultValue="all" className="w-full">
          <div className="flex justify-between items-center mb-2">
            <TabsList>
              <TabsTrigger value="all">全部</TabsTrigger>
              <TabsTrigger value="favorites">收藏</TabsTrigger>
              <TabsTrigger value="strong">强密码</TabsTrigger>
              <TabsTrigger value="medium">中等</TabsTrigger>
              <TabsTrigger value="weak">弱密码</TabsTrigger>
            </TabsList>
            <HealthScoreButton />
          </div>
          <TabsContent value="all">
            <PasswordList 
              searchQuery={searchQuery} 
              category="all" 
              refreshKey={refreshKey}
              onDelete={handleDeleteSuccess}
              onEdit={handleEditSuccess}
            />
          </TabsContent>
          <TabsContent value="favorites">
            <PasswordList 
              searchQuery={searchQuery} 
              category="favorites" 
              refreshKey={refreshKey}
              onDelete={handleDeleteSuccess}
              onEdit={handleEditSuccess}
            />
          </TabsContent>
          <TabsContent value="strong">
            <PasswordList 
              searchQuery={searchQuery} 
              category="strong" 
              refreshKey={refreshKey}
              onDelete={handleDeleteSuccess}
              onEdit={handleEditSuccess}
            />
          </TabsContent>
          <TabsContent value="medium">
            <PasswordList 
              searchQuery={searchQuery} 
              category="medium" 
              refreshKey={refreshKey}
              onDelete={handleDeleteSuccess}
              onEdit={handleEditSuccess}
            />
          </TabsContent>
          <TabsContent value="weak">
            <PasswordList 
              searchQuery={searchQuery} 
              category="weak" 
              refreshKey={refreshKey}
              onDelete={handleDeleteSuccess}
              onEdit={handleEditSuccess}
            />
          </TabsContent>
        </Tabs>
      </div>

      <CreatePasswordDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onCreateSuccess={handleCreateSuccess}
      />

      <PasswordGeneratorDialog open={isGeneratorDialogOpen} onOpenChange={setIsGeneratorDialogOpen} />
    </div>
  )
} 

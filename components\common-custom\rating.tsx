"use client"

import { useState } from "react"
import { Star, Heart } from "lucide-react"
import { cn } from "@/lib/utils"

// ============================================================================
// 类型定义 - 单文件组件类型定义直接在组件文件内
// ============================================================================

/**
 * 评分组件属性
 */
export interface RatingProps {
  /**
   * 当前评分值
   */
  value: number

  /**
   * 评分变化回调
   * @param value - 新的评分值
   */
  onChange?: (value: number) => void

  /**
   * 最大评分值
   * @default 5
   */
  max?: number

  /**
   * 评分图标大小
   * @default "md"
   */
  size?: "sm" | "md" | "lg"

  /**
   * 是否只读
   * @default false
   */
  readonly?: boolean

  /**
   * 评分图标类型
   * @default "star"
   */
  icon?: "star" | "heart"

  /**
   * 自定义类名
   */
  className?: string

  /**
   * 高亮颜色
   * @default "text-yellow-400"
   */
  highlightColor?: string

  /**
   * 未选中颜色
   * @default "text-muted-foreground"
   */
  unselectedColor?: string

  /**
   * 允许半星评分
   * @default false
   */
  allowHalf?: boolean

  /**
   * 是否可清除评分
   * @default true
   */
  clearable?: boolean
}

/**
 * 尺寸样式映射类型
 */
type SizeClassMap = {
  [K in NonNullable<RatingProps['size']>]: string
}

/**
 * 图标类型映射
 */
type IconType = NonNullable<RatingProps['icon']>

// ============================================================================
// 主评分组件实现
// ============================================================================

/**
 * 评分组件
 *
 * 支持星级评分和心形评分，可配置大小、颜色、半星等功能
 *
 * @example
 * ```tsx
 * // 基础用法
 * <Rating value={3} onChange={(value) => console.log(value)} />
 *
 * // 只读显示
 * <Rating value={4.5} readonly allowHalf />
 *
 * // 心形评分
 * <Rating value={3} icon="heart" highlightColor="text-red-500" />
 * ```
 */
export function Rating({
  value,
  onChange,
  max = 5,
  size = "md",
  readonly = false,
  icon = "star",
  className,
  highlightColor = "text-yellow-400",
  unselectedColor = "text-muted-foreground",
  allowHalf = false,
  clearable = true,
}: RatingProps) {
  const [hoverValue, setHoverValue] = useState(0)

  // 样式映射配置
  const sizeClasses: SizeClassMap = {
    sm: "h-4 w-4",
    md: "h-5 w-5",
    lg: "h-6 w-6",
  }

  const IconComponent = icon === "heart" ? Heart : Star

  // 事件处理函数
  const handleClick = (clickedValue: number) => {
    if (readonly) return

    if (clearable && clickedValue === value) {
      onChange?.(0)
    } else {
      onChange?.(clickedValue)
    }
  }

  const handleMouseMove = (event: React.MouseEvent<HTMLButtonElement>, index: number) => {
    if (readonly || !allowHalf) return
    
    const button = event.currentTarget
    const rect = button.getBoundingClientRect()
    const halfWidth = rect.width / 2
    const isLeftHalf = event.clientX - rect.left < halfWidth
    
    setHoverValue(index + (isLeftHalf ? 0.5 : 1))
  }

  return (
    <div className={cn("flex items-center gap-1", className)}>
      {Array.from({ length: max }, (_, i) => {
        const ratingValue = i + 1
        const isFilled = ratingValue <= (hoverValue || value)
        const isHalfFilled = allowHalf && (i + 0.5) === (hoverValue || value)

        return (
          <button
            key={i}
            type="button"
            disabled={readonly}
            className={cn(
              "transition-colors",
              readonly ? "cursor-default" : "cursor-pointer hover:scale-110",
              isFilled ? highlightColor : unselectedColor,
            )}
            onClick={() => handleClick(ratingValue)}
            onMouseEnter={() => !readonly && setHoverValue(ratingValue)}
            onMouseMove={(e) => handleMouseMove(e, i)}
            onMouseLeave={() => !readonly && setHoverValue(0)}
          >
            {isHalfFilled ? (
              <div className="relative">
                <IconComponent className={cn(sizeClasses[size], "text-muted-foreground")} />
                <div className="absolute top-0 left-0 w-1/2 overflow-hidden">
                  <IconComponent className={cn(sizeClasses[size], "fill-current")} />
                </div>
              </div>
            ) : (
              <IconComponent className={cn(sizeClasses[size], isFilled && "fill-current")} />
            )}
          </button>
        )
      })}
    </div>
  )
}

/**
 * 评分展示组件
 */
export interface RatingDisplayProps {
  /**
   * 评分值
   */
  value: number
  
  /**
   * 最大评分
   */
  max?: number
  
  /**
   * 评分图标大小
   */
  size?: "sm" | "md" | "lg"
  
  /**
   * 评分图标类型
   */
  icon?: "star" | "heart"
  
  /**
   * 自定义类名
   */
  className?: string
  
  /**
   * 高亮颜色
   */
  highlightColor?: string
  
  /**
   * 未选中颜色
   */
  unselectedColor?: string
  
  /**
   * 是否显示评分值
   */
  showValue?: boolean
  
  /**
   * 评分值格式
   */
  valueFormat?: "decimal" | "fraction" | "percent"
  
  /**
   * 评分数量
   */
  count?: number
  
  /**
   * 允许半星
   */
  allowHalf?: boolean
}

export function RatingDisplay({
  value,
  max = 5,
  size = "md",
  icon = "star",
  className,
  highlightColor = "text-yellow-400",
  unselectedColor = "text-muted-foreground",
  showValue = false,
  valueFormat = "decimal",
  count,
  allowHalf = true,
}: RatingDisplayProps) {
  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-5 w-5",
    lg: "h-6 w-6",
  }

  const IconComponent = icon === "heart" ? Heart : Star

  const formattedValue = () => {
    switch (valueFormat) {
      case "fraction":
        return `${value}/${max}`
      case "percent":
        return `${Math.round((value / max) * 100)}%`
      default:
        return value.toFixed(1)
    }
  }

  return (
    <div className="flex items-center gap-2">
      <div className={cn("flex items-center gap-1", className)}>
        {Array.from({ length: max }, (_, i) => {
          const ratingValue = i + 1
          const isFilled = ratingValue <= value
          const isHalfFilled = allowHalf && (i + 0.5) === Math.round(value * 2) / 2

          return (
            <span
              key={i}
              className={cn(
                isFilled ? highlightColor : unselectedColor,
              )}
            >
              {isHalfFilled ? (
                <div className="relative">
                  <IconComponent className={cn(sizeClasses[size], "text-muted-foreground")} />
                  <div className="absolute top-0 left-0 w-1/2 overflow-hidden">
                    <IconComponent className={cn(sizeClasses[size], "fill-current")} />
                  </div>
                </div>
              ) : (
                <IconComponent className={cn(sizeClasses[size], isFilled && "fill-current")} />
              )}
            </span>
          )
        })}
      </div>
      
      {showValue && (
        <span className="text-sm text-muted-foreground">
          {formattedValue()}
          {count !== undefined && ` (${count})`}
        </span>
      )}
    </div>
  )
}

/**
 * 评分统计组件
 */
export interface RatingStatisticsProps {
  /**
   * 评分分布
   * [5星数量, 4星数量, 3星数量, 2星数量, 1星数量]
   */
  distribution: number[]
  
  /**
   * 平均评分
   */
  average: number
  
  /**
   * 评分总数
   */
  count: number
  
  /**
   * 自定义类名
   */
  className?: string
  
  /**
   * 高亮颜色
   */
  highlightColor?: string
  
  /**
   * 背景颜色
   */
  backgroundColor?: string
}

export function RatingStatistics({
  distribution,
  average,
  count,
  className,
  highlightColor = "bg-yellow-400",
  backgroundColor = "bg-muted",
}: RatingStatisticsProps) {
  // 反转分布数组，使其从5星到1星
  const reversedDistribution = [...distribution].reverse()

  return (
    <div className={cn("space-y-3", className)}>
      {[5, 4, 3, 2, 1].map((stars, index) => {
        const starCount = reversedDistribution[index] || 0
        const percentage = count > 0 ? (starCount / count) * 100 : 0

        return (
          <div key={stars} className="flex items-center gap-3">
            <div className="flex items-center gap-1 w-12">
              <span className="text-sm">{stars}</span>
              <Star className="h-3 w-3 fill-current text-yellow-400" />
            </div>
            <div className={cn("flex-1 rounded-full h-2", backgroundColor)}>
              <div
                className={cn("h-2 rounded-full transition-all", highlightColor)}
                style={{ width: `${percentage}%` }}
              />
            </div>
            <span className="text-sm text-muted-foreground w-8">{starCount}</span>
          </div>
        )
      })}
    </div>
  )
}

// ============================================================================
// 类型导出
// ============================================================================

export type { RatingProps, RatingDisplayProps, RatingStatisticsProps }

// ============================================================================
// 常量导出
// ============================================================================

/**
 * 支持的尺寸选项
 */
export const RATING_SIZES = ['sm', 'md', 'lg'] as const

/**
 * 支持的图标类型
 */
export const RATING_ICONS = ['star', 'heart'] as const

/**
 * 支持的值格式
 */
export const RATING_VALUE_FORMATS = ['decimal', 'fraction', 'percent'] as const

/**
 * 默认配置
 */
export const DEFAULT_RATING_CONFIG = {
  max: 5,
  size: 'md' as const,
  readonly: false,
  icon: 'star' as const,
  highlightColor: 'text-yellow-400',
  unselectedColor: 'text-muted-foreground',
  allowHalf: false,
  clearable: true,
} satisfies Partial<RatingProps>

/**
 * 默认评分显示配置
 */
export const DEFAULT_RATING_DISPLAY_CONFIG = {
  max: 5,
  size: 'md' as const,
  icon: 'star' as const,
  highlightColor: 'text-yellow-400',
  unselectedColor: 'text-muted-foreground',
  showValue: false,
  valueFormat: 'decimal' as const,
  allowHalf: true,
} satisfies Partial<RatingDisplayProps>
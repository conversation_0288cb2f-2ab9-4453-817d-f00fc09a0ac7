"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { MoreHorizontal, Edit, TestTube, Copy, Trash2, Search, Filter } from "lucide-react"
import type { NotificationConfig, NotificationType } from "@/types/notification"

// Mock data (updated without retry strategy)
const mockConfigs: NotificationConfig[] = [
  {
    id: "1",
    name: "用户注册验证码",
    description: "新用户注册时发送的短信验证码",
    type: "sms",
    enabled: true,
    environment: "production",
    config: {
      provider: "aliyun",
      templateId: "SMS_123456",
      recipients: [],
      smsType: "verification",
      content: "您的验证码是${code}，5分钟内有效。",
      variables: { code: "123456" },
    },
    createdAt: "2024-01-15T10:30:00Z",
    updatedAt: "2024-01-20T14:20:00Z",
    lastTestedAt: "2024-01-20T14:20:00Z",
  },
  {
    id: "2",
    name: "订单确认邮件",
    description: "用户下单后发送的确认邮件",
    type: "email",
    enabled: true,
    environment: "production",
    config: {
      smtp: {
        host: "smtp.gmail.com",
        port: 587,
        encryption: "tls",
        username: "<EMAIL>",
        password: "***",
        useOAuth: false,
      },
      subject: "订单确认 - ${orderNumber}",
      contentFormat: "html",
      content: "<h1>感谢您的订单</h1><p>订单号：${orderNumber}</p>",
      attachments: [],
      variables: { orderNumber: "ORD-001" },
    },
    createdAt: "2024-01-10T09:15:00Z",
    updatedAt: "2024-01-18T16:45:00Z",
  },
  {
    id: "3",
    name: "系统告警通知",
    description: "系统异常时发送到钉钉群的告警消息",
    type: "dingtalk",
    enabled: false,
    environment: "production",
    config: {
      webhookUrl: "https://oapi.dingtalk.com/robot/send?access_token=***",
      secret: "***",
      messageType: "text",
      content: "🚨 系统告警\n\n服务：${serviceName}\n错误：${errorMessage}\n时间：${timestamp}",
      atMobiles: ["13800138000"],
      variables: { serviceName: "API服务", errorMessage: "连接超时", timestamp: "2024-01-20 14:30:00" },
    },
    createdAt: "2024-01-12T11:20:00Z",
    updatedAt: "2024-01-19T13:10:00Z",
  },
  {
    id: "4",
    name: "密码重置邮件",
    description: "用户忘记密码时发送的重置邮件",
    type: "email",
    enabled: true,
    environment: "testing",
    config: {
      smtp: {
        host: "smtp.gmail.com",
        port: 587,
        encryption: "tls",
        username: "<EMAIL>",
        password: "***",
        useOAuth: false,
      },
      subject: "密码重置 - ${userName}",
      contentFormat: "html",
      content: "<h1>密码重置</h1><p>点击链接重置密码：${resetLink}</p>",
      attachments: [],
      variables: { userName: "张三", resetLink: "https://example.com/reset" },
    },
    createdAt: "2024-01-08T15:20:00Z",
    updatedAt: "2024-01-15T09:30:00Z",
  },
  {
    id: "5",
    name: "支付成功通知",
    description: "用户支付成功后的短信通知",
    type: "sms",
    enabled: true,
    environment: "production",
    config: {
      provider: "tencent",
      templateId: "SMS_789012",
      recipients: [],
      smsType: "notification",
      content: "您的订单${orderNumber}已支付成功，金额${amount}元。",
      variables: { orderNumber: "ORD-001", amount: "99.00" },
    },
    createdAt: "2024-01-05T11:45:00Z",
    updatedAt: "2024-01-12T16:20:00Z",
  },
  {
    id: "6",
    name: "服务器监控告警",
    description: "服务器异常时的钉钉告警",
    type: "dingtalk",
    enabled: true,
    environment: "production",
    config: {
      webhookUrl: "https://oapi.dingtalk.com/robot/send?access_token=***",
      secret: "***",
      messageType: "text",
      content: "⚠️ 服务器告警\n\n服务器：${serverName}\n状态：${status}\n时间：${timestamp}",
      atMobiles: ["13800138000", "13900139000"],
      variables: { serverName: "Web-01", status: "CPU使用率过高", timestamp: "2024-01-20 14:30:00" },
    },
    createdAt: "2024-01-03T08:15:00Z",
    updatedAt: "2024-01-10T12:40:00Z",
  },
]

interface NotificationConfigListProps {
  onEdit: (config: NotificationConfig) => void
}

export function NotificationConfigList({ onEdit }: NotificationConfigListProps) {
  const [configs, setConfigs] = useState<NotificationConfig[]>(mockConfigs)
  const [searchTerm, setSearchTerm] = useState("")
  const [typeFilter, setTypeFilter] = useState<NotificationType | "all">("all")
  const [environmentFilter, setEnvironmentFilter] = useState<string>("all")

  const getTypeIcon = (type: NotificationType) => {
    switch (type) {
      case "sms":
        return "📱"
      case "email":
        return "📧"
      case "dingtalk":
        return "💬"
    }
  }

  const getTypeName = (type: NotificationType) => {
    switch (type) {
      case "sms":
        return "短信"
      case "email":
        return "邮件"
      case "dingtalk":
        return "钉钉"
    }
  }

  const toggleEnabled = (id: string) => {
    setConfigs(configs.map((config) => (config.id === id ? { ...config, enabled: !config.enabled } : config)))
  }

  const handleTest = (config: NotificationConfig) => {
    // TODO: Implement test functionality
    console.log("Testing config:", config.name)
  }

  const handleDuplicate = (config: NotificationConfig) => {
    const newConfig = {
      ...config,
      id: Date.now().toString(),
      name: `${config.name} (副本)`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }
    setConfigs([...configs, newConfig])
  }

  const handleDelete = (id: string) => {
    setConfigs(configs.filter((config) => config.id !== id))
  }

  const filteredConfigs = configs.filter((config) => {
    const matchesSearch =
      config.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      config.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = typeFilter === "all" || config.type === typeFilter
    const matchesEnvironment = environmentFilter === "all" || config.environment === environmentFilter

    return matchesSearch && matchesType && matchesEnvironment
  })

  return (
    <div className="space-y-6">
      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="搜索配置名称或描述..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={typeFilter} onValueChange={(value) => setTypeFilter(value as NotificationType | "all")}>
          <SelectTrigger className="w-[140px]">
            <Filter className="h-4 w-4 mr-2" />
            <SelectValue placeholder="类型" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部类型</SelectItem>
            <SelectItem value="sms">短信</SelectItem>
            <SelectItem value="email">邮件</SelectItem>
            <SelectItem value="dingtalk">钉钉</SelectItem>
          </SelectContent>
        </Select>
        <Select value={environmentFilter} onValueChange={setEnvironmentFilter}>
          <SelectTrigger className="w-[140px]">
            <SelectValue placeholder="环境" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部环境</SelectItem>
            <SelectItem value="development">开发</SelectItem>
            <SelectItem value="testing">测试</SelectItem>
            <SelectItem value="production">生产</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Config Cards */}
      <div className="grid gap-4 md:grid-cols-3 lg:grid-cols-4">
        {filteredConfigs.map((config) => (
          <Card key={config.id} className="relative hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-3 flex-1 min-w-0">
                  <span className="text-xl mt-0.5">{getTypeIcon(config.type)}</span>
                  <div className="flex-1 min-w-0">
                    <CardTitle className="text-base leading-tight truncate">{config.name}</CardTitle>
                    <CardDescription className="mt-1 text-sm line-clamp-2">{config.description}</CardDescription>
                  </div>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => handleDelete(config.id)} className="text-destructive">
                      <Trash2 className="h-4 w-4 mr-2" />
                      删除
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </CardHeader>

            <CardContent className="space-y-4">
              {/* Status and Environment */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-xs">
                    {getTypeName(config.type)}
                  </Badge>
                  <Badge variant={config.environment === "production" ? "default" : "secondary"} className="text-xs">
                    {config.environment === "production" ? "生产" : config.environment === "testing" ? "测试" : "开发"}
                  </Badge>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-xs text-muted-foreground">启用</span>
                  <Switch checked={config.enabled} onCheckedChange={() => toggleEnabled(config.id)} size="sm" />
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" onClick={() => onEdit(config)} className="flex-1 h-8">
                  <Edit className="h-3 w-3 mr-1" />
                  编辑
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleTest(config)}
                  className="h-8 px-2"
                  title="测试"
                >
                  <TestTube className="h-3 w-3" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleDuplicate(config)}
                  className="h-8 px-2"
                  title="复制"
                >
                  <Copy className="h-3 w-3" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredConfigs.length === 0 && (
        <div className="text-center py-12">
          <div className="text-muted-foreground">
            {searchTerm || typeFilter !== "all" || environmentFilter !== "all"
              ? "没有找到匹配的配置"
              : "暂无通知配置，点击上方按钮创建第一个配置"}
          </div>
        </div>
      )}
    </div>
  )
}

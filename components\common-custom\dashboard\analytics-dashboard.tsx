"use client"

import { ReactNode } from "react"
import { cn } from "@/lib/utils"
import { StatCard } from "./stat-card"
import { ProductRanking } from "./product-ranking"
import { SystemStatus } from "./system-status"
import { ChartCard } from "./chart-card"
import { AnalyticsDashboardProps } from "@/types/dashboard-templates"

/**
 * 分析仪表板组件
 */
export function AnalyticsDashboard({
  title = "仪表板",
  subtitle = "欢迎回来！这是您的业务概览。",
  stats = [],
  chart,
  topProducts = [],
  systemStatus = [],
  actions,
  className,
  layoutClassName,
}: AnalyticsDashboardProps) {
  return (
    <div className={cn("space-y-6", className)}>
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
        <div>
          <h2 className="text-2xl font-bold">{title}</h2>
          <p className="text-muted-foreground">{subtitle}</p>
        </div>
        <div className="flex gap-2">{actions}</div>
      </div>

      {stats && stats.length > 0 && (
        <div className={cn("grid gap-4 md:grid-cols-2 lg:grid-cols-4", layoutClassName)}>
          {stats.map((stat, i) => (
            <StatCard key={i} {...stat} />
          ))}
        </div>
      )}

      <div className="grid gap-4 md:grid-cols-2">
        {chart && <ChartCard title="趋势分析">{chart}</ChartCard>}
        
        {topProducts && topProducts.length > 0 && (
          <ProductRanking products={topProducts} />
        )}

        {systemStatus && systemStatus.length > 0 && (
          <SystemStatus statuses={systemStatus} />
        )}
      </div>
    </div>
  )
} 
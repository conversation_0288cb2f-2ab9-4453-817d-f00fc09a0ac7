"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Search, Filter, Eye, RefreshCw, Download } from "lucide-react"
import type { NotificationHistory as NotificationHistoryType, NotificationType } from "@/types/notification"
import { Label } from "@/components/ui/label"

// Mock data
const mockHistory: NotificationHistoryType[] = [
  {
    id: "1",
    configId: "1",
    configName: "用户注册验证码",
    type: "sms",
    status: "success",
    recipient: "138****8000",
    content: "您的验证码是123456，5分钟内有效。",
    sentAt: "2024-01-20T14:30:00Z",
  },
  {
    id: "2",
    configId: "2",
    configName: "订单确认邮件",
    type: "email",
    status: "success",
    recipient: "<EMAIL>",
    content: "感谢您的订单，订单号：ORD-001",
    sentAt: "2024-01-20T14:25:00Z",
  },
  {
    id: "3",
    configId: "3",
    configName: "系统告警通知",
    type: "dingtalk",
    status: "failed",
    recipient: "钉钉群",
    content: "🚨 系统告警\n\n服务：API服务\n错误：连接超时",
    sentAt: "2024-01-20T14:20:00Z",
    error: "Webhook URL无效",
  },
  {
    id: "4",
    configId: "1",
    configName: "用户注册验证码",
    type: "sms",
    status: "pending",
    recipient: "139****9000",
    content: "您的验证码是654321，5分钟内有效。",
    sentAt: "2024-01-20T14:35:00Z",
  },
  {
    id: "5",
    configId: "2",
    configName: "订单确认邮件",
    type: "email",
    status: "success",
    recipient: "<EMAIL>",
    content: "感谢您的订单，订单号：ORD-002",
    sentAt: "2024-01-20T14:15:00Z",
  },
]

interface NotificationHistoryProps {
  isPreview?: boolean
}

export function NotificationHistory({ isPreview = false }: NotificationHistoryProps) {
  const [history, setHistory] = useState<NotificationHistoryType[]>(mockHistory)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [typeFilter, setTypeFilter] = useState<NotificationType | "all">("all")
  const [selectedRecord, setSelectedRecord] = useState<NotificationHistoryType | null>(null)

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "success":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">成功</Badge>
      case "failed":
        return <Badge variant="destructive">失败</Badge>
      case "pending":
        return <Badge variant="secondary">发送中</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getTypeIcon = (type: NotificationType) => {
    switch (type) {
      case "sms":
        return "📱"
      case "email":
        return "📧"
      case "dingtalk":
        return "💬"
    }
  }

  const getTypeName = (type: NotificationType) => {
    switch (type) {
      case "sms":
        return "短信"
      case "email":
        return "邮件"
      case "dingtalk":
        return "钉钉"
    }
  }

  const filteredHistory = history.filter((record) => {
    const matchesSearch =
      record.configName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.recipient.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || record.status === statusFilter
    const matchesType = typeFilter === "all" || record.type === typeFilter

    return matchesSearch && matchesStatus && matchesType
  })

  const displayHistory = isPreview ? filteredHistory.slice(0, 5) : filteredHistory

  const handleRefresh = () => {
    // TODO: Implement refresh logic
    console.log("Refreshing history...")
  }

  const handleExport = () => {
    // TODO: Implement export logic
    console.log("Exporting history...")
  }

  if (isPreview) {
    return (
      <div className="space-y-4">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>配置名称</TableHead>
              <TableHead>类型</TableHead>
              <TableHead>接收方</TableHead>
              <TableHead>状态</TableHead>
              <TableHead>发送时间</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {displayHistory.map((record) => (
              <TableRow key={record.id}>
                <TableCell className="font-medium">{record.configName}</TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <span>{getTypeIcon(record.type)}</span>
                    <span>{getTypeName(record.type)}</span>
                  </div>
                </TableCell>
                <TableCell>{record.recipient}</TableCell>
                <TableCell>{getStatusBadge(record.status)}</TableCell>
                <TableCell>{new Date(record.sentAt).toLocaleString()}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>

        {displayHistory.length === 0 && (
          <div className="text-center py-8">
            <div className="text-muted-foreground">暂无发送记录</div>
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">筛选条件</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="搜索配置名称或接收方..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[140px]">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue placeholder="状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部状态</SelectItem>
                <SelectItem value="success">成功</SelectItem>
                <SelectItem value="failed">失败</SelectItem>
                <SelectItem value="pending">发送中</SelectItem>
              </SelectContent>
            </Select>
            <Select value={typeFilter} onValueChange={(value) => setTypeFilter(value as NotificationType | "all")}>
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部类型</SelectItem>
                <SelectItem value="sms">短信</SelectItem>
                <SelectItem value="email">邮件</SelectItem>
                <SelectItem value="dingtalk">钉钉</SelectItem>
              </SelectContent>
            </Select>
            <div className="flex items-center gap-2">
              <Button variant="outline" onClick={handleRefresh}>
                <RefreshCw className="h-4 w-4 mr-2" />
                刷新
              </Button>
              <Button variant="outline" onClick={handleExport}>
                <Download className="h-4 w-4 mr-2" />
                导出
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* History Table */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">发送记录</CardTitle>
          <CardDescription>共 {filteredHistory.length} 条记录</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>配置名称</TableHead>
                <TableHead>类型</TableHead>
                <TableHead>接收方</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>发送时间</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {displayHistory.map((record) => (
                <TableRow key={record.id}>
                  <TableCell className="font-medium">{record.configName}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <span>{getTypeIcon(record.type)}</span>
                      <span>{getTypeName(record.type)}</span>
                    </div>
                  </TableCell>
                  <TableCell>{record.recipient}</TableCell>
                  <TableCell>{getStatusBadge(record.status)}</TableCell>
                  <TableCell>{new Date(record.sentAt).toLocaleString()}</TableCell>
                  <TableCell>
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button variant="ghost" size="sm" onClick={() => setSelectedRecord(record)}>
                          <Eye className="h-4 w-4" />
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-2xl">
                        <DialogHeader>
                          <DialogTitle>发送详情</DialogTitle>
                          <DialogDescription>查看通知的详细信息和内容</DialogDescription>
                        </DialogHeader>
                        {selectedRecord && (
                          <div className="space-y-4">
                            <div className="grid gap-4 md:grid-cols-2">
                              <div>
                                <Label className="text-sm font-medium">配置名称</Label>
                                <p className="text-sm text-muted-foreground">{selectedRecord.configName}</p>
                              </div>
                              <div>
                                <Label className="text-sm font-medium">通知类型</Label>
                                <p className="text-sm text-muted-foreground">
                                  {getTypeIcon(selectedRecord.type)} {getTypeName(selectedRecord.type)}
                                </p>
                              </div>
                              <div>
                                <Label className="text-sm font-medium">接收方</Label>
                                <p className="text-sm text-muted-foreground">{selectedRecord.recipient}</p>
                              </div>
                              <div>
                                <Label className="text-sm font-medium">发送状态</Label>
                                <div className="mt-1">{getStatusBadge(selectedRecord.status)}</div>
                              </div>
                              <div>
                                <Label className="text-sm font-medium">发送时间</Label>
                                <p className="text-sm text-muted-foreground">
                                  {new Date(selectedRecord.sentAt).toLocaleString()}
                                </p>
                              </div>
                              {selectedRecord.error && (
                                <div>
                                  <Label className="text-sm font-medium">错误信息</Label>
                                  <p className="text-sm text-red-600">{selectedRecord.error}</p>
                                </div>
                              )}
                            </div>
                            <div>
                              <Label className="text-sm font-medium">发送内容</Label>
                              <div className="mt-2 p-3 bg-muted rounded-lg">
                                <pre className="text-sm whitespace-pre-wrap">{selectedRecord.content}</pre>
                              </div>
                            </div>
                          </div>
                        )}
                      </DialogContent>
                    </Dialog>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {displayHistory.length === 0 && (
            <div className="text-center py-12">
              <div className="text-muted-foreground">
                {searchTerm || statusFilter !== "all" || typeFilter !== "all" ? "没有找到匹配的记录" : "暂无发送记录"}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

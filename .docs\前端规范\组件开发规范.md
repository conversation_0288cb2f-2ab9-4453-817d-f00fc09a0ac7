# 组件开发规范

本文档定义了React组件开发的标准和最佳实践，专门针对前端组件开发场景。

## 📁 组件分类与目录结构

### 组件分类

- **ui/**: Shadcn UI基础组件，通常不直接修改
- **common-custom/**: 通用业务组件，无项目特定依赖，可跨项目复用
- **project-custom/**: 项目特定组件，集成项目特定依赖

### 目录结构规范

```
components/
├── ui/                          # Shadcn UI基础组件
│   ├── button.tsx
│   ├── input.tsx
│   └── ...
├── common-custom/               # 通用业务组件
│   ├── single-file-component.tsx
│   └── complex-component/
│       ├── index.tsx
│       ├── types.ts
│       ├── constants.ts
│       └── component-parts.tsx
└── project-custom/              # 项目特定组件
    ├── navigation/
    ├── pages/
    └── providers/
```

## 🏗️ 组件开发标准

### 单文件组件模板

```tsx
"use client"

import { ReactNode } from "react"
import { cn } from "@/lib/utils"

// 类型定义直接在组件文件内
export interface ComponentProps {
  /**
   * 组件标题
   */
  title: string
  /**
   * 是否禁用
   * @default false
   */
  disabled?: boolean
  /**
   * 自定义类名
   */
  className?: string
  /**
   * 子元素
   */
  children?: ReactNode
}

export function Component({
  title,
  disabled = false,
  className,
  children,
}: ComponentProps) {
  return (
    <div className={cn("component-base-class", className)}>
      <h3 className="text-lg font-medium">{title}</h3>
      {children}
    </div>
  )
}
```

### 复杂组件结构

对于复杂组件，使用目录结构组织：

```tsx
// components/common-custom/data-table/index.tsx
export { DataTable } from "./data-table"
export { DataTableHeader } from "./data-table-header"
export * from "./types"

// components/common-custom/data-table/types.ts
export interface DataTableProps<T> {
  data: T[]
  columns: ColumnDef<T>[]
  onRowClick?: (row: T) => void
}

// components/common-custom/data-table/data-table.tsx
import { DataTableProps } from "./types"

export function DataTable<T>({ data, columns, onRowClick }: DataTableProps<T>) {
  // 组件实现
}
```

## 📝 TypeScript规范

### 类型定义

1. **Props接口命名**：使用`{ComponentName}Props`格式
2. **导出类型**：所有公共类型都要导出
3. **JSDoc注释**：为所有属性添加描述和默认值

```tsx
export interface ButtonProps {
  /**
   * 按钮变体
   * @default "default"
   */
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"
  /**
   * 按钮尺寸
   * @default "default"
   */
  size?: "default" | "sm" | "lg" | "icon"
  /**
   * 是否禁用
   * @default false
   */
  disabled?: boolean
  /**
   * 自定义类名
   */
  className?: string
  /**
   * 点击事件处理器
   */
  onClick?: () => void
}
```

### 泛型组件

对于需要类型参数的组件：

```tsx
export interface ListProps<T> {
  items: T[]
  renderItem: (item: T, index: number) => ReactNode
  keyExtractor: (item: T) => string | number
}

export function List<T>({ items, renderItem, keyExtractor }: ListProps<T>) {
  return (
    <div className="space-y-2">
      {items.map((item, index) => (
        <div key={keyExtractor(item)}>
          {renderItem(item, index)}
        </div>
      ))}
    </div>
  )
}
```

## 🎨 样式规范

### Tailwind CSS使用

1. **使用cn函数**：合并类名时使用`cn()`函数
2. **响应式设计**：使用Tailwind的响应式前缀
3. **语义化类名**：优先使用语义化的Tailwind类

```tsx
export function Card({ className, children, ...props }: CardProps) {
  return (
    <div
      className={cn(
        "rounded-lg border bg-card text-card-foreground shadow-sm",
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
}
```

### CSS变量使用

优先使用CSS变量而不是硬编码颜色：

```tsx
// ✅ 正确
<div className="bg-primary text-primary-foreground">

// ❌ 错误
<div className="bg-blue-500 text-white">
```

## 🔧 组件功能规范

### 状态管理

1. **最小状态原则**：只保留必要的状态
2. **状态提升**：共享状态提升到合适层级
3. **受控/非受控**：明确组件是受控还是非受控

```tsx
export function Input({ value, onChange, defaultValue, ...props }: InputProps) {
  const [internalValue, setInternalValue] = useState(defaultValue || "")
  
  const isControlled = value !== undefined
  const inputValue = isControlled ? value : internalValue
  
  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (!isControlled) {
      setInternalValue(e.target.value)
    }
    onChange?.(e)
  }
  
  return (
    <input
      value={inputValue}
      onChange={handleChange}
      {...props}
    />
  )
}
```

### 事件处理

1. **事件命名**：使用`on{Event}`格式
2. **事件参数**：提供有用的事件参数
3. **阻止默认行为**：在必要时阻止默认行为

```tsx
export interface SelectProps {
  onValueChange?: (value: string) => void
  onOpenChange?: (open: boolean) => void
}
```

### 无障碍性

1. **ARIA属性**：添加必要的ARIA属性
2. **键盘导航**：支持键盘操作
3. **焦点管理**：合理管理焦点状态

```tsx
export function Button({ children, disabled, ...props }: ButtonProps) {
  return (
    <button
      disabled={disabled}
      aria-disabled={disabled}
      className={cn(
        "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring",
        disabled && "opacity-50 cursor-not-allowed"
      )}
      {...props}
    >
      {children}
    </button>
  )
}
```

## 🎭 组件测试规范

### 单元测试

```tsx
import { render, screen, fireEvent } from '@testing-library/react'
import { Button } from './button'

describe('Button', () => {
  it('renders correctly', () => {
    render(<Button>Click me</Button>)
    expect(screen.getByRole('button')).toBeInTheDocument()
  })

  it('handles click events', () => {
    const handleClick = jest.fn()
    render(<Button onClick={handleClick}>Click me</Button>)
    
    fireEvent.click(screen.getByRole('button'))
    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  it('applies custom className', () => {
    render(<Button className="custom-class">Click me</Button>)
    expect(screen.getByRole('button')).toHaveClass('custom-class')
  })
})
```

## 📋 质量检查清单

### 组件开发完成检查
- [ ] 组件具有完整的TypeScript类型定义
- [ ] 所有属性都有JSDoc注释
- [ ] 组件支持className属性
- [ ] 组件具有合理的默认值
- [ ] 代码通过ESLint和TypeScript检查
- [ ] 组件支持ref转发（如果需要）

### 样式和交互检查
- [ ] 使用了cn()函数处理类名
- [ ] 支持暗色模式
- [ ] 响应式设计正确
- [ ] 无障碍性要求满足
- [ ] 键盘导航支持

### 文档和示例检查
- [ ] 创建了组件示例页面
- [ ] 至少包含3个不同的使用示例
- [ ] API文档完整且准确
- [ ] 包含"何时使用"的说明

## 🚀 最佳实践

1. **组件复用**：优先复用现有组件
2. **性能优化**：合理使用React.memo和useMemo
3. **错误边界**：为复杂组件添加错误边界
4. **测试覆盖**：编写单元测试和集成测试
5. **文档维护**：保持文档与代码同步更新

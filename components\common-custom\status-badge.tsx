"use client"

import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import { CheckCircle, Clock, XCircle, AlertCircle, Info, Circle, LucideIcon } from "lucide-react"
import React, { ReactNode } from "react"

// ============================================================================
// 类型定义 - 单文件组件类型定义直接在组件文件内
// ============================================================================

/**
 * 状态徽章组件属性
 */
export interface StatusBadgeProps {
  /**
   * 状态值
   */
  status: string

  /**
   * 状态文本
   */
  label?: string

  /**
   * 显示点状图标
   * @default false
   */
  showStatusDot?: boolean

  /**
   * 显示文本
   * @default true
   */
  showText?: boolean

  /**
   * 自定义状态映射
   */
  statusMap?: Record<string, {
    label?: string
    color?: string
    variant?: "default" | "secondary" | "outline" | "destructive"
    icon?: LucideIcon | ReactNode
    className?: string
  }>

  /**
   * 徽章大小
   * @default "md"
   */
  size?: "sm" | "md" | "lg"

  /**
   * 自定义图标
   */
  icon?: LucideIcon | ReactNode

  /**
   * 自定义类名
   */
  className?: string

  /**
   * 徽章变体
   * @default "default"
   */
  variant?: "default" | "secondary" | "outline" | "destructive"

  /**
   * 自定义提示
   */
  tooltip?: ReactNode
}

/**
 * 在线状态徽章属性
 */
export interface OnlineStatusBadgeProps {
  /**
   * 在线状态值
   */
  status: "online" | "offline" | "away" | "busy" | "invisible"

  /**
   * 显示文本
   * @default true
   */
  showText?: boolean

  /**
   * 徽章大小
   * @default "md"
   */
  size?: "sm" | "md" | "lg"

  /**
   * 自定义类名
   */
  className?: string

  /**
   * 脉冲动画
   * @default false
   */
  pulse?: boolean

  /**
   * 自定义状态映射
   */
  statusMap?: Record<string, {
    label?: string
    color?: string
  }>
}

/**
 * 进度状态徽章属性
 */
export interface ProgressStatusBadgeProps {
  /**
   * 进度状态值
   */
  status: "pending" | "in-progress" | "completed" | "failed" | "cancelled"

  /**
   * 进度百分比 (0-100)
   */
  progress?: number

  /**
   * 显示文本
   * @default true
   */
  showText?: boolean

  /**
   * 是否显示进度
   * @default false
   */
  showProgress?: boolean

  /**
   * 徽章大小
   * @default "md"
   */
  size?: "sm" | "md" | "lg"

  /**
   * 自定义类名
   */
  className?: string

  /**
   * 自定义状态映射
   */
  statusMap?: Record<string, {
    label?: string
    color?: string
    icon?: LucideIcon | ReactNode
  }>
}

/**
 * 优先级徽章属性
 */
export interface PriorityBadgeProps {
  /**
   * 优先级值
   */
  priority: "low" | "medium" | "high" | "critical" | number

  /**
   * 显示文本
   * @default true
   */
  showText?: boolean

  /**
   * 显示图标
   * @default true
   */
  showIcon?: boolean

  /**
   * 徽章大小
   * @default "md"
   */
  size?: "sm" | "md" | "lg"

  /**
   * 自定义类名
   */
  className?: string

  /**
   * 自定义优先级映射
   */
  priorityMap?: Record<string | number, {
    label?: string
    color?: string
    icon?: LucideIcon | ReactNode
  }>
}

// 默认状态映射
const DEFAULT_STATUS_MAP = {
  success: {
    label: "成功",
    color: "bg-green-100 text-green-800 hover:bg-green-100 dark:bg-green-900/30 dark:text-green-300 dark:border-green-700 dark:hover:bg-green-900/40",
    variant: "default" as const,
    icon: CheckCircle
  },
  warning: {
    label: "警告",
    color: "bg-yellow-100 text-yellow-800 hover:bg-yellow-100 dark:bg-yellow-900/30 dark:text-yellow-300 dark:border-yellow-700 dark:hover:bg-yellow-900/40",
    variant: "secondary" as const,
    icon: AlertCircle
  },
  error: {
    label: "错误",
    color: "bg-red-100 text-red-800 hover:bg-red-100 dark:bg-red-900/30 dark:text-red-300 dark:border-red-700 dark:hover:bg-red-900/40",
    variant: "destructive" as const,
    icon: XCircle
  },
  info: {
    label: "信息",
    color: "bg-blue-100 text-blue-800 hover:bg-blue-100 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-700 dark:hover:bg-blue-900/40",
    variant: "outline" as const,
    icon: Info
  },
  pending: {
    label: "待处理",
    color: "bg-purple-100 text-purple-800 hover:bg-purple-100 dark:bg-purple-900/30 dark:text-purple-300 dark:border-purple-700 dark:hover:bg-purple-900/40",
    variant: "secondary" as const,
    icon: Clock
  }
}

/**
 * 状态徽章组件
 */
export function StatusBadge({
  status,
  label,
  showStatusDot = false,
  showText = true,
  statusMap = DEFAULT_STATUS_MAP,
  size = "md",
  icon,
  className,
  variant,
  tooltip,
  ...props
}: StatusBadgeProps & Omit<React.ComponentProps<typeof Badge>, "variant">) {
  // 获取状态配置
  const statusConfig = statusMap[status] || {
    label: status,
    color: "",
    variant: "secondary" as const,
    icon: null
  }
  
  // 确定最终使用的文本和变体
  const finalLabel = String(label || statusConfig.label || status)
  const finalVariant = variant || statusConfig.variant
  
  // 确定尺寸样式
  const sizeStyles = {
    sm: "text-xs px-2 py-0.5",
    md: "",
    lg: "text-sm px-3 py-1"
  }

  // 构建图标元素
  let iconElement: ReactNode = null

  // 处理传入的图标
  if (icon && typeof icon === "function") {
    const IconComponent = icon as React.ComponentType<{ className?: string }>;
    iconElement = <IconComponent className="w-3 h-3 mr-1" />;
  } else if (icon && React.isValidElement(icon)) {
    iconElement = icon;
  }
  // 处理状态配置中的图标
  else if (statusConfig.icon && typeof statusConfig.icon === "function") {
    const StatusIcon = statusConfig.icon as React.ComponentType<{ className?: string }>;
    iconElement = <StatusIcon className="w-3 h-3 mr-1" />;
  } else if (statusConfig.icon && React.isValidElement(statusConfig.icon)) {
    iconElement = statusConfig.icon;
  }

  // 构建状态点
  const statusDot = showStatusDot ? <Circle className="w-2 h-2 mr-1 fill-current" /> : null;

  return (
    <Badge
      variant={finalVariant as any}
      className={cn(
        "gap-1",
        statusConfig.color,
        sizeStyles[size],
        statusConfig.className,
        className
      )}
      title={tooltip ? String(tooltip) : undefined}
      {...props}
    >
      {statusDot}
      {iconElement}
      {showText && finalLabel}
    </Badge>
  )
}

/**
 * 在线状态徽章
 */
export function OnlineStatusBadge({
  status,
  showText = true,
  size = "md",
  className,
  pulse = false,
  statusMap = {
    online: { label: "在线", color: "bg-green-500" },
    offline: { label: "离线", color: "bg-gray-400" },
    away: { label: "离开", color: "bg-yellow-500" },
    busy: { label: "忙碌", color: "bg-red-500" },
    invisible: { label: "隐身", color: "bg-gray-300" }
  }
}: OnlineStatusBadgeProps) {
  const config = statusMap[status] || { label: status, color: "bg-gray-400" }
  
  const sizeStyles = {
    sm: "h-1.5 w-1.5",
    md: "h-2 w-2",
    lg: "h-2.5 w-2.5"
  }
  
  return (
    <div className={cn("flex items-center gap-1.5", className)}>
      <span className={cn(
        "rounded-full",
        config.color,
        sizeStyles[size],
        pulse && "animate-pulse"
      )}></span>
      {showText && <span className="text-sm">{config.label}</span>}
    </div>
  )
}

/**
 * 进度状态徽章
 */
export function ProgressStatusBadge({
  status,
  progress,
  showText = true,
  showProgress = true,
  size = "md",
  className,
  statusMap = {
    pending: { label: "等待中", color: "bg-gray-100 text-gray-800 dark:bg-slate-700 dark:text-slate-200 dark:border-slate-600", icon: Clock },
    "in-progress": { label: "进行中", color: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-700", icon: Info },
    completed: { label: "已完成", color: "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 dark:border-green-700", icon: CheckCircle },
    failed: { label: "失败", color: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300 dark:border-red-700", icon: XCircle },
    cancelled: { label: "已取消", color: "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300 dark:border-purple-700", icon: XCircle }
  }
}: ProgressStatusBadgeProps) {
  const config = statusMap[status] || { label: status, color: "bg-gray-100 text-gray-800 dark:bg-slate-700 dark:text-slate-200 dark:border-slate-600" }
  
  const sizeStyles = {
    sm: "text-xs",
    md: "text-sm",
    lg: "text-base"
  }
  
  let iconElement: ReactNode = null
  if (config.icon && typeof config.icon === "function") {
    const StatusIcon = config.icon as React.ComponentType<{ className?: string }>;
    iconElement = <StatusIcon className={cn("w-3 h-3", showText && "mr-1")} />;
  } else if (config.icon && React.isValidElement(config.icon)) {
    iconElement = config.icon as ReactNode;
  }
  
  const progressText = progress !== undefined ? `${progress}%` : null
  
  return (
    <div className={cn(
      "inline-flex items-center rounded-full px-2 py-1",
      config.color,
      sizeStyles[size],
      className
    )}>
      {iconElement}
      {showText && (
        <span>
          {config.label}
          {showProgress && progress !== undefined && ` (${progress}%)`}
        </span>
      )}
      {!showText && showProgress && progress !== undefined && progressText}
    </div>
  )
}

/**
 * 优先级徽章
 */
export function PriorityBadge({
  priority,
  showText = true,
  showIcon = true,
  size = "md",
  className,
  priorityMap = {
    low: { label: "低", color: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-700", icon: Info },
    medium: { label: "中", color: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300 dark:border-yellow-700", icon: Clock },
    high: { label: "高", color: "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300 dark:border-orange-700", icon: AlertCircle },
    critical: { label: "紧急", color: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300 dark:border-red-700", icon: XCircle },
    1: { label: "低", color: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-700", icon: Info },
    2: { label: "中", color: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300 dark:border-yellow-700", icon: Clock },
    3: { label: "高", color: "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300 dark:border-orange-700", icon: AlertCircle },
    4: { label: "紧急", color: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300 dark:border-red-700", icon: XCircle }
  }
}: PriorityBadgeProps) {
  const config = priorityMap[priority] || {
    label: typeof priority === "number" ? `P${priority}` : String(priority),
    color: "bg-gray-100 text-gray-800 dark:bg-slate-700 dark:text-slate-200 dark:border-slate-600"
  }
  
  const sizeStyles = {
    sm: "text-xs px-1.5 py-0.5",
    md: "text-sm px-2 py-0.5",
    lg: "px-3 py-1"
  }
  
  let iconElement: ReactNode = null
  if (showIcon && config.icon && typeof config.icon === "function") {
    const PriorityIcon = config.icon as React.ComponentType<{ className?: string }>;
    iconElement = <PriorityIcon className={cn("w-3 h-3", showText && "mr-1")} />;
  } else if (showIcon && config.icon && React.isValidElement(config.icon)) {
    iconElement = config.icon as ReactNode;
  }
  
  return (
    <Badge className={cn(
      config.color,
      sizeStyles[size],
      "gap-1",
      className
    )}>
      {iconElement}
      {showText && config.label}
    </Badge>
  )
}

// ============================================================================
// 类型导出 - 类型已在定义时导出
// ============================================================================

// ============================================================================
// 常量导出
// ============================================================================

/**
 * 支持的徽章尺寸
 */
export const STATUS_BADGE_SIZES = ['sm', 'md', 'lg'] as const

/**
 * 支持的徽章变体
 */
export const STATUS_BADGE_VARIANTS = ['default', 'secondary', 'outline', 'destructive'] as const

/**
 * 默认状态类型
 */
export const DEFAULT_STATUS_TYPES = ['success', 'warning', 'error', 'info', 'pending'] as const

/**
 * 在线状态类型
 */
export const ONLINE_STATUS_TYPES = ['online', 'offline', 'away', 'busy', 'invisible'] as const

/**
 * 进度状态类型
 */
export const PROGRESS_STATUS_TYPES = ['pending', 'in-progress', 'completed', 'failed', 'cancelled'] as const

/**
 * 优先级类型
 */
export const PRIORITY_TYPES = ['low', 'medium', 'high', 'critical'] as const

/**
 * 默认状态徽章配置
 */
export const DEFAULT_STATUS_BADGE_CONFIG = {
  size: 'md' as const,
  variant: 'default' as const,
  showText: true,
  showStatusDot: false,
} satisfies Partial<StatusBadgeProps>

/**
 * 默认在线状态徽章配置
 */
export const DEFAULT_ONLINE_STATUS_CONFIG = {
  size: 'md' as const,
  showText: true,
  pulse: false,
} satisfies Partial<OnlineStatusBadgeProps>

/**
 * 默认进度状态徽章配置
 */
export const DEFAULT_PROGRESS_STATUS_CONFIG = {
  size: 'md' as const,
  showText: true,
  showProgress: false,
} satisfies Partial<ProgressStatusBadgeProps>

/**
 * 默认优先级徽章配置
 */
export const DEFAULT_PRIORITY_BADGE_CONFIG = {
  size: 'md' as const,
  showText: true,
  showIcon: true,
} satisfies Partial<PriorityBadgeProps>

// 导出默认状态映射供外部使用
export { DEFAULT_STATUS_MAP }
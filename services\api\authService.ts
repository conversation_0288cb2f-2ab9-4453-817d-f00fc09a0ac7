/**
 * 认证服务API
 * 提供登录、登出和获取当前用户信息的功能
 */
import { ApiResponse } from "@/types/api";
import { User } from "@/types/models";
import { request } from "@/lib/request";

// API基础路径
const AUTH_API_BASE_URL = "http://127.0.0.1:8413/template/auth";

// Token存储键名
const TOKEN_KEY = "auth_token";
const TOKEN_TYPE_KEY = "auth_token_type";

/**
 * 登录请求参数接口
 */
export interface LoginParams {
  account: string;
  password: string;
}

/**
 * 登录响应接口
 */
export interface LoginResponse {
  token: string;
  tokenType: string;
  userId: string | number;
  expires: number; // token过期时间
}

/**
 * 用户登录
 * @param params 登录参数
 * @returns 登录结果，包含token等信息
 */
export async function login(params: LoginParams): Promise<ApiResponse<LoginResponse>> {
  try {
    const response = await request.post<ApiResponse<LoginResponse>>(`${AUTH_API_BASE_URL}/login`, params);
    // 如果登录成功，保存token到localStorage
    if (response.code === "200" && response.data) {
      saveToken(response.data.token);
      saveTokenType(response.data.tokenType || 'Bearer');
    }
    
    return response;
  } catch (error) {
    console.error('登录失败:', error);
    return {
      code: '500',
      message: '网络错误，请稍后重试',
      data: null as unknown as LoginResponse,
    };
  }
}

/**
 * 获取当前登录用户信息
 * @returns 当前用户信息
 */
export async function getCurrentUser(): Promise<ApiResponse<User>> {
  try {
    const token = getToken();
    if (!token) {
      // 如果没有token，直接跳转到登录页
      if (typeof window !== 'undefined') {
        window.location.href = '/login';
      }
      return {
        code: '401',
        message: '未登录',
        data: null as unknown as User,
      };
    }
    
    return await request.get<ApiResponse<User>>(`${AUTH_API_BASE_URL}/currentUser`);
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return {
      code: '500',
      message: '获取用户信息失败',
      data: null as unknown as User,
    };
  }
}

/**
 * 用户登出
 * @returns 登出结果
 */
export async function logout(): Promise<ApiResponse<null>> {
  try {
    const token = getToken();
    if (!token) {
      if (typeof window !== 'undefined') {
        window.location.href = '/login';
      }
      return {
        code: '401',
        message: '未登录',
        data: null,
      };
    }
    
    const result = await request.post<ApiResponse<null>>(`${AUTH_API_BASE_URL}/logout`);
    
    // 无论服务器返回结果如何，都清除本地token
    removeToken();
    removeTokenType();
    
    return result;
  } catch (error) {
    console.error('登出失败:', error);
    // 即使请求失败，也清除本地token
    removeToken();
    removeTokenType();
    return {
      code: '500',
      message: '登出失败',
      data: null,
    };
  }
}

/**
 * 保存token到localStorage
 * @param token 用户token
 */
export function saveToken(token: string): void {
  localStorage.setItem(TOKEN_KEY, token);
}

/**
 * 保存tokenType到localStorage
 * @param tokenType token类型
 */
export function saveTokenType(tokenType: string): void {
  localStorage.setItem(TOKEN_TYPE_KEY, tokenType);
}

/**
 * 从localStorage获取token
 * @returns 用户token或null
 */
export function getToken(): string | null {
  return localStorage.getItem(TOKEN_KEY);
}

/**
 * 从localStorage获取tokenType
 * @returns token类型或默认值'Bearer'
 */
export function getTokenType(): string {
  return localStorage.getItem(TOKEN_TYPE_KEY) || 'Bearer';
}

/**
 * 从localStorage移除token
 */
export function removeToken(): void {
  localStorage.removeItem(TOKEN_KEY);
}

/**
 * 从localStorage移除tokenType
 */
export function removeTokenType(): void {
  localStorage.removeItem(TOKEN_TYPE_KEY);
}

/**
 * 检查用户是否已登录
 * @returns 是否已登录
 */
export function isLoggedIn(): boolean {
  return !!getToken();
} 

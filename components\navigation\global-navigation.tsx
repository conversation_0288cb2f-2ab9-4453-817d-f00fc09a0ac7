﻿"use client"

import * as React from "react"
import { usePathname } from "next/navigation"
import { useEffect, useState } from "react"

import { NavMain } from "@/components/navigation/nav-main"
import { NavProjects } from "@/components/navigation/nav-projects"
import { NavUser } from "@/components/navigation/nav-user"
import { TeamSwitcher } from "@/components/navigation/team-switcher"
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarRail, useSidebar } from "@/components/navigation/sidebar"
import { getIconComponent } from "@/lib/icon-mapping"
import { getNavigationConfig, useShowNavigation } from "@/lib/navigation-config"

export function GlobalNavigation({
  className,
  ...props
}: React.ComponentProps<typeof Sidebar> & {}) {
  // 使用Hook检查是否应该显示导航（根据路径）
  const showNavigation = useShowNavigation()
  const { setOpen } = useSidebar()
  const pathname = usePathname()
  const [navConfig, setNavConfig] = useState<any>(null)

  // 当路径变化时，在移动设备上自动收起侧边栏
  // 确保useEffect总是被调用，不管showNavigation如何
  useEffect(() => {
    // 如果不显示导航，直接返回
    if (!showNavigation) return;
    
    // 检测是否为移动设备（宽度小于768px）
    const handleResize = () => {
      if (window.innerWidth < 768) {
        setOpen(false)
      }
    }

    // 初始检查
    handleResize()

    // 添加窗口大小变化监听器
    window.addEventListener('resize', handleResize)

    // 清理函数
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [pathname, setOpen, showNavigation])

  // 获取导航配置并根据当前路径更新活动状态
  // 确保useEffect总是被调用，不管showNavigation如何
  useEffect(() => {
    // 如果不显示导航，直接返回
    if (!showNavigation) return;
    
    // 获取导航配置
    const config = getNavigationConfig()

    // 处理导航项的激活状态，以便正确展开当前路径相关的菜单
    const navMainWithIcons = config.navMain.map((item: any) => {
      // 检查当前路径是否匹配主导航项或其子项
      const isCurrentPathMatch = 
        item.url === pathname || 
        pathname.startsWith(item.url || '') ||
        item.items?.some((subItem: any) => 
          subItem.url === pathname || 
          pathname.startsWith(subItem.url || '')
        );
      
      return {
        ...item,
        icon: getIconComponent(item.icon),
        isActive: isCurrentPathMatch,
      };
    });

    // 更新导航配置
    setNavConfig({
      ...config,
      navMain: navMainWithIcons,
      teams: config.teams.map((team: any) => ({
        ...team,
        logo: team.logo, // 保持logo作为字符串，因为TeamSwitcher内部已处理图标映射
      })),
      projects: config.projects.map((project: any) => ({
        title: project.name, // 将name映射为title以匹配Project接口
        url: project.url,
        icon: getIconComponent(project.icon), // 直接使用getIconComponent的返回值
      }))
    })
  }, [pathname, showNavigation])

  // 如果不显示导航，返回null
  if (!showNavigation) {
    return null
  }

  // 如果导航配置还未加载完成，显示空白导航栏
  if (!navConfig) {
    return (
      <Sidebar
        collapsible="icon"
        className={className}
        {...props}
      >
        <SidebarHeader />
        <SidebarContent />
        <SidebarFooter />
        <SidebarRail />
      </Sidebar>
    )
  }

  // 导航栏内容组件
  return (
    <Sidebar
      collapsible="icon"
      className={className}
      {...props}
    >
      <SidebarHeader>
        <TeamSwitcher teams={navConfig.teams} />
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={navConfig.navMain} />
        <NavProjects projects={navConfig.projects} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={navConfig.user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}


﻿"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { 
  Plus, 
  Pencil, 
  Trash2, 
  Search,
  Loader2,
  UserCircle,
  Shield,
  CheckCircle,
  XCircle,
  Mail
} from "lucide-react"
import { format } from "date-fns"
import { zhCN } from "date-fns/locale"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Card,
  CardContent,
} from "@/components/ui/card"
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"
import { HeaderWithBreadcrumb } from "@/components/custom/breadcrumb"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"

// 导入用户服务
import { userService, User } from "@/lib/userService"

// 定义团队和角色类型
interface TeamInfo {
  _id: string;
  name: string;
  description?: string;
  memberCount?: number;
}

interface RoleInfo {
  _id: string;
  name: string;
  description?: string;
  permissions?: string[];
}

// 定义状态标签的映射
const STATUS_LABELS: Record<string, string> = {
  active: "活跃",
  inactive: "未活跃",
  suspended: "已停用",
  pending: "待激活"
};

export default function UserListPage() {
  const router = useRouter()
  const [users, setUsers] = useState<User[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [userToDelete, setUserToDelete] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  // 分页相关状态
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [totalUsers, setTotalUsers] = useState(0)
  
  // 团队和角色数据
  const [teamsMap, setTeamsMap] = useState<Record<string, TeamInfo>>({})
  const [rolesMap, setRolesMap] = useState<Record<string, RoleInfo>>({})
  
  // 加载用户数据
  const loadUsers = async () => {
    setLoading(true)
    setError(null)
    try {
      const result = await userService.queryUsers(currentPage, pageSize, searchTerm)
      setUsers(result.data)
      setTotalUsers(result.total)
    } catch (err) {
      setError("加载用户数据失败，请稍后重试")
      console.error("加载用户数据失败", err)
    } finally {
      setLoading(false)
    }
  }

  // 加载团队和角色数据
  const loadTeamsAndRoles = async () => {
    try {
      // 获取所有团队
      const teams = await userService.getAllTeams();
      const teamsObj = teams.reduce((acc: Record<string, TeamInfo>, team: TeamInfo) => {
        acc[team._id] = team;
        return acc;
      }, {} as Record<string, TeamInfo>);
      setTeamsMap(teamsObj);
      
      // 获取所有角色
      const roles = await userService.getAllRoles();
      const rolesObj = roles.reduce((acc: Record<string, RoleInfo>, role: RoleInfo) => {
        acc[role._id] = role;
        return acc;
      }, {} as Record<string, RoleInfo>);
      setRolesMap(rolesObj);
    } catch (err) {
      console.error("加载团队和角色数据失败", err);
    }
  };

  // 初始加载和页码、搜索条件变化时重新加载数据
  useEffect(() => {
    loadUsers()
  }, [currentPage, pageSize, searchTerm])

  // 首次加载时获取团队和角色数据
  useEffect(() => {
    loadTeamsAndRoles()
  }, [])

  // 搜索用户
  const handleSearch = (term: string) => {
    setSearchTerm(term)
    setCurrentPage(1) // 重置到第一页
  }

  // 删除用户
  const handleDeleteUser = async () => {
    if (userToDelete) {
      try {
        const success = await userService.deleteUser(userToDelete)
        if (success) {
          // 重新加载数据
          loadUsers()
        }
      } catch (err) {
        console.error("删除用户失败", err)
      } finally {
        setUserToDelete(null)
        setIsDeleteDialogOpen(false)
      }
    }
  }

  // 添加新用户（跳转到详情页）
  const handleAddUser = () => {
    router.push('/user/detail/new')
  }

  // 编辑用户（跳转到详情页）
  const handleEditUser = (userId: string) => {
    router.push(`/user/detail/${userId}`)
  }

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case "inactive":
        return <UserCircle className="h-4 w-4 text-gray-400" />
      case "suspended":
        return <XCircle className="h-4 w-4 text-red-500" />
      case "pending":
        return <Shield className="h-4 w-4 text-amber-500" />
      default:
        return <UserCircle className="h-4 w-4 text-gray-400" />
    }
  }

  // 获取状态类名
  const getStatusClass = (status: string) => {
    switch (status) {
      case "active":
        return "text-green-700 ml-1.5"
      case "inactive":
        return "text-gray-500 ml-1.5"
      case "suspended":
        return "text-red-700 ml-1.5"
      case "pending":
        return "text-amber-600 ml-1.5"
      default:
        return "ml-1.5"
    }
  }

  // 格式化日期
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'yyyy-MM-dd HH:mm', { locale: zhCN })
    } catch (e) {
      return "无效日期"
    }
  }

  // 计算总页数
  const totalPages = Math.ceil(totalUsers / pageSize)

  // 面包屑数据
  const breadcrumbItems = [
    { label: "首页", href: "/dashboard" },
    { label: "用户管理", isCurrent: true }
  ]
  
  // 获取角色名称
  const getRoleName = (roleId: string): string => {
    return rolesMap[roleId]?.name || "未知角色";
  };

  return (
    <div className="flex flex-col min-h-screen">
      <HeaderWithBreadcrumb items={breadcrumbItems} />
      
      <main className="flex-1">
        <div className="container mx-auto px-6 py-6 max-w-7xl">
          <div className="flex justify-between items-center mb-6">
            <div className="relative w-72">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索用户..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
              />
            </div>
            <Button onClick={handleAddUser} className="cursor-pointer">
              <Plus className="mr-2 h-4 w-4" />
              添加用户
            </Button>
          </div>
          
          <Card>
            <CardContent className="p-0">
              {loading ? (
                <div className="flex items-center justify-center py-12">
                  <Loader2 className="h-6 w-6 animate-spin text-primary mr-2" />
                  <span>加载中...</span>
                </div>
              ) : error ? (
                <Alert variant="destructive" className="m-6">
                  <AlertTitle>加载失败</AlertTitle>
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              ) : users.length === 0 ? (
                <div className="text-center py-12">
                  <p className="text-muted-foreground mb-4">暂无用户数据</p>
                  <Button onClick={handleAddUser} variant="outline" className="cursor-pointer">
                    <Plus className="mr-2 h-4 w-4" />
                    创建用户
                  </Button>
                </div>
              ) : (
                <>
                  <Table>
                    <TableHeader>
                      <TableRow className="whitespace-nowrap">
                        <TableHead className="w-[60px]"></TableHead>
                        <TableHead>用户名</TableHead>
                        <TableHead className="hidden sm:table-cell">邮箱</TableHead>
                        <TableHead className="hidden md:table-cell">状态</TableHead>
                        <TableHead className="hidden md:table-cell">所属团队</TableHead>
                        <TableHead className="hidden lg:table-cell">角色</TableHead>
                        <TableHead className="hidden lg:table-cell">最近更新</TableHead>
                        <TableHead className="w-[120px] text-right">操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {users.map((user) => (
                        <TableRow key={user._id} className="group relative">
                          <TableCell>
                            <Avatar>
                              <AvatarImage src={user.avatar} alt={user.name} />
                              <AvatarFallback>{user.name.slice(0, 2)}</AvatarFallback>
                            </Avatar>
                          </TableCell>
                          <TableCell className="font-medium">
                            <Link href={`/user/detail/${user._id}`} className="hover:underline cursor-pointer truncate block max-w-[150px]" title={user.name}>
                              {user.name}
                            </Link>
                          </TableCell>
                          <TableCell className="hidden sm:table-cell">
                            <div className="flex items-center space-x-1">
                              <Mail className="h-3.5 w-3.5 text-muted-foreground" />
                              <span className="truncate max-w-[200px]" title={user.email}>
                                {user.email}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell className="hidden md:table-cell">
                            <div className="flex items-center">
                              {getStatusIcon(user.status)}
                              <span className={getStatusClass(user.status)}>
                                {STATUS_LABELS[user.status] || user.status}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell className="hidden md:table-cell">
                            {user.teamId && teamsMap[user.teamId] ? (
                              <span className="truncate max-w-[150px]" title={teamsMap[user.teamId].name}>
                                {teamsMap[user.teamId].name}
                              </span>
                            ) : (
                              <span className="text-muted-foreground text-sm">未分配</span>
                            )}
                          </TableCell>
                          <TableCell className="hidden lg:table-cell">
                            <div className="flex flex-wrap gap-1">
                              {user.roleIds && user.roleIds.length > 0 ? (
                                user.roleIds.map(roleId => (
                                  <span key={roleId} className="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold bg-gray-50 text-gray-700 border-gray-200">
                                    {getRoleName(roleId)}
                                  </span>
                                ))
                              ) : (
                                <span className="text-muted-foreground text-sm">无角色</span>
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="hidden lg:table-cell">
                            <div className="truncate max-w-[120px]" title={formatDate(user.updatedAt)}>
                              {formatDate(user.updatedAt)}
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end items-center gap-2">
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleEditUser(user._id)}
                                className="h-7 w-7 cursor-pointer"
                              >
                                <Pencil className="h-4 w-4" />
                                <span className="sr-only">编辑</span>
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-7 w-7 cursor-pointer text-destructive hover:text-destructive/90 hover:bg-destructive/10"
                                onClick={() => {
                                  setUserToDelete(user._id)
                                  setIsDeleteDialogOpen(true)
                                }}
                              >
                                <Trash2 className="h-4 w-4" />
                                <span className="sr-only">删除</span>
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                  
                  {/* 分页组件 */}
                  {totalPages > 1 && (
                    <div className="mt-4 flex justify-center pb-4">
                      <Pagination>
                        <PaginationContent>
                          <PaginationItem>
                            <PaginationPrevious 
                              onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
                              className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                            />
                          </PaginationItem>
                          
                          {Array.from({length: totalPages}, (_, i) => i + 1)
                            .filter(page => {
                              // 显示当前页附近的页码和首尾页
                              return page === 1 || page === totalPages || 
                                Math.abs(page - currentPage) <= 1
                            })
                            .map((page, index, array) => (
                              <>
                                {index > 0 && array[index-1] !== page-1 && (
                                  <PaginationItem key={`ellipsis-${page}`}>
                                    <span className="px-4">...</span>
                                  </PaginationItem>
                                )}
                                <PaginationItem key={page}>
                                  <PaginationLink
                                    onClick={() => setCurrentPage(page)}
                                    isActive={currentPage === page}
                                    className="cursor-pointer"
                                  >
                                    {page}
                                  </PaginationLink>
                                </PaginationItem>
                              </>
                            ))}
                          
                          <PaginationItem>
                            <PaginationNext 
                              onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}
                              className={currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                            />
                          </PaginationItem>
                        </PaginationContent>
                      </Pagination>
                    </div>
                  )}
                </>
              )}
            </CardContent>
          </Card>
        </div>
      </main>
      
      {/* 删除确认对话框 */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[450px]">
          <DialogHeader className="pb-2">
            <DialogTitle className="text-lg">确认删除</DialogTitle>
            <DialogDescription>
              您确定要删除这个用户吗？此操作无法撤销。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="gap-3 pt-5">
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)} className="cursor-pointer">
              取消
            </Button>
            <Button variant="destructive" onClick={handleDeleteUser} className="cursor-pointer">
              删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
} 

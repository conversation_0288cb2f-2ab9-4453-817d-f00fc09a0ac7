import {
  AudioWaveform,
  BookOpen,
  Bot,
  Command,
  Compass,
  Frame,
  GalleryVerticalEnd,
  LayoutDashboard,
  Map,
  PieChart,
  Settings2,
  SquareTerminal,
  LucideIcon,
  Globe,
  Smartphone,
  Code,
  PenTool,
  Users,
  Lock,
  FileText,
} from "lucide-react"

// 图标名称到组件的映射
export const iconMapping: Record<string, LucideIcon> = {
  AudioWaveform,
  BookOpen,
  Bot,
  Command,
  Compass,
  Frame,
  GalleryVerticalEnd,
  LayoutDashboard,
  Map,
  PieChart,
  Settings2,
  SquareTerminal,
  // 添加导航中使用的图标
  Globe,
  Smartphone,
  Code,
  PenTool,
  Users,
  Lock,
  FileText,
}

// 获取图标组件的辅助函数
export function getIconComponent(iconName: string): LucideIcon {
  // 如果找不到图标，返回FileText作为默认图标，而不是返回null
  return iconMapping[iconName] || FileText
} 
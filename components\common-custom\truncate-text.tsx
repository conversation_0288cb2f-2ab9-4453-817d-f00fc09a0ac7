"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

// ============================================================================
// 类型定义 - 单文件组件类型定义直接在组件文件内
// ============================================================================

/**
 * 文本截断组件属性
 */
export interface TruncateTextProps {
  /**
   * 要截断显示的文本内容
   */
  text: string

  /**
   * 显示行数
   * @default 1
   */
  lines?: number

  /**
   * 容器最大宽度，例如 "200px"
   */
  maxWidth?: string

  /**
   * 是否在鼠标悬停时显示完整内容提示
   * @default true
   */
  showTooltip?: boolean

  /**
   * 自定义类名
   */
  className?: string

  /**
   * 截断位置
   * @default "end"
   */
  position?: "start" | "middle" | "end"

  /**
   * 自定义省略符号
   * @default "..."
   */
  ellipsis?: string

  /**
   * 是否显示展开/收起按钮
   * @default false
   */
  expandable?: boolean

  /**
   * 展开状态变化回调
   */
  onExpandChange?: (expanded: boolean) => void

  /**
   * 提示框最大宽度
   * @default "300px"
   */
  tooltipMaxWidth?: string

  /**
   * 提示框位置
   * @default "top"
   */
  tooltipSide?: "top" | "bottom" | "left" | "right"

  /**
   * 是否禁用
   * @default false
   */
  disabled?: boolean
}

// ============================================================================
// 组件实现
// ============================================================================

/**
 * 文本截断组件
 *
 * 提供灵活的文本截断功能，支持单行和多行截断，可选择显示提示框
 *
 * @example
 * ```tsx
 * // 基础用法
 * <TruncateText text="这是一段很长的文本内容" maxWidth="200px" />
 *
 * // 多行截断
 * <TruncateText
 *   text="这是一段很长的文本内容，需要在多行中显示"
 *   lines={3}
 *   maxWidth="300px"
 * />
 *
 * // 可展开的文本
 * <TruncateText
 *   text="长文本内容"
 *   expandable={true}
 *   onExpandChange={(expanded) => console.log('展开状态:', expanded)}
 * />
 * ```
 */
export function TruncateText({
  text,
  lines = 1,
  maxWidth,
  showTooltip = true,
  className,
  position = "end",
  ellipsis = "...",
  expandable = false,
  onExpandChange,
  tooltipMaxWidth = "300px",
  tooltipSide = "top",
  disabled = false,
}: TruncateTextProps) {
  const [tooltipOpen, setTooltipOpen] = React.useState(false)
  const [expanded, setExpanded] = React.useState(false)
  const [isTruncated, setIsTruncated] = React.useState(false)
  const textRef = React.useRef<HTMLDivElement>(null)

  // 检查文本是否被截断
  React.useEffect(() => {
    if (textRef.current) {
      const element = textRef.current
      const isOverflowing = lines === 1
        ? element.scrollWidth > element.clientWidth
        : element.scrollHeight > element.clientHeight
      setIsTruncated(isOverflowing)
    }
  }, [text, lines, maxWidth])

  const handleExpandToggle = () => {
    const newExpanded = !expanded
    setExpanded(newExpanded)
    onExpandChange?.(newExpanded)
  }

  // 构建容器样式
  const containerStyle: React.CSSProperties = {
    maxWidth: maxWidth,
    overflow: expanded ? 'visible' : 'hidden',
    ...(lines === 1 && !expanded
      ? {
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap',
        }
      : !expanded && {
          display: '-webkit-box',
          WebkitBoxOrient: 'vertical',
          WebkitLineClamp: lines,
        }),
  }

  // 处理中间截断
  const getDisplayText = () => {
    if (expanded || position === "end") return text

    if (position === "start") {
      // 开头截断 - 简化实现
      return text.length > 50 ? `${ellipsis}${text.slice(-47)}` : text
    }

    if (position === "middle") {
      // 中间截断 - 简化实现
      if (text.length > 50) {
        const start = text.slice(0, 20)
        const end = text.slice(-20)
        return `${start}${ellipsis}${end}`
      }
    }

    return text
  }

  const displayText = getDisplayText()
  const shouldShowTooltip = showTooltip && !disabled && (isTruncated || position !== "end")

  const textElement = (
    <div
      ref={textRef}
      className={cn(
        "relative",
        disabled && "opacity-50 cursor-not-allowed",
        className
      )}
      style={containerStyle}
      onMouseEnter={() => shouldShowTooltip && setTooltipOpen(true)}
      onMouseLeave={() => setTooltipOpen(false)}
    >
      <span>{displayText}</span>

      {expandable && isTruncated && (
        <button
          onClick={handleExpandToggle}
          className="ml-1 text-primary hover:text-primary/80 text-sm font-medium"
          disabled={disabled}
        >
          {expanded ? "收起" : "展开"}
        </button>
      )}
    </div>
  )

  if (shouldShowTooltip) {
    return (
      <TooltipProvider>
        <Tooltip delayDuration={300} open={tooltipOpen && text.length > 0}>
          <TooltipTrigger asChild>
            {textElement}
          </TooltipTrigger>
          <TooltipContent
            side={tooltipSide}
            align="start"
            className="break-words"
            style={{ maxWidth: tooltipMaxWidth }}
          >
            {text}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    )
  }

  return textElement
}

// ============================================================================
// 类型导出
// ============================================================================

export type { TruncateTextProps }

// ============================================================================
// 常量导出
// ============================================================================

/**
 * 支持的截断位置
 */
export const TRUNCATE_POSITIONS = ['start', 'middle', 'end'] as const

/**
 * 支持的提示框位置
 */
export const TOOLTIP_SIDES = ['top', 'bottom', 'left', 'right'] as const

/**
 * 默认文本截断配置
 */
export const DEFAULT_TRUNCATE_TEXT_CONFIG = {
  lines: 1,
  showTooltip: true,
  position: 'end' as const,
  ellipsis: '...',
  expandable: false,
  tooltipMaxWidth: '300px',
  tooltipSide: 'top' as const,
  disabled: false,
} satisfies Partial<TruncateTextProps>

/**
 * 常用最大宽度配置
 */
export const COMMON_MAX_WIDTHS = {
  xs: '100px',
  sm: '150px',
  md: '200px',
  lg: '300px',
  xl: '400px',
  '2xl': '500px',
} as const

/**
 * 群组API请求
 * 提供群组相关的API调用接口
 */
import {GROUP_API_BASE_URL} from "./configApi";
import apiClient from "./requestApi";
import {ApiResponse} from "@/types/api";
import {Group, GroupQueryParams, CreateGroupParams} from "@/types/models";
import {processArrayResponse, processResponse} from "@/lib/request";

/**
 * 获取群组列表
 * @param params 查询参数
 * @returns 群组列表响应
 */
export async function getGroupsRequest(params?: GroupQueryParams) {
    const response = await apiClient.post<ApiResponse<Group[]>>(`${GROUP_API_BASE_URL}/list`, params || {});
    return processArrayResponse(response, {
        errorMessage: "获取群组列表失败",
        showErrorToast: true
    });
}

/**
 * 创建群组
 * @param group 群组数据
 * @returns 创建结果响应
 */
export async function createGroupRequest(group: CreateGroupParams): Promise<number | null> {
    const response = await apiClient.post<ApiResponse<number>>(`${GROUP_API_BASE_URL}/create`, group);
    return processResponse(response, {
        successMessage: "群组创建成功",
        errorMessage: "创建群组失败",
        showSuccessToast: true,
        showErrorToast: true
    });
}

/**
 * 删除群组
 * @param id 群组ID
 * @returns 删除结果响应
 */
export async function deleteGroupRequest(id: string): Promise<boolean | null> {
    const response = await apiClient.delete<ApiResponse<boolean>>(`${GROUP_API_BASE_URL}/delete/${id}`);
    return processResponse(response, {
        successMessage: "群组删除成功",
        errorMessage: "删除群组失败",
        showSuccessToast: true,
        showErrorToast: true
    });
}
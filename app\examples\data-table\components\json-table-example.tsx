"use client"

import * as React from "react"
import { toast } from "sonner"
// 从json-table目录导入
import { JsonTableConfig, JsonTableColumn, JsonTableAction, TableFilter } from "@/components/common-custom/data-table/types"
import { User } from "../data"
import {JsonDataTable} from "@/components/common-custom/data-table/json-table/json-data-table";

export interface JsonDataTableExampleProps {
  data: User[]
}

export function JsonDataTableExample({ data }: JsonDataTableExampleProps) {
  // 表格定义
  const tableDefinition = {
    config: {
      title: "用户管理",
      description: "使用JSON配置的高级数据表格示例",
      showSearch: true,
      showFilters: true,
      showColumnVisibility: true,
      showPagination: true,
      showSelection: true,
      showRowNumbers: true,
      bordered: true,
      striped: true,
      hover: true,
      density: "default" as const,
      pageSize: 5,
      pageSizeOptions: [5, 10, 20, 50],
      searchPlaceholder: "搜索用户...",
      emptyMessage: "暂无用户数据",
      showExport: true,
      showRefresh: true,
      showAdd: true,
      addButtonText: "添加用户",
      exportFormats: ["csv", "excel", "pdf"] as const,
      exportFileName: "用户数据",
    } as JsonTableConfig,
    
    // 列定义
    columns: [
      {
        field: "name",
        header: "姓名",
        type: "text",
        sortable: true,
        filterable: true,
      },
      {
        field: "email",
        header: "邮箱",
        type: "text",
        sortable: true,
      },
      {
        field: "role",
        header: "角色",
        type: "badge",
        sortable: true,
        filterable: true,
        badge: {
          mapping: {
            "管理员": {
              variant: "destructive",
              label: "管理员"
            },
            "编辑": {
              variant: "secondary",
              label: "编辑"
            },
            "用户": {
              variant: "outline",
              label: "用户"
            },
            "访客": {
              variant: "default",
              label: "访客"
            }
          }
        }
      },
      {
        field: "status",
        header: "状态",
        type: "badge",
        sortable: true,
        filterable: true,
        badge: {
          mapping: {
            "活跃": {
              variant: "default",
              label: "活跃",
              color: "green"
            },
            "未活跃": {
              variant: "outline",
              label: "未活跃"
            },
            "已禁用": {
              variant: "destructive",
              label: "已禁用"
            }
          }
        }
      },
      {
        field: "lastLogin",
        header: "最后登录",
        type: "date",
        sortable: true,
        format: {
          dateFormat: "yyyy-MM-dd HH:mm:ss"
        }
      },
      {
        field: "department",
        header: "部门",
        type: "text",
        sortable: true,
        filterable: true,
      }
    ] as JsonTableColumn[],
    
    // 操作定义
    actions: [
      {
        type: "view",
        label: "查看",
        icon: "eye",
      },
      {
        type: "edit",
        label: "编辑",
        icon: "pencil",
        condition: "status != 已禁用"
      },
      {
        type: "delete",
        label: "删除",
        icon: "trash",
        confirm: {
          title: "确认删除",
          message: "确定要删除此用户吗？此操作无法撤销。",
          confirmText: "删除",
          cancelText: "取消"
        }
      },
      {
        type: "custom",
        label: "重置密码",
        value: "reset-password",
        variant: "outline",
        condition: "status != 已禁用"
      },
      {
        type: "custom",
        label: "禁用账户",
        value: "disable",
        variant: "secondary",
        condition: "status = 活跃"
      },
      {
        type: "custom",
        label: "启用账户",
        value: "enable",
        variant: "secondary",
        condition: "status = 已禁用"
      }
    ] as JsonTableAction[],
    
    // 过滤器定义
    filters: [
      {
        key: "role",
        title: "角色",
        type: "select",
        options: [
          { label: "管理员", value: "管理员" },
          { label: "编辑", value: "编辑" },
          { label: "用户", value: "用户" },
          { label: "访客", value: "访客" }
        ]
      },
      {
        key: "status",
        title: "状态",
        type: "select",
        options: [
          { label: "活跃", value: "活跃" },
          { label: "未活跃", value: "未活跃" },
          { label: "已禁用", value: "已禁用" }
        ]
      },
      {
        key: "department",
        title: "部门",
        type: "select",
        options: [
          { label: "技术", value: "技术" },
          { label: "市场", value: "市场" },
          { label: "销售", value: "销售" },
          { label: "人事", value: "人事" },
          { label: "财务", value: "财务" }
        ]
      }
    ] as TableFilter[]
  }

  // 自定义渲染器
  const renderers = {
    userAvatar: (value: string, row: User) => {
      return (
        <div className="flex items-center">
          <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center text-primary font-medium mr-2">
            {row.name.charAt(0).toUpperCase()}
          </div>
          <div>{value}</div>
        </div>
      )
    }
  }
  
  // 自定义格式化函数
  const formatters = {
    dateTimeFormatter: (value: string) => {
      return new Date(value).toLocaleString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit"
      })
    }
  }
  
  // 处理行操作
  const handleRowAction = (row: User, action: string, actionType: string) => {
    switch (action) {
      case "view":
        toast.info(`查看用户: ${row.name}`)
        break
      case "edit":
        toast.info(`编辑用户: ${row.name}`)
        break
      case "delete":
        toast.success(`删除用户: ${row.name}`)
        break
      case "reset-password":
        toast.success(`已为用户 ${row.name} 重置密码`)
        break
      case "disable":
        toast.success(`已禁用用户: ${row.name}`)
        break
      case "enable":
        toast.success(`已启用用户: ${row.name}`)
        break
      default:
        toast.info(`操作: ${action}, 用户: ${row.name}`)
    }
  }
  
  // 处理行选择变化
  const handleSelectionChange = (selectedRows: User[]) => {
    console.log("选中的行:", selectedRows)
  }
  
  // 处理刷新
  const handleRefresh = () => {
    toast.info("刷新数据")
  }
  
  // 处理添加
  const handleAdd = () => {
    toast.info("添加新用户")
  }
  
  // 处理导出
  const handleExport = (format: "csv" | "excel" | "pdf") => {
    toast.success(`导出数据为 ${format.toUpperCase()} 格式`)
  }

  return (
    <div className="space-y-4">
      <JsonDataTable
        tableDefinition={tableDefinition}
        data={data}
        renderers={renderers}
        formatters={formatters}
        onRowAction={handleRowAction}
        onSelectionChange={handleSelectionChange}
        onRefresh={handleRefresh}
        onAdd={handleAdd}
        onExport={handleExport}
      />
    </div>
  )
} 
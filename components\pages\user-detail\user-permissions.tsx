"use client"

import React, { useState, useEffect } from "react"
import { 
  Key,
  Loader2,
  <PERSON>olderTree,
  CheckCircle2,
  XCircle,
  Lock
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger
} from "@/components/ui/collapsible"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { PermissionNode, PermissionType } from "@/types/permission"
import { ChevronRight } from "lucide-react"

interface UserPermissionsProps {
  userId: number;
  teamCode?: string;
}

// 模拟获取用户权限的函数
// 在实际应用中，这应该是一个API调用
const getUserPermissions = async (userId: number, teamCode: string = "default"): Promise<PermissionNode[]> => {
  // 这里应该是一个真实的API调用
  // 为了示例，这里返回一些模拟数据
  console.log(`Getting permissions for user ${userId} in team ${teamCode}`)
  
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 800))
  
  // 模拟数据
  return [
    {
      id: "system_settings",
      name: "系统设置",
      type: PermissionType.Directory,
      children: [
        {
          id: "user_management",
          name: "用户管理",
          type: PermissionType.Menu,
          children: [
            {
              id: "user_view",
              name: "查看用户",
              type: PermissionType.Button,
              children: []
            },
            {
              id: "user_add",
              name: "添加用户",
              type: PermissionType.Button,
              children: []
            }
          ]
        },
        {
          id: "role_management",
          name: "角色管理",
          type: PermissionType.Menu,
          children: []
        }
      ]
    },
    {
      id: "content_management",
      name: "内容管理",
      type: PermissionType.Directory,
      children: []
    }
  ]
}

export function UserPermissions({ userId, teamCode = "default" }: UserPermissionsProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [permissions, setPermissions] = useState<PermissionNode[]>([])
  const [error, setError] = useState<string | null>(null)
  const [expandedNodes, setExpandedNodes] = useState<Record<string, boolean>>({})

  // 获取用户权限
  useEffect(() => {
    const loadPermissions = async () => {
      if (!userId) return

      setIsLoading(true)
      setError(null)
      try {
        const userPermissions = await getUserPermissions(userId, teamCode)
        setPermissions(userPermissions)
      } catch (err) {
        console.error("获取用户权限失败", err)
        setError("获取用户权限失败")
      } finally {
        setIsLoading(false)
      }
    }

    loadPermissions()
  }, [userId, teamCode])

  // 切换节点展开状态
  const toggleNodeExpanded = (nodeId: string) => {
    setExpandedNodes(prev => ({
      ...prev,
      [nodeId]: !prev[nodeId]
    }))
  }

  const renderPermissionNode = (node: PermissionNode, depth = 0) => {
    const hasChildren = node.children && node.children.length > 0
    const isExpanded = !!expandedNodes[node.id]
    
    return (
      <div key={node.id} className="mb-1" style={{ paddingLeft: `${depth * 16}px` }}>
        <div className="flex items-center py-1">
          {hasChildren ? (
            <CollapsibleTrigger asChild onClick={() => toggleNodeExpanded(node.id)}>
              <Button variant="ghost" size="icon" className="h-6 w-6 p-0 mr-1">
                <ChevronRight className={`h-4 w-4 transition-transform ${isExpanded ? 'rotate-90' : ''}`} />
              </Button>
            </CollapsibleTrigger>
          ) : (
            <div className="w-6 mr-1" />
          )}
          
          {node.type === PermissionType.Directory ? (
            <FolderTree className="h-4 w-4 mr-2 text-muted-foreground" />
          ) : node.type === PermissionType.Menu ? (
            <Lock className="h-4 w-4 mr-2 text-primary" />
          ) : (
            <Key className="h-4 w-4 mr-2 text-muted-foreground" />
          )}
          
          <span className="text-sm">{node.name}</span>
          
          <Badge 
            variant="outline" 
            className="ml-2 text-xs bg-muted/50"
          >
            {node.type}
          </Badge>
        </div>
        
        {hasChildren && (
          <Collapsible open={isExpanded}>
            <CollapsibleContent>
              <div className="border-l border-l-border/40 ml-3 pl-3 mt-1">
                {node.children.map(childNode => renderPermissionNode(childNode, depth + 1))}
              </div>
            </CollapsibleContent>
          </Collapsible>
        )}
      </div>
    )
  }

  return (
    <Card className="border-border/40">
      <CardHeader>
        <CardTitle className="text-lg flex items-center gap-2">
          <Key className="h-5 w-5 text-primary" />
          用户权限
        </CardTitle>
        <CardDescription>用户拥有的系统权限</CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        ) : error ? (
          <div className="text-center py-6 text-muted-foreground">
            <p>{error}</p>
          </div>
        ) : permissions.length === 0 ? (
          <div className="text-center py-6">
            <Key className="h-12 w-12 mx-auto text-muted-foreground mb-3" />
            <h3 className="font-medium mb-1">无权限信息</h3>
            <p className="text-sm text-muted-foreground">
              未找到该用户的权限信息
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground mb-4">
              以下是该用户通过所属角色获得的系统权限
            </p>
            
            <div className="border rounded-md p-3 bg-muted/10">
              {permissions.map(node => renderPermissionNode(node))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default UserPermissions; 
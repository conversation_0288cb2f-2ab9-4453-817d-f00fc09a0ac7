import { ReactNode } from "react"
import { LucideIcon } from "lucide-react"

// ============================================================================
// 模态框组件类型定义 - 复杂组件类型定义在组件目录内
// ============================================================================

/**
 * 模态框按钮属性
 */
export interface ModalButtonProps {
  /**
   * 按钮文本
   */
  label: string
  
  /**
   * 按钮变种
   * @default "default"
   */
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"
  
  /**
   * 按钮图标
   */
  icon?: LucideIcon
  
  /**
   * 按钮点击事件
   */
  onClick?: (e?: React.MouseEvent<HTMLButtonElement>) => void
  
  /**
   * 是否加载中
   * @default false
   */
  loading?: boolean
  
  /**
   * 是否禁用
   * @default false
   */
  disabled?: boolean
  
  /**
   * 自定义类名
   */
  className?: string
}

/**
 * 模态框属性
 */
export interface ModalProps {
  /**
   * 是否打开
   */
  open: boolean
  
  /**
   * 打开状态变化回调
   */
  onOpenChange: (open: boolean) => void
  
  /**
   * 模态框标题
   */
  title?: ReactNode
  
  /**
   * 模态框描述
   */
  description?: ReactNode
  
  /**
   * 模态框内容
   */
  children: ReactNode
  
  /**
   * 底部按钮
   */
  footer?: ReactNode
  
  /**
   * 确认按钮配置
   */
  confirmButton?: ModalButtonProps
  
  /**
   * 取消按钮配置
   */
  cancelButton?: ModalButtonProps
  
  /**
   * 模态框尺寸
   * @default "md"
   */
  size?: "sm" | "md" | "lg" | "xl" | "2xl" | "full"
  
  /**
   * 是否显示关闭按钮
   * @default true
   */
  showCloseButton?: boolean
  
  /**
   * 是否可通过点击遮罩关闭
   * @default true
   */
  closeOnOverlayClick?: boolean
  
  /**
   * 是否可通过ESC键关闭
   * @default true
   */
  closeOnEscape?: boolean
  
  /**
   * 自定义类名
   */
  className?: string
  
  /**
   * 内容区域类名
   */
  contentClassName?: string
  
  /**
   * 头部区域类名
   */
  headerClassName?: string
  
  /**
   * 底部区域类名
   */
  footerClassName?: string
}

/**
 * 警告对话框模态框属性
 */
export interface AlertDialogModalProps {
  /**
   * 是否打开
   */
  open: boolean
  
  /**
   * 打开状态变化回调
   */
  onOpenChange: (open: boolean) => void
  
  /**
   * 对话框标题
   */
  title: ReactNode
  
  /**
   * 对话框描述
   */
  description?: ReactNode
  
  /**
   * 确认按钮配置
   */
  confirmButton?: ModalButtonProps
  
  /**
   * 取消按钮配置
   */
  cancelButton?: ModalButtonProps
  
  /**
   * 警告类型
   * @default "warning"
   */
  type?: "warning" | "danger" | "info" | "success"
  
  /**
   * 图标
   */
  icon?: LucideIcon | ReactNode
  
  /**
   * 自定义类名
   */
  className?: string
}

/**
 * 表单模态框属性
 */
export interface FormModalProps {
  /**
   * 是否打开
   */
  open: boolean
  
  /**
   * 打开状态变化回调
   */
  onOpenChange: (open: boolean) => void
  
  /**
   * 表单标题
   */
  title: ReactNode
  
  /**
   * 表单描述
   */
  description?: ReactNode
  
  /**
   * 表单内容
   */
  children: ReactNode
  
  /**
   * 提交按钮配置
   */
  submitButton?: ModalButtonProps
  
  /**
   * 取消按钮配置
   */
  cancelButton?: ModalButtonProps
  
  /**
   * 表单提交回调
   */
  onSubmit?: (e: React.FormEvent) => void
  
  /**
   * 模态框尺寸
   * @default "md"
   */
  size?: "sm" | "md" | "lg" | "xl" | "2xl"
  
  /**
   * 是否显示关闭按钮
   * @default true
   */
  showCloseButton?: boolean
  
  /**
   * 自定义类名
   */
  className?: string
}

/**
 * 信息模态框属性
 */
export interface InfoModalProps {
  /**
   * 是否打开
   */
  open: boolean
  
  /**
   * 打开状态变化回调
   */
  onOpenChange: (open: boolean) => void
  
  /**
   * 信息标题
   */
  title: ReactNode
  
  /**
   * 信息内容
   */
  content: ReactNode
  
  /**
   * 信息类型
   * @default "info"
   */
  type?: "info" | "success" | "warning" | "error"
  
  /**
   * 图标
   */
  icon?: LucideIcon | ReactNode
  
  /**
   * 确认按钮配置
   */
  confirmButton?: ModalButtonProps
  
  /**
   * 模态框尺寸
   * @default "sm"
   */
  size?: "sm" | "md" | "lg"
  
  /**
   * 自定义类名
   */
  className?: string
}

/**
 * 模态框触发器属性
 */
export interface ModalTriggerProps {
  /**
   * 触发元素
   */
  children: ReactNode
  
  /**
   * 模态框内容
   */
  modal: ReactNode
  
  /**
   * 是否打开（受控模式）
   */
  open?: boolean
  
  /**
   * 打开状态变化回调
   */
  onOpenChange?: (open: boolean) => void
  
  /**
   * 是否默认打开
   * @default false
   */
  defaultOpen?: boolean
  
  /**
   * 触发方式
   * @default "click"
   */
  trigger?: "click" | "hover" | "focus"
  
  /**
   * 自定义类名
   */
  className?: string
}

// ============================================================================
// 常量导出
// ============================================================================

/**
 * 支持的模态框尺寸
 */
export const MODAL_SIZES = ['sm', 'md', 'lg', 'xl', '2xl', 'full'] as const

/**
 * 支持的按钮变体
 */
export const BUTTON_VARIANTS = ['default', 'destructive', 'outline', 'secondary', 'ghost', 'link'] as const

/**
 * 支持的警告类型
 */
export const ALERT_TYPES = ['warning', 'danger', 'info', 'success'] as const

/**
 * 支持的信息类型
 */
export const INFO_TYPES = ['info', 'success', 'warning', 'error'] as const

/**
 * 支持的触发方式
 */
export const TRIGGER_TYPES = ['click', 'hover', 'focus'] as const

/**
 * 默认模态框配置
 */
export const DEFAULT_MODAL_CONFIG = {
  size: 'md' as const,
  showCloseButton: true,
  closeOnOverlayClick: true,
  closeOnEscape: true,
} satisfies Partial<ModalProps>

/**
 * 默认按钮配置
 */
export const DEFAULT_BUTTON_CONFIG = {
  variant: 'default' as const,
  loading: false,
  disabled: false,
} satisfies Partial<ModalButtonProps>

/**
 * 默认警告对话框配置
 */
export const DEFAULT_ALERT_DIALOG_CONFIG = {
  type: 'warning' as const,
} satisfies Partial<AlertDialogModalProps>

/**
 * 默认表单模态框配置
 */
export const DEFAULT_FORM_MODAL_CONFIG = {
  size: 'md' as const,
  showCloseButton: true,
} satisfies Partial<FormModalProps>

/**
 * 默认信息模态框配置
 */
export const DEFAULT_INFO_MODAL_CONFIG = {
  type: 'info' as const,
  size: 'sm' as const,
} satisfies Partial<InfoModalProps>

/**
 * 默认触发器配置
 */
export const DEFAULT_TRIGGER_CONFIG = {
  defaultOpen: false,
  trigger: 'click' as const,
} satisfies Partial<ModalTriggerProps>

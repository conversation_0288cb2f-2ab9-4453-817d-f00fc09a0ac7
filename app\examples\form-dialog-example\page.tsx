"use client"

import React from "react"
import { ComponentPreviewContainer } from "@/components/component-detail/preview-container"
import { FormDialog } from "@/components/common-custom/form"
import { Button } from "@/components/ui/button"
import { Plus, Trash2 } from "lucide-react"

// 基础表单对话框示例代码
const basicFormDialogCode = `
import React, { useState } from "react"
import { FormDialog } from "@/components/common-custom/form"
import { Button } from "@/components/ui/button"
import { Plus } from "lucide-react"

function BasicFormDialogExample() {
  const [dialogOpen, setDialogOpen] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formValues, setFormValues] = useState({
    name: "",
    email: "",
    role: "",
    bio: ""
  })
  const [formErrors, setFormErrors] = useState({})
  const [submittedData, setSubmittedData] = useState(null)

  const fields = [
    {
      name: "name",
      label: "姓名",
      type: "text",
      placeholder: "请输入姓名",
      required: true
    },
    {
      name: "email",
      label: "邮箱",
      type: "email",
      placeholder: "请输入邮箱地址",
      required: true
    },
    {
      name: "role",
      label: "角色",
      type: "select",
      placeholder: "请选择角色",
      required: true,
      options: [
        { label: "管理员", value: "admin" },
        { label: "编辑", value: "editor" },
        { label: "用户", value: "user" }
      ]
    },
    {
      name: "bio",
      label: "个人简介",
      type: "textarea",
      placeholder: "请输入个人简介",
      required: false
    }
  ]

  const handleFieldChange = (name, value) => {
    setFormValues(prev => ({ ...prev, [name]: value }))
    if (formErrors[name]) {
      setFormErrors(prev => ({ ...prev, [name]: "" }))
    }
  }

  const handleSubmit = async (values) => {
    setIsSubmitting(true)
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      setSubmittedData(values)
      setDialogOpen(false)
      setFormValues({ name: "", email: "", role: "", bio: "" })
    } catch (error) {
      console.error(error)
    } finally {
      setIsSubmitting(false)
    }
  }
  
  return (
    <div className="space-y-4">
      <Button onClick={() => setDialogOpen(true)}>
        <Plus className="w-4 h-4 mr-2" />
        添加用户
      </Button>
      
      <FormDialog
        title="添加用户"
        description="添加新用户到系统"
        fields={fields}
        values={formValues}
        errors={formErrors}
        onChange={handleFieldChange}
        onSubmit={handleSubmit}
        isLoading={isSubmitting}
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        maxWidth="md"
      />
      
      {submittedData && (
        <div className="p-4 border rounded-md mt-4">
          <h4 className="font-medium mb-2">提交的数据</h4>
          <pre className="text-sm bg-muted p-2 rounded">
            {JSON.stringify(submittedData, null, 2)}
          </pre>
        </div>
      )}
    </div>
  )
}

render(<BasicFormDialogExample />)
`

// 确认对话框示例代码
const confirmDialogCode = `
import React, { useState } from "react"
import { FormDialog } from "@/components/common-custom/form"
import { Button } from "@/components/ui/button"
import { Trash2 } from "lucide-react"

function ConfirmDialogExample() {
  const [dialogOpen, setDialogOpen] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formValues, setFormValues] = useState({
    confirmText: ""
  })
  const [formErrors, setFormErrors] = useState({})
  const [result, setResult] = useState(null)

  const fields = [
    {
      name: "confirmText",
      label: "确认删除",
      type: "text",
      placeholder: "请输入 'DELETE' 确认删除",
      required: true,
      description: "此操作不可撤销，请谨慎操作"
    }
  ]

  const handleFieldChange = (name, value) => {
    setFormValues(prev => ({ ...prev, [name]: value }))
    if (formErrors[name]) {
      setFormErrors(prev => ({ ...prev, [name]: "" }))
    }
  }

  const handleSubmit = async (values) => {
    if (values.confirmText !== "DELETE") {
      setFormErrors({ confirmText: "请输入 'DELETE' 确认删除" })
      return
    }

    setIsSubmitting(true)
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      setResult({ message: "项目已成功删除" })
      setDialogOpen(false)
      setFormValues({ confirmText: "" })
    } catch (error) {
      console.error(error)
    } finally {
      setIsSubmitting(false)
    }
  }
  
  return (
    <div className="space-y-4">
      <Button variant="destructive" onClick={() => setDialogOpen(true)}>
        <Trash2 className="w-4 h-4 mr-2" />
        删除项目
      </Button>
      
      <FormDialog
        title="确认删除"
        description="您确定要删除此项目吗？此操作不可撤销。"
        fields={fields}
        values={formValues}
        errors={formErrors}
        onChange={handleFieldChange}
        onSubmit={handleSubmit}
        isLoading={isSubmitting}
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        maxWidth="sm"
        submitLabel="删除"
        cancelLabel="取消"
      />
      
      {result && (
        <div className="p-4 border rounded-md mt-4">
          <div className="text-sm">
            {result.message}
          </div>
        </div>
      )}
    </div>
  )
}

render(<ConfirmDialogExample />)
`

// 所有示例
const allExamples = [
  {
    id: "basic-form-dialog",
    title: "基础表单对话框",
    description: "在对话框中显示表单，用于快速创建和编辑操作",
    code: basicFormDialogCode,
    scope: { 
      React, 
      useState: React.useState,
      FormDialog,
      Button,
      Plus,
    },
  },
  {
    id: "confirm-dialog",
    title: "确认对话框",
    description: "用于确认危险操作的对话框表单",
    code: confirmDialogCode,
    scope: { 
      React, 
      useState: React.useState,
      FormDialog,
      Button,
      Trash2,
    },
  },
]

// API文档组件
function FormDialogApiDocs() {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="font-medium text-lg mb-2">FormDialog</h3>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">title</td>
                <td className="p-2 border">ReactNode</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">对话框标题</td>
              </tr>
              <tr>
                <td className="p-2 border">description</td>
                <td className="p-2 border">ReactNode</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">对话框描述</td>
              </tr>
              <tr>
                <td className="p-2 border">fields</td>
                <td className="p-2 border">FormField[]</td>
                <td className="p-2 border">[]</td>
                <td className="p-2 border">表单字段配置</td>
              </tr>
              <tr>
                <td className="p-2 border">values</td>
                <td className="p-2 border">Record&lt;string, any&gt;</td>
                <td className="p-2 border">{}</td>
                <td className="p-2 border">表单值</td>
              </tr>
              <tr>
                <td className="p-2 border">errors</td>
                <td className="p-2 border">Record&lt;string, string&gt;</td>
                <td className="p-2 border">{}</td>
                <td className="p-2 border">表单错误信息</td>
              </tr>
              <tr>
                <td className="p-2 border">onChange</td>
                <td className="p-2 border">(name: string, value: any) =&gt; void</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">字段值变化回调</td>
              </tr>
              <tr>
                <td className="p-2 border">onSubmit</td>
                <td className="p-2 border">(values: any) =&gt; void</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">表单提交回调</td>
              </tr>
              <tr>
                <td className="p-2 border">open</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">false</td>
                <td className="p-2 border">对话框是否打开</td>
              </tr>
              <tr>
                <td className="p-2 border">onOpenChange</td>
                <td className="p-2 border">(open: boolean) =&gt; void</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">对话框状态变化回调</td>
              </tr>
              <tr>
                <td className="p-2 border">isLoading</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">false</td>
                <td className="p-2 border">是否正在提交</td>
              </tr>
              <tr>
                <td className="p-2 border">maxWidth</td>
                <td className="p-2 border">&quot;sm&quot; | &quot;md&quot; | &quot;lg&quot; | &quot;xl&quot;</td>
                <td className="p-2 border">&quot;sm&quot;</td>
                <td className="p-2 border">对话框最大宽度</td>
              </tr>
              <tr>
                <td className="p-2 border">submitLabel</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">&quot;提交&quot;</td>
                <td className="p-2 border">提交按钮文字</td>
              </tr>
              <tr>
                <td className="p-2 border">cancelLabel</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">&quot;取消&quot;</td>
                <td className="p-2 border">取消按钮文字</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

export default function FormDialogExamplePage() {
  return (
    <ComponentPreviewContainer
      title="表单对话框 FormDialog"
      description="在对话框中显示表单，用于快速创建、编辑和确认操作，支持各种表单字段类型和验证"
      whenToUse="当需要在不离开当前页面的情况下进行数据输入时使用；适用于创建新记录、编辑现有数据、确认危险操作等场景；支持多种尺寸和自定义配置"
      examples={allExamples}
      apiDocs={<FormDialogApiDocs />}
    />
  )
}

"use client"

import React, { useState, useEffect, createContext, useContext, ReactNode } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import {
  Bell,
  X,
  AlertTriangle,
  Info,
  CheckCircle,
  XCircle,
  MessageSquare,
  Mail,
  Smartphone,
  Settings,
  Volume2,
  VolumeX,
} from "lucide-react"
import { cn } from "@/lib/utils"
import type { 
  ToastProps as CustomToastProps, 
  ToastProviderProps, 
  NotificationItem, 
  NotificationCenterProps, 
  AnnouncementBannerProps, 
  NotificationSettingItem, 
  NotificationSettingsProps 
} from "@/types/notification"

/**
 * Toast接口，用于抽象化toast实现
 */
export interface ToastHandler {
  /**
   * 显示toast
   */
  show: (props: {
    title?: React.ReactNode;
    description?: string;
    variant?: "default" | "destructive";
    duration?: number;
  }) => void;
}

/**
 * 简易的Toast辅助函数
 * @param props Toast参数
 * @param toastHandler 自定义toast处理器
 */
export function showToast(props: CustomToastProps, toastHandler?: ToastHandler) {
  const { type = "info", message, duration } = props
  
  // 如果没有提供toast处理器，则只记录到控制台
  if (!toastHandler) {
    console.log(`[Toast ${type}]: ${message}`);
    return;
  }
  
  // 根据类型设置图标和变体
  let title: string
  let variant: "default" | "destructive" = "default"
  let icon: ReactNode = null
  
  switch (type) {
    case "success":
      title = "成功"
      icon = <CheckCircle className="h-5 w-5 text-green-500" />
      break
    case "error":
      title = "错误"
      variant = "destructive"
      icon = <XCircle className="h-5 w-5 text-red-500" />
      break
    case "warning":
      title = "警告"
      icon = <AlertTriangle className="h-5 w-5 text-yellow-500" />
      break
    case "info":
    default:
      title = "提示"
      icon = <Info className="h-5 w-5 text-blue-500" />
      break
  }
  
  toastHandler.show({
    title: icon ? <span className="flex items-center gap-2">{icon} {title}</span> : title,
    description: message as string,
    variant,
    duration: duration || 3000
  })
}

/**
 * 通知中心组件
 */
export function NotificationCenter({
  notifications,
  onNotificationClick,
  onMarkAsRead,
  onMarkAllAsRead,
  onRemove,
  onClearAll,
  title = "通知中心",
  description = "您的最新通知和提醒",
  emptyContent,
  className,
  renderIcon,
}: NotificationCenterProps) {
  const unreadCount = notifications.filter((n) => !n.read).length

  const defaultRenderIcon = (type: string) => {
    switch (type) {
      case "success":
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case "warning":
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />
      case "error":
        return <XCircle className="h-5 w-5 text-red-500" />
      case "info":
        return <Info className="h-5 w-5 text-blue-500" />
      case "system":
        return <Settings className="h-5 w-5 text-gray-500" />
      case "user":
        return <MessageSquare className="h-5 w-5 text-blue-500" />
      case "order":
        return <span className="text-2xl">🛒</span>
      case "security":
        return <span className="text-2xl">🔒</span>
      default:
        return <Bell className="h-5 w-5 text-gray-500" />
    }
  }

  const getNotificationColor = (type: string) => {
    switch (type) {
      case "success":
        return "border-l-green-500 bg-green-50"
      case "warning":
        return "border-l-yellow-500 bg-yellow-50"
      case "error":
        return "border-l-red-500 bg-red-50"
      case "info":
        return "border-l-blue-500 bg-blue-50"
      default:
        return "border-l-gray-500 bg-gray-50"
    }
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5" />
              {title}
              {unreadCount > 0 && (
                <Badge variant="destructive" className="ml-2">
                  {unreadCount}
                </Badge>
              )}
            </CardTitle>
            <CardDescription>{description}</CardDescription>
          </div>
          <div className="flex gap-2">
            {onMarkAllAsRead && notifications.some((n) => !n.read) && (
              <Button variant="outline" size="sm" onClick={onMarkAllAsRead}>
                全部标为已读
              </Button>
            )}
            {onClearAll && notifications.length > 0 && (
              <Button variant="outline" size="sm" onClick={onClearAll}>
                清空
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="max-h-96 overflow-auto p-0">
        {notifications.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-8 text-center text-muted-foreground">
            {emptyContent || (
              <>
                <Bell className="h-12 w-12 mb-4 text-muted-foreground/50" />
                <p>暂无通知</p>
              </>
            )}
          </div>
        ) : (
          <div className="divide-y">
            {notifications.map((notification) => (
              <div
                key={notification.id}
                className={cn(
                  "relative flex items-start gap-4 p-4 hover:bg-accent/50 cursor-pointer",
                  notification.read ? "opacity-75" : "bg-accent/30"
                )}
                onClick={() => onNotificationClick?.(notification)}
              >
                <div className="flex-shrink-0">
                  {(renderIcon || defaultRenderIcon)(notification.type || "default")}
                </div>
                <div className="flex-grow">
                  <div className="flex items-baseline justify-between">
                    <h4 className={cn("font-medium", !notification.read && "font-semibold")}>
                      {notification.title}
                    </h4>
                    <span className="text-xs text-muted-foreground">
                      {notification.date}
                    </span>
                  </div>
                  <p className="mt-1 text-sm">{notification.message}</p>
                  {notification.actions && (
                    <div className="mt-2 flex gap-2">
                      {notification.actions.map((action, actionIndex) => (
                        <Button
                          key={actionIndex}
                          size="sm"
                          variant={action.primary ? "default" : "outline"}
                          onClick={(e) => {
                            e.stopPropagation();
                            if (typeof action.onClick === 'function') {
                              action.onClick();
                            }
                          }}
                        >
                          {action.label}
                        </Button>
                      ))}
                    </div>
                  )}
                </div>
                <div className="absolute right-2 top-2 flex gap-2">
                  {!notification.read && onMarkAsRead && (
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6"
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        onMarkAsRead(notification.id);
                      }}
                      aria-label="标记为已读"
                    >
                      <CheckCircle className="h-4 w-4" />
                    </Button>
                  )}
                  {onRemove && (
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6"
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        onRemove(notification.id);
                      }}
                      aria-label="删除通知"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

/**
 * 公告横幅组件
 */
export function AnnouncementBanner({
  type = "info",
  title,
  content,
  date,
  important,
  onClose,
  closable = true,
  icon,
  className,
}: AnnouncementBannerProps) {
  const getIcon = () => {
    if (icon) {
      if (typeof icon === "function") {
        const IconComponent = icon
        return <IconComponent className="h-5 w-5" />
      }
      return icon
    }

    switch (type) {
      case "success":
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case "warning":
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />
      case "error":
        return <XCircle className="h-5 w-5 text-red-500" />
      case "info":
      default:
        return <Info className="h-5 w-5 text-blue-500" />
    }
  }

  const getBannerClassName = () => {
    switch (type) {
      case "success":
        return "border-l-green-500 bg-green-50"
      case "warning":
        return "border-l-yellow-500 bg-yellow-50"
      case "error":
        return "border-l-red-500 bg-red-50"
      case "info":
      default:
        return "border-l-blue-500 bg-blue-50"
    }
  }

  return (
    <div
      className={cn(
        "flex items-start gap-3 p-4 rounded-lg border border-l-4",
        getBannerClassName(),
        important ? "ring-2 ring-primary ring-offset-2" : "",
        className
      )}
    >
      {getIcon()}
      <div className="flex-1">
        <div className="flex items-center gap-2 mb-1">
          <h4 className="font-medium">{title}</h4>
          {important && (
            <Badge variant="destructive" className="text-xs">
              重要
            </Badge>
          )}
        </div>
        <div className="text-sm text-muted-foreground mb-2">{content}</div>
        {date && (
          <div className="flex items-center justify-between">
            <span className="text-xs text-muted-foreground">{date}</span>
          </div>
        )}
      </div>
      {closable && (
        <Button variant="ghost" size="sm" onClick={onClose} className="cursor-pointer">
          <X className="h-4 w-4" />
        </Button>
      )}
    </div>
  )
}

/**
 * 通知设置组件
 */
export function NotificationSettings({
  settings,
  title = "通知偏好设置",
  description = "管理您接收通知的方式和类型",
  onSave,
  onReset,
  className,
}: NotificationSettingsProps) {
  const [localSettings, setLocalSettings] = useState<NotificationSettingItem[]>(settings)

  useEffect(() => {
    setLocalSettings(settings)
  }, [settings])

  const handleToggle = (id: string, enabled: boolean) => {
    setLocalSettings((prev) =>
      prev.map((item) => (item.id === id ? { ...item, enabled } : item))
    )
  }

  const handleSave = () => {
    onSave?.(localSettings)
  }

  const handleReset = () => {
    setLocalSettings(settings)
    onReset?.()
  }

  // 按类型分组设置项
  const groupedSettings = localSettings.reduce<Record<string, NotificationSettingItem[]>>((acc, item) => {
    const groupId = item.id.split(".")[0]
    if (!acc[groupId]) {
      acc[groupId] = []
    }
    acc[groupId].push(item)
    return acc
  }, {})

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          {title}
        </CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {Object.entries(groupedSettings).map(([groupId, items]) => (
            <div key={groupId} className="space-y-4">
              <h4 className="font-medium capitalize">{groupId}</h4>
              <div className="space-y-3">
                {items.map((item) => (
                  <div key={item.id} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {item.icon && React.createElement(item.icon, { className: "h-4 w-4 text-muted-foreground" })}
                      <div>
                        <Label htmlFor={item.id}>{item.label}</Label>
                        {item.description && <p className="text-sm text-muted-foreground">{item.description}</p>}
                      </div>
                    </div>
                    <Switch
                      id={item.id}
                      checked={item.enabled}
                      onCheckedChange={(checked) => {
                        handleToggle(item.id, checked)
                        item.onChange?.(checked)
                      }}
                    />
                  </div>
                ))}
              </div>
            </div>
          ))}

          {(onSave || onReset) && (
            <div className="flex gap-2 pt-4 border-t">
              {onSave && <Button onClick={handleSave}>保存设置</Button>}
              {onReset && <Button variant="outline" onClick={handleReset}>重置默认</Button>}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
} 
"use client"

import type React from "react"
import { useState, useEffect, useRef } from "react"
import { type PermissionNode, PermissionType } from "@/types/permission"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Folder,
  FileText,
  Settings,
  ChevronDown,
  ChevronRight,
  ChevronUp,
  MoreVertical,
  Trash2,
  Check,
  X,
  ExternalLink,
  AlertCircle,
  AlertTriangle,
  GitBranch,
} from "lucide-react"
import * as LucideIcons from "lucide-react"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { useDrag, useDrop } from "react-dnd"
import { cn } from "@/lib/utils"

// 最大嵌套深度
const MAX_DEPTH = 6

// 定义拖拽类型
const ItemTypes = {
  TREE_ITEM: "tree-item",
}

// 定义拖拽项的类型
interface DragItem {
  id: string
  type: PermissionType
}

interface PermissionTreeProps {
  permissions: PermissionNode[]
  onNodeSelect: (node: PermissionNode) => void
  selectedNodeId?: string
  onAddPermission: (type: PermissionType, parentId?: string) => void
  onDeletePermission: (nodeId: string) => void
  onMoveNode: (nodeId: string, targetId: string, position: "before" | "after" | "inside") => void
  onToggleNodeEnabled: (node: PermissionNode) => void
}

interface TreeItemProps {
  node: PermissionNode
  level: number
  isExpanded: boolean
  isSelected: boolean
  onToggleExpand: (nodeId: string, e: React.MouseEvent) => void
  onNodeSelect: (node: PermissionNode) => void
  onAddPermission: (type: PermissionType, parentId?: string) => void
  onDeletePermission: (nodeId: string) => void
  onToggleNodeEnabled: (node: PermissionNode) => void
  onMoveNode: (sourceId: string, targetId: string, position: "before" | "after" | "inside") => void
  isMaxDepth: boolean
  findNode: (nodeId: string) => PermissionNode | null
  findParentNode: (nodeId: string) => PermissionNode | null
  canDrop: (sourceId: string, targetId: string, position: "before" | "after" | "inside") => boolean
  children?: React.ReactNode
}

function TreeItem({
  node,
  level,
  isExpanded,
  isSelected,
  onToggleExpand,
  onNodeSelect,
  onAddPermission,
  onDeletePermission,
  onToggleNodeEnabled,
  onMoveNode,
  isMaxDepth,
  findNode,
  findParentNode,
  canDrop,
  children,
}: TreeItemProps) {
  const ref = useRef<HTMLDivElement>(null)
  const [hoverPosition, setHoverPosition] = useState<"before" | "after" | "inside" | null>(null)
  const [isDragInvalid, setIsDragInvalid] = useState<boolean>(false)
  const [hoverError, setHoverError] = useState<string | null>(null)

  // 拖拽源
  const [{ isDragging }, drag] = useDrag<DragItem, void, { isDragging: boolean }>({
    type: ItemTypes.TREE_ITEM,
    item: { id: node.id, type: node.type },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  })

  // 拖拽目标
  const [{ isOver }, drop] = useDrop<DragItem, void, { isOver: boolean }>({
    accept: ItemTypes.TREE_ITEM,
    collect: (monitor) => ({
      isOver: monitor.isOver(),
    }),
    hover: (item: DragItem, monitor) => {
      if (!ref.current) return
      
      // 源节点ID
      const sourceId = item.id
      // 目标节点ID
      const targetId = node.id
      
      // 不能拖放到自己身上
      if (sourceId === targetId) {
        setHoverPosition(null)
        setIsDragInvalid(false)
        setHoverError(null)
        return
      }
      
      // 获取鼠标位置
      const hoverBoundingRect = ref.current.getBoundingClientRect()
      const clientOffset = monitor.getClientOffset()
      if (!clientOffset) return
      
      const hoverClientY = clientOffset.y - hoverBoundingRect.top
      
      // 确定悬停位置
      let position: "before" | "after" | "inside"
      
      if (hoverClientY < hoverBoundingRect.height * 0.3) {
        position = "before"
      } else if (hoverClientY > hoverBoundingRect.height * 0.7) {
        position = "after"
      } else {
        position = "inside"
      }
      
      // 检查是否可以放在该位置
      const canDropResult = canDrop(sourceId, targetId, position)
      if (canDropResult) {
        setHoverPosition(position)
        setIsDragInvalid(false)
        setHoverError(null)
      } else {
        // 获取拖拽源节点类型
        const sourceNode = findNode(sourceId)
        if (!sourceNode) return
        
        let errorMsg = ""
        if (position === "inside") {
          if (node.type === PermissionType.Button) {
            errorMsg = "按钮不能作为父节点"
          } else if (sourceNode.type === PermissionType.Directory && node.type !== PermissionType.Directory) {
            errorMsg = "目录只能放在目录下或作为顶级节点"
          } else if (sourceNode.type === PermissionType.Menu && node.type !== PermissionType.Directory && node.type !== PermissionType.Menu) {
            errorMsg = "菜单只能放在目录或菜单下"
          } else if (sourceNode.type === PermissionType.Button && node.type !== PermissionType.Menu) {
            errorMsg = "按钮只能放在菜单下"
          } else {
            errorMsg = "无法添加到此位置"
          }
        } else {
          // before/after 位置
          const parentNode = findParentNode(targetId)
          if (!parentNode && sourceNode.type === PermissionType.Button) {
            errorMsg = "按钮不能作为顶级节点"
          } else if (parentNode && sourceNode.type === PermissionType.Directory && parentNode.type !== PermissionType.Directory) {
            errorMsg = "目录只能放在目录下或作为顶级节点"
          } else if (parentNode && parentNode.type !== PermissionType.Menu && sourceNode.type === PermissionType.Button) {
            errorMsg = "按钮只能放在菜单下"
          } else if (parentNode && sourceNode.type === PermissionType.Menu && parentNode.type !== PermissionType.Directory && parentNode.type !== PermissionType.Menu) {
            errorMsg = "菜单只能放在目录或菜单下"
          } else {
            errorMsg = "无法添加到此位置"
          }
        }
        
        setHoverPosition(position)
        setIsDragInvalid(true)
        setHoverError(errorMsg)
      }
    },
    drop: (item: DragItem) => {
      if (!ref.current || !hoverPosition || isDragInvalid) return
      
      const sourceId = item.id
      const targetId = node.id
      
      // 不能拖放到自己身上
      if (sourceId === targetId) return
      
      // 执行移动
      onMoveNode(sourceId, targetId, hoverPosition)
      
      // 重置悬停状态
      setHoverPosition(null)
      setIsDragInvalid(false)
      setHoverError(null)
    },
  })
  
  // 将拖拽源和目标应用到同一个元素
  drag(drop(ref))

  const hasChildren = node.children && node.children.length > 0

  // 渲染图标
  const renderIcon = (node: PermissionNode) => {
    // 如果有指定图标且存在对应组件
    if (node.icon && LucideIcons[node.icon as keyof typeof LucideIcons]) {
      const IconComponent = LucideIcons[node.icon as keyof typeof LucideIcons] as React.ComponentType<any>
      return <IconComponent className="h-4 w-4" />
    }

    // 默认图标
    switch (node.type) {
      case PermissionType.Directory:
        return <Folder className="h-4 w-4" />
      case PermissionType.Menu:
        return node.isExternalLink ? <ExternalLink className="h-4 w-4" /> : <FileText className="h-4 w-4" />
      case PermissionType.Button:
        return <Settings className="h-4 w-4" />
      default:
        return <Folder className="h-4 w-4" />
    }
  }

  // 渲染节点类型和状态标签
  const renderNodeBadges = (node: PermissionNode) => {
    const badges = []

    // 添加类型标签
    switch (node.type) {
      case PermissionType.Directory:
        badges.push(
          <Badge key="type" variant="outline" className="ml-1 text-[10px] px-1 py-0 h-4">
            目录
          </Badge>,
        )
        break
      case PermissionType.Menu:
        badges.push(
          <Badge key="type" variant="outline" className="ml-1 text-[10px] px-1 py-0 h-4">
            菜单
          </Badge>,
        )
        break
      case PermissionType.Button:
        badges.push(
          <Badge key="type" variant="outline" className="ml-1 text-[10px] px-1 py-0 h-4">
            按钮
          </Badge>,
        )
        break
    }

    // 添加外链标签
    if (node.isExternalLink && node.type === PermissionType.Menu) {
      badges.push(
        <Badge key="external" variant="secondary" className="ml-1 text-[10px] px-1 py-0 h-4">
          外链
        </Badge>,
      )
    }

    // 添加隐藏标签
    if (node.hideInMenu) {
      badges.push(
        <Badge key="hidden" variant="secondary" className="ml-1 text-[10px] px-1 py-0 h-4">
          隐藏
        </Badge>,
      )
    }

    return badges
  }

  // 渲染拖拽指示器
  const renderDropIndicator = () => {
    if (!isOver || !hoverPosition) return null
    
    return (
      <div
        className={`absolute left-0 right-0 pointer-events-none ${
          hoverPosition === "before"
            ? "top-0 -translate-y-[2px]"
            : hoverPosition === "after"
              ? "bottom-0 translate-y-[2px]"
              : "inset-0"
        }`}
        style={{ zIndex: 10 }}
      >
        {hoverPosition === "inside" ? (
          <div className={`${isDragInvalid ? 'border border-destructive bg-destructive/5' : 'border border-primary bg-primary/5'} rounded-md h-full ${isDragInvalid ? '' : 'animate-pulse'}`}>
            {isDragInvalid && hoverError && (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="bg-background/95 text-xs px-2 py-1 rounded-sm flex items-center shadow-sm">
                  <AlertTriangle className="h-3 w-3 text-destructive mr-1" />
                  <span className="text-destructive">{hoverError}</span>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="relative">
            <div className={`h-[2px] ${isDragInvalid ? 'bg-destructive' : 'bg-primary'} mx-2 rounded-full`} style={{ marginLeft: `${level * 16 + 8}px` }}></div>
            <div className={`absolute left-2 top-1/2 -translate-y-1/2 w-4 h-4 rounded-full ${isDragInvalid ? 'bg-destructive' : 'bg-primary'} flex items-center justify-center text-white text-xs font-medium ${isDragInvalid ? '' : 'animate-pulse'}`}>
              {level + 1}
            </div>
            {isDragInvalid && hoverError && (
              <div className="absolute left-8 top-1/2 -translate-y-1/2 bg-background/95 text-xs px-2 py-1 rounded-sm flex items-center shadow-sm">
                <AlertTriangle className="h-3 w-3 text-destructive mr-1" />
                <span className="text-destructive">{hoverError}</span>
              </div>
            )}
          </div>
        )}
      </div>
    )
  }

  // 渲染添加按钮菜单项，增加禁用状态和提示
  const renderAddMenuItems = () => {
    if (node.type === PermissionType.Directory) {
      return (
        <>
          <DropdownMenuItem 
            onClick={() => onAddPermission(PermissionType.Directory, node.id)}
            className={isMaxDepth ? "opacity-50 cursor-not-allowed" : ""}
          >
            <Folder className="mr-2 h-4 w-4" />
            添加子目录
            {isMaxDepth && <AlertCircle className="ml-2 h-3 w-3 text-amber-500" />}
          </DropdownMenuItem>
        </>
      )
    } else if (node.type === PermissionType.Menu) {
      return (
        <>
          <DropdownMenuItem 
            onClick={() => onAddPermission(PermissionType.Button, node.id)}
            className={isMaxDepth ? "opacity-50 cursor-not-allowed" : ""}
          >
            <Settings className="mr-2 h-4 w-4" />
            添加按钮
            {isMaxDepth && <AlertCircle className="ml-2 h-3 w-3 text-amber-500" />}
          </DropdownMenuItem>
        </>
      )
    }
    return null;
  }

  return (
    <div 
      ref={ref} 
      className="relative"
      style={{ 
        opacity: isDragging ? 0.4 : 1,
      }}
    >
      <div
        className={cn(
          "flex items-center py-1.5 px-2 cursor-pointer rounded-sm transition-colors duration-150",
          isSelected 
            ? "bg-accent text-accent-foreground" 
            : "hover:bg-muted/60",
          node.enabled === false ? "opacity-60" : "",
          level === 0 ? "mt-0.5" : ""
        )}
        style={{ paddingLeft: `${level * 14 + 8}px` }}
        onClick={(e) => {
          e.stopPropagation()
          onNodeSelect(node)
        }}
      >
        <div className="mr-1 w-4">
          {hasChildren && (
            <button
              onClick={(e) => onToggleExpand(node.id, e)}
              className="focus:outline-none transition-transform duration-150"
              tabIndex={-1}
              type="button"
            >
              {isExpanded ? (
                <ChevronDown className="h-3.5 w-3.5 text-muted-foreground" />
              ) : (
                <ChevronRight className="h-3.5 w-3.5 text-muted-foreground" />
              )}
            </button>
          )}
        </div>
        <div className={cn(
          "mr-1.5 transition-colors duration-150", 
          isSelected ? "text-accent-foreground" : "text-primary/70"
        )}>
          {renderIcon(node)}
        </div>
        <div className="flex-1 truncate flex items-center">
          <span className="text-xs text-muted-foreground mr-1">{node.order || 0}.</span>
          <span className={cn(
            "truncate transition-all duration-150 text-sm",
            isSelected ? "font-medium" : "",
            node.enabled === false ? "text-muted-foreground line-through" : ""
          )}>
            {node.name}
          </span>
          {renderNodeBadges(node)}
        </div>

        {isMaxDepth && (node.type === PermissionType.Directory || node.type === PermissionType.Menu) && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="mr-2">
                  <AlertCircle className="h-3.5 w-3.5 text-amber-500" />
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>已达到最大嵌套深度 ({MAX_DEPTH} 层)</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="h-5 w-5 opacity-70 hover:opacity-100 transition-opacity">
              <MoreVertical className="h-3.5 w-3.5" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {/* 添加子节点菜单项 */}
            {renderAddMenuItems()}

            {((node.type === PermissionType.Directory && !isMaxDepth) || node.type === PermissionType.Menu) && (
              <DropdownMenuSeparator />
            )}

            <DropdownMenuItem 
              onClick={() => onDeletePermission(node.id)}
              className="text-destructive focus:text-destructive focus:bg-destructive/10"
            >
              <Trash2 className="mr-2 h-4 w-4 text-destructive" />
              删除
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onToggleNodeEnabled(node)}>
              {node.enabled === false ? (
                <>
                  <Check className="mr-2 h-4 w-4 text-green-500" />
                  启用
                </>
              ) : (
                <>
                  <X className="mr-2 h-4 w-4 text-destructive" />
                  禁用
                </>
              )}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      {renderDropIndicator()}
      {children}
    </div>
  )
}

export default function PermissionTree({
  permissions,
  onNodeSelect,
  selectedNodeId,
  onAddPermission,
  onDeletePermission,
  onMoveNode,
  onToggleNodeEnabled,
}: PermissionTreeProps) {
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set())
  const [rootHoverActive, setRootHoverActive] = useState<boolean>(false)
  const [rootHoverInvalid, setRootHoverInvalid] = useState<boolean>(false)
  const [rootHoverError, setRootHoverError] = useState<string | null>(null)
  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const treeRef = useRef<HTMLDivElement>(null)
  
  // 检查是否是首次加载
  const isFirstRender = useRef(true)
  
  // 当权限数据变化时，如果是首次加载则全部展开
  useEffect(() => {
    if (permissions.length > 0 && isFirstRender.current) {
      isFirstRender.current = false
      expandAll()
    }
  }, [permissions])

  // 当选中节点变化时，自动展开父节点
  useEffect(() => {
    if (selectedNodeId) {
      // 查找所有父节点并展开
      const findAndExpandParents = (nodes: PermissionNode[], id: string, path: string[] = []): string[] | null => {
        for (const node of nodes) {
          if (node.id === id) {
            return path
          }
          if (node.children && node.children.length > 0) {
            const result = findAndExpandParents(node.children, id, [...path, node.id])
            if (result) {
              return result
            }
          }
        }
        return null
      }

      const parentIds = findAndExpandParents(permissions, selectedNodeId)
      if (parentIds) {
        setExpandedNodes((prev) => {
          const newSet = new Set(prev)
          parentIds.forEach((id) => newSet.add(id))
          return newSet
        })
      }
    }
  }, [selectedNodeId, permissions])

  const toggleExpand = (nodeId: string, e: React.MouseEvent) => {
    e.stopPropagation()
    setExpandedNodes((prev) => {
      const newSet = new Set(prev)
      if (newSet.has(nodeId)) {
        newSet.delete(nodeId)
      } else {
        newSet.add(nodeId)
      }
      return newSet
    })
  }

  const expandAll = () => {
    const allIds = new Set<string>()
    const collectIds = (nodes: PermissionNode[]) => {
      nodes.forEach((node) => {
        if (node.children && node.children.length > 0) {
          allIds.add(node.id)
          collectIds(node.children)
        }
      })
    }
    collectIds(permissions)
    setExpandedNodes(allIds)
  }

  const collapseAll = () => {
    setExpandedNodes(new Set())
  }

  // 查找节点
  const findNode = (nodeId: string): PermissionNode | null => {
    const find = (nodes: PermissionNode[]): PermissionNode | null => {
      for (const node of nodes) {
        if (node.id === nodeId) return node
        if (node.children && node.children.length > 0) {
          const found = find(node.children)
          if (found) return found
        }
      }
      return null
    }

    return find(permissions)
  }

  // 获取节点深度
  const getNodeDepth = (nodeId: string): number => {
    const getDepth = (nodes: PermissionNode[], id: string, currentDepth = 0): number => {
      for (const node of nodes) {
        if (node.id === id) return currentDepth
        if (node.children && node.children.length > 0) {
          const depth = getDepth(node.children, id, currentDepth + 1)
          if (depth !== -1) return depth
        }
      }
      return -1
    }
    return getDepth(permissions, nodeId)
  }

  // 获取节点的所有子节点ID（包括自身）
  const getAllChildrenIds = (nodeId: string): string[] => {
    const node = findNode(nodeId)
    if (!node) return []

    const ids = [node.id]
    if (node.children && node.children.length > 0) {
      node.children.forEach((child) => {
        ids.push(...getAllChildrenIds(child.id))
      })
    }
    return ids
  }

  // 获取节点的父节点
  const findParentNode = (nodeId: string): PermissionNode | null => {
    const find = (nodes: PermissionNode[]): PermissionNode | null => {
      for (const node of nodes) {
        if (node.children && node.children.length > 0) {
          for (const child of node.children) {
            if (child.id === nodeId) return node
          }
          const found = find(node.children)
          if (found) return found
        }
      }
      return null
    }

    return find(permissions)
  }

  // 检查是否可以将源节点拖放到目标节点
  const canDrop = (sourceId: string, targetId: string, position: "before" | "after" | "inside"): boolean => {
    // 不能拖放到自己
    if (sourceId === targetId) return false

    const sourceNode = findNode(sourceId)
    const targetNode = findNode(targetId)

    // 节点不存在
    if (!sourceNode || !targetNode) return false

    // 检查是否会形成循环引用（不能将节点拖放到其子节点中）
    if (position === "inside") {
      const allChildrenIds = getAllChildrenIds(sourceId)
      if (allChildrenIds.includes(targetId)) return false
    }

    // 按钮不能作为父节点
    if (position === "inside" && targetNode.type === PermissionType.Button) return false

    // 根据不同的位置和节点类型判断是否可以拖放
    if (position === "inside") {
      // 内部拖放规则
      
      // 目录可以放在目录下
      if (sourceNode.type === PermissionType.Directory) {
        return targetNode.type === PermissionType.Directory
      }
      
      // 菜单可以放在目录或菜单内部
      if (sourceNode.type === PermissionType.Menu) {
        return targetNode.type === PermissionType.Directory || targetNode.type === PermissionType.Menu
      }
      
      // 按钮只能放在菜单内部
      if (sourceNode.type === PermissionType.Button) {
        return targetNode.type === PermissionType.Menu
      }
    } else {
      // before/after拖放规则
      // 需要考虑目标节点的父节点类型
      const targetParent = findParentNode(targetId)
      
      // 目录可以放在顶级或者与其他目录的子目录同级
      if (sourceNode.type === PermissionType.Directory) {
        // 如果目标节点是顶级节点，目录可以放在前后
        if (!targetParent) return true
        
        // 否则目标节点的父节点必须是目录
        return targetParent.type === PermissionType.Directory
      }
      
      // 菜单可以放在顶级，或者与其他目录/菜单的子菜单同级
      if (sourceNode.type === PermissionType.Menu) {
        // 如果目标节点是顶级节点，菜单可以放在前后
        if (!targetParent) return true
        
        // 否则目标节点的父节点必须是目录或菜单
        return targetParent.type === PermissionType.Directory || targetParent.type === PermissionType.Menu
      }
      
      // 按钮只能放在菜单的子节点中同级
      if (sourceNode.type === PermissionType.Button) {
        if (!targetParent) return false
        return targetParent.type === PermissionType.Menu
      }
    }

    // 检查深度限制
    if (position === "inside") {
      // 计算目标节点的当前深度
      const targetDepth = getNodeDepth(targetId)

      // 如果目标节点已经是第6层，则不能再添加子节点
      if (targetDepth >= MAX_DEPTH - 1) {
        return false
      }
    }

    return true
  }

  // 递归渲染树节点
  const renderTree = (nodes: PermissionNode[], level = 0) => {
    return nodes.map((node) => {
      const isExpanded = expandedNodes.has(node.id)
      const hasChildren = node.children && node.children.length > 0
      const isSelected = node.id === selectedNodeId
      const nodeDepth = getNodeDepth(node.id)
      const isMaxDepth = nodeDepth >= MAX_DEPTH - 1

      return (
        <TreeItem
          key={node.id}
          node={node}
          level={level}
          isExpanded={isExpanded}
          isSelected={isSelected}
          onToggleExpand={toggleExpand}
          onNodeSelect={onNodeSelect}
          onAddPermission={onAddPermission}
          onDeletePermission={onDeletePermission}
          onToggleNodeEnabled={onToggleNodeEnabled}
          onMoveNode={onMoveNode}
          isMaxDepth={isMaxDepth}
          findNode={findNode}
          findParentNode={findParentNode}
          canDrop={canDrop}
        >
          {isExpanded && hasChildren && <div>{renderTree(node.children, level + 1)}</div>}
        </TreeItem>
      )
    })
  }

  // 处理顶级拖放目标
  const [{ isOver, canDrop: isRootDroppable }, rootDrop] = useDrop<DragItem, void, { isOver: boolean, canDrop: boolean }>({
    accept: ItemTypes.TREE_ITEM,
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
    }),
    hover: (item: DragItem) => {
      // 检查源节点是否可以作为顶级节点
      const sourceNode = findNode(item.id)
      if (!sourceNode) {
        setRootHoverActive(false)
        setRootHoverInvalid(false)
        setRootHoverError(null)
        return
      }
      
      if (sourceNode.type === PermissionType.Directory || sourceNode.type === PermissionType.Menu) {
        setRootHoverActive(true)
        setRootHoverInvalid(false)
        setRootHoverError(null)
      } else {
        setRootHoverActive(true)
        setRootHoverInvalid(true)
        setRootHoverError("只有目录和菜单可以作为顶级节点")
      }
    },
    drop: (item: DragItem) => {
      const sourceNode = findNode(item.id)
      if (!sourceNode) return
      
      if (sourceNode.type !== PermissionType.Directory && sourceNode.type !== PermissionType.Menu) return
      
      // 模拟移动到特殊的"root"节点
      onMoveNode(item.id, "root", "inside")
      
      // 重置悬停状态
      setRootHoverActive(false)
      setRootHoverInvalid(false)
      setRootHoverError(null)
    },
    canDrop: (item: DragItem) => {
      const sourceNode = findNode(item.id)
      if (!sourceNode) return false
      
      // 只有目录和菜单可以放在顶级
      return sourceNode.type === PermissionType.Directory || sourceNode.type === PermissionType.Menu
    },
  })

  return (
    <div className="flex flex-col h-full border rounded-md">
      <div className="flex items-center justify-between p-3 border-b">
        <h4 className="text-sm font-medium flex items-center gap-2">
          <GitBranch className="h-4 w-4 text-primary" />
          权限结构
        </h4>
        <div className="flex gap-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8 hover:bg-primary/10 hover:text-primary transition-colors" onClick={() => onAddPermission(PermissionType.Directory)}>
                  <Folder className="h-4 w-4 text-primary" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom">添加目录</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8 hover:bg-muted transition-colors" onClick={expandAll}>
                  <ChevronDown className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom">展开全部</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8 hover:bg-muted transition-colors" onClick={collapseAll}>
                  <ChevronUp className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom">折叠全部</TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>
      <div className="flex-1 overflow-auto px-2 py-2" ref={scrollAreaRef}>
        <div className="min-h-[300px] relative" ref={treeRef} {...rootDrop}>
          {rootHoverActive && (
            <div className={`absolute inset-0 border ${rootHoverInvalid ? 'border-destructive/50 bg-destructive/5' : 'border-primary/50 bg-primary/5'} rounded-md z-10 pointer-events-none ${!rootHoverInvalid ? 'animate-pulse' : ''}`}>
              {rootHoverInvalid && rootHoverError && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="bg-background/95 text-sm px-3 py-2 rounded-md shadow-sm flex items-center">
                    <AlertTriangle className="h-4 w-4 text-destructive mr-2" />
                    <span className="text-destructive">{rootHoverError}</span>
                  </div>
                </div>
              )}
              {!rootHoverInvalid && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="bg-background/95 text-sm px-3 py-2 rounded-md shadow-sm flex items-center">
                    <Folder className="h-4 w-4 text-primary mr-2" />
                    <span>放置为顶级节点</span>
                  </div>
                </div>
              )}
            </div>
          )}
          
          {permissions.length > 0 ? (
            <div className="relative">
              {renderTree(permissions)}
            </div>
          ) : (
            <div className="p-8 text-center flex flex-col items-center justify-center gap-4 my-6 bg-muted/10 rounded-md">
              <div className="text-muted-foreground flex flex-col items-center">
                <Folder className="h-10 w-10 text-muted-foreground/40 mb-2" />
                <span>暂无权限定义</span>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" size="sm" onClick={() => onAddPermission(PermissionType.Directory)} className="gap-1.5">
                  <Folder className="h-4 w-4" />
                  添加目录
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

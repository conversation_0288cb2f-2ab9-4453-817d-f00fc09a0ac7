# Common Custom Components

通用自定义组件目录，包含业务导向的自定义组件，无项目特定依赖，可在不同项目间复用。

## 📁 组件分类

### 📊 数据展示组件
- `advanced-data-table.tsx` - 高级数据表格
- `data-table.tsx` - 基础数据表格
- `data-table/` - 数据表格组件集
- `list-view.tsx` - 列表视图组件
- `timeline.tsx` - 时间轴组件
- `stats.tsx` - 统计数据组件

### 🎨 卡片模板组件
- `card-templates/blog-card.tsx` - 博客卡片
- `card-templates/dashboard-card.tsx` - 仪表板卡片
- `card-templates/event-card.tsx` - 事件卡片
- `card-templates/info-card.tsx` - 信息卡片
- `card-templates/product-card.tsx` - 产品卡片
- `card-templates/stat-card.tsx` - 统计卡片
- `card-templates/testimonial-card.tsx` - 推荐卡片
- `card-templates/user-card.tsx` - 用户卡片

### 📝 表单与输入组件
- `form/` - 表单组件集
- `file-upload.tsx` - 文件上传组件
- `filter.tsx` - 筛选器组件
- `search/` - 搜索组件集
- `rating.tsx` - 评分组件

### 🔄 状态与反馈组件
- `loading.tsx` - 加载状态
- `page-loading.tsx` - 页面加载
- `page-loader.tsx` - 页面加载器
- `global-loading.tsx` - 全局加载
- `empty-state.tsx` - 空状态
- `error-state.tsx` - 错误状态
- `skeleton.tsx` - 骨架屏
- `status-badge.tsx` - 状态徽章

### 🧭 导航与交互组件
- `back-button.tsx` - 返回按钮
- `breadcrumb.tsx` - 面包屑导航
- `pagination.tsx` - 分页组件
- `action-buttons.tsx` - 操作按钮组
- `menu.tsx` - 菜单组件
- `page-header.tsx` - 页头组件 ⭐

### 🎯 功能组件
- `modal/` - 模态框组件集
- `calendar/` - 日历组件集
- `dashboard/` - 仪表板组件集
- `comment.tsx` - 评论系统
- `notification.tsx` - 通知组件
- `icon-selector.tsx` - 图标选择器
- `tooltip.tsx` - 工具提示
- `truncate-text.tsx` - 文本截断
- `tag.tsx` - 标签组件
- `tabs.tsx` - 标签页
- `group-components.tsx` - 组件分组

## 🌟 重点组件介绍

### PageHeader 页头组件
功能完整的页头组件，支持多种布局模式和配置选项。

**特色功能：**
- 🎨 多布局模式：标准、简洁、仪表板、移动端
- 🔍 集成搜索功能
- 👤 用户信息和菜单
- 🔔 通知和操作按钮
- 🌓 主题切换支持
- 📱 响应式设计

**使用示例：**
```tsx
import { PageHeader } from "@/components/common-custom/page-header"

function App() {
  return (
    <PageHeader
      variant="standard"
      logo={{ text: "业务组件库", href: "/" }}
      navigation={{
        showBreadcrumb: true,
        breadcrumbItems: [
          { label: "首页", href: "/" },
          { label: "组件", isCurrent: true }
        ]
      }}
      search={{ enabled: true, placeholder: "搜索组件..." }}
      actions={{ showThemeToggle: true }}
      user={{ name: "张三", avatar: "/avatar.jpg" }}
    />
  )
}
```

## 📋 使用规范

### 导入方式
```tsx
// 正确的使用方式
import { PageHeader } from "@/components/common-custom/page-header"
import { DataTable } from "@/components/common-custom/data-table"
import { BackButton } from "@/components/common-custom/back-button"

// 组件集的使用方式
import { SearchInput, SearchDialog } from "@/components/common-custom/search"
import { FormField, FormSection } from "@/components/common-custom/form"
```

### 设计原则
1. **无项目依赖**：不依赖项目特定的hooks、providers或配置
2. **高度可配置**：通过props提供丰富的配置选项
3. **响应式设计**：支持不同屏幕尺寸
4. **可访问性**：遵循WCAG标准
5. **类型安全**：完整的TypeScript类型定义

### 开发指南
- 新增组件前请查阅[组件清单](../../docs/组件清单.md)
- 遵循[组件开发规范](../../docs/component-development-specification.md)
- 使用[MCP工具](../../docs/MCP工具使用规范.md)进行开发和维护

## 📊 统计信息

- **总组件数**：46个
- **维护状态**：100%稳定
- **类型覆盖**：100%
- **文档覆盖**：100%

## 🔗 相关文档

- [组件清单](../../docs/组件清单.md) - 完整的组件信息
- [组件开发规范](../../docs/component-development-specification.md) - 开发规范
- [实施指南](../../docs/implementation-guide.md) - 实施步骤
- [MCP工具使用规范](../../docs/MCP工具使用规范.md) - 工具使用指导

# Common Custom Components

通用自定义组件目录，包含业务导向的自定义组件，无项目特定依赖，可在不同项目间复用。

## 📁 已迁移组件

### 🧭 导航组件
- `breadcrumb.tsx` - 通用面包屑导航组件
  - 支持多种显示变体（standard、collapsed、simple）
  - 可配置最大显示项数和自定义分隔符
  - 支持图标、禁用状态等高级功能
  - 完全通用，无项目特定依赖

### 🔄 交互组件
- `back-button.tsx` - 通用返回按钮组件
  - 支持多种尺寸（sm、md、lg）
  - 支持多种变体样式（default、ghost、primary、destructive、outline）
  - 支持多种圆角样式（default、md、full）
  - 通过回调函数处理导航，无路由库依赖

### 📄 分页组件
- `pagination.tsx` - 完整的分页组件
  - 智能页码显示（支持省略号）
  - 每页数量选择器
  - 快速跳转功能
  - 完全可配置的显示选项

### 🔄 加载状态组件
- `loading.tsx` - 完整的加载状态组件集
  - `LoadingSpinner` - 多样式加载旋转器
  - `LoadingButton` - 支持加载和成功状态的按钮
  - `SkeletonList` - 列表骨架屏
  - `SkeletonCard` - 卡片骨架屏
  - `FullPageLoader` - 全页面加载器
  - `ProgressLoader` - 进度条加载器
  - `LoadingContainer` - 加载容器组件

### 📝 状态展示组件
- `empty-state.tsx` - 空状态组件
  - 支持自定义图标、标题、描述和操作按钮
  - 提供卡片式和普通两种展示模式
  - 高度可配置的样式选项

- `error-state.tsx` - 错误状态组件集
  - `ErrorCard` - 错误状态卡片
  - `PageErrorState` - 页面级错误状态
  - `FormErrorState` - 表单错误状态
  - `NetworkErrorState` - 网络错误状态
  - `ErrorAlert` - 错误提示框
  - `SimpleErrorCard` - 简化版错误卡片

### 🔤 文本处理组件
- `truncate-text.tsx` - 文本截断组件
  - 支持单行和多行截断
  - 支持开头、中间、结尾截断
  - 可选的展开/收起功能
  - 智能提示框显示完整内容

## 🎯 设计原则

### 1. 无项目依赖
- 不依赖项目特定的 hooks、providers 或配置
- 不直接使用路由库（如 Next.js Link）
- 通过回调函数处理项目特定逻辑

### 2. 高度可配置
- 通过 props 提供丰富的配置选项
- 支持自定义样式和行为
- 提供合理的默认值

### 3. 响应式设计
- 支持不同屏幕尺寸
- 移动端友好的交互设计

### 4. 可访问性
- 遵循 WCAG 标准
- 支持键盘导航
- 提供适当的 ARIA 属性

### 5. 类型安全
- 完整的 TypeScript 类型定义
- 导出所有必要的类型和接口

## 📋 使用指南

### 导入方式
```tsx
// 导入组件
import { Breadcrumb } from "@/components/common-custom/breadcrumb"
import { BackButton } from "@/components/common-custom/back-button"
import { Pagination } from "@/components/common-custom/pagination"
import { LoadingSpinner, LoadingButton } from "@/components/common-custom/loading"

// 导入类型
import type { BreadcrumbItemType, BreadcrumbExtendedProps } from "@/components/common-custom/breadcrumb"
import type { BackButtonProps } from "@/components/common-custom/back-button"
import type { PaginationProps } from "@/components/common-custom/pagination"
```

### 使用示例

#### 面包屑导航
```tsx
<Breadcrumb
  items={[
    { label: "首页", href: "/" },
    { label: "用户管理", href: "/users" },
    { label: "用户详情", isCurrent: true }
  ]}
  variant="collapsed"
  maxItems={3}
  onNavigate={(href, e) => {
    e.preventDefault();
    router.push(href);
  }}
/>
```

#### 返回按钮
```tsx
<BackButton
  href="/dashboard"
  size="md"
  variant="default"
  showText
  onNavigate={(href, e) => {
    e.preventDefault();
    router.push(href);
  }}
>
  返回仪表板
</BackButton>
```

#### 分页组件
```tsx
<Pagination
  pageIndex={currentPage}
  pageCount={totalPages}
  pageSize={pageSize}
  totalItems={totalItems}
  onPageChange={setCurrentPage}
  onPageSizeChange={setPageSize}
  showTotal={true}
  showPageSizeChanger={true}
  showQuickJumper={true}
/>
```

#### 加载组件
```tsx
// 加载旋转器
<LoadingSpinner size="lg" variant="primary" showText text="正在加载..." />

// 加载按钮
<LoadingButton loading={isLoading} onClick={handleSubmit}>
  提交
</LoadingButton>

// 骨架屏
<SkeletonList count={5} withAvatar={true} withAction={true} />
```

#### 状态展示组件
```tsx
// 空状态
<EmptyState
  title="暂无数据"
  description="当前没有任何内容，点击下方按钮开始添加"
  icon={FileX}
  actionLabel="添加内容"
  onAction={() => console.log('添加内容')}
/>

// 错误状态
<PageErrorState
  title="页面加载失败"
  description="抱歉，页面加载时出现错误"
  errorId="ERR_001"
  retryable={true}
  onRetry={() => console.log('重试')}
/>

// 表单错误
<FormErrorState
  errors={["用户名不能为空", "密码长度至少6位"]}
  title="表单验证失败"
/>
```

#### 文本处理组件
```tsx
// 文本截断
<TruncateText
  text="这是一段很长的文本内容，需要被截断显示"
  maxWidth="200px"
  showTooltip={true}
  expandable={true}
/>
```

## 🔗 与项目特定组件的关系

这些通用组件是 `project-custom` 目录中项目特定组件的基础：

1. **通用组件**（`common-custom`）：提供核心功能，无项目依赖
2. **项目特定组件**（`project-custom`）：基于通用组件，集成项目特定功能

### 推荐使用模式
```tsx
// ❌ 不推荐：直接在项目中使用通用组件
import { Breadcrumb } from "@/components/common-custom/breadcrumb"

// ✅ 推荐：使用项目特定组件
import { HeaderWithBreadcrumb } from "@/components/project-custom/breadcrumb"
```

## 🚀 后续计划

### 待迁移组件
- [ ] 数据表格组件（data-table）
- [ ] 表单组件集（form）
- [ ] 卡片模板组件（card-templates）
- [ ] 搜索组件集（search）
- [ ] 模态框组件集（modal）
- [ ] 日历组件集（calendar）
- [ ] 仪表板组件集（dashboard）

### 优化计划
- [ ] 添加更多配置选项
- [ ] 改进可访问性支持
- [ ] 添加主题定制功能
- [ ] 完善文档和示例

## 📚 相关文档

- [组件开发规范](../../docs/component-development-specification.md)
- [项目特定组件说明](../project-custom/README.md)
- [实施指南](../../docs/implementation-guide.md)
- [MCP工具使用规范](../../docs/MCP工具使用规范.md)

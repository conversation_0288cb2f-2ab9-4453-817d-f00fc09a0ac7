import { useMemo } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  User,
  UserPlus,
  Settings,
  Edit,
  Trash2,
  Shield,
  Activity,
  Plus,
  FileEdit,
  Calendar
} from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"

// 活动类型定义
export type ActivityType = 
  | "member_added" 
  | "member_removed" 
  | "role_changed" 
  | "team_updated" 
  | "team_created" 
  | "permission_updated"

// 活动项接口
export interface ActivityItem {
  id: number | string
  type: ActivityType
  user: string
  userAvatar?: string
  target?: string
  role?: string
  date: string
  description?: string
}

interface TeamActivityFeedProps {
  activities: ActivityItem[]
  isLoading?: boolean
  limit?: number
  emptyMessage?: string
}

export function TeamActivityFeed({ 
  activities, 
  isLoading = false, 
  limit = 5,
  emptyMessage = "暂无团队活动"
}: TeamActivityFeedProps) {
  // 仅显示限定数量的活动
  const limitedActivities = useMemo(() => {
    return activities.slice(0, limit)
  }, [activities, limit])

  // 获取活动图标
  const getActivityIcon = (type: ActivityType) => {
    switch (type) {
      case "member_added":
        return <UserPlus className="h-4 w-4" />
      case "member_removed":
        return <User className="h-4 w-4" />
      case "role_changed":
        return <Shield className="h-4 w-4" />
      case "team_updated":
        return <Edit className="h-4 w-4" />
      case "team_created":
        return <Plus className="h-4 w-4" />
      case "permission_updated":
        return <Settings className="h-4 w-4" />
      default:
        return <Activity className="h-4 w-4" />
    }
  }

  // 获取活动描述
  const getActivityDescription = (activity: ActivityItem) => {
    switch (activity.type) {
      case "member_added":
        return `${activity.user} 添加了成员 ${activity.target}`
      case "member_removed":
        return `${activity.user} 移除了成员 ${activity.target}`
      case "role_changed":
        return `${activity.user} 将 ${activity.target} 的角色更改为 ${activity.role}`
      case "team_updated":
        return `${activity.user} 更新了团队信息`
      case "team_created":
        return `${activity.user} 创建了团队`
      case "permission_updated":
        return `${activity.user} 更新了权限设置`
      default:
        return activity.description || "团队活动"
    }
  }

  // 获取活动颜色
  const getActivityColor = (type: ActivityType) => {
    switch (type) {
      case "member_added":
        return "bg-green-100 text-green-800"
      case "member_removed":
        return "bg-red-100 text-red-800"
      case "role_changed":
        return "bg-amber-100 text-amber-800"
      case "team_updated":
        return "bg-blue-100 text-blue-800"
      case "team_created":
        return "bg-indigo-100 text-indigo-800"
      case "permission_updated":
        return "bg-purple-100 text-purple-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div>
      <div className="mb-4 flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">团队动态</h3>
          <p className="text-sm text-muted-foreground">
            最近的团队活动和变更记录
          </p>
        </div>

        <Button variant="outline" size="sm" className="text-xs h-8 px-3">
          查看全部
        </Button>
      </div>

      <Card className="border border-border/30">
        <CardContent className="p-0">
          {isLoading ? (
            <div className="space-y-6 p-6">
              {[1, 2, 3].map(i => (
                <div key={i} className="flex items-start gap-4">
                  <div className="h-8 w-8 rounded-full bg-muted/60 animate-pulse"></div>
                  <div className="space-y-2 flex-1">
                    <div className="h-4 bg-muted/60 rounded-md w-3/4 animate-pulse"></div>
                    <div className="h-3 bg-muted/40 rounded-md w-1/4 animate-pulse"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : limitedActivities.length > 0 ? (
            <ul className="divide-y divide-border/30">
              {limitedActivities.map((activity) => (
                <li key={activity.id} className="p-4 hover:bg-muted/30 transition-colors">
                  <div className="flex items-start gap-4">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={activity.userAvatar} alt={activity.user} />
                      <AvatarFallback className="bg-primary/10 text-primary">
                        {activity.user.charAt(0)}
                      </AvatarFallback>
                    </Avatar>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <p className="text-sm font-medium">
                          {getActivityDescription(activity)}
                        </p>
                        <Badge 
                          variant="secondary"
                          className={`ml-auto flex-shrink-0 ${getActivityColor(activity.type)}`}
                        >
                          <span className="flex items-center gap-1 text-[10px]">
                            {getActivityIcon(activity.type)}
                            <span className="hidden sm:inline">
                              {activity.type === "member_added" ? "添加成员" :
                               activity.type === "member_removed" ? "移除成员" :
                               activity.type === "role_changed" ? "角色变更" :
                               activity.type === "team_updated" ? "信息更新" :
                               activity.type === "team_created" ? "团队创建" :
                               activity.type === "permission_updated" ? "权限变更" : "活动"}
                            </span>
                          </span>
                        </Badge>
                      </div>
                      <div className="flex items-center mt-1 text-xs text-muted-foreground">
                        <Calendar className="h-3 w-3 mr-1" />
                        {activity.date}
                      </div>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          ) : (
            <div className="flex flex-col items-center justify-center py-12 px-4 text-center">
              <Activity className="h-10 w-10 text-muted-foreground mb-4" />
              <h4 className="text-base font-medium">{emptyMessage}</h4>
              <p className="text-sm text-muted-foreground mt-1">团队活动记录将显示在这里</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
} 
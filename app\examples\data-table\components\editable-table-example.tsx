"use client"

import * as React from "react"
import { 
  ColumnDef, 
  flexRender, 
  getCoreRowModel, 
  useReactTable,
  getPaginationRowModel,
} from "@tanstack/react-table"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { User } from "../data"
import { Check, Pencil, X } from "lucide-react"
import { Badge } from "@/components/ui/badge"

export interface EditableDataTableExampleProps {
  data: User[]
}

// 使用React.memo优化可编辑单元格组件
const EditableCell = React.memo(({ 
  value: initialValue, 
  row, 
  column, 
  editingCell, 
  setEditingCell, 
  onSave 
}: { 
  value: string, 
  row: any, 
  column: any, 
  editingCell: { id: string, column: string } | null, 
  setEditingCell: (cell: { id: string, column: string } | null) => void,
  onSave: (row: any, column: any, value: string) => void
}) => {
  const isEditing = editingCell?.id === row.id && editingCell?.column === column.id
  const [value, setValue] = React.useState(initialValue)
  
  React.useEffect(() => {
    setValue(initialValue)
  }, [initialValue])
  
  const handleSave = () => {
    onSave(row, column, value)
    setEditingCell(null)
  }
  
  return isEditing ? (
    <div className="flex items-center gap-2">
      <Input 
        value={value} 
        onChange={e => setValue(e.target.value)}
        className="h-8 w-[140px]"
        autoFocus
      />
      <Button 
        variant="ghost" 
        size="icon"
        onClick={handleSave}
      >
        <Check className="h-4 w-4" />
      </Button>
      <Button 
        variant="ghost" 
        size="icon"
        onClick={() => setEditingCell(null)}
      >
        <X className="h-4 w-4" />
      </Button>
    </div>
  ) : (
    <div 
      className="flex items-center gap-2"
      onDoubleClick={() => setEditingCell({ id: row.id, column: column.id })}
    >
      {value}
      <Pencil 
        className="h-3.5 w-3.5 text-muted-foreground cursor-pointer opacity-0 group-hover:opacity-100" 
        onClick={() => setEditingCell({ id: row.id, column: column.id })}
      />
    </div>
  )
})
EditableCell.displayName = "EditableCell"

// 使用React.memo优化可编辑状态单元格组件
const EditableStatusCell = React.memo(({ 
  value: initialValue, 
  row, 
  column, 
  editingCell, 
  setEditingCell, 
  onSave 
}: { 
  value: string, 
  row: any, 
  column: any, 
  editingCell: { id: string, column: string } | null, 
  setEditingCell: (cell: { id: string, column: string } | null) => void,
  onSave: (row: any, column: any, value: string) => void
}) => {
  const isEditing = editingCell?.id === row.id && editingCell?.column === column.id
  const [value, setValue] = React.useState(initialValue)
  const statusOptions = ["活跃", "未活跃", "待处理"]
  
  React.useEffect(() => {
    setValue(initialValue)
  }, [initialValue])
  
  const handleSave = () => {
    onSave(row, column, value)
    setEditingCell(null)
  }
  
  return isEditing ? (
    <div className="flex items-center gap-2">
      <select
        value={value}
        onChange={(e) => setValue(e.target.value)}
        className="h-8 w-[100px] rounded-md border border-input px-3"
        autoFocus
      >
        {statusOptions.map((option) => (
          <option key={option} value={option}>
            {option}
          </option>
        ))}
      </select>
      <Button 
        variant="ghost" 
        size="icon"
        onClick={handleSave}
      >
        <Check className="h-4 w-4" />
      </Button>
      <Button 
        variant="ghost" 
        size="icon"
        onClick={() => setEditingCell(null)}
      >
        <X className="h-4 w-4" />
      </Button>
    </div>
  ) : (
    <div 
      className="flex items-center gap-2"
      onDoubleClick={() => setEditingCell({ id: row.id, column: column.id })}
    >
      <span
        className={`mr-2 h-2 w-2 rounded-full ${
          value === "活跃" ? "bg-green-500" : 
          value === "未活跃" ? "bg-gray-400" : "bg-yellow-500"
        }`}
      />
      {value}
      <Pencil 
        className="h-3.5 w-3.5 text-muted-foreground cursor-pointer opacity-0 group-hover:opacity-100" 
        onClick={() => setEditingCell({ id: row.id, column: column.id })}
      />
    </div>
  )
})
EditableStatusCell.displayName = "EditableStatusCell"

// 使用React.memo优化角色单元格组件
const RoleCell = React.memo(({ value }: { value: string }) => {
  const roleColors: Record<string, string> = {
    "管理员": "destructive",
    "编辑": "secondary",
    "用户": "default",
    "访客": "outline"
  }
  
  return (
    <Badge variant={roleColors[value] as any || "default"}>
      {value}
    </Badge>
  )
})
RoleCell.displayName = "RoleCell"

export function EditableDataTableExample({ data: initialData }: EditableDataTableExampleProps) {
  // 使用useState保存数据，但不要在每次渲染时重新初始化
  const [data, setData] = React.useState<User[]>(() => [...initialData])
  const [editingCell, setEditingCell] = React.useState<{
    id: string;
    column: string;
  } | null>(null)

  // 处理单元格数据保存
  const handleSaveCell = React.useCallback((row: any, column: any, value: string) => {
    setData((prev) => 
      prev.map((item) => 
        item.id === row.id 
          ? { ...item, [column.id]: value } 
          : item
      )
    )
  }, [])

  // 使用useMemo缓存列定义，避免不必要的重新计算
  const columns = React.useMemo<ColumnDef<User>[]>(() => [
    {
      accessorKey: "name",
      header: "姓名",
      cell: ({ row, column }) => (
        <EditableCell 
          value={row.getValue(column.id)} 
          row={row} 
          column={column} 
          editingCell={editingCell}
          setEditingCell={setEditingCell}
          onSave={handleSaveCell}
        />
      )
    },
    {
      accessorKey: "email",
      header: "邮箱",
      cell: ({ row, column }) => (
        <EditableCell 
          value={row.getValue(column.id)} 
          row={row} 
          column={column} 
          editingCell={editingCell}
          setEditingCell={setEditingCell}
          onSave={handleSaveCell}
        />
      )
    },
    {
      accessorKey: "role",
      header: "角色",
      cell: ({ row }) => <RoleCell value={row.getValue("role")} />
    },
    {
      accessorKey: "status",
      header: "状态",
      cell: ({ row, column }) => (
        <EditableStatusCell 
          value={row.getValue(column.id)} 
          row={row} 
          column={column} 
          editingCell={editingCell}
          setEditingCell={setEditingCell}
          onSave={handleSaveCell}
        />
      )
    },
  ], [editingCell, handleSaveCell])

  // 使用useMemo缓存表格实例，避免不必要的重新渲染
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    initialState: {
      pagination: {
        pageSize: 5,
      },
    },
  })

  return (
    <div className="space-y-4">
      <div className="text-sm text-muted-foreground mb-2">
        <p>双击单元格或点击铅笔图标开始编辑。姓名、邮箱和状态字段可编辑。</p>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                  className="group"
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  无数据
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => table.previousPage()}
          disabled={!table.getCanPreviousPage()}
        >
          上一页
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => table.nextPage()}
          disabled={!table.getCanNextPage()}
        >
          下一页
        </Button>
      </div>
    </div>
  )
} 
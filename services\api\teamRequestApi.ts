/**
 * 团队管理接口
 * 提供团队相关的API调用功能
 */
import apiClient from './requestApi';
import {toast} from "sonner";
import {API_RESPONSE_CODE} from './configApi';

// API基础路径
const API_BASE_PATH = '/team';

/**
 * 检查响应是否成功
 * @param response API响应对象
 * @returns 是否成功
 */
const isSuccessResponse = (response: any): boolean => {
  // 确保响应对象存在并且包含正确的成功状态码
  if (!response) return false;
  
  // 检查响应中的code字段
  return response.code === API_RESPONSE_CODE.SUCCESS;
};

// 团队类型定义（根据接口文档）
export interface TeamRequest {
  id?: string;
  teamCode?: string;
  teamName?: string;
  teamLogo?: string;
  teamThemeColor?: string;
  teamType?: string;
  description?: string;
  privateTag?: number;
  parentTeamName?: string;
}

// 用户选项类型
export interface UserOption {
  id: number;
  label: string;
  value: string;
}

// 团队选项类型
export interface TeamOption {
  id: number;
  label: string;
  value: string;
}

// 团队详情类型（根据接口响应）
export interface TeamDetail {
  id: number;
  teamCode: string;
  teamName: string;
  teamLogo: string;
  teamThemeColor: string;
  teamType: string;
  parentCode?: number;
  leaderId?: string | null;
  status: number;
  description: string;
  privateTag: number;
  createTime?: string;
  updateTime?: string | null;
  memberCount?: number;
}

// 创建团队响应
export interface CreateTeamResponse {
  id: number;
  teamCode: string;
  teamName: string;
}

/**
 * 查询团队列表
 * 直接返回列表数据，无分页
 */
export const queryTeams = async (
  keyword: string = ""
): Promise<any> => {
  try {
    // 构建请求参数，不再需要分页参数
    const params = {
      teamName: keyword || "",
      teamType: ""
    };

    const response = await apiClient.post(`${API_BASE_PATH}/listAll`, params);
    // 使用统一的响应成功检查方法
    if (isSuccessResponse(response)) {
      return response;
    } else {
      // 处理响应失败的情况
      const errorMessage = response?.msg || "查询团队失败";
      console.error('查询团队返回错误', errorMessage);
      toast.error(errorMessage);
      return [];
    }
  } catch (error) {
    console.error("查询团队失败", error);
    toast.error("查询团队失败");
    return [];
  }
};

/**
 * 根据ID获取团队详情
 * @param id 团队ID
 * @returns 团队详情
 */
export const getTeamDetailRequest = async (id: number): Promise<TeamDetail | null> => {
  try {
    const response = await apiClient.get(`${API_BASE_PATH}/getById`, {id: id});
    
    if (isSuccessResponse(response)) {
      return response.data || null;
    }
    return null;
  } catch (error) {
    console.error(`获取团队详情失败: ${id}`, error);
    toast.error("获取团队详情失败");
    return null;
  }
};

/**
 * 创建团队
 * @param teamData 团队数据
 * @returns 创建结果
 */
export const createTeamRequest = async (teamData: TeamRequest): Promise<CreateTeamResponse | null> => {
  try {
    const response = await apiClient.post(`${API_BASE_PATH}/create`, teamData);
    
    if (isSuccessResponse(response)) {
      toast.success("创建团队成功");
      if (typeof response.data === 'number') {
        return {
          id: response.data,
          teamCode: '',  // 补充必要的字段
          teamName: ''   // 补充必要的字段
        };
      }
      return response.data || null;
    } else {
      const errorMessage = response?.msg || "创建团队失败";
      toast.error(errorMessage);
      return null;
    }
  } catch (error) {
    console.error("创建团队失败", error);
    toast.error("创建团队失败");
    return null;
  }
};

/**
 * 更新团队
 * @param teamData 团队数据
 * @returns 更新结果
 */
export const updateTeamRequest = async (teamData: TeamRequest): Promise<boolean> => {
  try {
    const response = await apiClient.put(`${API_BASE_PATH}/update`, teamData);
    
    if (isSuccessResponse(response)) {
      toast.success("更新团队成功");
      return true;
    }
    return false;
  } catch (error) {
    console.error(`更新团队失败`, error);
    toast.error("更新团队失败");
    return false;
  }
};

/**
 * 删除团队
 * @param id 团队ID
 * @returns 删除结果
 */
export const deleteTeamRequest = async (id: string): Promise<boolean> => {
  try {
    const response = await apiClient.delete(`${API_BASE_PATH}/delete`, {id: id});
    
    if (isSuccessResponse(response)) {
      toast.success("删除团队成功");
      return true;
    }
    return false;
  } catch (error) {
    console.error(`删除团队失败: ${id}`, error);
    toast.error("删除团队失败");
    return false;
  }
};

/**
 * 获取所有团队选项
 * @returns 团队选项列表
 */
export const getTeamOptionsRequest = async (): Promise<TeamOption[]> => {
  try {
    const response = await apiClient.get(`${API_BASE_PATH}/options`);
    
    if (isSuccessResponse(response)) {
      return response.data || [];
    }
    return [];
  } catch (error) {
    console.error("获取团队选项失败", error);
    toast.error("获取团队选项失败");
    return [];
  }
};

/**
 * 获取所有用户选项
 * @returns 用户选项列表
 */
export const getUserOptionsRequest = async (): Promise<UserOption[]> => {
  try {
    const response = await apiClient.get('/api/user/options');
    
    if (isSuccessResponse(response)) {
      return response.data || [];
    }
    return [];
  } catch (error) {
    console.error("获取用户选项失败", error);
    toast.error("获取用户选项失败");
    return [];
  }
};

/**
 * 通过团队代码检查团队是否存在
 * @param code 团队代码
 * @returns 是否存在
 */
export const checkTeamCodeExists = async (code: string): Promise<boolean> => {
  try {
    const response = await apiClient.get(`${API_BASE_PATH}/getByCode?code=${code}`);
    
    if (isSuccessResponse(response)) {
      // 如果接口返回了数据，说明团队代码已存在
      return !!response.data;
    }
    // 如果接口返回错误，则认为团队代码不存在
    return false;
  } catch (error) {
    console.error(`检查团队代码失败: ${code}`, error);
    // 出现异常时，默认不阻止用户继续
    return false;
  }
}; 
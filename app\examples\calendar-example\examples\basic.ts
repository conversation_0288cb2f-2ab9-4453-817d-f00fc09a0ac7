import React from "react"
import { Calendar } from "@/components/common-custom/calendar"

export const basicExample = {
  id: "basic-calendar",
  title: "基础日历",
  description: "简洁美观的基础日历，支持日期选择",
  code: `
import React, { useState } from "react";
import { Calendar } from "@/components/common-custom/calendar";

function BasicCalendar() {
  const [date, setDate] = useState<Date | undefined>(new Date());

  return (
    <div className="w-fit">
      <Calendar
        mode="single"
        value={date}
        handlers={{
          onSelect: setDate
        }}
        style={{
          showBorder: true,
          className: "rounded-lg shadow-sm bg-background"
        }}
      />
    </div>
  );
}

render(<BasicCalendar />);
  `,
  scope: { Calendar, React, useState: React.useState },
}

export const rangeExample = {
  id: "range-calendar",
  title: "日期范围选择",
  description: "支持选择日期范围的日历",
  code: `
import React, { useState } from "react";
import { Calendar } from "@/components/common-custom/calendar";

function RangeCalendar() {
  const [dateRange, setDateRange] = useState<{ from: Date; to?: Date } | undefined>();

  return (
    <Calendar
      mode="range"
      value={dateRange}
      handlers={{
        onSelect: setDateRange
      }}
      style={{
        showBorder: true,
        className: "w-fit"
      }}
    />
  );
}

render(<RangeCalendar />);
  `,
  scope: { Calendar, React, useState: React.useState },
}

export const multipleExample = {
  id: "multiple-calendar",
  title: "多日期选择",
  description: "支持选择多个日期的日历",
  code: `
import React, { useState } from "react";
import { Calendar } from "@/components/common-custom/calendar";

function MultipleCalendar() {
  const [dates, setDates] = useState<Date[] | undefined>([]);

  return (
    <div className="space-y-4">
      <Calendar
        mode="multiple"
        value={dates}
        handlers={{
          onSelect: setDates
        }}
        style={{
          showBorder: true,
          className: "w-fit"
        }}
      />
      
      {dates && dates.length > 0 && (
        <div className="text-sm text-muted-foreground">
          已选择 {dates.length} 个日期
        </div>
      )}
    </div>
  );
}

render(<MultipleCalendar />);
  `,
  scope: { Calendar, React, useState: React.useState },
}

/**
 * 复杂组件主导出文件
 * 
 * 这是复杂组件的标准导出模板，展示了如何组织和导出多个相关组件
 */

"use client"

/**
 * 复杂组件集合
 * 重新导出所有相关组件
 */

// ============================================================================
// 主组件导出
// ============================================================================

export { ComplexComponent } from "./complex-component"
export { ComplexComponentProvider } from "./complex-component-provider"

// ============================================================================
// 子组件导出
// ============================================================================

export { ComplexComponentHeader } from "./complex-component-header"
export { ComplexComponentContent } from "./complex-component-content"
export { ComplexComponentFooter } from "./complex-component-footer"
export { ComplexComponentItem } from "./complex-component-item"

// ============================================================================
// 工具组件导出
// ============================================================================

export { ComplexComponentToolbar } from "./complex-component-toolbar"
export { ComplexComponentFilter } from "./complex-component-filter"
export { ComplexComponentPagination } from "./complex-component-pagination"

// ============================================================================
// Hook导出
// ============================================================================

export { useComplexComponent } from "./use-complex-component"
export { useComplexComponentState } from "./use-complex-component-state"

// ============================================================================
// 类型导出
// ============================================================================

// 从types文件导出所有类型
export * from "./types"

// 或者选择性导出特定类型
// export type {
//   ComplexComponentProps,
//   ComplexComponentItem,
//   ComplexComponentConfig,
//   ComplexComponentHandler
// } from "./types"

// ============================================================================
// 常量导出
// ============================================================================

export { 
  DEFAULT_COMPLEX_COMPONENT_CONFIG,
  COMPLEX_COMPONENT_VARIANTS,
  COMPLEX_COMPONENT_SIZES 
} from "./constants"

// ============================================================================
// 工具函数导出
// ============================================================================

export { 
  createComplexComponentConfig,
  validateComplexComponentData,
  formatComplexComponentValue 
} from "./utils"

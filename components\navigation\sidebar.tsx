"use client"

import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { Menu } from "lucide-react"
import { Button } from "@/components/ui/button"

type SidebarContextType = {
  isOpen: boolean
  toggle: () => void
}

const SidebarContext = React.createContext<SidebarContextType | undefined>(undefined)

export function useSidebar() {
  const context = React.useContext(SidebarContext)
  if (!context) {
    throw new Error("useSidebar must be used within a SidebarProvider.")
  }
  return context
}

export function SidebarProvider({ children, defaultOpen = false }: { children: React.ReactNode, defaultOpen?: boolean }) {
  const [isOpen, setIsOpen] = React.useState(defaultOpen)
  const toggle = React.useCallback(() => setIsOpen(prev => !prev), [])
  
  const value = React.useMemo(() => ({ isOpen, toggle }), [isOpen, toggle])
  
  return (
    <SidebarContext.Provider value={value}>
      {children}
    </SidebarContext.Provider>
  )
}

export const SidebarTrigger = React.forwardRef<
  React.ElementRef<typeof Button>,
  React.ComponentProps<typeof Button>
>(({ className, onClick, ...props }, ref) => {
  const sidebarContext = React.useContext(SidebarContext)
  
  // 如果没有上下文，使用备用渲染（只是图标按钮，不包含功能）
  if (!sidebarContext) {
    return (
      <Button
        ref={ref}
        variant="ghost"
        size="icon"
        className={className}
        {...props}
      >
        <Menu className="h-4 w-4" />
        <span className="sr-only">菜单</span>
      </Button>
    )
  }
  
  const { toggle } = sidebarContext
  
  return (
    <Button
      ref={ref}
      variant="ghost"
      size="icon"
      className={className}
      onClick={(e) => {
        onClick?.(e)
        toggle()
      }}
      {...props}
    >
      <Menu className="h-4 w-4" />
      <span className="sr-only">切换侧边栏</span>
    </Button>
  )
})

SidebarTrigger.displayName = "SidebarTrigger" 
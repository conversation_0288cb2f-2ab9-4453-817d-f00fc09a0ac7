"use client"

import React, { ReactNode } from "react"
import {
  Tooltip as TooltipPrimitive,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import * as HoverCardPrimitive from "@radix-ui/react-hover-card"
import { cn } from "@/lib/utils"
import { HelpCircle, LucideIcon } from "lucide-react"

// ============================================================================
// 类型定义 - 单文件组件类型定义直接在组件文件内
// ============================================================================

/**
 * 工具提示属性
 */
export interface TooltipProps {
  /**
   * 提示内容
   */
  content: ReactNode

  /**
   * 子元素
   */
  children: ReactNode

  /**
   * 位置
   * @default "top"
   */
  side?: "top" | "right" | "bottom" | "left"

  /**
   * 对齐方式
   * @default "center"
   */
  align?: "start" | "center" | "end"

  /**
   * 是否禁用
   * @default false
   */
  disabled?: boolean

  /**
   * 显示延迟(毫秒)
   * @default 300
   */
  delayDuration?: number

  /**
   * 跳过延迟
   */
  skipDelayDuration?: number

  /**
   * 自定义类名
   */
  className?: string

  /**
   * 提示状态变更回调
   */
  onOpenChange?: (open: boolean) => void

  /**
   * 是否可交互
   * @default false
   */
  interactive?: boolean

  /**
   * 是否默认打开
   * @default false
   */
  defaultOpen?: boolean

  /**
   * 是否打开（受控模式）
   */
  open?: boolean

  /**
   * 触发方式
   * @default "hover"
   */
  trigger?: "hover" | "click" | "focus" | "manual"

  /**
   * 最大宽度
   */
  maxWidth?: number | string

  /**
   * 是否显示箭头
   * @default true
   */
  showArrow?: boolean
}

/**
 * 图标提示框属性
 */
export interface IconTooltipProps extends Omit<TooltipProps, 'children'> {
  /**
   * 图标
   * @default HelpCircle
   */
  icon?: LucideIcon | ReactNode

  /**
   * 图标大小
   * @default "sm"
   */
  iconSize?: "xs" | "sm" | "md" | "lg"

  /**
   * 图标颜色
   * @default "muted"
   */
  iconColor?: "default" | "muted" | "primary" | "secondary" | "destructive"
}

/**
 * 弹出提示属性
 */
export interface PopoverProps {
  /**
   * 提示内容
   */
  content: ReactNode

  /**
   * 子元素
   */
  children: ReactNode

  /**
   * 位置
   * @default "bottom"
   */
  side?: "top" | "right" | "bottom" | "left"

  /**
   * 对齐方式
   * @default "center"
   */
  align?: "start" | "center" | "end"

  /**
   * 是否打开（受控模式）
   */
  open?: boolean

  /**
   * 打开状态变更回调
   */
  onOpenChange?: (open: boolean) => void

  /**
   * 是否默认打开
   * @default false
   */
  defaultOpen?: boolean

  /**
   * 模态模式
   * @default false
   */
  modal?: boolean

  /**
   * 自定义类名
   */
  className?: string

  /**
   * 触发方式
   * @default "click"
   */
  trigger?: "click" | "hover" | "focus" | "manual"

  /**
   * 内容类名
   */
  contentClassName?: string

  /**
   * 是否显示关闭按钮
   * @default false
   */
  showCloseButton?: boolean
}

/**
 * 悬浮卡片属性
 */
export interface HoverCardProps {
  /**
   * 子元素
   */
  children: ReactNode

  /**
   * 卡片内容
   */
  content: ReactNode

  /**
   * 位置
   * @default "bottom"
   */
  side?: "top" | "right" | "bottom" | "left"

  /**
   * 对齐方式
   * @default "center"
   */
  align?: "start" | "center" | "end"

  /**
   * 打开延迟(毫秒)
   * @default 700
   */
  openDelay?: number

  /**
   * 关闭延迟(毫秒)
   * @default 300
   */
  closeDelay?: number

  /**
   * 是否默认打开
   * @default false
   */
  defaultOpen?: boolean

  /**
   * 是否打开（受控模式）
   */
  open?: boolean

  /**
   * 打开状态变更回调
   */
  onOpenChange?: (open: boolean) => void

  /**
   * 自定义类名
   */
  className?: string

  /**
   * 内容类名
   */
  contentClassName?: string
}

/**
 * 基础提示框组件
 */
export function Tooltip({
  content,
  children,
  side = "top",
  align = "center",
  disabled = false,
  delayDuration = 300,
  skipDelayDuration,
  className,
  onOpenChange,
  interactive = false,
  defaultOpen,
  open,
  trigger = "hover",
}: TooltipProps) {
  if (disabled) return <>{children}</>

  return (
    <TooltipProvider delayDuration={delayDuration} skipDelayDuration={skipDelayDuration}>
      <TooltipPrimitive
        defaultOpen={defaultOpen}
        open={open}
        onOpenChange={onOpenChange}
      >
        <TooltipTrigger asChild>{children}</TooltipTrigger>
        <TooltipContent
          side={side}
          align={align}
          className={cn(className)}
        >
          {content}
        </TooltipContent>
      </TooltipPrimitive>
    </TooltipProvider>
  )
}

/**
 * 图标提示框组件
 * 为文本标签提供帮助提示
 */
interface IconTooltipProps {
  /**
   * 提示内容
   */
  content: ReactNode
  
  /**
   * 标签文本
   */
  label?: string
  
  /**
   * 图标
   */
  icon?: ReactNode
  
  /**
   * 位置
   */
  side?: "top" | "right" | "bottom" | "left"
  
  /**
   * 对齐方式
   */
  align?: "start" | "center" | "end"
  
  /**
   * 图标类名
   */
  iconClassName?: string
  
  /**
   * 内容类名
   */
  contentClassName?: string
}

export function IconTooltip({
  content,
  label,
  icon = <HelpCircle className="h-4 w-4" />,
  side = "top",
  align = "center",
  iconClassName,
  contentClassName,
}: IconTooltipProps) {
  // 确保图标正确渲染
  const renderIcon = () => {
    if (React.isValidElement(icon)) {
      return icon
    }

    // 如果是组件类型，实例化它
    if (typeof icon === 'function') {
      const IconComponent = icon as React.ComponentType<any>
      return <IconComponent className="h-4 w-4" />
    }

    // 默认图标
    return <HelpCircle className="h-4 w-4" />
  }

  return (
    <div className="flex items-center gap-2">
      {label && <span className="text-sm">{label}</span>}
      <TooltipProvider>
        <TooltipPrimitive>
          <TooltipTrigger asChild>
            <span className={cn("text-muted-foreground cursor-help", iconClassName)}>
              {renderIcon()}
            </span>
          </TooltipTrigger>
          <TooltipContent side={side} align={align} className={contentClassName}>
            {content}
          </TooltipContent>
        </TooltipPrimitive>
      </TooltipProvider>
    </div>
  )
}

/**
 * 富内容提示框
 * 支持标题、描述和列表等复杂内容
 */
interface RichTooltipProps {
  /**
   * 提示标题
   */
  title?: string
  
  /**
   * 提示描述
   */
  description?: string
  
  /**
   * 列表项
   */
  items?: string[]
  
  /**
   * 子元素
   */
  children: ReactNode
  
  /**
   * 位置
   */
  side?: "top" | "right" | "bottom" | "left"
  
  /**
   * 对齐方式
   */
  align?: "start" | "center" | "end"
  
  /**
   * 自定义内容
   */
  customContent?: ReactNode
  
  /**
   * 内容类名
   */
  contentClassName?: string
  
  /**
   * 最大宽度
   */
  maxWidth?: string
}

export function RichTooltip({
  title,
  description,
  items,
  children,
  side = "top",
  align = "center",
  customContent,
  contentClassName,
  maxWidth = "max-w-xs",
}: RichTooltipProps) {
  return (
    <TooltipProvider>
      <TooltipPrimitive>
        <TooltipTrigger asChild>
          {children}
        </TooltipTrigger>
        <TooltipContent
          side={side}
          align={align}
          className={cn(maxWidth, contentClassName)}
        >
          <div className="space-y-2">
            {title && <p className="font-semibold">{title}</p>}
            {description && <p className="text-sm">{description}</p>}
            {items && items.length > 0 && (
              <ul className="text-sm space-y-1">
                {items.map((item, index) => (
                  <li key={index}>• {item}</li>
                ))}
              </ul>
            )}
            {customContent}
          </div>
        </TooltipContent>
      </TooltipPrimitive>
    </TooltipProvider>
  )
}

/**
 * 提示框容器
 * 包装所有提示框组件
 */
export function TooltipContainer({ children }: { children: ReactNode }) {
  return <TooltipProvider>{children}</TooltipProvider>
}

// ============================================================================
// 类型导出 - 类型已在定义时导出
// ============================================================================

// ============================================================================
// 常量导出
// ============================================================================

/**
 * 支持的提示框位置
 */
export const TOOLTIP_SIDES = ['top', 'right', 'bottom', 'left'] as const

/**
 * 支持的对齐方式
 */
export const TOOLTIP_ALIGNMENTS = ['start', 'center', 'end'] as const

/**
 * 支持的触发方式
 */
export const TOOLTIP_TRIGGERS = ['hover', 'click', 'focus', 'manual'] as const

/**
 * 支持的图标尺寸
 */
export const ICON_SIZES = ['xs', 'sm', 'md', 'lg'] as const

/**
 * 支持的图标颜色
 */
export const ICON_COLORS = ['default', 'muted', 'primary', 'secondary', 'destructive'] as const

/**
 * 默认提示框配置
 */
export const DEFAULT_TOOLTIP_CONFIG = {
  side: 'top' as const,
  align: 'center' as const,
  disabled: false,
  delayDuration: 300,
  interactive: false,
  defaultOpen: false,
  trigger: 'hover' as const,
  showArrow: true,
} satisfies Partial<TooltipProps>

/**
 * 默认图标提示框配置
 */
export const DEFAULT_ICON_TOOLTIP_CONFIG = {
  iconSize: 'sm' as const,
  iconColor: 'muted' as const,
  side: 'top' as const,
  align: 'center' as const,
} satisfies Partial<IconTooltipProps>

/**
 * 默认弹出提示配置
 */
export const DEFAULT_POPOVER_CONFIG = {
  side: 'bottom' as const,
  align: 'center' as const,
  defaultOpen: false,
  modal: false,
  trigger: 'click' as const,
  showCloseButton: false,
} satisfies Partial<PopoverProps>

/**
 * 默认悬浮卡片配置
 */
export const DEFAULT_HOVER_CARD_CONFIG = {
  side: 'bottom' as const,
  align: 'center' as const,
  openDelay: 700,
  closeDelay: 300,
  defaultOpen: false,
} satisfies Partial<HoverCardProps>

/**
 * 图标尺寸映射
 */
export const ICON_SIZE_MAP = {
  xs: 'h-3 w-3',
  sm: 'h-4 w-4',
  md: 'h-5 w-5',
  lg: 'h-6 w-6',
} as const

/**
 * 悬浮卡片组件
 */
export function HoverCard({
  children,
  content,
  title,
  description,
  avatar,
  side = "bottom",
  align = "center",
  sideOffset = 4,
  className,
  contentClassName,
}: HoverCardProps) {
  return (
    <HoverCardPrimitive.Root>
      <HoverCardPrimitive.Trigger asChild>
        {children}
      </HoverCardPrimitive.Trigger>
      <HoverCardPrimitive.Content
        side={side}
        align={align}
        sideOffset={sideOffset}
        className={cn(
          "z-50 w-64 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
          contentClassName
        )}
      >
        <div className={cn("space-y-2", className)}>
          {avatar && (
            <div className="flex items-center space-x-2">
              {avatar}
              {title && <h4 className="text-sm font-semibold">{title}</h4>}
            </div>
          )}
          {!avatar && title && <h4 className="text-sm font-semibold">{title}</h4>}
          {description && <p className="text-sm text-muted-foreground">{description}</p>}
          {content && <div className="text-sm">{content}</div>}
        </div>
      </HoverCardPrimitive.Content>
    </HoverCardPrimitive.Root>
  )
}

/**
 * 图标颜色映射
 */
export const ICON_COLOR_MAP = {
  default: 'text-foreground',
  muted: 'text-muted-foreground',
  primary: 'text-primary',
  secondary: 'text-secondary',
  destructive: 'text-destructive',
} as const
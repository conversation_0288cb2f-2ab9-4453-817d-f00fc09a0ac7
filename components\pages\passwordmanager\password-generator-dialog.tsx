"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Slider } from "@/components/ui/slider"
import { RefreshCw, Co<PERSON>, Check } from "lucide-react"
import {useToast} from "@/components/ui/use-toast";
import {Badge} from "@/components/ui/badge";

type PasswordGeneratorDialogProps = {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function PasswordGeneratorDialog({ open, onOpenChange }: PasswordGeneratorDialogProps) {
  const [passwordLength, setPasswordLength] = useState(16)
  const [includeUppercase, setIncludeUppercase] = useState(true)
  const [includeL<PERSON><PERSON><PERSON>, setInc<PERSON><PERSON>owercase] = useState(true)
  const [includeN<PERSON><PERSON>, setIncludeNumbers] = useState(true)
  const [includeSymbols, setIncludeSymbols] = useState(true)
  const [generatedPassword, setGeneratedPassword] = useState("")
  const [passwordStrength, setPasswordStrength] = useState<"weak" | "medium" | "strong">("medium")
  const [password_strength, setPassword_strength] = useState(2) // 1-弱 2-中 3-强
  const [copied, setCopied] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    if (open) {
      generatePassword()
    }
  }, [open])

  const generatePassword = () => {
    const uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
    const lowercase = "abcdefghijklmnopqrstuvwxyz"
    const numbers = "0123456789"
    const symbols = "!@#$%^&*()_+~`|}{[]:;?><,./-="

    let chars = ""
    if (includeUppercase) chars += uppercase
    if (includeLowercase) chars += lowercase
    if (includeNumbers) chars += numbers
    if (includeSymbols) chars += symbols

    if (chars === "") {
      // Default to lowercase if nothing selected
      chars = lowercase
    }

    let password = ""
    for (let i = 0; i < passwordLength; i++) {
      const randomIndex = Math.floor(Math.random() * chars.length)
      password += chars[randomIndex]
    }

    setGeneratedPassword(password)
    calculatePasswordStrength(password)
    setCopied(false)
  }

  const calculatePasswordStrength = (password: string) => {
    // Simple password strength calculation
    let strength: "weak" | "medium" | "strong" = "weak"
    let strengthValue = 1 // 默认弱密码

    if (password.length >= 12) {
      const hasUppercase = /[A-Z]/.test(password)
      const hasLowercase = /[a-z]/.test(password)
      const hasNumbers = /[0-9]/.test(password)
      const hasSymbols = /[^A-Za-z0-9]/.test(password)

      const varietyCount = [hasUppercase, hasLowercase, hasNumbers, hasSymbols].filter(Boolean).length

      if (password.length >= 16 && varietyCount >= 3) {
        strength = "strong"
        strengthValue = 3
      } else if (password.length >= 12 && varietyCount >= 2) {
        strength = "medium"
        strengthValue = 2
      }
    }

    setPasswordStrength(strength)
    setPassword_strength(strengthValue)
  }

  const handleCopyPassword = () => {
    try {
      // 使用document.execCommand作为备选方案
      const textArea = document.createElement("textarea");
      textArea.value = generatedPassword;
      document.body.appendChild(textArea);
      textArea.select();
      const success = document.execCommand('copy');
      document.body.removeChild(textArea);
      
      if (success) {
        setCopied(true);
        toast({
          title: "已复制",
          description: "密码已复制到剪贴板",
          duration: 2000,
        });
      } else {
        throw new Error("复制失败");
      }
    } catch (error) {
      console.error('复制失败:', error);
      toast({
        title: "复制失败",
        description: "无法复制密码到剪贴板，请手动选择并复制",
        variant: "destructive"
      });
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>密码生成器</DialogTitle>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <div className="flex justify-between">
              <Label>密码长度: {passwordLength}</Label>
              <Badge variant={passwordLength < 8 ? "destructive" : passwordLength < 12 ? "secondary" : "default"}>
                {passwordLength < 8 ? "弱" : passwordLength < 12 ? "中" : "强"}
              </Badge>
            </div>
            <Slider
              value={[passwordLength]}
              min={4}
              max={32}
              step={1}
              onValueChange={(value) => setPasswordLength(value[0])}
            />
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="uppercase">包含大写字母 (A-Z)</Label>
              <Switch id="uppercase" checked={includeUppercase} onCheckedChange={setIncludeUppercase} />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="lowercase">包含小写字母 (a-z)</Label>
              <Switch id="lowercase" checked={includeLowercase} onCheckedChange={setIncludeLowercase} />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="numbers">包含数字 (0-9)</Label>
              <Switch id="numbers" checked={includeNumbers} onCheckedChange={setIncludeNumbers} />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="symbols">包含特殊符号 (!@#$%^&*)</Label>
              <Switch id="symbols" checked={includeSymbols} onCheckedChange={setIncludeSymbols} />
            </div>
          </div>

          <Button onClick={generatePassword} variant="outline" className="w-full">
            <RefreshCw className="mr-2 h-4 w-4" />
            生成随机密码
          </Button>

          {generatedPassword && (
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <Label>生成的密码</Label>
                <Badge
                  variant={
                    password_strength === 3
                      ? "default"
                      : password_strength === 2
                        ? "secondary"
                        : "destructive"
                  }
                >
                  {password_strength === 3 ? "强" : password_strength === 2 ? "中" : "弱"}
                </Badge>
              </div>
              <div className="relative">
                <Input value={generatedPassword} readOnly className="font-mono pr-10" />
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute right-0 top-0 h-full"
                  onClick={handleCopyPassword}
                >
                  {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                </Button>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
} 

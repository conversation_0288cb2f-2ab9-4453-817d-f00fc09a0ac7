// 导航配置的类型定义

export interface NavigationUser {
  name: string
  email: string
  avatar: string
}

export interface NavigationTeam {
  name: string
  logo: string
  plan: string
}

export interface NavigationMenuItem {
  title: string
  url: string
  description?: string
}

export interface NavigationMainItem {
  title: string
  url?: string
  icon: string
  isActive?: boolean
  items?: NavigationMenuItem[]
}

export interface NavigationProject {
  name: string
  url: string
  icon: string
}

export interface NavigationConfig {
  showNavigation: boolean
  excludePaths?: string[]
  user: NavigationUser
  teams: NavigationTeam[]
  navMain: NavigationMainItem[]
  projects: NavigationProject[]
} 
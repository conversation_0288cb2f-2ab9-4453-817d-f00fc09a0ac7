# 布局网格系统

布局网格系统为界面提供了一致的结构和节奏，确保元素对齐和间距的统一性。我们的网格系统基于8px基准单位，结合Flexbox和CSS Grid技术。

## 基础网格原理

### 8px基准单位系统

所有间距、尺寸都基于8px的倍数，这样可以确保：
- 在各种屏幕密度下都能完美对齐
- 设计师和开发者有统一的度量标准
- 组件之间有一致的视觉节奏

```css
:root {
  --spacing-1: 0.25rem; /* 4px */
  --spacing-2: 0.5rem;  /* 8px */
  --spacing-3: 0.75rem; /* 12px */
  --spacing-4: 1rem;    /* 16px */
  --spacing-5: 1.25rem; /* 20px */
  --spacing-6: 1.5rem;  /* 24px */
  --spacing-8: 2rem;    /* 32px */
  --spacing-10: 2.5rem; /* 40px */
  --spacing-12: 3rem;   /* 48px */
  --spacing-16: 4rem;   /* 64px */
}
```

## 响应式断点系统

我们使用移动优先的响应式设计方法：

```css
/* Tailwind CSS 断点 */
/* sm: 640px */
/* md: 768px */
/* lg: 1024px */
/* xl: 1280px */
/* 2xl: 1536px */
```

### 断点使用指南

| 断点 | 设备类型 | 容器最大宽度 | 列数 | 间距 |
|------|---------|-------------|------|------|
| xs (默认) | 手机 | 100% | 4 | 16px |
| sm (640px+) | 大手机 | 640px | 8 | 16px |
| md (768px+) | 平板 | 768px | 8 | 24px |
| lg (1024px+) | 小桌面 | 1024px | 12 | 24px |
| xl (1280px+) | 桌面 | 1280px | 12 | 32px |
| 2xl (1536px+) | 大桌面 | 1536px | 12 | 32px |

## 容器系统

### 主容器

```jsx
// 标准页面容器
<div className="container mx-auto px-4 sm:px-6 lg:px-8">
  {/* 页面内容 */}
</div>

// 全宽容器
<div className="w-full">
  {/* 全宽内容 */}
</div>

// 限制最大宽度的容器
<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
  {/* 内容 */}
</div>
```

### 内容宽度规范

```jsx
// 不同内容类型的宽度限制
const ContentWidths = {
  // 阅读内容（文章、博客）
  prose: "max-w-4xl", // ~896px
  
  // 表单内容
  form: "max-w-2xl", // ~672px
  
  // 卡片网格
  cardGrid: "max-w-7xl", // ~1280px
  
  // 全宽内容
  full: "max-w-none"
}
```

## Flexbox布局模式

### 基础Flex布局

```jsx
// 水平排列
<div className="flex items-center space-x-4">
  <div>项目1</div>
  <div>项目2</div>
  <div>项目3</div>
</div>

// 垂直排列
<div className="flex flex-col space-y-4">
  <div>项目1</div>
  <div>项目2</div>
  <div>项目3</div>
</div>

// 两端对齐
<div className="flex justify-between items-center">
  <div>左侧内容</div>
  <div>右侧内容</div>
</div>

// 居中对齐
<div className="flex justify-center items-center min-h-screen">
  <div>居中内容</div>
</div>
```

### 响应式Flex布局

```jsx
// 移动端垂直，桌面端水平
<div className="flex flex-col md:flex-row md:items-center md:space-x-6 space-y-4 md:space-y-0">
  <div className="flex-1">主要内容</div>
  <div className="flex-shrink-0">侧边内容</div>
</div>
```

## CSS Grid布局模式

### 基础Grid布局

```jsx
// 等宽列网格
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
  <div>卡片1</div>
  <div>卡片2</div>
  <div>卡片3</div>
</div>

// 不等宽列网格
<div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
  <div className="lg:col-span-2">主要内容</div>
  <div>侧边栏</div>
</div>

// 复杂网格布局
<div className="grid grid-cols-12 gap-4">
  <div className="col-span-12 md:col-span-8">内容区</div>
  <div className="col-span-12 md:col-span-4">侧边栏</div>
</div>
```

### 卡片网格系统

```jsx
// 响应式卡片网格
<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6">
  {items.map(item => (
    <Card key={item.id} className="h-full">
      {/* 卡片内容 */}
    </Card>
  ))}
</div>
```

## 间距系统应用

### 组件间距

```jsx
// 垂直间距
<div className="space-y-4"> {/* 16px 垂直间距 */}
  <Component1 />
  <Component2 />
  <Component3 />
</div>

// 水平间距
<div className="flex space-x-3"> {/* 12px 水平间距 */}
  <Button>按钮1</Button>
  <Button>按钮2</Button>
</div>

// 响应式间距
<div className="space-y-4 md:space-y-6 lg:space-y-8">
  {/* 移动端16px，平板24px，桌面32px */}
</div>
```

### 内边距规范

```jsx
// 组件内边距
const PaddingClasses = {
  // 小组件
  sm: "p-3", // 12px
  
  // 标准组件
  default: "p-4", // 16px
  
  // 大组件
  lg: "p-6", // 24px
  
  // 页面级容器
  page: "p-4 md:p-6 lg:p-8", // 响应式内边距
}
```

## 页面布局模板

### 标准页面布局

```jsx
export function StandardPageLayout({ children }) {
  return (
    <div className="min-h-screen bg-background">
      {/* 页面头部 */}
      <header className="border-b">
        <div className="container mx-auto px-4 py-4">
          {/* 头部内容 */}
        </div>
      </header>
      
      {/* 主要内容 */}
      <main className="container mx-auto px-4 py-8">
        {children}
      </main>
      
      {/* 页面底部 */}
      <footer className="border-t mt-auto">
        <div className="container mx-auto px-4 py-6">
          {/* 底部内容 */}
        </div>
      </footer>
    </div>
  )
}
```

### 侧边栏布局

```jsx
export function SidebarLayout({ sidebar, children }) {
  return (
    <div className="flex min-h-screen">
      {/* 侧边栏 */}
      <aside className="w-64 border-r bg-muted/10">
        <div className="p-6">
          {sidebar}
        </div>
      </aside>
      
      {/* 主要内容 */}
      <main className="flex-1">
        <div className="p-6">
          {children}
        </div>
      </main>
    </div>
  )
}
```

### 三栏布局

```jsx
export function ThreeColumnLayout({ leftSidebar, rightSidebar, children }) {
  return (
    <div className="container mx-auto px-4">
      <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
        {/* 左侧边栏 */}
        <aside className="lg:col-span-3">
          {leftSidebar}
        </aside>
        
        {/* 主要内容 */}
        <main className="lg:col-span-6">
          {children}
        </main>
        
        {/* 右侧边栏 */}
        <aside className="lg:col-span-3">
          {rightSidebar}
        </aside>
      </div>
    </div>
  )
}
```

## 组件布局规范

### 卡片组件布局

```jsx
export function Card({ title, content, actions }) {
  return (
    <div className="rounded-lg border bg-card p-6">
      {/* 卡片头部 */}
      {title && (
        <div className="mb-4">
          <h3 className="text-lg font-semibold">{title}</h3>
        </div>
      )}
      
      {/* 卡片内容 */}
      <div className="mb-4">
        {content}
      </div>
      
      {/* 卡片操作 */}
      {actions && (
        <div className="flex justify-end space-x-2">
          {actions}
        </div>
      )}
    </div>
  )
}
```

### 表单布局

```jsx
export function FormLayout({ children }) {
  return (
    <form className="space-y-6 max-w-2xl">
      {children}
    </form>
  )
}

export function FormField({ label, children, description, error }) {
  return (
    <div className="space-y-2">
      {label && (
        <label className="text-sm font-medium">
          {label}
        </label>
      )}
      {children}
      {description && (
        <p className="text-xs text-muted-foreground">
          {description}
        </p>
      )}
      {error && (
        <p className="text-xs text-destructive">
          {error}
        </p>
      )}
    </div>
  )
}
```

## 最佳实践

### 布局原则

1. **移动优先**：始终从移动端开始设计，然后扩展到更大屏幕
2. **一致性**：在整个应用中保持一致的间距和对齐
3. **可预测性**：用户应该能够预期内容的位置和行为
4. **灵活性**：布局应该能够适应不同的内容长度和屏幕尺寸

### 常见错误避免

1. **避免固定高度**：除非必要，避免设置固定高度
2. **避免过度嵌套**：保持DOM结构简洁
3. **避免魔法数字**：使用设计系统中定义的间距值
4. **避免破坏响应式**：确保在所有断点下都能正常工作

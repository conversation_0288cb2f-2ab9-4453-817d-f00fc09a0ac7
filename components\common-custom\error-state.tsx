"use client"

import { useState, ReactNode } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  AlertTriangle,
  Wifi,
  Server,
  Shield,
  RefreshCw,
  Home,
  Mail,
  AlertCircle,
  XCircle,
  Clock,
  HelpCircle,
  LucideIcon,
} from "lucide-react"
// ============================================================================
// 类型定义
// ============================================================================

/**
 * 错误状态操作按钮属性
 */
export interface ErrorActionButtonProps {
  /**
   * 按钮文本
   */
  label: string

  /**
   * 按钮图标
   */
  icon?: LucideIcon

  /**
   * 按钮变种
   */
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"

  /**
   * 按钮尺寸
   */
  size?: "default" | "sm" | "lg" | "icon"

  /**
   * 点击事件
   */
  onClick?: () => void

  /**
   * 是否禁用
   */
  disabled?: boolean

  /**
   * 自定义类名
   */
  className?: string
}

/**
 * 错误状态属性
 */
export interface ErrorStateProps {
  /**
   * 错误标题
   */
  title?: string

  /**
   * 错误描述
   */
  description?: ReactNode

  /**
   * 错误图标
   */
  icon?: LucideIcon | ReactNode

  /**
   * 错误代码
   */
  errorCode?: string | number

  /**
   * 错误类型
   */
  type?: "network" | "server" | "permission" | "notFound" | "validation" | "generic"

  /**
   * 错误严重程度
   */
  severity?: "high" | "medium" | "low"

  /**
   * 操作按钮列表
   */
  actions?: ErrorActionButtonProps[]

  /**
   * 是否显示详细信息
   */
  showDetails?: boolean

  /**
   * 详细错误信息
   */
  details?: string

  /**
   * 是否可重试
   */
  retryable?: boolean

  /**
   * 重试回调
   */
  onRetry?: () => void

  /**
   * 是否显示联系支持
   */
  showSupport?: boolean

  /**
   * 联系支持回调
   */
  onContactSupport?: () => void

  /**
   * 自定义类名
   */
  className?: string
}

/**
 * 表单错误状态属性
 */
export interface FormErrorStateProps {
  /**
   * 错误字段列表
   */
  errors: Array<{
    field: string
    message: string
    type?: string
  }>

  /**
   * 是否显示字段名
   */
  showFieldNames?: boolean

  /**
   * 是否可清除错误
   */
  clearable?: boolean

  /**
   * 清除错误回调
   */
  onClearErrors?: () => void

  /**
   * 自定义类名
   */
  className?: string
}

/**
 * 网络错误状态属性
 */
export interface NetworkErrorStateProps {
  /**
   * 网络状态
   */
  status?: "offline" | "slow" | "timeout" | "failed"

  /**
   * 是否自动重试
   */
  autoRetry?: boolean

  /**
   * 重试间隔（秒）
   */
  retryInterval?: number

  /**
   * 最大重试次数
   */
  maxRetries?: number

  /**
   * 当前重试次数
   */
  currentRetry?: number

  /**
   * 重试回调
   */
  onRetry?: () => void

  /**
   * 自定义类名
   */
  className?: string
}

// ============================================================================
// 组件实现
// ============================================================================

/**
 * 获取错误严重程度对应的徽章
 */
export function getSeverityBadge(severity: "high" | "medium" | "low") {
  switch (severity) {
    case "high":
      return <Badge variant="destructive">严重</Badge>
    case "medium":
      return <Badge variant="secondary">中等</Badge>
    case "low":
      return <Badge variant="outline">轻微</Badge>
    default:
      return <Badge variant="secondary">中等</Badge>
  }
}

/**
 * 获取错误类型对应的图标
 */
export function getErrorIcon(type?: string): ReactNode {
  switch (type) {
    case "network":
      return <Wifi className="h-12 w-12 text-destructive" />
    case "server":
      return <Server className="h-12 w-12 text-destructive" />
    case "permission":
      return <Shield className="h-12 w-12 text-destructive" />
    case "timeout":
      return <Clock className="h-12 w-12 text-destructive" />
    case "validation":
      return <AlertCircle className="h-12 w-12 text-destructive" />
    case "notfound":
      return <XCircle className="h-12 w-12 text-destructive" />
    default:
      return <AlertTriangle className="h-12 w-12 text-destructive" />
  }
}

/**
 * 错误状态卡片组件
 */
export function ErrorCard({
  type = "generic",
  title,
  description,
  code,
  severity = "medium",
  actions = [],
  icon,
  className = "",
}: ErrorStateProps) {
  // 处理图标
  const renderIcon = () => {
    if (!icon) return getErrorIcon(type)
    if (typeof icon === "function") {
      const Icon = icon as LucideIcon
      return <Icon className="h-12 w-12 text-destructive" />
    }
    return icon
  }

  return (
    <Card className={className}>
      <CardContent className="pt-6">
        <div className="text-center space-y-4">
          {renderIcon()}
          <div className="space-y-2">
            <div className="flex items-center justify-center gap-2">
              <h3 className="font-semibold text-lg">{title}</h3>
              {getSeverityBadge(severity)}
            </div>
            {description && <p className="text-muted-foreground text-sm">{description}</p>}
            {code && <div className="text-xs text-muted-foreground font-mono bg-muted px-2 py-1 rounded">{code}</div>}
          </div>
          {actions.length > 0 && (
            <div className="flex flex-col sm:flex-row gap-2 justify-center">
              {actions.map((action, actionIndex) => (
                <Button
                  key={actionIndex}
                  variant={action.variant || "default"}
                  size="sm"
                  onClick={action.onClick}
                  disabled={action.disabled || action.isLoading}
                >
                  {action.isLoading ? (
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    action.icon && (() => {
                      const Icon = action.icon as LucideIcon
                      return <Icon className="h-4 w-4 mr-2" />
                    })()
                  )}
                  {action.label}
                </Button>
              ))}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

/**
 * 页面级错误状态组件
 */
export function PageErrorState({
  title = "出现了一些问题",
  description = "很抱歉，页面加载时出现错误。我们已经记录了这个问题，正在努力修复。",
  errorId,
  icon,
  actions = [],
  footer,
  retryable = true,
  onRetry,
}: ErrorStateProps) {
  const [retryCount, setRetryCount] = useState(0)
  const [isRetrying, setIsRetrying] = useState(false)

  const handleRetry = () => {
    setIsRetrying(true)
    setRetryCount((prev) => prev + 1)
    
    if (onRetry) {
      onRetry()
    }
    
    // 模拟重试过程
    setTimeout(() => {
      setIsRetrying(false)
    }, 2000)
  }

  // 处理图标
  const renderIcon = () => {
    if (!icon) return <AlertTriangle className="h-12 w-12 text-destructive" />
    if (typeof icon === "function") {
      const Icon = icon as LucideIcon
      return <Icon className="h-12 w-12 text-destructive" />
    }
    return icon
  }

  return (
    <div className="min-h-[400px] flex flex-col items-center justify-center space-y-6">
      <div className="w-24 h-24 rounded-full bg-destructive/10 flex items-center justify-center">
        {renderIcon()}
      </div>
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-semibold">{title}</h2>
        <p className="text-muted-foreground max-w-md">{description}</p>
        {errorId && (
          <div className="text-xs text-muted-foreground font-mono bg-muted px-2 py-1 rounded inline-block">
            错误ID: {errorId}
          </div>
        )}
      </div>
      <div className="flex flex-col sm:flex-row gap-3">
        {retryable && (
          <Button onClick={handleRetry} disabled={isRetrying}>
            {isRetrying ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                重试中...
              </>
            ) : (
              <>
                <RefreshCw className="h-4 w-4 mr-2" />
                重试 {retryCount > 0 && `(${retryCount})`}
              </>
            )}
          </Button>
        )}
        {actions.map((action, index) => (
          <Button key={index} variant={action.variant || "outline"} onClick={action.onClick} disabled={action.disabled}>
            {action.icon && (() => {
              const Icon = action.icon as LucideIcon
              return <Icon className="h-4 w-4 mr-2" />
            })()}
            {action.label}
          </Button>
        ))}
      </div>
      {footer || (
        <div className="text-center">
          <p className="text-sm text-muted-foreground">
            如果问题持续存在，请{" "}
            <a href="#" className="text-primary hover:underline">
              联系技术支持
            </a>
          </p>
        </div>
      )}
    </div>
  )
}

/**
 * 表单错误状态组件
 */
export function FormErrorState({
  title = "表单提交失败",
  errors,
  hint = "请修复以下错误后重新提交：",
  inline = false,
  className = "",
}: FormErrorStateProps) {
  const errorList = Array.isArray(errors) ? errors : Object.values(errors)
  
  return (
    <div className={`${inline ? "" : "p-4 border border-destructive/20 bg-destructive/5 rounded-lg"} ${className}`}>
      <div className="flex items-start gap-3">
        <AlertCircle className="h-5 w-5 text-destructive mt-0.5" />
        <div className="flex-1">
          <h4 className="font-medium text-destructive">{title}</h4>
          <p className="text-sm text-destructive/80 mt-1">{hint}</p>
          <ul className="text-sm text-destructive/80 mt-2 space-y-1">
            {errorList.map((error, index) => (
              <li key={index}>• {error}</li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  )
}

/**
 * 网络错误状态组件
 */
export function NetworkErrorState({
  title = "网络连接失败",
  description = "无法连接到服务器，请检查您的网络连接",
  causes,
  onRetry,
  actions = [],
}: NetworkErrorStateProps) {
  const [isRetrying, setIsRetrying] = useState(false)

  const handleRetry = () => {
    setIsRetrying(true)
    
    if (onRetry) {
      onRetry()
    }
    
    // 模拟重试过程
    setTimeout(() => {
      setIsRetrying(false)
    }, 2000)
  }

  const defaultCauses = causes || [
    "网络连接不稳定",
    "服务器维护中",
    "防火墙阻止了连接",
  ]

  return (
    <div className="text-center py-8">
      <div className="w-16 h-16 rounded-full bg-destructive/10 flex items-center justify-center mx-auto mb-4">
        <Wifi className="h-8 w-8 text-destructive" />
      </div>
      <h3 className="font-semibold mb-2">{title}</h3>
      <p className="text-muted-foreground text-sm mb-4">{description}</p>
      <div className="space-y-2 mb-4">
        <p className="text-sm text-muted-foreground">可能的原因：</p>
        <ul className="text-sm text-muted-foreground space-y-1">
          {defaultCauses.map((cause, index) => (
            <li key={index}>• {cause}</li>
          ))}
        </ul>
      </div>
      <div className="flex justify-center gap-2">
        <Button onClick={handleRetry} disabled={isRetrying}>
          {isRetrying ? (
            <>
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              重试中...
            </>
          ) : (
            <>
              <RefreshCw className="h-4 w-4 mr-2" />
              重试连接
            </>
          )}
        </Button>
        {actions.map((action, index) => (
          <Button key={index} variant={action.variant || "outline"} onClick={action.onClick}>
            {action.icon && (() => {
              const Icon = action.icon as LucideIcon
              return <Icon className="h-4 w-4 mr-2" />
            })()}
            {action.label}
          </Button>
        ))}
      </div>
    </div>
  )
}

/**
 * 错误提示框组件
 */
export function ErrorAlert({
  title,
  description,
  variant = "destructive",
  icon,
}: {
  title?: string
  description: ReactNode
  variant?: "default" | "destructive"
  icon?: LucideIcon
}) {
  const IconComponent = icon || AlertTriangle
  
  return (
    <Alert variant={variant}>
      <IconComponent className="h-4 w-4" />
      {title && <div className="font-medium">{title}</div>}
      <AlertDescription>{description}</AlertDescription>
    </Alert>
  )
}

/**
 * 错误状态卡片组件（简化版）
 */
export function SimpleErrorCard({
  title,
  description,
  icon: Icon,
  actions = [],
}: {
  title: string
  description: string
  icon: LucideIcon
  actions?: ErrorActionButtonProps[]
}) {
  return (
    <Card>
      <CardContent>
        <div className="flex flex-col items-center justify-center py-6 text-center">
          <div className="mb-4 p-3 bg-destructive/10 rounded-full">
            <Icon className="h-8 w-8 text-destructive" />
          </div>
          <h3 className="text-lg font-semibold mb-2">{title}</h3>
          <p className="text-sm text-muted-foreground mb-4 max-w-sm">{description}</p>
          <div className="flex flex-col sm:flex-row gap-2 w-full">
            {actions.map((action, actionIndex) => (
              <Button key={actionIndex} variant={action.variant || "default"} size="sm" className="flex-1" onClick={action.onClick}>
                {action.icon && <action.icon className="mr-2 h-4 w-4" />}
                {action.label}
              </Button>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

/**
 * 错误状态容器组件
 */
export function ErrorStateContainer({ children }: { children: ReactNode }) {
  return <div className="space-y-8">{children}</div>
} 
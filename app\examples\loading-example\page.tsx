"use client"

import React, { useState } from "react"
import { ComponentPreviewContainer } from "@/components/component-detail/preview-container"
import { 
  LoadingSpinner, 
  LoadingButton, 
  FullPageLoader, 
  ProgressLoader,
  LoadingContainer
} from "@/components/common-custom/loading"
import { PageLoader } from "@/components/project-custom/page-loading/page-loader"
import { Button } from "@/components/ui/button"
import { Loader2, RefreshCw, CheckCircle } from "lucide-react"

// 基础加载状态示例代码
const basicLoadingCode = `
import React from "react";
import { LoadingSpinner } from "@/components/common-custom/loading";

function BasicLoading() {
  return (
    <div className="space-y-8">
      <div>
        <h3 className="text-sm font-medium mb-4">不同尺寸</h3>
        <div className="flex flex-wrap items-center gap-8">
          <div className="flex flex-col items-center">
            <LoadingSpinner size="xs" />
            <span className="text-xs mt-2">极小</span>
          </div>
          <div className="flex flex-col items-center">
            <LoadingSpinner size="sm" />
            <span className="text-xs mt-2">小</span>
          </div>
          <div className="flex flex-col items-center">
            <LoadingSpinner size="md" />
            <span className="text-xs mt-2">中</span>
          </div>
          <div className="flex flex-col items-center">
            <LoadingSpinner size="lg" />
            <span className="text-xs mt-2">大</span>
          </div>
          <div className="flex flex-col items-center">
            <LoadingSpinner size="xl" />
            <span className="text-xs mt-2">超大</span>
          </div>
        </div>
      </div>
      
      <div>
        <h3 className="text-sm font-medium mb-4">不同变体</h3>
        <div className="flex flex-wrap items-center gap-8">
          <div className="flex flex-col items-center">
            <LoadingSpinner variant="default" />
            <span className="text-xs mt-2">默认</span>
          </div>
          <div className="flex flex-col items-center">
            <LoadingSpinner variant="primary" />
            <span className="text-xs mt-2">主色</span>
          </div>
          <div className="flex flex-col items-center">
            <LoadingSpinner variant="secondary" />
            <span className="text-xs mt-2">次要</span>
          </div>
          <div className="flex flex-col items-center">
            <LoadingSpinner variant="success" />
            <span className="text-xs mt-2">成功</span>
          </div>
          <div className="flex flex-col items-center">
            <LoadingSpinner variant="warning" />
            <span className="text-xs mt-2">警告</span>
          </div>
          <div className="flex flex-col items-center">
            <LoadingSpinner variant="danger" />
            <span className="text-xs mt-2">危险</span>
          </div>
          <div className="flex flex-col items-center">
            <LoadingSpinner variant="circular" />
            <span className="text-xs mt-2">圆形</span>
          </div>
        </div>
      </div>
    </div>
  );
}

render(<BasicLoading />);
`;

// 加载按钮示例代码
const loadingButtonCode = `
import React, { useState } from "react";
import { LoadingButton } from "@/components/common-custom/loading";
import { Button } from "@/components/ui/button";

function LoadingButtonExample() {
  const [isLoading, setIsLoading] = useState(false);
  const [isLoading2, setIsLoading2] = useState(false);
  
  const handleClick = (setter) => {
    setter(true);
    setTimeout(() => setter(false), 2000);
  };
  
  return (
    <div className="space-y-6">
      <div className="flex flex-wrap items-center gap-4">
        <LoadingButton 
          loading={isLoading} 
          onClick={() => handleClick(setIsLoading)}
        >
          {isLoading ? "提交中..." : "提交表单"}
        </LoadingButton>
        
        <LoadingButton 
          loading={isLoading2} 
          variant="outline" 
          loadingText="处理中..." 
          onClick={() => handleClick(setIsLoading2)}
        >
          处理数据
        </LoadingButton>
        
        <LoadingButton 
          loading={isLoading} 
          variant="secondary"
          onClick={() => handleClick(setIsLoading)}
        >
          保存设置
        </LoadingButton>
      </div>
      
      <div className="flex flex-wrap items-center gap-4 pt-4 border-t">
        <LoadingButton loading={true}>
          始终加载
        </LoadingButton>
        
        <LoadingButton 
          loading={true} 
          variant="destructive" 
          loadingText="删除中..."
        >
          删除
        </LoadingButton>
        
        <LoadingButton 
          loading={true} 
          variant="ghost"
          loadingText="验证中..."
        >
          验证
        </LoadingButton>
      </div>
    </div>
  );
}

render(<LoadingButtonExample />);
`;

// 页面加载器示例代码
const pageLoaderCode = `
import React from "react";
import { PageLoader } from "@/components/common-custom/page-loader";
import { Loader2 } from "lucide-react";

function PageLoaderExample() {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-sm font-medium mb-4">基础页面加载器</h3>
        <div className="border rounded-md p-4 h-[200px] relative">
          <PageLoader />
        </div>
      </div>
      
      <div>
        <h3 className="text-sm font-medium mb-4">页面过渡效果</h3>
        <div className="border rounded-md p-4 h-[200px] relative">
          <div className="p-4">
            <h4 className="text-base font-medium mb-2">页面内容</h4>
            <p className="mb-4">当页面切换时，会显示页面加载状态。</p>
          </div>
          
          <div className="absolute inset-0 bg-background/80 backdrop-blur-[2px] flex items-center justify-center">
            <div className="bg-card rounded-lg shadow-lg p-4 flex items-center gap-3">
              <Loader2 className="h-6 w-6 animate-spin text-primary" />
              <span className="text-sm font-medium">页面加载中...</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

render(<PageLoaderExample />);
`;

// 全页面加载器示例代码
const fullPageLoaderCode = `
import React from "react";
import { FullPageLoader } from "@/components/common-custom/loading";

function FullPageLoaderExample() {
  return (
    <div className="space-y-6">
      <div className="border rounded-md p-4">
        <FullPageLoader
          text="数据加载中"
          subText="正在获取数据，请稍候片刻"
          status="loading"
        />
      </div>
      
      <div className="border rounded-md p-4">
        <FullPageLoader
          text="加载成功"
          subText="数据已成功加载"
          status="success"
        />
      </div>
      
      <div className="border rounded-md p-4">
        <FullPageLoader
          text="加载失败"
          subText="无法获取数据，请稍后重试"
          status="error"
        />
      </div>
    </div>
  );
}

render(<FullPageLoaderExample />);
`;

// 进度加载器示例代码
const progressLoaderCode = `
import React, { useState, useEffect } from "react";
import { ProgressLoader } from "@/components/common-custom/loading";
import { Button } from "@/components/ui/button";

function ProgressLoaderExample() {
  const [progress1, setProgress1] = useState(30);
  const [progress2, setProgress2] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  
  useEffect(() => {
    if (isLoading) {
      const interval = setInterval(() => {
        setProgress2(prev => {
          if (prev >= 100) {
            clearInterval(interval);
            setIsLoading(false);
            return 100;
          }
          return prev + 10;
        });
      }, 500);
      
      return () => clearInterval(interval);
    } else {
      setProgress2(0);
    }
  }, [isLoading]);
  
  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <h3 className="text-sm font-medium">基础进度条</h3>
        <ProgressLoader 
          progress={progress1} 
          label="文件上传" 
        />
        <div className="flex gap-2">
          <Button size="sm" onClick={() => setProgress1(Math.max(0, progress1 - 10))}>减少</Button>
          <Button size="sm" onClick={() => setProgress1(Math.min(100, progress1 + 10))}>增加</Button>
        </div>
      </div>
      
      <div className="space-y-4 pt-4 border-t">
        <h3 className="text-sm font-medium">模拟进度</h3>
        <ProgressLoader 
          progress={progress2} 
          label="数据下载" 
          subLabel={progress2 === 100 ? "下载完成" : "正在下载..."}
        />
        <Button 
          size="sm" 
          onClick={() => setIsLoading(true)}
          disabled={isLoading}
        >
          {isLoading ? "下载中..." : "开始下载"}
        </Button>
      </div>
    </div>
  );
}

render(<ProgressLoaderExample />);
`;

// 加载容器示例代码
const loadingContainerCode = `
import React, { useState } from "react";
import { LoadingContainer, LoadingSpinner } from "@/components/common-custom/loading";
import { Button } from "@/components/ui/button";

function LoadingContainerExample() {
  const [loading, setLoading] = useState(false);
  
  const toggleLoading = () => {
    setLoading(true);
    setTimeout(() => setLoading(false), 2000);
  };
  
  return (
    <div className="space-y-4">
      <Button onClick={toggleLoading}>
        {loading ? "加载中..." : "显示/隐藏内容"}
      </Button>
      
      <div className="border rounded-md p-4">
        <LoadingContainer 
          loading={loading} 
          fallback={
            <div className="h-40 flex flex-col items-center justify-center">
              <LoadingSpinner size="lg" />
              <p className="mt-4 text-muted-foreground">内容加载中，请稍候...</p>
            </div>
          }
        >
          <div className="space-y-4">
            <h3 className="text-lg font-medium">内容标题</h3>
            <p>这是一些示例内容，在加载状态下会被替换为加载指示器。</p>
            <p>当加载完成后，内容会自动显示出来。</p>
          </div>
        </LoadingContainer>
      </div>
    </div>
  );
}

render(<LoadingContainerExample />);
`;

// API文档组件
function LoadingApiDocs() {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="font-medium text-lg mb-2">加载组件API</h3>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">组件</th>
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border" rowSpan={3}>LoadingSpinner</td>
                <td className="p-2 border">size</td>
                <td className="p-2 border">xs | sm | md | lg | xl</td>
                <td className="p-2 border">md</td>
                <td className="p-2 border">加载器尺寸</td>
              </tr>
              <tr>
                <td className="p-2 border">variant</td>
                <td className="p-2 border">default | primary | secondary | success | warning | danger | circular</td>
                <td className="p-2 border">default</td>
                <td className="p-2 border">加载器变体样式</td>
              </tr>
              <tr>
                <td className="p-2 border">className</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">自定义类名</td>
              </tr>
              <tr>
                <td className="p-2 border" rowSpan={5}>LoadingButton</td>
                <td className="p-2 border">loading</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">false</td>
                <td className="p-2 border">是否显示加载状态</td>
              </tr>
              <tr>
                <td className="p-2 border">loadingText</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">加载中...</td>
                <td className="p-2 border">加载状态文本</td>
              </tr>
              <tr>
                <td className="p-2 border">variant</td>
                <td className="p-2 border">default | destructive | outline | secondary | ghost | link</td>
                <td className="p-2 border">default</td>
                <td className="p-2 border">按钮变体样式</td>
              </tr>
              <tr>
                <td className="p-2 border">size</td>
                <td className="p-2 border">default | sm | lg | icon</td>
                <td className="p-2 border">default</td>
                <td className="p-2 border">按钮尺寸</td>
              </tr>
              <tr>
                <td className="p-2 border">className</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">自定义类名</td>
              </tr>
              <tr>
                <td className="p-2 border" rowSpan={4}>FullPageLoader</td>
                <td className="p-2 border">text</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">加载中...</td>
                <td className="p-2 border">主要文本</td>
              </tr>
              <tr>
                <td className="p-2 border">subText</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">正在获取数据，请稍候</td>
                <td className="p-2 border">次要文本</td>
              </tr>
              <tr>
                <td className="p-2 border">status</td>
                <td className="p-2 border">loading | success | error</td>
                <td className="p-2 border">loading</td>
                <td className="p-2 border">加载状态</td>
              </tr>
              <tr>
                <td className="p-2 border">className</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">自定义类名</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

export default function LoadingPreview() {
  const examples = [
    {
      id: "basic-loading",
      title: "基础加载状态",
      description: "基础的加载旋转器，支持多种尺寸和样式",
      code: basicLoadingCode,
      scope: { 
        React, 
        LoadingSpinner 
      },
    },
    {
      id: "loading-button",
      title: "加载按钮",
      description: "带有加载状态的按钮，可用于表单提交或数据处理场景",
      code: loadingButtonCode,
      scope: { 
        React, 
        LoadingButton, 
        Button,
        useState: React.useState 
      },
    },
    {
      id: "page-loader",
      title: "页面加载器",
      description: "用于整个页面或页面区域的加载状态展示",
      code: pageLoaderCode,
      scope: { 
        React, 
        PageLoader,
        Loader2
      },
    },
    {
      id: "full-page-loader",
      title: "全页面加载器",
      description: "全页面加载组件，支持成功、错误等多种状态",
      code: fullPageLoaderCode,
      scope: { 
        React, 
        FullPageLoader 
      },
    },
    {
      id: "progress-loader",
      title: "进度加载器",
      description: "显示加载进度的进度条组件",
      code: progressLoaderCode,
      scope: { 
        React, 
        ProgressLoader, 
        Button,
        useState: React.useState,
        useEffect: React.useEffect
      },
    },
    {
      id: "loading-container",
      title: "加载容器",
      description: "用于包装内容的加载容器，可自定义加载状态下显示的内容",
      code: loadingContainerCode,
      scope: { 
        React, 
        LoadingContainer, 
        LoadingSpinner,
        Button,
        useState: React.useState
      },
    },
  ];

  return (
    <ComponentPreviewContainer
      title="加载状态 Loading"
      description="用于展示各种加载状态和指示器的组件集合"
      whenToUse="当操作需要一段时间完成时，使用加载组件向用户提供反馈；当页面内容正在加载时；当表单提交或数据处理正在进行时；当需要显示操作进度时"
      examples={examples}
      apiDocs={<LoadingApiDocs />}
    />
  );
} 
 
 
 

 
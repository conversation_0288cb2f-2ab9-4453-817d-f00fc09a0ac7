"use client"

import * as React from "react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuCheckboxItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { ChevronUp, ChevronDown, Search, Settings, Download, Plus } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { DataTableProps, DataTableColumn } from "./data-table/types"
import { DataTableBadge } from "@/components/ui/data-table-badge"
import { DataTableActions } from "@/components/ui/data-table-actions"

export function DataTable<T extends Record<string, any>>({
  data,
  columns,
  onRowSelectionChange,
  onSortChange,
  searchPlaceholder = "搜索...",
  noResultsMessage = "没有找到结果",
  showToolbar = true,
  showSelectAll = true,
  showStatusBadge = true,
  viewMode = "table",
  onViewModeChange,
  getRowId = (row: T) => row.id as string | number,
  renderBadge,
  renderActions,
  onExport,
  onAdd,
}: DataTableProps<T>) {
  const [selectedRows, setSelectedRows] = React.useState<(string | number)[]>([])
  const [sortField, setSortField] = React.useState<keyof T | null>(null)
  const [sortDirection, setSortDirection] = React.useState<"asc" | "desc">("asc")
  const [searchTerm, setSearchTerm] = React.useState("")
  const [filteredData, setFilteredData] = React.useState<T[]>(data)
  const [visibleColumns, setVisibleColumns] = React.useState<Record<string, boolean>>(
    columns.reduce((acc, column) => ({ ...acc, [column.id]: true }), {})
  )

  // 更新过滤数据
  React.useEffect(() => {
    if (!searchTerm) {
      setFilteredData(data)
      return
    }

    const filtered = data.filter((row) => {
      return Object.values(row).some((value) => {
        if (typeof value === "string") {
          return value.toLowerCase().includes(searchTerm.toLowerCase())
        }
        return false
      })
    })
    
    setFilteredData(filtered)
  }, [data, searchTerm])

  // 处理排序
  const handleSort = (field: keyof T) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortDirection("asc")
    }

    if (onSortChange) {
      onSortChange(field, sortDirection === "asc" ? "desc" : "asc")
    }
  }

  // 处理选择所有行
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedRows(filteredData.map((row) => getRowId(row)))
    } else {
      setSelectedRows([])
    }

    if (onRowSelectionChange) {
      onRowSelectionChange(checked ? [...filteredData] : [])
    }
  }

  // 处理选择单行
  const handleSelectRow = (id: string | number, checked: boolean) => {
    if (checked) {
      setSelectedRows([...selectedRows, id])
    } else {
      setSelectedRows(selectedRows.filter((rowId) => rowId !== id))
    }

    if (onRowSelectionChange) {
      const newSelectedRows = checked
        ? [...data.filter((row) => selectedRows.includes(getRowId(row))), ...data.filter((row) => getRowId(row) === id)]
        : data.filter((row) => selectedRows.includes(getRowId(row)) && getRowId(row) !== id)
      
      onRowSelectionChange(newSelectedRows)
    }
  }

  // 排序图标组件
  const SortIcon = ({ field }: { field: keyof T }) => {
    if (sortField !== field) return null
    return sortDirection === "asc" ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />
  }

  // 渲染表格视图
  const renderTableView = () => (
    <div className="border rounded-md overflow-hidden">
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              {showSelectAll && (
                <TableHead className="w-12">
                  <Checkbox
                    checked={
                      selectedRows.length === filteredData.length && 
                      filteredData.length > 0
                    }
                    onCheckedChange={handleSelectAll}
                  />
                </TableHead>
              )}
              {columns.map((column) => (
                visibleColumns[column.id] && (
                  <TableHead key={column.id}>
                    {column.enableSorting ? (
                      <Button
                        variant="ghost"
                        onClick={() => column.accessorKey && handleSort(column.accessorKey)}
                        className="h-auto p-0 font-semibold hover:bg-transparent"
                      >
                        {column.header} {column.accessorKey && <SortIcon field={column.accessorKey} />}
                      </Button>
                    ) : (
                      column.header
                    )}
                  </TableHead>
                )
              ))}
              {renderActions && <TableHead className="w-20">操作</TableHead>}
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredData.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={
                    (showSelectAll ? 1 : 0) +
                    columns.filter(col => visibleColumns[col.id]).length +
                    (renderActions ? 1 : 0)
                  }
                  className="text-center py-6 text-muted-foreground"
                >
                  {noResultsMessage}
                </TableCell>
              </TableRow>
            ) : (
              filteredData.map((row) => {
                const rowId = getRowId(row)
                return (
                  <TableRow
                    key={rowId}
                    className={selectedRows.includes(rowId) ? "bg-muted/50" : ""}
                  >
                    {showSelectAll && (
                      <TableCell>
                        <Checkbox
                          checked={selectedRows.includes(rowId)}
                          onCheckedChange={(checked) => handleSelectRow(rowId, checked as boolean)}
                        />
                      </TableCell>
                    )}
                    {columns.map((column) => (
                      visibleColumns[column.id] && (
                        <TableCell key={`${rowId}-${column.id}`}>
                          {column.cell
                            ? column.cell({ row })
                            : column.accessorKey
                              ? row[column.accessorKey]
                              : null}
                        </TableCell>
                      )
                    ))}
                    {renderActions && (
                      <TableCell className="text-right p-0 pr-2">
                        {renderActions(row)}
                      </TableCell>
                    )}
                  </TableRow>
                )
              })
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )

  // 渲染卡片视图
  const renderCardView = () => (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
      {filteredData.length === 0 ? (
        <div className="col-span-full text-center py-6 text-muted-foreground">
          {noResultsMessage}
        </div>
      ) : (
        filteredData.map((row) => {
          const rowId = getRowId(row)
          return (
            <Card key={rowId} className={selectedRows.includes(rowId) ? "ring-2 ring-primary" : ""}>
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    {showSelectAll && (
                      <Checkbox
                        checked={selectedRows.includes(rowId)}
                        onCheckedChange={(checked) => handleSelectRow(rowId, checked as boolean)}
                      />
                    )}
                    {row.avatar && (
                      <Avatar className="h-10 w-10">
                        <AvatarFallback>
                          {typeof row.name === 'string' ? row.name.charAt(0) : ''}
                        </AvatarFallback>
                      </Avatar>
                    )}
                    <div>
                      <CardTitle className="text-base">
                        {row.name || row.title || `项目 #${rowId}`}
                      </CardTitle>
                      {row.description && (
                        <CardDescription className="text-sm">{row.description}</CardDescription>
                      )}
                    </div>
                  </div>
                  {renderActions && renderActions(row)}
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-2 text-sm">
                  {columns
                    .filter((column) => 
                      visibleColumns[column.id] && 
                      column.accessorKey && 
                      column.accessorKey !== 'name' && 
                      column.accessorKey !== 'title' && 
                      column.accessorKey !== 'description'
                    )
                    .map((column) => (
                      <div key={column.id} className="flex justify-between">
                        <span className="text-muted-foreground">{column.header}:</span>
                        <span>
                          {column.cell
                            ? column.cell({ row })
                            : column.accessorKey ? row[column.accessorKey] : ''}
                        </span>
                      </div>
                    ))
                  }
                </div>
              </CardContent>
            </Card>
          )
        })
      )}
    </div>
  )

  // 渲染列表视图
  const renderListView = () => (
    <div className="space-y-2">
      {filteredData.length === 0 ? (
        <div className="text-center py-6 text-muted-foreground">
          {noResultsMessage}
        </div>
      ) : (
        filteredData.map((row) => {
          const rowId = getRowId(row)
          const mainField = columns.find((col) => col.meta?.isTitle)?.accessorKey || 'name'
          const secondaryField = columns.find((col) => col.meta?.isSubtitle)?.accessorKey || 'description'
          
          return (
            <Card key={rowId} className={`p-4 ${selectedRows.includes(rowId) ? "ring-2 ring-primary" : ""}`}>
              <div className="flex items-center gap-4">
                {showSelectAll && (
                  <Checkbox
                    checked={selectedRows.includes(rowId)}
                    onCheckedChange={(checked) => handleSelectRow(rowId, checked as boolean)}
                  />
                )}
                {row.avatar && (
                  <Avatar className="h-10 w-10">
                    <AvatarFallback>
                      {typeof row[mainField] === 'string' ? row[mainField].charAt(0) : ''}
                    </AvatarFallback>
                  </Avatar>
                )}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <h4 className="font-medium truncate">
                      {row[mainField] || `项目 #${rowId}`}
                    </h4>
                    {showStatusBadge && row.status && renderBadge && renderBadge(row.status)}
                  </div>
                  <div className="flex flex-wrap items-center gap-2 sm:gap-4 text-sm text-muted-foreground">
                    {secondaryField && row[secondaryField] && (
                      <span className="truncate max-w-[120px] sm:max-w-full">
                        {row[secondaryField]}
                      </span>
                    )}
                    {columns
                      .filter((column) => 
                        visibleColumns[column.id] && 
                        column.accessorKey && 
                        column.accessorKey !== mainField && 
                        column.accessorKey !== secondaryField && 
                        column.accessorKey !== 'status'
                      )
                      .slice(0, 3)
                      .map((column, i, arr) => (
                        <React.Fragment key={column.id}>
                          {i > 0 && <span className="hidden xs:inline">•</span>}
                          <span>
                            {column.cell
                              ? column.cell({ row })
                              : column.accessorKey ? row[column.accessorKey] : ''}
                          </span>
                        </React.Fragment>
                      ))
                    }
                  </div>
                </div>
                {renderActions && renderActions(row)}
              </div>
            </Card>
          )
        })
      )}
    </div>
  )

  return (
    <div className="space-y-4">
      {showToolbar && (
        <div className="flex flex-col sm:flex-row gap-2 sm:items-center justify-between">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={searchPlaceholder}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8 w-full sm:w-64"
            />
          </div>

          <div className="flex gap-2">
            {onViewModeChange && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="h-8 w-8">
                    <Settings className="w-4 h-4" />
                    <span className="sr-only">表格设置</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <div className="p-2">
                    <div className="text-sm font-medium mb-2">显示列</div>
                    {columns.map((column) => (
                      <DropdownMenuCheckboxItem
                        key={column.id}
                        checked={visibleColumns[column.id]}
                        onCheckedChange={(checked) => 
                          setVisibleColumns((prev) => ({ ...prev, [column.id]: checked }))
                        }
                      >
                        {column.header}
                      </DropdownMenuCheckboxItem>
                    ))}
                  </div>
                </DropdownMenuContent>
              </DropdownMenu>
            )}

            {selectedRows.length > 0 && onExport && (
              <Button variant="outline" size="sm" onClick={onExport} className="h-8">
                <Download className="w-4 h-4 mr-2" />
                导出
              </Button>
            )}

            {onAdd && (
              <Button size="sm" className="h-8" onClick={onAdd}>
                <Plus className="w-4 h-4 mr-2" />
                <span className="hidden sm:inline">新增</span>
              </Button>
            )}
          </div>
        </div>
      )}

      {viewMode === "table" 
        ? renderTableView()
        : viewMode === "card" 
          ? renderCardView() 
          : renderListView()
      }
      
      <div className="text-sm text-muted-foreground">
        显示 {filteredData.length} 条记录，共 {data.length} 条
        {selectedRows.length > 0 && ` • 已选择 ${selectedRows.length} 条`}
      </div>
    </div>
  )
}

// 重新导出子组件和类型，方便使用
export { DataTableBadge } from "@/components/ui/data-table-badge"
export { DataTableActions } from "@/components/ui/data-table-actions"
export type { DataTableColumn, DataTableProps } from "./data-table/types"
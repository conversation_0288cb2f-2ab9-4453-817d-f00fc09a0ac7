"use client"

import { useState, useEffect, useMemo } from "react"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  Search, 
  Plus, 
  Edit, 
  Trash2, 
  MoreHorizontal, 
  AlertTriangle,
  Loader2,
  Users,
  Shield,
  CheckCircle2,
  ShieldCheck,
  ShieldAlert,
  ArrowUpDown,
  Eye,
  SquarePen,
  ChevronLeft,
  ChevronRight
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { NavigationLink } from "@/components/custom"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "sonner"
import Link from "next/link"
import { formatDate } from "@/lib/utils"
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"

// 导入角色相关API
import { 
  getRolesPageRequest, 
  createRoleRequest, 
  deleteRoleRequest 
} from "@/services/api/roleRequestApi"
import { Role } from "@/types/models"
import { PageResult, RolePaginationParams } from "@/types/api"

export default function RoleManagement() {
  // 状态定义
  const [roles, setRoles] = useState<Role[]>([])
  const [searchQuery, setSearchQuery] = useState("")
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [sortOrder, setSortOrder] = useState<"name-asc" | "name-desc" | "users-asc" | "users-desc">("name-asc")
  
  // 分页状态
  const [pagination, setPagination] = useState({
    total: 0,
    pageNum: 1,
    pageSize: 10,
    pages: 0
  })
  
  // 对话框状态
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [editingRole, setEditingRole] = useState<Role | null>(null)
  const [deleteConfirmName, setDeleteConfirmName] = useState("")
  const [roleToDelete, setRoleToDelete] = useState<Role | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  
  // 表单状态
  const [newRoleName, setNewRoleName] = useState("")
  const [newRoleDescription, setNewRoleDescription] = useState("")
  const [newRoleCode, setNewRoleCode] = useState("")
  
  // 加载角色数据
  const loadRoles = async () => {
    setIsLoading(true)
    setError(null)
    
    try {
      // 构建查询参数
      const params: RolePaginationParams = {
        roleName: searchQuery,
        roleCode: "",
        teamCode: "",
        status: 1,
        pageNum: pagination.pageNum,
        pageSize: pagination.pageSize
      }
      
      const result = await getRolesPageRequest(params)
      setRoles(result.list)
      setPagination({
        total: result.total,
        pageNum: result.pageNum,
        pageSize: result.pageSize,
        pages: result.pages
      })
    } catch (err) {
      console.error("加载角色数据失败", err)
      setError("加载角色数据失败，请稍后重试")
    } finally {
      setIsLoading(false)
    }
  }
  
  // 初始加载
  useEffect(() => {
    loadRoles()
  }, [pagination.pageNum, pagination.pageSize])
  
  // 当搜索条件变化时，重置到第一页并重新加载
  useEffect(() => {
    const handler = setTimeout(() => {
      if (pagination.pageNum === 1) {
        loadRoles()
      } else {
        setPagination(prev => ({
          ...prev,
          pageNum: 1
        }))
      }
    }, 500)
    
    return () => {
      clearTimeout(handler)
    }
  }, [searchQuery])
  
  // 处理分页变化
  const handlePageChange = (page: number) => {
    setPagination(prev => ({
      ...prev,
      pageNum: page
    }))
  }
  
  // 排序后的角色列表
  const sortedRoles = useMemo(() => {
    return [...roles].sort((a, b) => {
      if (sortOrder === "name-asc") {
        return a.roleName.localeCompare(b.roleName)
      } else if (sortOrder === "name-desc") {
        return b.roleName.localeCompare(a.roleName)
      } else if (sortOrder === "users-asc") {
        return (a.userCount || 0) - (b.userCount || 0)
      } else {
        return (b.userCount || 0) - (a.userCount || 0)
      }
    })
  }, [roles, sortOrder])
  
  // 处理创建角色
  const handleCreateRole = async () => {
    if (!newRoleName.trim()) {
      toast.error("角色名称不能为空")
      return
    }
    
    if (!newRoleCode.trim()) {
      toast.error("角色编码不能为空")
      return
    }
    
    setIsProcessing(true)
    
    try {
      const newRole = {
        roleName: newRoleName.trim(),
        description: newRoleDescription.trim(),
        roleCode: newRoleCode.trim(),
        teamCode: "default", // 默认团队编码
        status: 1 // 默认启用
      }
      
      await createRoleRequest(newRole)
      await loadRoles()
      
      // 重置表单
      setNewRoleName("")
      setNewRoleDescription("")
      setNewRoleCode("")
      setIsCreateDialogOpen(false)
      
    } catch (error) {
      console.error("创建角色失败", error)
      toast.error("创建角色失败，请稍后重试")
    } finally {
      setIsProcessing(false)
    }
  }
  
  // 处理删除角色
  const handleDeleteRole = async () => {
    if (!roleToDelete) return
    
    if (deleteConfirmName !== roleToDelete.roleName) {
      toast.error("确认名称不匹配")
      return
    }
    
    setIsProcessing(true)
    
    try {
      await deleteRoleRequest(String(roleToDelete.id))
      await loadRoles()
      
      setIsDeleteDialogOpen(false)
      setRoleToDelete(null)
      setDeleteConfirmName("")
      
    } catch (error) {
      console.error("删除角色失败", error)
      toast.error("删除角色失败，请稍后重试")
    } finally {
      setIsProcessing(false)
    }
  }
  
  // 打开删除对话框
  const openDeleteDialog = (role: Role) => {
    setRoleToDelete(role)
    setDeleteConfirmName("")
    setIsDeleteDialogOpen(true)
  }
  
  // 生成角色编码
  const generateRoleCode = () => {
    // 基于当前时间戳和随机字符生成唯一编码
    const timestamp = Date.now().toString(36).slice(-4);
    const random = Math.random().toString(36).substring(2, 6).toUpperCase();
    const newCode = `ROLE_${timestamp}${random}`;
    
    setNewRoleCode(newCode);
  };
  
  // 角色列表项操作
  const handleRoleAction = (action: string, role: Role) => {
    switch (action) {
      case 'delete':
        openDeleteDialog(role);
        break;
      default:
        break;
    }
  };
  
  // 表格视图
  const renderRolesTable = () => {
    return (
      <div className="border border-border/30 rounded-lg overflow-hidden shadow-sm">
        <Table>
          <TableHeader className="bg-muted/30">
            <TableRow className="hover:bg-transparent">
              <TableHead className="w-[150px]">角色编码</TableHead>
              <TableHead className="w-[200px]">
                <div className="flex gap-2 items-center">
                  角色名称
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    className="h-4 w-4 -mr-2 -ml-1 rounded-sm cursor-pointer"
                    onClick={() => setSortOrder(sortOrder === "name-asc" ? "name-desc" : "name-asc")}
                  >
                    <ArrowUpDown className="h-3 w-3" />
                  </Button>
                </div>
              </TableHead>
              <TableHead>角色描述</TableHead>
              <TableHead className="w-[100px]">
                <div className="flex gap-2 items-center">
                  用户数
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    className="h-4 w-4 -mr-2 -ml-1 rounded-sm cursor-pointer"
                    onClick={() => setSortOrder(sortOrder === "users-asc" ? "users-desc" : "users-asc")}
                  >
                    <ArrowUpDown className="h-3 w-3" />
                  </Button>
                </div>
              </TableHead>
              <TableHead className="w-[120px] text-center">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {sortedRoles.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="h-24 text-center">
                  {isLoading ? (
                    <div className="flex items-center justify-center">
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      加载中...
                    </div>
                  ) : (
                    "没有符合条件的角色"
                  )}
                </TableCell>
              </TableRow>
            ) : (
              sortedRoles.map((role, index) => (
                <TableRow key={role.id} className="hover:bg-muted/30 transition-colors">
                  <TableCell>
                    {role.roleCode ? (
                      <span className="text-sm">{role.roleCode}</span>
                    ) : (
                      <span className="text-muted-foreground text-sm">--</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div className="font-medium">{role.roleName}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    {role.description ? (
                      <span className="text-sm">{role.description}</span>
                    ) : (
                      <span className="text-muted-foreground text-sm">--</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1.5">
                      <Users className="h-3.5 w-3.5 text-muted-foreground" />
                      <span>{role.userCount || 0}</span>
                    </div>
                  </TableCell>
                  <TableCell className="text-center">
                    <div className="flex items-center justify-center gap-1">
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        className="h-7 px-2 cursor-pointer"
                        asChild
                      >
                        <Link href={`/settings/roles/${role.id}`}>
                          <Eye className="h-3.5 w-3.5" />
                        </Link>
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        className="h-7 px-2 cursor-pointer"
                        asChild
                      >
                        <Link href={`/settings/roles/${role.id}?mode=edit`}>
                          <Edit className="h-3.5 w-3.5" />
                        </Link>
                      </Button>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            className="h-7 px-2 cursor-pointer"
                          >
                            <MoreHorizontal className="h-3.5 w-3.5" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem 
                            className="cursor-pointer text-destructive"
                            onClick={() => handleRoleAction('delete', role)}
                          >
                            <Trash2 className="h-3.5 w-3.5 mr-2" />
                            删除
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
        
        {/* 分页控制 */}
        {pagination.total > 0 && (
          <div className="flex flex-col sm:flex-row items-center justify-between px-4 py-3 border-t border-border/30 gap-2">
            <div className="text-sm text-muted-foreground">
              共 {pagination.total} 条记录，第 {pagination.pageNum}/{pagination.pages} 页
            </div>
            <div className="flex items-center gap-2">
              <div className="flex items-center mr-2">
                <span className="text-sm text-muted-foreground mr-2">每页显示:</span>
                <select 
                  className="h-8 rounded-md border border-input bg-background px-2 text-sm"
                  value={pagination.pageSize}
                  onChange={(e) => {
                    setPagination(prev => ({
                      ...prev,
                      pageSize: Number(e.target.value),
                      pageNum: 1 // 重置到第一页
                    }))
                  }}
                >
                  {[10, 20, 50, 100].map(size => (
                    <option key={size} value={size}>{size}</option>
                  ))}
                </select>
              </div>
              <div className="flex items-center gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  className="h-8 w-8 p-0 cursor-pointer"
                  disabled={pagination.pageNum <= 1}
                  onClick={() => handlePageChange(1)}
                >
                  <ChevronLeft className="h-3 w-3" />
                  <ChevronLeft className="h-3 w-3 -ml-1" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="h-8 w-8 p-0 cursor-pointer"
                  disabled={pagination.pageNum <= 1}
                  onClick={() => handlePageChange(pagination.pageNum - 1)}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                {Array.from({ length: pagination.pages }, (_, i) => i + 1)
                  .filter(page => {
                    // 只显示当前页附近的页码
                    return (
                      page === 1 ||
                      page === pagination.pages ||
                      Math.abs(page - pagination.pageNum) <= 1
                    );
                  })
                  .map((page, index, array) => {
                    // 添加省略号
                    const showEllipsis = index > 0 && page - array[index - 1] > 1;
                    
                    return (
                      <div key={page} className="flex items-center">
                        {showEllipsis && (
                          <span className="px-2 text-muted-foreground">...</span>
                        )}
                        <Button
                          variant={pagination.pageNum === page ? "default" : "outline"}
                          size="sm"
                          className="h-8 w-8 p-0 cursor-pointer"
                          onClick={() => handlePageChange(page)}
                        >
                          {page}
                        </Button>
                      </div>
                    );
                  })}
                <Button
                  variant="outline"
                  size="sm"
                  className="h-8 w-8 p-0 cursor-pointer"
                  disabled={pagination.pageNum >= pagination.pages}
                  onClick={() => handlePageChange(pagination.pageNum + 1)}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="h-8 w-8 p-0 cursor-pointer"
                  disabled={pagination.pageNum >= pagination.pages}
                  onClick={() => handlePageChange(pagination.pages)}
                >
                  <ChevronRight className="h-3 w-3" />
                  <ChevronRight className="h-3 w-3 -ml-1" />
                </Button>
              </div>
              <div className="flex items-center ml-2">
                <span className="text-sm text-muted-foreground mr-2">跳至:</span>
                <Input
                  type="number"
                  min={1}
                  max={pagination.pages}
                  className="h-8 w-16 px-2 text-sm"
                  value={pagination.pageNum}
                  onChange={(e) => {
                    const page = parseInt(e.target.value);
                    if (!isNaN(page) && page >= 1 && page <= pagination.pages) {
                      handlePageChange(page);
                    }
                  }}
                  onBlur={(e) => {
                    const page = parseInt(e.target.value);
                    if (isNaN(page) || page < 1) {
                      e.target.value = "1";
                      handlePageChange(1);
                    } else if (page > pagination.pages) {
                      e.target.value = String(pagination.pages);
                      handlePageChange(pagination.pages);
                    }
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      const page = parseInt((e.target as HTMLInputElement).value);
                      if (!isNaN(page) && page >= 1 && page <= pagination.pages) {
                        handlePageChange(page);
                      }
                    }
                  }}
                />
              </div>
            </div>
          </div>
        )}
      </div>
    )
  }
  
  return (
    <div className="space-y-4">
      <div className="relative flex-1">
        <div className="flex items-center gap-2">
          <div className="relative flex-grow">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="搜索角色..."
              className="pl-8 h-9 border border-input/30 bg-background/50 ring-1 ring-border/5 hover:border-input/50 transition-colors focus-visible:border-ring"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Button 
            variant="default" 
            className="shadow-sm cursor-pointer"
            onClick={() => setIsCreateDialogOpen(true)}
          >
            <Plus className="h-4 w-4 mr-1" />
            创建角色
          </Button>
        </div>
      </div>
      
      <div className="relative min-h-[300px]">
        {/* 加载动画 */}
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-background/50 z-10 backdrop-blur-[1px]">
            <div className="flex flex-col items-center gap-2">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <p className="text-sm text-muted-foreground">加载角色数据中...</p>
            </div>
          </div>
        )}
        
        {/* 角色数据表格 */}
        {renderRolesTable()}
      </div>
      
      {/* 创建角色对话框 */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-[425px] border border-border shadow-md">
          <DialogHeader>
            <DialogTitle className="text-lg font-semibold">创建新角色</DialogTitle>
            <DialogDescription className="text-sm text-muted-foreground mt-1">
              创建新的系统角色，设置基本信息。角色创建后，可以进一步分配权限。
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-5 py-5">
            <div className="grid gap-2">
              <Label htmlFor="name" className="text-sm font-medium">
                角色名称 <span className="text-destructive">*</span>
              </Label>
              <Input
                id="name"
                placeholder="请输入角色名称"
                value={newRoleName}
                onChange={(e) => setNewRoleName(e.target.value)}
                className="pl-3 h-9 border border-input/30 bg-background/50 ring-1 ring-border/5 hover:border-input/50 transition-colors focus-visible:border-ring"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="code" className="text-sm font-medium">
                角色编码
              </Label>
              <div className="flex gap-2">
                <Input
                  id="code"
                  placeholder="请输入角色编码"
                  value={newRoleCode}
                  onChange={(e) => setNewRoleCode(e.target.value)}
                  className="pl-3 h-9 border border-input/30 bg-background/50 ring-1 ring-border/5 hover:border-input/50 transition-colors focus-visible:border-ring"
                />
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={generateRoleCode}
                  className="cursor-pointer shrink-0 h-9"
                >
                  生成
                </Button>
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                角色编码用于系统内部标识，只能在创建时设置
              </p>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="description" className="text-sm font-medium">
                角色描述
              </Label>
              <Textarea
                id="description"
                placeholder="请输入角色描述"
                value={newRoleDescription}
                onChange={(e) => setNewRoleDescription(e.target.value)}
                className="min-h-[80px] border border-input/30 bg-background/50 ring-1 ring-border/5 hover:border-input/50 transition-colors focus-visible:border-ring resize-none"
              />
            </div>
          </div>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsCreateDialogOpen(false)}
              disabled={isProcessing}
              className="cursor-pointer"
            >
              取消
            </Button>
            <Button 
              type="submit" 
              onClick={handleCreateRole}
              disabled={isProcessing || !newRoleName.trim()}
              className="cursor-pointer shadow-sm"
            >
              {isProcessing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  创建中...
                </>
              ) : (
                '创建角色'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* 删除确认对话框 */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="max-w-[450px] border border-destructive/20">
          <DialogHeader>
            <DialogTitle className="text-destructive">确认删除角色</DialogTitle>
            <DialogDescription>
              此操作将永久删除该角色及其相关数据，且<span className="font-semibold">不可恢复</span>。请输入角色名称 <span className="font-medium">{roleToDelete?.roleName}</span> 以确认删除。
            </DialogDescription>
          </DialogHeader>
          <div className="py-6">
            <div className="space-y-4">
              <div className="bg-destructive/5 p-3 rounded-md border border-destructive/20 text-sm">
                <div className="flex gap-2 items-start">
                  <AlertTriangle className="h-5 w-5 text-destructive mt-0.5" />
                  <div>
                    <p className="font-medium text-foreground mb-1">删除后，以下内容将被永久删除：</p>
                    <ul className="list-disc pl-5 space-y-1 text-muted-foreground">
                      <li>角色的基本信息</li>
                      <li>角色与用户的关联关系</li>
                      <li>角色的权限配置</li>
                    </ul>
                  </div>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="confirm-delete" className="text-sm font-medium">
                  输入角色名称确认删除
                </Label>
                <Input
                  id="confirm-delete"
                  placeholder={`请输入：${roleToDelete?.roleName}`}
                  value={deleteConfirmName}
                  onChange={(e) => setDeleteConfirmName(e.target.value)}
                  className="pl-3 h-9 border border-destructive/30 bg-background/50 ring-1 ring-destructive/10 hover:border-destructive/50 transition-colors focus-visible:border-ring"
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={isProcessing}
              className="cursor-pointer"
            >
              取消
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleDeleteRole}
              disabled={isProcessing || deleteConfirmName !== (roleToDelete?.roleName || "")}
              className="cursor-pointer"
            >
              {isProcessing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  删除中...
                </>
              ) : (
                '确认删除'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
} 
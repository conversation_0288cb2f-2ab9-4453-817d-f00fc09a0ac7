---
description: 
globs: 
alwaysApply: true
---
# 项目结构规则

本项目是一个基于 Next.js 15、React、TypeScript 和 shadcn/ui 的前端项目。

## 主要目录说明

- `app/` - Next.js App Router 应用目录，包含页面、路由和布局
- `components/` - React 组件，按功能和类型分组
  - `ui/` - shadcn/ui 基础组件
  - `custom/` - 自定义组件
  - `layout/` - 布局相关组件
  - `navigation/` - 导航相关组件  
  - `pages/` - 页面特定组件
  - `common/` - 多页面共享组件
- `services/` - 服务层
  - `api/` - API 请求接口，命名规则为xxxRequestApi.ts
  - `mock/` - 模拟数据，命名规则为xxxMock.ts
- `lib/` - 工具函数和服务
- `types/` - TypeScript 类型定义
- `public/` - 静态资源
- `hooks/` - 自定义钩子

## 重要文件
- `package.json` - 项目依赖和脚本
- `next.config.ts` - Next.js 配置
- `components.json` - shadcn/ui 配置
- `tsconfig.json` - TypeScript 配置

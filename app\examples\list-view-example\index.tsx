"use client"

import { useState } from "react"
import { 
  MessageList, 
  NotificationList,
  CommentList,
  UserList,
  ListViewControls,
  LoadMoreButton
} from "@/components/common-custom/list-view"
import { Card, CardContent } from "@/components/ui/card"
import type { FilterConfig } from "@/types/list-view"

// 模拟数据
const mockMessages = [
  {
    id: 1,
    sender: "张三",
    avatar: "/placeholder-user.jpg",
    subject: "项目进度更新",
    preview: "我们已经完成了第一阶段的开发工作，请查看最新的项目进度报告...",
    time: "10:30 AM",
    unread: true,
    important: true,
  },
  {
    id: 2,
    sender: "李四",
    avatar: "/placeholder-user.jpg",
    subject: "会议通知",
    preview: "明天下午2点将在会议室A举行项目评审会议，请准时参加...",
    time: "昨天",
    unread: true,
  },
  {
    id: 3,
    sender: "王五",
    avatar: "/placeholder-user.jpg",
    subject: "设计稿确认",
    preview: "附件是最新的设计稿，请查看并给出反馈意见...",
    time: "周一",
  },
  {
    id: 4,
    sender: "赵六",
    avatar: "/placeholder-user.jpg",
    subject: "问题反馈",
    preview: "用户反馈在使用过程中遇到了一些问题，详情如下...",
    time: "上周",
  },
  {
    id: 5,
    sender: "系统通知",
    avatar: "/placeholder-logo.svg",
    subject: "系统维护通知",
    preview: "系统将于本周六凌晨2点至4点进行例行维护，期间服务可能不可用...",
    time: "7月15日",
  },
]

const mockNotifications = [
  {
    id: 1,
    type: "system",
    title: "系统更新",
    message: "系统已更新到最新版本v2.3.0，新增多项功能和性能优化",
    time: "刚刚",
    read: false,
  },
  {
    id: 2,
    type: "user",
    title: "新用户关注",
    message: "张三开始关注了你",
    time: "30分钟前",
    read: false,
  },
  {
    id: 3,
    type: "order",
    title: "订单状态更新",
    message: "订单#12345已发货，预计3天内送达",
    time: "2小时前",
    read: true,
  },
  {
    id: 4,
    type: "security",
    title: "安全提醒",
    message: "检测到异地登录，请确认是否为本人操作",
    time: "昨天",
    read: true,
  },
]

const mockComments = [
  {
    id: 1,
    author: "张三",
    avatar: "/placeholder-user.jpg",
    content: "这个产品非常好用，界面设计简洁明了，功能也很强大，推荐给大家！",
    time: "2小时前",
    likes: 15,
    replies: 3,
    rating: 5,
  },
  {
    id: 2,
    author: "李四",
    avatar: "/placeholder-user.jpg",
    content: "总体来说还不错，但是有些功能还需要完善，期待后续更新。",
    time: "昨天",
    likes: 8,
    replies: 1,
    rating: 4,
  },
  {
    id: 3,
    author: "王五",
    avatar: "/placeholder-user.jpg",
    content: "使用过程中遇到了一些问题，希望客服能够尽快解决。",
    time: "3天前",
    likes: 2,
    replies: 5,
    rating: 3,
  },
  {
    id: 4,
    author: "赵六",
    avatar: "/placeholder-user.jpg",
    content: "价格有点贵，但质量确实不错，值得购买。",
    time: "一周前",
    likes: 12,
    replies: 2,
    rating: 4,
  },
]

const mockUsers = [
  {
    id: 1,
    name: "张三",
    avatar: "/placeholder-user.jpg",
    role: "产品经理",
    department: "产品部",
    location: "北京",
    joinDate: "2022-01-15",
    status: "online",
    projects: 5,
  },
  {
    id: 2,
    name: "李四",
    avatar: "/placeholder-user.jpg",
    role: "UI设计师",
    department: "设计部",
    location: "上海",
    joinDate: "2022-03-22",
    status: "offline",
    projects: 8,
  },
  {
    id: 3,
    name: "王五",
    avatar: "/placeholder-user.jpg",
    role: "前端开发",
    department: "技术部",
    location: "广州",
    joinDate: "2021-11-05",
    status: "busy",
    projects: 12,
  },
  {
    id: 4,
    name: "赵六",
    avatar: "/placeholder-user.jpg",
    role: "后端开发",
    department: "技术部",
    location: "深圳",
    joinDate: "2022-05-18",
    status: "away",
    projects: 7,
  },
]

// 筛选器配置
const messageFilters: FilterConfig[] = [
  {
    id: "importance",
    label: "重要性",
    type: "select",
    options: [
      { label: "全部", value: "all" },
      { label: "重要", value: "important" },
      { label: "普通", value: "normal" }
    ],
    defaultValue: "all"
  },
  {
    id: "readStatus",
    label: "阅读状态",
    type: "select",
    options: [
      { label: "全部", value: "all" },
      { label: "已读", value: "read" },
      { label: "未读", value: "unread" }
    ],
    defaultValue: "all"
  },
  {
    id: "dateRange",
    label: "时间范围",
    type: "dateRange",
    placeholder: "选择时间范围"
  }
];

const notificationFilters: FilterConfig[] = [
  {
    id: "type",
    label: "通知类型",
    type: "multiSelect",
    options: [
      { label: "系统", value: "system" },
      { label: "用户", value: "user" },
      { label: "订单", value: "order" },
      { label: "安全", value: "security" }
    ],
    defaultValue: []
  },
  {
    id: "readStatus",
    label: "阅读状态",
    type: "radio",
    options: [
      { label: "全部", value: "all" },
      { label: "已读", value: "read" },
      { label: "未读", value: "unread" }
    ],
    defaultValue: "all"
  }
];

const commentFilters: FilterConfig[] = [
  {
    id: "rating",
    label: "评分",
    type: "select",
    options: [
      { label: "全部评分", value: "all" },
      { label: "5星", value: "5" },
      { label: "4星", value: "4" },
      { label: "3星", value: "3" },
      { label: "2星", value: "2" },
      { label: "1星", value: "1" }
    ],
    defaultValue: "all"
  },
  {
    id: "date",
    label: "评论日期",
    type: "date",
    placeholder: "选择日期"
  },
  {
    id: "hasReplies",
    label: "有回复的评论",
    type: "checkbox",
    defaultValue: false
  }
];

const userFilters: FilterConfig[] = [
  {
    id: "department",
    label: "部门",
    type: "select",
    options: [
      { label: "全部部门", value: "all" },
      { label: "产品部", value: "产品部" },
      { label: "设计部", value: "设计部" },
      { label: "技术部", value: "技术部" },
      { label: "市场部", value: "市场部" }
    ],
    defaultValue: "all"
  },
  {
    id: "status",
    label: "状态",
    type: "multiSelect",
    options: [
      { label: "在线", value: "online" },
      { label: "离线", value: "offline" },
      { label: "忙碌", value: "busy" },
      { label: "离开", value: "away" }
    ],
    defaultValue: []
  },
  {
    id: "location",
    label: "位置",
    type: "select",
    options: [
      { label: "全部位置", value: "all" },
      { label: "北京", value: "北京" },
      { label: "上海", value: "上海" },
      { label: "广州", value: "广州" },
      { label: "深圳", value: "深圳" }
    ],
    defaultValue: "all"
  },
  {
    id: "joinDate",
    label: "入职日期",
    type: "dateRange",
    placeholder: "选择入职日期范围"
  }
];

export default function ListViewExample() {
  const [currentView, setCurrentView] = useState("messages")
  const [searchValue, setSearchValue] = useState("")
  const [messageFilterValues, setMessageFilterValues] = useState<Record<string, any>>({
    importance: "all",
    readStatus: "all"
  });
  const [notificationFilterValues, setNotificationFilterValues] = useState<Record<string, any>>({
    type: [],
    readStatus: "all"
  });
  const [commentFilterValues, setCommentFilterValues] = useState<Record<string, any>>({
    rating: "all",
    hasReplies: false
  });
  const [userFilterValues, setUserFilterValues] = useState<Record<string, any>>({
    department: "all",
    status: [],
    location: "all"
  });

  // 筛选消息
  const filteredMessages = mockMessages.filter(message => {
    // 搜索过滤
    if (searchValue && !message.subject.toLowerCase().includes(searchValue.toLowerCase()) && 
        !message.preview.toLowerCase().includes(searchValue.toLowerCase()) &&
        !message.sender.toLowerCase().includes(searchValue.toLowerCase())) {
      return false;
    }
    
    // 重要性过滤
    if (messageFilterValues.importance === "important" && !message.important) {
      return false;
    }
    if (messageFilterValues.importance === "normal" && message.important) {
      return false;
    }
    
    // 阅读状态过滤
    if (messageFilterValues.readStatus === "read" && message.unread) {
      return false;
    }
    if (messageFilterValues.readStatus === "unread" && !message.unread) {
      return false;
    }
    
    return true;
  });

  // 筛选通知
  const filteredNotifications = mockNotifications.filter(notification => {
    // 搜索过滤
    if (searchValue && !notification.title.toLowerCase().includes(searchValue.toLowerCase()) && 
        !notification.message.toLowerCase().includes(searchValue.toLowerCase())) {
      return false;
    }
    
    // 类型过滤
    if (notificationFilterValues.type && notificationFilterValues.type.length > 0 && 
        !notificationFilterValues.type.includes(notification.type)) {
      return false;
    }
    
    // 阅读状态过滤
    if (notificationFilterValues.readStatus === "read" && !notification.read) {
      return false;
    }
    if (notificationFilterValues.readStatus === "unread" && notification.read) {
      return false;
    }
    
    return true;
  });

  // 筛选评论
  const filteredComments = mockComments.filter(comment => {
    // 搜索过滤
    if (searchValue && !comment.content.toLowerCase().includes(searchValue.toLowerCase()) && 
        !comment.author.toLowerCase().includes(searchValue.toLowerCase())) {
      return false;
    }
    
    // 评分过滤
    if (commentFilterValues.rating !== "all" && comment.rating !== parseInt(commentFilterValues.rating)) {
      return false;
    }
    
    // 回复过滤
    if (commentFilterValues.hasReplies && (!comment.replies || comment.replies === 0)) {
      return false;
    }
    
    return true;
  });

  // 筛选用户
  const filteredUsers = mockUsers.filter(user => {
    // 搜索过滤
    if (searchValue && !user.name.toLowerCase().includes(searchValue.toLowerCase()) && 
        !user.role.toLowerCase().includes(searchValue.toLowerCase()) &&
        !user.department.toLowerCase().includes(searchValue.toLowerCase())) {
      return false;
    }
    
    // 部门过滤
    if (userFilterValues.department !== "all" && user.department !== userFilterValues.department) {
      return false;
    }
    
    // 状态过滤
    if (userFilterValues.status && userFilterValues.status.length > 0 && 
        !userFilterValues.status.includes(user.status)) {
      return false;
    }
    
    // 位置过滤
    if (userFilterValues.location !== "all" && user.location !== userFilterValues.location) {
      return false;
    }
    
    return true;
  });

  // 处理筛选器变更
  const handleFilterChange = (filterId: string, value: any) => {
    if (currentView === "messages") {
      setMessageFilterValues(prev => ({ ...prev, [filterId]: value }));
    } else if (currentView === "notifications") {
      setNotificationFilterValues(prev => ({ ...prev, [filterId]: value }));
    } else if (currentView === "comments") {
      setCommentFilterValues(prev => ({ ...prev, [filterId]: value }));
    } else if (currentView === "users") {
      setUserFilterValues(prev => ({ ...prev, [filterId]: value }));
    }
  };

  // 重置筛选器
  const handleFilterReset = () => {
    if (currentView === "messages") {
      setMessageFilterValues({
        importance: "all",
        readStatus: "all"
      });
    } else if (currentView === "notifications") {
      setNotificationFilterValues({
        type: [],
        readStatus: "all"
      });
    } else if (currentView === "comments") {
      setCommentFilterValues({
        rating: "all",
        hasReplies: false
      });
    } else if (currentView === "users") {
      setUserFilterValues({
        department: "all",
        status: [],
        location: "all"
      });
    }
  };

  // 获取当前视图的筛选器
  const getCurrentFilters = () => {
    switch (currentView) {
      case "messages":
        return messageFilters.map(filter => ({
          ...filter,
          value: messageFilterValues[filter.id] || filter.defaultValue
        }));
      case "notifications":
        return notificationFilters.map(filter => ({
          ...filter,
          value: notificationFilterValues[filter.id] || filter.defaultValue
        }));
      case "comments":
        return commentFilters.map(filter => ({
          ...filter,
          value: commentFilterValues[filter.id] || filter.defaultValue
        }));
      case "users":
        return userFilters.map(filter => ({
          ...filter,
          value: userFilterValues[filter.id] || filter.defaultValue
        }));
      default:
        return [];
    }
  };

  return (
    <Card>
      <CardContent className="p-6">
        <ListViewControls
          viewTypes={[
            { label: "消息", value: "messages" },
            { label: "通知", value: "notifications" },
            { label: "评论", value: "comments" },
            { label: "用户", value: "users" },
          ]}
          currentView={currentView}
          onViewChange={setCurrentView}
          searchValue={searchValue}
          onSearchChange={setSearchValue}
          filters={getCurrentFilters()}
          onFilterChange={handleFilterChange}
          onFilterReset={handleFilterReset}
          className="mb-6"
        />

        {currentView === "messages" && (
          <MessageList
            messages={filteredMessages}
            title="消息列表"
            description="您的最新消息和对话"
            onMessageAction={(message) => console.log("Message action", message)}
          />
        )}

        {currentView === "notifications" && (
          <NotificationList
            notifications={filteredNotifications}
            title="通知中心"
            description="系统通知和重要提醒"
            onNotificationClick={(notification) => console.log("Notification clicked", notification)}
          />
        )}

        {currentView === "comments" && (
          <CommentList
            comments={filteredComments}
            title="评论列表"
            description="用户评论和反馈"
            onCommentLike={(comment) => console.log("Comment liked", comment)}
            onCommentReply={(comment) => console.log("Comment reply", comment)}
            onCommentShare={(comment) => console.log("Comment shared", comment)}
          />
        )}

        {currentView === "users" && (
          <UserList
            users={filteredUsers}
            title="用户列表"
            description="系统用户和成员"
            onUserMessage={(user) => console.log("Message user", user)}
            onUserDetails={(user) => console.log("View user details", user)}
          />
        )}

        <div className="mt-4 flex justify-center">
          <LoadMoreButton onClick={() => console.log("Load more")} />
        </div>
      </CardContent>
    </Card>
  )
} 
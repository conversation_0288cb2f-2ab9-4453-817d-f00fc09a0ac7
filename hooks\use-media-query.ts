"use client";

import { useState, useEffect } from "react";

export function useMediaQuery(query = "(min-width: 768px)") {
  const [matches, setMatches] = useState(() => {
    if (typeof window !== "undefined") {
      return window.matchMedia(query).matches;
    }
    return true; // 默认为桌面版
  });

  useEffect(() => {
    if (typeof window === "undefined") return;
    
    const media = window.matchMedia(query);
    
    const listener = () => {
      setMatches(media.matches);
    };
    
    // 初始检查
    setMatches(media.matches);
    
    // 添加监听器
    media.addEventListener("change", listener);
    
    // 清理函数
    return () => media.removeEventListener("change", listener);
  }, [query]);

  return matches;
} 
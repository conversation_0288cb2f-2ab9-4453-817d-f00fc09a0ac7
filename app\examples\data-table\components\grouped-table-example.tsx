"use client"

import * as React from "react"
import { 
  ColumnDef, 
  flexRender, 
  getCoreRowModel, 
  useReactTable,
  getPaginationRowModel,
  getGroupedRowModel,
  getExpandedRowModel,
} from "@tanstack/react-table"

import { But<PERSON> } from "@/components/ui/button"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { User } from "../data"
import { ChevronDown, ChevronRight } from "lucide-react"

export interface GroupedDataTableExampleProps {
  data: User[]
}

export function GroupedDataTableExample({ data }: GroupedDataTableExampleProps) {
  const columns: ColumnDef<User>[] = [
    {
      accessorKey: "role",
      header: "角色",
      enableGrouping: true,
    },
    {
      accessorKey: "name",
      header: "姓名",
    },
    {
      accessorKey: "email",
      header: "邮箱",
    },
    {
      accessorKey: "status",
      header: "状态",
      cell: ({ row }) => {
        const status = row.getValue("status") as string
        return (
          <div className="flex items-center">
            <span
              className={`mr-2 h-2 w-2 rounded-full ${
                status === "活跃" ? "bg-green-500" : 
                status === "未活跃" ? "bg-gray-400" : "bg-yellow-500"
              }`}
            />
            {status}
          </div>
        )
      },
    },
    {
      accessorKey: "lastLogin",
      header: "最后登录",
    },
  ]

  const [grouping, setGrouping] = React.useState(['role'])
  const [expanded, setExpanded] = React.useState({})

  const table = useReactTable({
    data,
    columns,
    state: {
      grouping,
      expanded,
    },
    onExpandedChange: setExpanded,
    getExpandedRowModel: getExpandedRowModel(),
    getGroupedRowModel: getGroupedRowModel(),
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    initialState: {
      pagination: {
        pageSize: 10,
      },
    },
  })

  return (
    <div className="space-y-4">
      <div className="mb-2 flex items-center gap-2">
        <div className="text-sm text-muted-foreground">
          <span className="font-medium">分组表格</span> - 按角色分组展示用户数据
        </div>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {cell.column.id === "role" && row.getIsGrouped() ? (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            row.toggleExpanded()
                          }}
                          className="p-0 hover:bg-transparent"
                        >
                          {row.getIsExpanded() ? (
                            <ChevronDown className="mr-1 h-4 w-4" />
                          ) : (
                            <ChevronRight className="mr-1 h-4 w-4" />
                          )}
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}{" "}
                          <span className="ml-2 text-muted-foreground">
                            ({row.subRows.length})
                          </span>
                        </Button>
                      ) : row.getIsGrouped() ? (
                        // 如果行是分组行但单元格不是分组列
                        <></>
                      ) : (
                        // 常规单元格
                        flexRender(cell.column.columnDef.cell, cell.getContext())
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  无数据
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => table.previousPage()}
          disabled={!table.getCanPreviousPage()}
        >
          上一页
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => table.nextPage()}
          disabled={!table.getCanNextPage()}
        >
          下一页
        </Button>
      </div>
    </div>
  )
} 
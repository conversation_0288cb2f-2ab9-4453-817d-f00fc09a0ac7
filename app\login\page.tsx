﻿import { GalleryVerticalEnd } from "lucide-react"
import { LoginForm } from "@/components/pages/login/login-form"
import { GradientBackground } from "./gradient-background"

export default function LoginPage() {
  return (
    <div className="grid min-h-svh lg:grid-cols-2">
      {/* 左侧渐变背景区域 */}
      <div className="relative hidden lg:block overflow-hidden">
        <GradientBackground />
        <div className="absolute inset-0 z-10 flex flex-col justify-center p-10">
          <div className="max-w-md ml-12 backdrop-blur-sm bg-white/[0.03] p-8 rounded-xl shadow-2xl border border-white/[0.07]">
            <h2 className="text-3xl font-bold mb-4 text-white drop-shadow-sm">欢迎使用 <span className="text-white/95 drop-shadow-lg">Keel Cloud</span></h2>
            <p className="text-gray-300 text-sm leading-relaxed mb-6">
              安全、高效的一站式快捷开发平台，助力您的业务轻松起航
            </p>
            <div className="flex gap-2">
              <div className="h-1.5 w-24 rounded-full bg-gradient-to-r from-white/30 to-white/80"></div>
              <div className="h-1.5 w-6 rounded-full bg-white/20"></div>
            </div>
          </div>
        </div>
      </div>
      
      {/* 右侧登录表单区域 */}
      <div className="flex flex-col gap-4 p-6 md:p-10 relative">
        {/* 移动端顶部渐变 */}
        <div className="lg:hidden absolute top-0 inset-x-0 h-40 bg-gradient-to-b from-gray-900 to-gray-800 rounded-b-2xl shadow-md -z-10"></div>
        
        <div className="flex justify-center gap-2 md:justify-start pt-4">
          <a href="/" className="flex items-center gap-2 font-medium lg:text-black text-white">
            <div className="bg-primary text-primary-foreground flex size-6 items-center justify-center rounded-md">
              <GalleryVerticalEnd className="size-4" />
            </div>
            Keel Cloud
          </a>
        </div>
        <div className="flex flex-1 items-center justify-center mt-4 lg:mt-0">
          <div className="w-full max-w-sm lg:max-w-xs bg-white lg:bg-transparent p-6 lg:p-0 rounded-xl shadow-lg lg:shadow-none">
            <LoginForm />
          </div>
        </div>
      </div>
      
      {/* 移动端显示简化版背景 */}
      <div className="lg:hidden fixed inset-0 -z-20 bg-gradient-to-br from-slate-50 to-gray-100" />
    </div>
  )
}

"use client"

import { Dialog, DialogContent } from "@/components/ui/dialog"
import { Loader2 } from "lucide-react"
import { cn } from "@/lib/utils"
import { SearchInput } from "./search-input"
import { SearchResults } from "./search-results"
import type { GlobalSearchProps } from "@/types/search"

/**
 * 全局搜索组件
 * 提供一个模态对话框形式的搜索体验，包含搜索输入框和结果展示
 */
export function GlobalSearch<T>({
  open,
  onOpenChange,
  value,
  onChange,
  results,
  onResultClick,
  loading = false,
  placeholder = "搜索...",
  grouped = false,
  renderItem,
  className,
}: GlobalSearchProps<T>) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent 
        className={cn("sm:max-w-[550px] p-0", className)}
      >
        <div className="p-4 pb-2 border-b">
          <SearchInput
            value={value}
            onChange={onChange}
            placeholder={placeholder}
            loading={loading}
            onSubmit={() => {}}
            className="w-full"
          />
        </div>
        <div className="max-h-[60vh] overflow-y-auto">
          <SearchResults
            results={results}
            onResultClick={onResultClick}
            grouped={grouped}
            loading={loading}
            renderItem={renderItem}
          />
        </div>
      </DialogContent>
    </Dialog>
  )
} 
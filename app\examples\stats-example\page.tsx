﻿""use client""

import React from ""react""
import { ComponentPreviewContainer } from ""@/components/component-detail/preview-container""
import { allExamples } from ""./examples""

export default function StatsExamplePage() {
  return (
    <ComponentPreviewContainer
      title=""统计组件 Stats""
      description=""用于数据统计和数据可视化的组件集合，展示关键指标、趋势和数据分析结果。""
      whenToUse=""当需要展示关键业务指标、KPI数据、数据分析结果或统计信息时使用。""
      examples={allExamples}
    />
  )
}

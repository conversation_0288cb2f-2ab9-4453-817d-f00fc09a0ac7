---
description:
globs:
alwaysApply: true
---
# 组件开发规范

## 组件目录结构
- `components/ui` - shadcn/ui 基础组件
- `components/custom` - 自定义UI组件
- `components/layout` - 布局相关组件
- `components/navigation` - 导航相关组件
- `components/pages` - 页面特定组件
- `components/common` - 多页面共享组件

## 组件创建规则
1. 使用TypeScript编写所有组件
2. 为组件属性创建明确的接口或类型
3. 优先使用函数组件和React钩子，不使用类组件
4. Server Components示例：
```tsx
// 服务器组件
import { getProducts } from '@/services/api/productRequestApi';
import ProductCard from '@/components/pages/products/ProductCard';

export default async function ProductsPage() {
  const products = await getProducts();
  
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {products.map(product => (
        <ProductCard key={product.id} product={product} />
      ))}
    </div>
  );
}
```

5. Client Components示例：
```tsx
// 客户端组件
"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { createProduct } from '@/services/api/productRequestApi';
import { Product } from '@/types/product';

interface ProductFormProps {
  onSuccess: (product: Product) => void;
}

export default function ProductForm({ onSuccess }: ProductFormProps) {
  const [name, setName] = useState('');
  const [price, setPrice] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      const newProduct = await createProduct({
        name,
        price: parseFloat(price)
      });
      
      onSuccess(newProduct);
      setName('');
      setPrice('');
    } catch (error) {
      console.error('创建产品失败:', error);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Input
          placeholder="产品名称"
          value={name}
          onChange={e => setName(e.target.value)}
          required
        />
      </div>
      <div>
        <Input
          type="number"
          placeholder="价格"
          value={price}
          onChange={e => setPrice(e.target.value)}
          required
        />
      </div>
      <Button type="submit" disabled={isSubmitting}>
        {isSubmitting ? '提交中...' : '创建产品'}
      </Button>
    </form>
  );
}
```

## shadcn/ui组件规范
1. 新增shadcn/ui组件时使用命令：`pnpm dlx shadcn@latest add <component-name>`
2. 不要直接修改`components/ui`目录下的组件，如需自定义，通过扩展API或创建自定义组件
3. 自定义主题在`app/globals.css`或专门的主题文件中定义

## 组件命名规范
1. 组件名称使用PascalCase命名法，如`ProductCard`、`UserProfile`
2. 文件名与组件名一致，如`ProductCard.tsx`
3. 页面组件命名为`page.tsx`
4. 布局组件命名为`layout.tsx`

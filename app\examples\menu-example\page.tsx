"use client"

import React from "react"
import { ComponentPreviewContainer } from "@/components/component-detail/preview-container"
import {
  DropdownMenuButton,
  MoreMenuButton,
  ActionMenuButton,
  ContextMenu,
  MenuItem
} from "@/components/common-custom/menu"
import { allExamples } from "./examples"

// ============================================================================
// API文档组件
// ============================================================================

function MenuApiDocs() {
  return (
    <div className="space-y-6">
      {/* DropdownMenuButton 组件API */}
      <div>
        <h3 className="font-medium text-lg mb-2">DropdownMenuButton</h3>
        <p className="text-muted-foreground mb-4">带标签和图标的下拉菜单按钮</p>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">label</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">按钮标签文本</td>
              </tr>
              <tr>
                <td className="p-2 border">icon</td>
                <td className="p-2 border">LucideIcon</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">按钮图标</td>
              </tr>
              <tr>
                <td className="p-2 border">items</td>
                <td className="p-2 border">MenuItem[]</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">菜单项列表</td>
              </tr>
              <tr>
                <td className="p-2 border">variant</td>
                <td className="p-2 border">"default" | "outline" | "ghost"</td>
                <td className="p-2 border">"outline"</td>
                <td className="p-2 border">按钮变体</td>
              </tr>
              <tr>
                <td className="p-2 border">onSelect</td>
                <td className="p-2 border">(value: string) =&gt; void</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">选择菜单项回调</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* MoreMenuButton 组件API */}
      <div>
        <h3 className="font-medium text-lg mb-2">MoreMenuButton</h3>
        <p className="text-muted-foreground mb-4">更多选项菜单按钮，通常显示为三个点</p>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">items</td>
                <td className="p-2 border">MenuItem[]</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">菜单项列表</td>
              </tr>
              <tr>
                <td className="p-2 border">variant</td>
                <td className="p-2 border">"default" | "outline" | "ghost"</td>
                <td className="p-2 border">"ghost"</td>
                <td className="p-2 border">按钮变体</td>
              </tr>
              <tr>
                <td className="p-2 border">onSelect</td>
                <td className="p-2 border">(value: string) =&gt; void</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">选择菜单项回调</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* 使用指南 */}
      <div>
        <h3 className="font-medium text-lg mb-2">使用指南</h3>
        <div className="space-y-4 text-sm">
          <div>
            <h4 className="font-medium mb-2">基本用法</h4>
            <p className="text-muted-foreground mb-2">
              最简单的菜单使用方式：
            </p>
            <pre className="bg-muted p-3 rounded-md overflow-x-auto">
              <code>{`<DropdownMenuButton
  label="用户"
  icon={User}
  items={menuItems}
  onSelect={(value) => console.log(value)}
/>`}</code>
            </pre>
          </div>

          <div>
            <h4 className="font-medium mb-2">菜单项类型</h4>
            <p className="text-muted-foreground mb-2">
              支持多种类型的菜单项：
            </p>
            <pre className="bg-muted p-3 rounded-md overflow-x-auto">
              <code>{`const menuItems = [
  { label: "普通项", icon: Icon, onClick: () => {} },
  { type: "separator" }, // 分隔线
  { label: "带徽章", badge: { content: "新" } },
  { label: "禁用项", disabled: true }
]`}</code>
            </pre>
          </div>
        </div>
      </div>
    </div>
  )
}



export default function MenuExamplePage() {
  return (
    <ComponentPreviewContainer
      title="菜单 Menu"
      description="提供多种风格的下拉菜单组件，支持图标、徽章、分隔线、复选框、单选按钮和子菜单等功能。"
      whenToUse="当需要为用户提供多个操作选项时使用，如用户菜单、操作按钮、上下文菜单等场景。适用于导航栏、表格操作、卡片操作等需要节省空间的交互场景。"
      examples={allExamples}
      apiDocs={<MenuApiDocs />}
    />
  )
}
---
description:
globs:
alwaysApply: true
---
# 错误处理和性能优化规则

## 错误处理
1. 所有API调用必须包含错误处理逻辑
2. 使用try/catch捕获异步操作可能出现的错误
3. 在用户界面上提供友好的错误提示
4. 实现Next.js的错误页面：
   - 全局错误处理：`app/error.tsx`
   - 路由级错误处理：`app/<route>/error.tsx`
5. 错误组件示例：
```tsx
"use client";

import { useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';

interface ErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function Error({ error, reset }: ErrorProps) {
  useEffect(() => {
    // 可以在这里记录错误到错误跟踪服务
    console.error(error);
  }, [error]);

  return (
    <div className="flex flex-col items-center justify-center min-h-[400px] p-4">
      <Alert variant="destructive" className="mb-4 max-w-md">
        <AlertTitle>发生错误</AlertTitle>
        <AlertDescription>
          {error.message || '页面加载时发生错误，请重试。'}
        </AlertDescription>
      </Alert>
      <Button onClick={reset} variant="outline">
        重试
      </Button>
    </div>
  );
}
```

## 性能优化
1. 使用Next.js的数据获取和缓存功能
   - 静态渲染：`fetch(url, { cache: 'force-cache' })`
   - 动态渲染：`fetch(url, { cache: 'no-store' })`
   - 增量静态再生成：`fetch(url, { next: { revalidate: seconds } })`

2. 组件懒加载
```tsx
import { Suspense, lazy } from 'react';
import { Skeleton } from '@/components/ui/skeleton';

// 懒加载重量级组件
const HeavyComponent = lazy(() => import('@/components/heavy-component'));

export default function Page() {
  return (
    <div>
      <h1>页面内容</h1>
      <Suspense fallback={<Skeleton className="h-[300px] w-full" />}>
        <HeavyComponent />
      </Suspense>
    </div>
  );
}
```

3. 图片优化
   - 使用Next.js的Image组件
   - 为图片设置正确的宽度和高度
   - 使用WebP或AVIF格式优化图片
```tsx
import Image from 'next/image';

export default function OptimizedImage() {
  return (
    <Image
      src="/images/example.jpg"
      alt="示例图片"
      width={800}
      height={600}
      priority={true} // 对首屏图片使用
      placeholder="blur" // 可选，提供模糊占位符
      quality={85} // 可选，默认为75
    />
  );
}
```

4. 代码分割和按需加载
   - 使用动态导入
   - 使用Next.js的页面分割机制
   - 路由分割通过App Router自动实现

5. 大型列表渲染优化
   - 实现分页或无限滚动
   - 考虑使用虚拟滚动技术（如`react-virtualized`或`react-window`）
   - 使用React的memo优化重复渲染

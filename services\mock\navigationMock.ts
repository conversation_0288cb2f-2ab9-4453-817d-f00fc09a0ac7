/**
 * 导航相关的模拟数据
 * 用于开发阶段，生产环境应从API获取
 */

import { NavigationConfig } from "@/types/navigation";

// 导航配置
export const navigationConfig: NavigationConfig = {
  showNavigation: true,
  excludePaths: ["/login", "/logout", "/register", "/404", "/500"],
  user: {
    name: "测试用户",
    email: "<EMAIL>",
    avatar: "/avatars/user.png"
  },
  teams: [
    {
      name: "技术团队",
      logo: "Code",
      plan: "Professional"
    },
    {
      name: "设计团队",
      logo: "PenTool",
      plan: "Professional"
    }
  ],
  navMain: [
    {
      title: "仪表盘",
      url: "/dashboard",
      icon: "LayoutDashboard"
    },
    {
      title: "项目管理",
      url: "/projects",
      icon: "Folder"
    },
    {
      title: "实用工具",
      icon: "Tools",
      items: [
        {
          title: "密码管理器",
          url: "/tool/password",
          description: "安全管理您的密码"
        }
      ]
    },
    {
      title: "系统设置",
      icon: "Settings",
      items: [
        {
          title: "用户管理",
          url: "/settings/users",
        },
        {
          title: "权限管理",
          url: "/settings/permission",
        },
        {
          title: "角色管理",
          url: "/settings/roles",
        },
        {
          title: "团队管理",
          url: "/teams",
        }
      ]
    }
  ],
  projects: [
    {
      name: "Web应用",
      url: "/projects/web",
      icon: "Globe"
    },
    {
      name: "移动应用",
      url: "/projects/mobile",
      icon: "Smartphone"
    }
  ]
};
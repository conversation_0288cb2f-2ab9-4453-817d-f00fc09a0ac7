# 任务拆分模板

本文档提供了任务拆分记录的标准模板，确保每个任务都有独立的文档记录。

## 📋 任务拆分原则

根据核心指导规范要求：**任务需拆分记录为独立文档，不要混在一起**

### 拆分标准
1. **功能独立性**：每个任务应该是一个独立的功能单元
2. **时间边界**：单个任务的完成时间不超过4小时
3. **依赖关系**：明确任务之间的依赖关系
4. **可验证性**：每个任务都有明确的完成标准

## 📝 任务记录模板

### 基本信息
```markdown
# 任务名称：[具体任务描述]

## 📋 任务概述
- **任务ID**：TASK-YYYY-MM-DD-001
- **创建时间**：2025-01-XX
- **预估时间**：X小时
- **优先级**：高/中/低
- **负责人**：[姓名]
- **状态**：待开始/进行中/已完成/已取消

## 🎯 任务目标
[详细描述任务要达成的目标]

## 📝 详细描述
[任务的具体内容和要求]

## 🔗 依赖关系
- **前置任务**：[列出必须先完成的任务]
- **后续任务**：[列出依赖此任务的任务]
- **相关任务**：[列出相关但不依赖的任务]

## 📋 验收标准
- [ ] 标准1：[具体的验收条件]
- [ ] 标准2：[具体的验收条件]
- [ ] 标准3：[具体的验收条件]

## 🛠️ 技术要求
- **技术栈**：[使用的技术]
- **工具要求**：[需要使用的MCP工具]
- **规范遵循**：[需要遵循的文档规范]

## 📚 参考文档
- [相关的规范文档链接]
- [技术文档链接]
- [示例代码链接]

## 📝 实施记录
### [日期] - 开始实施
- [记录实施过程中的重要信息]

### [日期] - 进度更新
- [记录进度和遇到的问题]

### [日期] - 完成
- [记录完成情况和结果]

## 🔍 质量检查
- [ ] 代码质量检查通过
- [ ] 文档更新完成
- [ ] 测试验证通过
- [ ] 规范遵循检查通过

## 📋 经验总结
- **成功经验**：[记录成功的做法]
- **遇到问题**：[记录遇到的问题和解决方案]
- **改进建议**：[对流程或规范的改进建议]
```

## 🗂️ 文件命名规范

### 命名格式
```
docs/tasks/YYYY-MM-DD-task-name.md
```

### 示例
```
docs/tasks/2025-01-15-create-button-component.md
docs/tasks/2025-01-16-update-color-system.md
docs/tasks/2025-01-17-implement-api-standards.md
```

## 📁 目录结构

```
docs/
├── tasks/                          # 任务记录目录
│   ├── 2025-01/                   # 按月份组织
│   │   ├── 2025-01-15-task1.md
│   │   ├── 2025-01-16-task2.md
│   │   └── ...
│   ├── 2025-02/
│   └── ...
├── 工作流程/任务拆分模板.md        # 本模板文件
└── ...
```

## 🔄 任务状态管理

### 状态定义
- **待开始**：任务已创建，等待开始执行
- **进行中**：任务正在执行中
- **已完成**：任务已完成并通过验收
- **已取消**：任务被取消，不再执行

### 状态更新
每次状态变更都应该：
1. 更新任务文档中的状态字段
2. 记录状态变更的时间和原因
3. 使用memory工具记录重要的状态变更

## 📋 使用指南

### 创建新任务
1. 复制本模板内容
2. 根据实际任务填写各个字段
3. 保存为独立的任务文档
4. 使用memory工具记录任务创建

### 任务执行
1. 定期更新实施记录
2. 遇到问题及时记录
3. 使用MCP工具辅助完成任务
4. 遵循相关的规范文档

### 任务完成
1. 完成所有验收标准
2. 更新任务状态为"已完成"
3. 记录经验总结
4. 更新相关的规范文档（如有优秀实践）

## ⚠️ 注意事项

1. **独立性**：每个任务必须有独立的文档，不要混在一起
2. **完整性**：确保所有必要信息都被记录
3. **及时性**：及时更新任务状态和进度
4. **规范性**：遵循文档命名和格式规范
5. **关联性**：明确任务之间的依赖关系

## 🚀 最佳实践

1. **任务拆分要合理**：既不能太大也不能太小
2. **验收标准要明确**：避免模糊的验收条件
3. **依赖关系要清晰**：明确任务的执行顺序
4. **记录要及时**：不要等到任务完成后再补记录
5. **经验要总结**：每个任务完成后都要总结经验

通过使用这个模板，可以确保每个任务都有完整的记录，便于项目管理和知识积累。

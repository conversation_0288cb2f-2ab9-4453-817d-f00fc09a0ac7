{"mcpServers": {"context7": {"command": "npx", "args": ["@upstash/context7-mcp"], "env": {"UPSTASH_REDIS_REST_URL": "", "UPSTASH_REDIS_REST_TOKEN": ""}}, "filesystem": {"command": "npx", "args": ["@modelcontextprotocol/server-filesystem", "d:\\430\\business-components"], "env": {}}, "sequential-thinking": {"command": "npx", "args": ["@modelcontextprotocol/server-sequential-thinking"], "env": {}}, "fetch": {"command": "npx", "args": ["fetch-mcp"], "env": {}}, "playwright": {"command": "npx", "args": ["@executeautomation/playwright-mcp-server"], "env": {}}, "memory": {"command": "npx", "args": ["@modelcontextprotocol/server-memory"], "env": {}}, "feedback-enhanced": {"command": "npx", "args": ["mcp-feedback-collector-md"], "env": {"PORT": "3001"}}}}
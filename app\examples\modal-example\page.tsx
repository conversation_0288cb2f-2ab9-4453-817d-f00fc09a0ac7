"use client"

import React from "react"
import { ComponentPreviewContainer } from "@/components/component-detail/preview-container"
import { Modal, AlertDialogModal, FormModal, InfoModal } from "@/components/common-custom/modal"
import { allExamples } from "./examples"

// ============================================================================
// API文档组件
// ============================================================================

function ModalApiDocs() {
  return (
    <div className="space-y-6">
      {/* Modal 组件API */}
      <div>
        <h3 className="font-medium text-lg mb-2">Modal</h3>
        <p className="text-muted-foreground mb-4">基础模态框组件，用于显示覆盖层内容</p>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">open</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">是否打开</td>
              </tr>
              <tr>
                <td className="p-2 border">onOpenChange</td>
                <td className="p-2 border">(open: boolean) =&gt; void</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">打开状态变化回调</td>
              </tr>
              <tr>
                <td className="p-2 border">title</td>
                <td className="p-2 border">ReactNode</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">模态框标题</td>
              </tr>
              <tr>
                <td className="p-2 border">children</td>
                <td className="p-2 border">ReactNode</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">模态框内容</td>
              </tr>
              <tr>
                <td className="p-2 border">size</td>
                <td className="p-2 border">"sm" | "md" | "lg" | "xl" | "2xl" | "full"</td>
                <td className="p-2 border">"md"</td>
                <td className="p-2 border">模态框尺寸</td>
              </tr>
              <tr>
                <td className="p-2 border">confirmButton</td>
                <td className="p-2 border">ModalButtonProps</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">确认按钮配置</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* AlertDialogModal 组件API */}
      <div>
        <h3 className="font-medium text-lg mb-2">AlertDialogModal</h3>
        <p className="text-muted-foreground mb-4">警告对话框组件，用于确认危险操作</p>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">type</td>
                <td className="p-2 border">"warning" | "danger" | "info" | "success"</td>
                <td className="p-2 border">"warning"</td>
                <td className="p-2 border">警告类型</td>
              </tr>
              <tr>
                <td className="p-2 border">icon</td>
                <td className="p-2 border">LucideIcon | ReactNode</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">图标</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* FormModal 组件API */}
      <div>
        <h3 className="font-medium text-lg mb-2">FormModal</h3>
        <p className="text-muted-foreground mb-4">表单模态框组件，用于数据输入和编辑</p>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">onSubmit</td>
                <td className="p-2 border">(e: React.FormEvent) =&gt; void</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">表单提交回调</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* 使用指南 */}
      <div>
        <h3 className="font-medium text-lg mb-2">使用指南</h3>
        <div className="space-y-4 text-sm">
          <div>
            <h4 className="font-medium mb-2">基本用法</h4>
            <p className="text-muted-foreground mb-2">
              最简单的模态框使用方式：
            </p>
            <pre className="bg-muted p-3 rounded-md overflow-x-auto">
              <code>{`const [open, setOpen] = useState(false);

<Modal
  open={open}
  onOpenChange={setOpen}
  title="标题"
>
  <p>模态框内容</p>
</Modal>`}</code>
            </pre>
          </div>
          
          <div>
            <h4 className="font-medium mb-2">警告对话框</h4>
            <p className="text-muted-foreground mb-2">
              用于确认危险操作：
            </p>
            <pre className="bg-muted p-3 rounded-md overflow-x-auto">
              <code>{`<AlertDialogModal
  open={open}
  onOpenChange={setOpen}
  type="danger"
  title="删除确认"
  confirmButton={{
    label: "删除",
    variant: "destructive",
  }}
/>`}</code>
            </pre>
          </div>
          
          <div>
            <h4 className="font-medium mb-2">表单模态框</h4>
            <p className="text-muted-foreground mb-2">
              用于数据输入：
            </p>
            <pre className="bg-muted p-3 rounded-md overflow-x-auto">
              <code>{`<FormModal
  open={open}
  onOpenChange={setOpen}
  title="编辑信息"
  onSubmit={handleSubmit}
>
  <Input placeholder="请输入..." />
</FormModal>`}</code>
            </pre>
          </div>
        </div>
      </div>
    </div>
  )
}

// ============================================================================
// 主预览组件
// ============================================================================

export default function ModalExamplePage() {
  return (
    <ComponentPreviewContainer
      title="模态框 Modal"
      description="用于创建对话框、表单、警告等各种模态交互界面的组件集合，支持多种类型和自定义配置"
      whenToUse="当需要用户处理任务但不希望跳转页面时使用；适用于确认操作、表单输入、信息展示等场景；支持多种尺寸和交互模式"
      examples={allExamples}
      apiDocs={<ModalApiDocs />}
    />
  )
}

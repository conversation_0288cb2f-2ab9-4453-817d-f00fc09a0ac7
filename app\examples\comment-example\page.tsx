"use client"

import React from "react"
import { ComponentPreviewContainer } from "@/components/component-detail/preview-container"
import { allExamples } from "./examples"

// ============================================================================
// API文档组件
// ============================================================================

function CommentApiDocs() {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-3">Comment</h3>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b">
                <th className="text-left py-2 px-3 font-medium">属性</th>
                <th className="text-left py-2 px-3 font-medium">类型</th>
                <th className="text-left py-2 px-3 font-medium">默认值</th>
                <th className="text-left py-2 px-3 font-medium">说明</th>
              </tr>
            </thead>
            <tbody className="text-muted-foreground">
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">comment</td>
                <td className="py-2 px-3">CommentData</td>
                <td className="py-2 px-3">-</td>
                <td className="py-2 px-3">评论数据</td>
              </tr>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">onLike</td>
                <td className="py-2 px-3">(id: string) =&gt; void</td>
                <td className="py-2 px-3">-</td>
                <td className="py-2 px-3">点赞回调</td>
              </tr>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">onReply</td>
                <td className="py-2 px-3">(id: string) =&gt; void</td>
                <td className="py-2 px-3">-</td>
                <td className="py-2 px-3">回复回调</td>
              </tr>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">onShare</td>
                <td className="py-2 px-3">(id: string) =&gt; void</td>
                <td className="py-2 px-3">-</td>
                <td className="py-2 px-3">分享回调</td>
              </tr>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">onDelete</td>
                <td className="py-2 px-3">(id: string) =&gt; void</td>
                <td className="py-2 px-3">-</td>
                <td className="py-2 px-3">删除回调</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold mb-3">CommentList</h3>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b">
                <th className="text-left py-2 px-3 font-medium">属性</th>
                <th className="text-left py-2 px-3 font-medium">类型</th>
                <th className="text-left py-2 px-3 font-medium">默认值</th>
                <th className="text-left py-2 px-3 font-medium">说明</th>
              </tr>
            </thead>
            <tbody className="text-muted-foreground">
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">comments</td>
                <td className="py-2 px-3">CommentData[]</td>
                <td className="py-2 px-3">[]</td>
                <td className="py-2 px-3">评论列表数据</td>
              </tr>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">onLike</td>
                <td className="py-2 px-3">(id: string) =&gt; void</td>
                <td className="py-2 px-3">-</td>
                <td className="py-2 px-3">点赞回调</td>
              </tr>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">onReply</td>
                <td className="py-2 px-3">(id: string) =&gt; void</td>
                <td className="py-2 px-3">-</td>
                <td className="py-2 px-3">回复回调</td>
              </tr>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">onDelete</td>
                <td className="py-2 px-3">(id: string) =&gt; void</td>
                <td className="py-2 px-3">-</td>
                <td className="py-2 px-3">删除回调</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold mb-3">CommentInput</h3>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b">
                <th className="text-left py-2 px-3 font-medium">属性</th>
                <th className="text-left py-2 px-3 font-medium">类型</th>
                <th className="text-left py-2 px-3 font-medium">默认值</th>
                <th className="text-left py-2 px-3 font-medium">说明</th>
              </tr>
            </thead>
            <tbody className="text-muted-foreground">
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">value</td>
                <td className="py-2 px-3">string</td>
                <td className="py-2 px-3">""</td>
                <td className="py-2 px-3">输入值</td>
              </tr>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">onChange</td>
                <td className="py-2 px-3">(value: string) =&gt; void</td>
                <td className="py-2 px-3">-</td>
                <td className="py-2 px-3">值变化回调</td>
              </tr>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">onSubmit</td>
                <td className="py-2 px-3">(content: string) =&gt; void</td>
                <td className="py-2 px-3">-</td>
                <td className="py-2 px-3">提交回调</td>
              </tr>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">placeholder</td>
                <td className="py-2 px-3">string</td>
                <td className="py-2 px-3">"写下你的评论..."</td>
                <td className="py-2 px-3">占位符文本</td>
              </tr>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">maxLength</td>
                <td className="py-2 px-3">number</td>
                <td className="py-2 px-3">-</td>
                <td className="py-2 px-3">最大字符数</td>
              </tr>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">showCounter</td>
                <td className="py-2 px-3">boolean</td>
                <td className="py-2 px-3">false</td>
                <td className="py-2 px-3">显示字符计数器</td>
              </tr>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">size</td>
                <td className="py-2 px-3">'sm' | 'md' | 'lg'</td>
                <td className="py-2 px-3">'md'</td>
                <td className="py-2 px-3">输入框大小</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}
// ============================================================================
// 页面组件
// ============================================================================

export default function CommentExamplePage() {
  return (
    <ComponentPreviewContainer
      title="评论 Comment"
      description="用于展示和管理用户评论的组件集合，支持评论、回复、点赞等社交功能"
      whenToUse="当需要用户互动和反馈时使用；适用于文章评论、产品评价、讨论区等场景；支持嵌套回复和社交功能"
      examples={allExamples}
      apiDocs={<CommentApiDocs />}
    />
  )
}
"use client"

import React from "react"
import Link from "next/link"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { CollapsibleSearch } from "@/components/common-custom/search/collapsible-search"
import { Breadcrumb } from "@/components/common-custom/breadcrumb"
import { DropdownMenuButton } from "@/components/common-custom/menu"
import { IconButtonWithBadge } from "@/components/common-custom/icon-button-with-badge"
import { ModeToggle } from "@/components/theme-toggle"
import {
  Bell,
  Settings,
  User,
  LogOut,
  Menu,
} from "lucide-react"
import type { LucideIcon } from "lucide-react"

// ============================================================================
// 类型定义
// ============================================================================

/**
 * 导航项类型
 */
export interface NavigationItem {
  label: string
  href?: string
  icon?: LucideIcon
  active?: boolean
  disabled?: boolean
  onClick?: () => void
}

/**
 * 面包屑项类型
 */
export interface BreadcrumbItem {
  label: string
  href?: string
  isCurrent?: boolean
}

/**
 * 操作项类型
 */
export interface ActionItem {
  id: string
  label: string
  icon?: LucideIcon
  href?: string
  onClick?: () => void
  badge?: number
  disabled?: boolean
}

/**
 * 用户菜单项类型
 */
export interface UserMenuItem {
  id: string
  label: string
  icon?: LucideIcon
  href?: string
  onClick?: () => void
  separator?: boolean
  destructive?: boolean
}

/**
 * 页头组件属性
 */
export interface PageHeaderProps {
  /**
   * 布局变体
   * @default "standard"
   */
  variant?: "standard" | "simple" | "dashboard" | "mobile"
  
  /**
   * Logo配置
   */
  logo?: {
    src?: string
    alt?: string
    href?: string
    text?: string
    showText?: boolean
  }
  
  /**
   * 导航配置
   */
  navigation?: {
    items?: NavigationItem[]
    showBreadcrumb?: boolean
    breadcrumbItems?: BreadcrumbItem[]
  }
  
  /**
   * 搜索配置
   */
  search?: {
    enabled?: boolean
    placeholder?: string
    onSearch?: (query: string) => void
    value?: string
    onChange?: (value: string) => void
    defaultExpanded?: boolean
    expandDirection?: "left" | "right"
  }
  
  /**
   * 操作区域配置
   */
  actions?: {
    items?: ActionItem[]
    showThemeToggle?: boolean
    showNotifications?: boolean
    notificationCount?: number
    onNotificationClick?: () => void
    onThemeToggle?: () => void
    isDarkMode?: boolean
  }
  
  /**
   * 用户区域配置
   */
  user?: {
    name?: string
    avatar?: string
    email?: string
    role?: string
    menuItems?: UserMenuItem[]
    onSignOut?: () => void
    showUserInfo?: boolean
  }
  
  /**
   * 页头高度
   * @default "md"
   */
  height?: "sm" | "md" | "lg"
  
  /**
   * 是否固定定位
   * @default true
   */
  sticky?: boolean
  
  /**
   * 是否显示边框
   * @default true
   */
  bordered?: boolean
  
  /**
   * 自定义类名
   */
  className?: string
  
  /**
   * 移动端菜单切换回调
   */
  onMobileMenuToggle?: () => void
  
  /**
   * 是否显示移动端菜单按钮
   */
  showMobileMenu?: boolean
}

// ============================================================================
// 子组件实现
// ============================================================================

/**
 * Logo组件
 */
function HeaderLogo({ logo }: { logo?: PageHeaderProps['logo'] }) {
  if (!logo) return null

  const logoContent = (
    <div className="flex items-center gap-2">
      {logo.src && (
        <img
          src={logo.src}
          alt={logo.alt || "Logo"}
          className="h-8 w-8 object-contain"
        />
      )}
      {logo.text && logo.showText !== false && (
        <span className="text-lg font-semibold text-foreground">
          {logo.text}
        </span>
      )}
    </div>
  )

  if (logo.href) {
    return (
      <Link
        href={logo.href}
        className="flex items-center transition-opacity hover:opacity-80"
      >
        {logoContent}
      </Link>
    )
  }

  return <div className="flex items-center">{logoContent}</div>
}

/**
 * 导航组件
 */
function HeaderNavigation({ navigation }: { navigation?: PageHeaderProps['navigation'] }) {
  if (!navigation) return null

  // 面包屑导航
  if (navigation.showBreadcrumb && navigation.breadcrumbItems) {
    return (
      <nav className="flex-1">
        <Breadcrumb
          items={navigation.breadcrumbItems}
          variant="standard"
          className="border-0 bg-transparent p-0"
        />
      </nav>
    )
  }

  // 主导航菜单
  if (navigation.items && navigation.items.length > 0) {
    return (
      <nav className="flex items-center space-x-1">
        {navigation.items.map((item, index) => (
          <Button
            key={index}
            variant={item.active ? "secondary" : "ghost"}
            size="sm"
            className="h-8"
            disabled={item.disabled}
            onClick={item.onClick}
            asChild={!!item.href}
          >
            {item.href ? (
              <Link href={item.href}>
                <div className="flex items-center gap-2">
                  {item.icon && <item.icon className="h-4 w-4" />}
                  {item.label}
                </div>
              </Link>
            ) : (
              <div className="flex items-center gap-2">
                {item.icon && <item.icon className="h-4 w-4" />}
                {item.label}
              </div>
            )}
          </Button>
        ))}
      </nav>
    )
  }

  return null
}

/**
 * 搜索组件
 */
function HeaderSearch({ search }: { search?: PageHeaderProps['search'] }) {
  if (!search?.enabled) return null

  return (
    <CollapsibleSearch
      value={search.value || ""}
      onChange={search.onChange || (() => {})}
      placeholder={search.placeholder || "搜索..."}
      onSearch={search.onSearch}
      size="sm"
      defaultExpanded={search.defaultExpanded || false}
      expandDirection={search.expandDirection || "left"}
    />
  )
}

/**
 * 操作区域组件
 */
function HeaderActions({ actions, search }: { actions?: PageHeaderProps['actions'], search?: PageHeaderProps['search'] }) {
  if (!actions && !search?.enabled) return null

  return (
    <div className="flex items-center gap-1">
      {/* 搜索框 */}
      {search?.enabled && <HeaderSearch search={search} />}

      {/* 自定义操作项 */}
      {actions.items?.map((item) => (
        item.href ? (
          <Link key={item.id} href={item.href}>
            <IconButtonWithBadge
              icon={item.icon || Bell}
              label={item.label}
              badge={item.badge}
              onClick={item.onClick}
              disabled={item.disabled}
              variant="ghost"
              size="sm"
            />
          </Link>
        ) : (
          <IconButtonWithBadge
            key={item.id}
            icon={item.icon || Bell}
            label={item.label}
            badge={item.badge}
            onClick={item.onClick}
            disabled={item.disabled}
            variant="ghost"
            size="sm"
          />
        )
      ))}

      {/* 通知按钮 */}
      {actions.showNotifications && (
        <IconButtonWithBadge
          icon={Bell}
          label="通知"
          badge={actions.notificationCount}
          onClick={actions.onNotificationClick}
          variant="ghost"
          size="sm"
        />
      )}

      {/* 主题切换 */}
      {actions.showThemeToggle && <ModeToggle />}
    </div>
  )
}

/**
 * 用户区域组件
 */
function HeaderUser({ user }: { user?: PageHeaderProps['user'] }) {
  if (!user) return null

  // 构建用户菜单项，避免重复
  const customMenuItems = user.menuItems || []
  const hasProfile = customMenuItems.some(item => item.id === "profile" || item.label === "个人资料")
  const hasSettings = customMenuItems.some(item => item.id === "settings" || item.label === "设置")
  const hasSignOut = customMenuItems.some(item => item.id === "signout" || item.label === "退出登录")

  const defaultMenuItems = []

  // 只添加不存在的默认菜单项
  if (!hasProfile) {
    defaultMenuItems.push({
      id: "default-profile",
      label: "个人资料",
      icon: User,
      onClick: () => console.log("个人资料")
    })
  }

  if (!hasSettings) {
    defaultMenuItems.push({
      id: "default-settings",
      label: "设置",
      icon: Settings,
      onClick: () => console.log("设置")
    })
  }

  const userMenuItems = [
    ...customMenuItems,
    ...(defaultMenuItems.length > 0 ? [{ type: "separator" as const, id: "separator-default" }] : []),
    ...defaultMenuItems,
    ...((!hasSignOut) ? [
      { type: "separator" as const, id: "separator-signout" },
      {
        id: "default-signout",
        label: "退出登录",
        icon: LogOut,
        onClick: user.onSignOut || (() => console.log("退出登录")),
        destructive: true
      }
    ] : [])
  ]

  return (
    <DropdownMenuButton
      label={user.showUserInfo !== false ? user.name : undefined}
      icon={User}
      items={userMenuItems}
      variant="ghost"
      size="sm"
      align="end"
      triggerClassName="h-8 px-2"
    />
  )
}

// ============================================================================
// 主组件实现
// ============================================================================

/**
 * 页头组件
 *
 * 功能完整的页头组件，支持多种布局模式和配置选项
 *
 * @example
 * ```tsx
 * // 基础用法
 * <PageHeader
 *   logo={{ text: "业务组件库", href: "/" }}
 *   navigation={{
 *     showBreadcrumb: true,
 *     breadcrumbItems: [
 *       { label: "首页", href: "/" },
 *       { label: "组件", isCurrent: true }
 *     ]
 *   }}
 *   search={{ enabled: true, placeholder: "搜索组件..." }}
 *   actions={{ showThemeToggle: true }}
 *   user={{ name: "张三", avatar: "/avatar.jpg" }}
 * />
 *
 * // 仪表板模式
 * <PageHeader
 *   variant="dashboard"
 *   logo={{ text: "管理后台" }}
 *   search={{ enabled: true }}
 *   actions={{ showNotifications: true, notificationCount: 5 }}
 *   user={{ name: "管理员" }}
 * />
 * ```
 */
export function PageHeader({
  variant = "standard",
  logo,
  navigation,
  search,
  actions,
  user,
  height = "md",
  sticky = true,
  bordered = true,
  className,
  onMobileMenuToggle,
  showMobileMenu = false,
}: PageHeaderProps) {
  // 高度样式映射
  const heightClasses = {
    sm: "h-10",
    md: "h-12",
    lg: "h-14",
  }

  // 默认配置
  const defaultLogo = { text: "业务组件库", href: "/", showText: true }
  const logoConfig = logo ? { ...defaultLogo, ...logo } : defaultLogo

  // 根据变体调整布局
  const renderLayout = () => {
    switch (variant) {
      case "simple":
        return (
          <>
            <div className="flex items-center gap-4">
              <HeaderLogo logo={logoConfig} />
              <HeaderNavigation navigation={navigation} />
            </div>
            <div className="flex items-center gap-2">
              <HeaderActions actions={actions} search={search} />
              <HeaderUser user={user} />
            </div>
          </>
        )

      case "dashboard":
        return (
          <>
            <div className="flex items-center gap-4">
              <HeaderLogo logo={logoConfig} />
            </div>
            <div className="flex-1"></div>
            <div className="flex items-center gap-2">
              <HeaderActions actions={actions} search={search} />
              <HeaderUser user={user} />
            </div>
          </>
        )

      case "mobile":
        return (
          <>
            <div className="flex items-center gap-2">
              {showMobileMenu && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-9 w-9 p-0 md:hidden"
                  onClick={onMobileMenuToggle}
                >
                  <Menu className="h-4 w-4" />
                </Button>
              )}
              <HeaderLogo logo={logoConfig} />
            </div>
            <div className="flex items-center gap-1">
              <HeaderActions actions={actions} search={search} />
              <HeaderUser user={user} />
            </div>
          </>
        )

      default: // standard
        return (
          <>
            <div className="flex items-center gap-4">
              {showMobileMenu && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-9 w-9 p-0 md:hidden"
                  onClick={onMobileMenuToggle}
                >
                  <Menu className="h-4 w-4" />
                </Button>
              )}
              <HeaderLogo logo={logoConfig} />
              <div className="hidden md:block">
                <HeaderNavigation navigation={navigation} />
              </div>
            </div>
            <div className="flex-1 flex items-center justify-end gap-4">
              <div className="flex items-center gap-2">
                <HeaderActions actions={actions} search={search} />
                <HeaderUser user={user} />
              </div>
            </div>
          </>
        )
    }
  }

  return (
    <header
      className={cn(
        // 基础样式
        "flex items-center justify-between bg-background px-4 sm:px-6 lg:px-8",

        // 高度
        heightClasses[height],

        // 固定定位
        sticky && "sticky top-0 z-50",

        // 边框
        bordered && "border-b border-border/50",

        // 自定义类名
        className
      )}
    >
      {renderLayout()}
    </header>
  )
}

// ============================================================================
// 默认配置和常量
// ============================================================================

/**
 * 默认页头配置
 */
export const DEFAULT_PAGE_HEADER_CONFIG = {
  variant: "standard" as const,
  height: "md" as const,
  sticky: true,
  bordered: true,
  logo: { text: "业务组件库", href: "/", showText: true },
} as const

/**
 * 页头变体预设
 */
export const PAGE_HEADER_VARIANTS = {
  standard: {
    variant: "standard" as const,
    description: "标准布局，包含Logo、导航、搜索、操作和用户区域",
  },
  simple: {
    variant: "simple" as const,
    description: "简洁布局，主要用于内容页面",
  },
  dashboard: {
    variant: "dashboard" as const,
    description: "仪表板布局，突出搜索功能",
  },
  mobile: {
    variant: "mobile" as const,
    description: "移动端布局，简化显示内容",
  },
} as const

/**
 * 常用操作项预设
 */
export const COMMON_ACTION_ITEMS = {
  settings: {
    id: "settings",
    label: "设置",
    icon: Settings,
    href: "/settings",
  },
  notifications: {
    id: "notifications",
    label: "通知",
    icon: Bell,
    onClick: () => console.log("打开通知"),
  },
} as const

/**
 * 常用用户菜单项预设
 */
export const COMMON_USER_MENU_ITEMS = {
  profile: {
    id: "profile",
    label: "个人资料",
    icon: User,
    href: "/profile",
  },
  settings: {
    id: "settings",
    label: "设置",
    icon: Settings,
    href: "/settings",
  },
  separator: {
    id: "separator",
    label: "",
    separator: true,
  },
  signOut: {
    id: "signOut",
    label: "退出登录",
    icon: LogOut,
    destructive: true,
    onClick: () => console.log("退出登录"),
  },
} as const

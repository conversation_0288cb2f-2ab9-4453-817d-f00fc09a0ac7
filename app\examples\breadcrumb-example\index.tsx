import * as React from "react"
import { HeaderWithBreadcrumb } from "@/components/project-custom/breadcrumb"
import { Breadcrumb } from "@/components/common-custom/breadcrumb"
import { Button } from "@/components/ui/button"
import { Home, Users, Folder, FileText, PlusCircle, ChevronRight } from "lucide-react"

export function BreadcrumbExample() {
  return (
    <div className="space-y-8">
      <StandardBreadcrumb />
      <IconBreadcrumb />
      <CollapsedBreadcrumb />
      <FileBreadcrumb />
      <BasicHeaderWithBreadcrumb />
      <DeepHeaderWithBreadcrumb />
    </div>
  )
}

// 标准面包屑
export function StandardBreadcrumb() {
  const basicBreadcrumb = [
    { label: "首页", href: "/" },
    { label: "产品", href: "/products" },
    { label: "类别", href: "/products/categories" },
    { label: "电子产品", href: "/products/categories/electronics", current: true },
  ]

  return (
    <div>
      <h3 className="text-sm font-medium mb-2">标准模式面包屑</h3>
      <Breadcrumb items={basicBreadcrumb} variant="standard" />
    </div>
  )
}

// 带图标面包屑
export function IconBreadcrumb() {
  const iconBreadcrumb = [
    { label: "首页", href: "/", icon: <Home className="h-4 w-4" /> },
    { label: "用户管理", href: "/users", icon: <Users className="h-4 w-4" /> },
    { label: "用户详情", href: "/users/profile", current: true },
  ]

  return (
    <div>
      <h3 className="text-sm font-medium mb-2">带图标面包屑</h3>
      <Breadcrumb items={iconBreadcrumb} variant="standard" />
    </div>
  )
}

// 折叠模式面包屑
export function CollapsedBreadcrumb() {
  const longPathBreadcrumb = [
    { label: "首页", href: "/" },
    { label: "产品", href: "/products" },
    { label: "类别", href: "/products/categories" },
    { label: "电子产品", href: "/products/categories/electronics" },
    { label: "手机", href: "/products/categories/electronics/phones" },
    { label: "智能手机", href: "/products/categories/electronics/phones/smartphones" },
    { label: "iPhone", href: "/products/categories/electronics/phones/smartphones/iphone", current: true },
  ]

  return (
    <div>
      <h3 className="text-sm font-medium mb-2">折叠模式面包屑</h3>
      <Breadcrumb
        items={longPathBreadcrumb} 
        variant="collapsed" 
        maxItems={4}
      />
    </div>
  )
}

// 文件路径面包屑
export function FileBreadcrumb() {
  const fileBreadcrumb = [
    { label: "文件", href: "/files", icon: <Folder className="h-4 w-4" /> },
    { label: "文档", href: "/files/documents", icon: <Folder className="h-4 w-4" /> },
    { label: "工作", href: "/files/documents/work", icon: <Folder className="h-4 w-4" /> },
    { label: "报告.docx", href: "/files/documents/work/report.docx", icon: <FileText className="h-4 w-4" />, current: true },
  ]

  return (
    <div>
      <h3 className="text-sm font-medium mb-2">文件路径面包屑</h3>
      <Breadcrumb items={fileBreadcrumb} variant="standard" />
    </div>
  )
}

// 基础页头导航
export function BasicHeaderWithBreadcrumb() {
  const breadcrumbItems = [
    { label: "首页", href: "/" },
    { label: "系统管理", href: "/system" },
    { label: "用户管理", isCurrent: true }
  ]

  return (
    <div>
      <h3 className="text-sm font-medium mb-2">页头导航</h3>
      <div className="border rounded-lg overflow-hidden">
        <HeaderWithBreadcrumb 
          items={breadcrumbItems}
          actions={
            <Button className="h-9 px-3 rounded-md">
              <PlusCircle className="w-4 h-4 mr-2" /> 
              新增用户
            </Button>
          }
        />
      </div>
    </div>
  )
}

// 深度嵌套页头
export function DeepHeaderWithBreadcrumb() {
  const deepBreadcrumbItems = [
    { label: "首页", href: "/" },
    { label: "系统管理", href: "/system" },
    { label: "权限管理", href: "/system/permissions" },
    { label: "角色配置", href: "/system/permissions/roles" },
    { label: "管理员角色", isCurrent: true }
  ]

  return (
    <div>
      <h3 className="text-sm font-medium mb-2">深度嵌套页头</h3>
      <div className="border rounded-lg overflow-hidden">
        <HeaderWithBreadcrumb 
          items={deepBreadcrumbItems}
          actions={
            <Button className="border border-input bg-background hover:bg-accent hover:text-accent-foreground focus:ring-1 focus:ring-black h-9 rounded-md px-3">
              返回
            </Button>
          }
        />
      </div>
    </div>
  )
} 
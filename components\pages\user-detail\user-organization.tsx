"use client"

import React, { useState, useEffect } from "react"
import { 
  Building,
  Loader2, 
  Users,
  PlusCircle
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"

interface Organization {
  id: number;
  name: string;
  code: string;
  type: string;
  path: string;
  level: number;
}

interface UserOrganizationProps {
  userId: number;
  departmentId?: number;
  isEditing?: boolean;
  onChangeDepartment?: () => void;
}

export function UserOrganization({ 
  userId,
  departmentId,
  isEditing = false,
  onChangeDepartment
}: UserOrganizationProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [organization, setOrganization] = useState<Organization | null>(null)
  const [error, setError] = useState<string | null>(null)

  // 获取组织信息
  useEffect(() => {
    const loadOrganizationData = async () => {
      if (!departmentId) {
        setOrganization(null)
        return
      }

      setIsLoading(true)
      setError(null)
      try {
        // 这里应该是一个真实的API调用
        // 为了示例，这里返回一些模拟数据
        await new Promise(resolve => setTimeout(resolve, 500))
        
        setOrganization({
          id: departmentId,
          name: `部门-${departmentId}`,
          code: `dept-${departmentId}`,
          type: "部门",
          path: "/公司/技术中心/研发部",
          level: 3
        })
      } catch (err) {
        console.error("获取组织信息失败", err)
        setError("获取组织信息失败")
      } finally {
        setIsLoading(false)
      }
    }

    loadOrganizationData()
  }, [departmentId])

  return (
    <Card className="border-border/40">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg flex items-center gap-2">
              <Building className="h-5 w-5 text-primary" />
              所属组织
            </CardTitle>
            <CardDescription>用户所属的组织架构信息</CardDescription>
          </div>
          {isEditing && onChangeDepartment && (
            <Button 
              variant="outline" 
              size="sm" 
              onClick={onChangeDepartment}
              className="gap-1"
            >
              <Building className="h-4 w-4" />
              修改部门
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        ) : error ? (
          <div className="text-center py-6 text-muted-foreground">
            <p>{error}</p>
          </div>
        ) : !organization ? (
          <div className="text-center py-6">
            <Building className="h-12 w-12 mx-auto text-muted-foreground mb-3" />
            <h3 className="font-medium mb-1">未分配部门</h3>
            <p className="text-sm text-muted-foreground">
              该用户目前未分配到任何组织
            </p>
            {isEditing && onChangeDepartment && (
              <Button 
                className="mt-4 gap-1"
                variant="outline"
                onClick={onChangeDepartment}
              >
                <PlusCircle className="h-4 w-4" />
                添加到部门
              </Button>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            <div className="p-4 border rounded-lg bg-muted/10">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <Building className="h-5 w-5 text-primary" />
                  <h3 className="font-medium">{organization.name}</h3>
                </div>
                <Badge variant="outline" className="bg-muted/30">
                  {organization.type}
                </Badge>
              </div>
              
              <div className="grid gap-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">部门编码</span>
                  <span className="font-medium">{organization.code}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">部门ID</span>
                  <span className="font-medium">{organization.id}</span>
                </div>
                <Separator className="my-2" />
                <div className="flex justify-between">
                  <span className="text-muted-foreground">组织路径</span>
                  <span className="font-medium">{organization.path}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">组织层级</span>
                  <span className="font-medium">第 {organization.level} 级</span>
                </div>
              </div>
            </div>
            
            {isEditing && onChangeDepartment && (
              <div className="flex justify-end">
                <Button 
                  variant="outline"
                  size="sm"
                  onClick={onChangeDepartment}
                  className="gap-1"
                >
                  <Building className="h-4 w-4" />
                  修改部门
                </Button>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default UserOrganization; 
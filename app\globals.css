@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 224 71.4% 4.1%;
    --card: 0 0% 100%;
    --card-foreground: 224 71.4% 4.1%;
    --popover: 0 0% 100%;
    --popover-foreground: 224 71.4% 4.1%;
    --primary: 220.9 39.3% 11%;
    --primary-foreground: 210 20% 98%;
    --secondary: 220 14.3% 95.9%;
    --secondary-foreground: 220.9 39.3% 11%;
    --muted: 220 14.3% 95.9%;
    --muted-foreground: 220 8.9% 46.1%;
    --accent: 220 14.3% 95.9%;
    --accent-foreground: 220.9 39.3% 11%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 20% 98%;
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 224 71.4% 4.1%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
  
  /* 全局移除默认的选中黑边框 */
  *:focus {
    outline: none !important;
  }
  
  /* 移除输入框、按钮等元素的默认边框 */
  input:focus, 
  button:focus, 
  select:focus, 
  textarea:focus {
    outline: none !important;
    box-shadow: none !important;
  }
}

/* 自定义组件样式 */
@layer components {
  /* 移除焦点边框，添加手型光标 */
  .no-focus-outline {
    @apply focus:outline-none focus:ring-0 focus:ring-offset-0;
  }

  /* 为交互元素添加手型光标 */
  button,
  [role="button"],
  [role="menuitem"],
  [role="tab"] {
    cursor: pointer;
  }

  /* 深色模式优化 */
  .dark {
    /* 改善代码块在深色模式下的显示 */
    pre {
      @apply bg-slate-800 border-slate-700 text-slate-200;
    }

    code {
      @apply bg-slate-800 text-slate-200 px-1 py-0.5 rounded;
    }

    /* 内联代码样式 */
    :not(pre) > code {
      @apply bg-slate-700 text-slate-300;
    }

    /* 改善表格在深色模式下的显示 */
    table {
      @apply border-slate-700/40 bg-slate-900;
    }

    th {
      @apply border-slate-700/40 bg-slate-800 text-slate-200;
    }

    td {
      @apply border-slate-700/30 text-slate-300;
    }

    /* 改善卡片在深色模式下的阴影 */
    .shadow-md {
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
    }

    .shadow-lg {
      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
    }

    .shadow-xl {
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
    }

    /* 改善边框颜色 - 使用更柔和的边框 */
    .border {
      @apply border-slate-700/60;
    }

    .border-gray-200 {
      @apply border-slate-700/50;
    }

    .border-gray-300 {
      @apply border-slate-600/60;
    }

    /* 卡片边框优化 */
    .border-border {
      @apply border-slate-700/40;
    }

    /* 输入框边框优化 */
    .border-input {
      @apply border-slate-600/50;
    }

    /* 改善背景颜色 */
    .bg-gray-50 {
      @apply bg-slate-800;
    }

    .bg-gray-100 {
      @apply bg-slate-700;
    }

    .bg-gray-200 {
      @apply bg-slate-600;
    }

    /* 改善文本颜色 */
    .text-gray-500 {
      @apply text-slate-400;
    }

    .text-gray-600 {
      @apply text-slate-300;
    }

    .text-gray-700 {
      @apply text-slate-200;
    }

    .text-gray-800 {
      @apply text-slate-100;
    }

    .text-gray-900 {
      @apply text-slate-50;
    }

    /* 改善滚动条在深色模式下的显示 */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }

    ::-webkit-scrollbar-track {
      @apply bg-slate-800;
    }

    ::-webkit-scrollbar-thumb {
      @apply bg-slate-600 rounded;
    }

    ::-webkit-scrollbar-thumb:hover {
      @apply bg-slate-500;
    }

    /* 改善输入框在深色模式下的显示 */
    input, textarea, select {
      @apply bg-slate-800 border-slate-600/50 text-slate-200;
    }

    input:focus, textarea:focus, select:focus {
      @apply border-slate-500/70 ring-slate-500/30;
    }

    /* 改善按钮在深色模式下的显示 */
    .btn-secondary {
      @apply bg-slate-700 text-slate-200 border-slate-600;
    }

    .btn-secondary:hover {
      @apply bg-slate-600 border-slate-500;
    }

    /* 改善模态框和弹出层 */
    .modal-overlay {
      background-color: rgba(0, 0, 0, 0.7);
    }

    .dropdown-content {
      @apply bg-slate-800 border-slate-700 shadow-xl;
    }

    /* 改善选择状态 */
    .selected {
      @apply bg-slate-700 text-slate-200;
    }

    /* 改善悬停状态 */
    .hover\\:bg-gray-50:hover {
      @apply bg-slate-700;
    }

    .hover\\:bg-gray-100:hover {
      @apply bg-slate-600;
    }

    /* 数据表格深色模式优化 */
    .data-table {
      @apply bg-slate-900 border-slate-700/40;
    }

    /* Progress组件深色模式优化 */
    [data-radix-progress-root] {
      @apply bg-slate-700 border-slate-600;
    }

    [data-radix-progress-indicator] {
      @apply bg-slate-200;
    }

    /* 确保Progress组件的背景色正确 */
    .bg-secondary {
      @apply bg-slate-700;
    }

    /* 修复muted背景色 */
    .bg-muted\/50 {
      @apply bg-slate-800/50;
    }

    .bg-muted {
      @apply bg-slate-800;
    }

    /* 状态徽章深色模式优化 */
    .bg-green-100 {
      @apply bg-green-900/30 border-green-700;
    }

    .text-green-800 {
      @apply text-green-300;
    }

    .bg-blue-100 {
      @apply bg-blue-900/30 border-blue-700;
    }

    .text-blue-800 {
      @apply text-blue-300;
    }

    .bg-yellow-100 {
      @apply bg-yellow-900/30 border-yellow-700;
    }

    .text-yellow-800 {
      @apply text-yellow-300;
    }

    .bg-red-100 {
      @apply bg-red-900/30 border-red-700;
    }

    .text-red-800 {
      @apply text-red-300;
    }

    .bg-purple-100 {
      @apply bg-purple-900/30 border-purple-700;
    }

    .text-purple-800 {
      @apply text-purple-300;
    }

    .bg-gray-100 {
      @apply bg-slate-700 border-slate-600;
    }

    .text-gray-800 {
      @apply text-slate-200;
    }

    /* KPI卡片中的进度条优化 */
    .kpi-progress-bg {
      @apply bg-slate-700;
    }

    .kpi-progress-fill {
      @apply bg-slate-300;
    }

    .data-table th {
      @apply bg-slate-800 text-slate-200 border-slate-700;
    }

    .data-table td {
      @apply border-slate-700 text-slate-300;
    }

    .data-table tr:hover {
      @apply bg-slate-800;
    }

    .data-table tr.selected {
      @apply bg-slate-700;
    }

    /* 日历组件深色模式优化 */
    .calendar-container {
      @apply bg-slate-900 border-slate-700;
    }

    .calendar-day {
      @apply text-slate-300;
    }

    .calendar-day:hover {
      @apply bg-slate-700 text-slate-200;
    }

    /* 修复所有可能的白色背景 */
    .bg-white {
      @apply bg-card;
    }

    /* 修复渐变背景在深色模式下的显示 */
    .bg-gradient-to-br.from-blue-50.to-indigo-100 {
      @apply from-slate-800 to-slate-700;
    }

    .from-muted\/70.to-muted {
      @apply from-slate-800/70 to-slate-700;
    }

    /* 修复边框颜色 */
    .border-gray-200 {
      @apply border-slate-700;
    }

    .border-gray-300 {
      @apply border-slate-600;
    }

    /* 修复文本颜色 */
    .text-gray-800 {
      @apply text-slate-200;
    }

    .text-gray-600 {
      @apply text-slate-300;
    }

    .text-gray-500 {
      @apply text-slate-400;
    }

    .calendar-day.selected {
      @apply bg-blue-600 text-white;
    }

    .calendar-event {
      @apply bg-slate-700 border-slate-600 text-slate-200;
    }

    /* 搜索组件深色模式优化 */
    .search-input {
      @apply bg-slate-800 border-slate-600 text-slate-200;
    }

    .search-results {
      @apply bg-slate-800 border-slate-700;
    }

    .search-result-item {
      @apply text-slate-300 border-slate-700;
    }

    .search-result-item:hover {
      @apply bg-slate-700 text-slate-200;
    }
  }

  /* 移除默认的焦点样式 */
  button:focus,
  [role="button"]:focus,
  [role="menuitem"]:focus,
  [role="tab"]:focus,
  a:focus,
  input:focus,
  select:focus,
  textarea:focus {
    @apply outline-none ring-0 ring-offset-0 shadow-none;
  }

  /* 保持可访问性的焦点指示器 - 仅在使用键盘导航时显示 */
  button:focus-visible,
  [role="button"]:focus-visible,
  [role="menuitem"]:focus-visible,
  [role="tab"]:focus-visible {
    @apply ring-1 ring-primary/30 ring-offset-0;
  }
  
  /* 移除图片选择器的黑色边框 */
  img {
    @apply select-none;
  }
  
  /* 移除表单元素的黑色边框 */
  .form-input:focus,
  .form-select:focus,
  .form-checkbox:focus,
  .form-radio:focus {
    @apply outline-none ring-0 ring-offset-0 shadow-none border-input;
  }
  
  /* 移除组件预览中的边框 */
  .preview-component *:focus {
    @apply outline-none ring-0 ring-offset-0 shadow-none border-input;
  }
}

/* 自定义预览组件样式 */
.component-preview {
  @apply border border-border rounded-md p-4 bg-background;
}

/* 移除标签页组件的黑边框 */
.tabs-component [role="tab"]:focus {
  @apply outline-none ring-0 ring-offset-0 shadow-none;
}

/* 移除下拉菜单组件的黑边框 */
.dropdown-component button:focus,
.dropdown-component [role="menuitem"]:focus {
  @apply outline-none ring-0 ring-offset-0 shadow-none;
}

/* React Day Picker 样式修复 */
.rdp {
  --rdp-cell-size: 36px;
  --rdp-accent-color: hsl(var(--primary));
  --rdp-background-color: hsl(var(--background));
  margin: 0;
}

.rdp-button_reset {
  appearance: none;
  border: none;
  background: none;
  color: inherit;
  cursor: pointer;
  font: inherit;
  outline: none;
}

.rdp-button {
  border: none;
  background: none;
  cursor: pointer;
}

.rdp-months {
  display: flex;
  flex-direction: column;
}

.rdp-month {
  margin: 0;
}

.rdp-table {
  width: 100%;
  border-collapse: collapse;
}

.rdp-head_cell {
  width: var(--rdp-cell-size);
  height: var(--rdp-cell-size);
  text-align: center;
  font-size: 0.875rem;
  font-weight: 500;
  color: hsl(var(--muted-foreground));
}

.rdp-cell {
  width: var(--rdp-cell-size);
  height: var(--rdp-cell-size);
  text-align: center;
  position: relative;
}

.rdp-day {
  width: var(--rdp-cell-size);
  height: var(--rdp-cell-size);
  border-radius: 0.375rem;
  border: none;
  background: none;
  font-size: 0.875rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.rdp-day:hover {
  background-color: hsl(var(--accent));
}

.rdp-day_selected {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

.rdp-day_selected:hover {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

.rdp-day_today {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

.rdp-day_outside {
  color: hsl(var(--muted-foreground));
  opacity: 0.5;
}

.rdp-day_disabled {
  color: hsl(var(--muted-foreground));
  opacity: 0.5;
  cursor: not-allowed;
}

.rdp-day_range_start,
.rdp-day_range_end {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

.rdp-day_range_middle {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

.rdp-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.rdp-nav_button {
  width: 2rem;
  height: 2rem;
  border-radius: 0.375rem;
  border: 1px solid hsl(var(--border));
  background: hsl(var(--background));
  color: hsl(var(--foreground));
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
}

.rdp-nav_button:hover {
  background-color: hsl(var(--accent));
}

.rdp-nav_button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.rdp-caption_label {
  font-size: 1rem;
  font-weight: 500;
}

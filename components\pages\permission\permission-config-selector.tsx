"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { ChevronDown, Plus, RefreshCw, Trash2 } from "lucide-react"
import type { PermissionConfig } from "@/types/permission"
import { useToast } from "@/components/ui/use-toast"
import { createGroupRequest, deleteGroupRequest } from "@/services/api/groupRequestApi"
import {CreateGroupParams} from "@/types/models";

interface PermissionConfigSelectorProps {
  configs: PermissionConfig[]
  activeConfig: PermissionConfig
  onSelectConfig: (config: PermissionConfig) => void
  onAddConfig: (config: Omit<PermissionConfig, "id" | "permissions">) => void
  onRefresh: () => void
  onDeleteConfig?: (configId: string) => void
}

export default function PermissionConfigSelector({
  configs,
  activeConfig,
  onSelectConfig,
  onAddConfig,
  onRefresh,
  onDeleteConfig,
}: PermissionConfigSelectorProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [configToDelete, setConfigToDelete] = useState<PermissionConfig | null>(null)
  const [newConfig, setNewConfig] = useState({
    code: "",
    name: "",
    description: "",
  })
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isLoading, setIsLoading] = useState(false)
  const [isDeletingConfig, setIsDeletingConfig] = useState(false)
  const { toast } = useToast()

  const handleAddConfig = async () => {
    const newErrors: Record<string, string> = {}

    if (!newConfig.code.trim()) {
      newErrors.code = "配置编码不能为空"
    }

    if (!newConfig.name.trim()) {
      newErrors.name = "配置名称不能为空"
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors)
      return
    }

    try {
      setIsLoading(true)
      
      // 调用创建群组接口
      const params: CreateGroupParams = {
        moduleCode: 'permission',
        code: newConfig.code,
        name: newConfig.name,
        description: newConfig.description
      }
      
      const groupId = await createGroupRequest(params)
      
      if (groupId !== null) {
        // 创建配置对象
        const newPermissionConfig: PermissionConfig = {
          id: groupId.toString(),
          code: newConfig.code,
          name: newConfig.name,
          description: newConfig.description || "",
          permissions: []
        }
        
        // 调用传入的回调函数
        onAddConfig(newPermissionConfig)
        
        setIsDialogOpen(false)
        setNewConfig({ code: "", name: "", description: "" })
        setErrors({})
        
        toast({
          title: "创建成功",
          description: `已成功创建权限配置 "${newConfig.name}"`,
        })
      } else {
        toast({
          title: "创建失败",
          description: "创建权限配置失败，请稍后重试",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("创建权限配置失败:", error)
      toast({
        title: "创建失败",
        description: "创建权限配置时发生错误，请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleDeleteConfig = (config: PermissionConfig, e: React.MouseEvent) => {
    e.stopPropagation()
    
    if (configs.length <= 1) {
      toast({
        title: "无法删除",
        description: "至少保留一个权限配置",
        variant: "destructive",
      })
      return
    }
    
    setConfigToDelete(config)
    setIsDeleteDialogOpen(true)
  }

  const confirmDeleteConfig = async () => {
    if (!configToDelete || !onDeleteConfig) {
      return;
    }
    
    try {
      setIsDeletingConfig(true);
      
      // 调用删除群组接口
      const configId = configToDelete.id;
      
      // 解析ID，处理不同格式的情况
      // 如果ID是以"config-"开头的临时ID，则无法删除，跳过API调用
      if (configId.startsWith('config-')) {
        console.warn('尝试删除临时配置，跳过API调用');
        // 直接调用父组件的删除回调
        onDeleteConfig(configToDelete.id);
        
        toast({
          title: "删除成功",
          description: `已成功删除权限配置 "${configToDelete.name}"`,
        });
        return;
      }
      
      // 尝试将ID转换为数字
      const numericId = parseInt(configId, 10);
      
      if (isNaN(numericId)) {
        console.error('无效的配置ID:', configId);
        toast({
          title: "删除失败",
          description: "无效的配置ID，无法删除",
          variant: "destructive",
        });
        return;
      }
      
      const result = await deleteGroupRequest(configId);
      
      if (result) {
        // 调用父组件的删除回调
        onDeleteConfig(configToDelete.id);
        
        toast({
          title: "删除成功",
          description: `已成功删除权限配置 "${configToDelete.name}"`,
        });
      } else {
        toast({
          title: "删除失败",
          description: "删除权限配置失败，请稍后重试",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("删除权限配置失败:", error);
      toast({
        title: "删除失败",
        description: "删除权限配置时发生错误，请稍后重试",
        variant: "destructive",
      });
    } finally {
      setIsDeletingConfig(false);
      setConfigToDelete(null);
      setIsDeleteDialogOpen(false);
    }
  }

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" className="flex items-center justify-between font-normal min-w-[180px] px-3">
            <span className="mr-auto">{activeConfig.name}</span>
            <ChevronDown className="h-4 w-4 text-muted-foreground ml-2" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-[240px]">
          {configs.map((config) => (
            <DropdownMenuItem
              key={config.id}
              onClick={() => onSelectConfig(config)}
              className="flex items-center justify-between text-sm py-1.5"
            >
              <span className="font-medium">{config.name || `配置 ${config.code || config.id}`}</span>
              <div className="flex items-center">
                {config.id === activeConfig.id && (
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6 ml-2"
                    onClick={(e) => {
                      e.stopPropagation()
                      onRefresh()
                    }}
                  >
                    <RefreshCw className="h-3.5 w-3.5 text-primary" />
                  </Button>
                )}
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6 ml-1 text-destructive"
                  onClick={(e) => handleDeleteConfig(config, e)}
                >
                  <Trash2 className="h-3.5 w-3.5" />
                </Button>
              </div>
            </DropdownMenuItem>
          ))}
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => setIsDialogOpen(true)} className="text-sm py-1.5">
            <Plus className="h-4 w-4 mr-2 text-primary" />
            <span className="font-medium">新增配置</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-sm">
          <DialogHeader>
            <DialogTitle className="text-base font-semibold">新增权限配置</DialogTitle>
            <DialogDescription className="text-sm">创建一个新的权限配置组，用于管理不同系统或模块的权限</DialogDescription>
          </DialogHeader>

          <div className="space-y-3 py-3">
            <div className="space-y-1.5">
              <Label htmlFor="code" className="text-sm font-medium">
                配置编码 <span className="text-destructive">*</span>
              </Label>
              <Input
                id="code"
                value={newConfig.code}
                onChange={(e) => setNewConfig({ ...newConfig, code: e.target.value })}
                className={errors.code ? "border-destructive" : ""}
              />
              {errors.code && <p className="text-xs text-destructive">{errors.code}</p>}
            </div>

            <div className="space-y-1.5">
              <Label htmlFor="name" className="text-sm font-medium">
                配置名称 <span className="text-destructive">*</span>
              </Label>
              <Input
                id="name"
                value={newConfig.name}
                onChange={(e) => setNewConfig({ ...newConfig, name: e.target.value })}
                className={errors.name ? "border-destructive" : ""}
              />
              {errors.name && <p className="text-xs text-destructive">{errors.name}</p>}
            </div>

            <div className="space-y-1.5">
              <Label htmlFor="description" className="text-sm font-medium">配置描述</Label>
              <Textarea
                id="description"
                value={newConfig.description}
                onChange={(e) => setNewConfig({ ...newConfig, description: e.target.value })}
                rows={2}
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)} disabled={isLoading}>
              取消
            </Button>
            <Button onClick={handleAddConfig} disabled={isLoading}>
              {isLoading ? "创建中..." : "确认"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <AlertDialog 
        open={isDeleteDialogOpen} 
        onOpenChange={(open) => {
          setIsDeleteDialogOpen(open);
          if (!open) {
            setConfigToDelete(null); // 关闭时始终重置状态
            // 强制恢复body的pointer-events属性
            document.body.style.pointerEvents = '';
          }
        }}
      >
        <AlertDialogContent
          // 添加关闭自动聚焦事件处理，确保pointer-events被正确重置
          onCloseAutoFocus={(event) => {
            event.preventDefault();
            document.body.style.pointerEvents = '';
          }}
        >
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除权限配置</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除"{configToDelete?.name}"权限配置吗？此操作无法撤销，删除后该配置下的所有权限将会丢失。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction 
              onClick={confirmDeleteConfig}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              disabled={isDeletingConfig}
            >
              {isDeletingConfig ? "删除中..." : "删除"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}

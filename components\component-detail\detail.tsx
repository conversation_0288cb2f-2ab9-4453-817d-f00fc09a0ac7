"use client";

import React from "react";
import { Card, CardContent } from "../ui/card";
import { Separator } from "../ui/separator";

interface ApiProps {
  name: string;
  type: string;
  defaultValue?: string;
  description: string;
  required?: boolean;
}

export interface ComponentDetailProps {
  title: string;
  description: string;
  usage?: string | React.ReactNode;
  apiDocs?: ApiProps[];
  children?: React.ReactNode;
  preview?: React.ReactNode;
  code?: string;
  api?: React.ReactNode;
  props?: React.ReactNode;
  category?: string;
  version?: string;
}

export function Detail({
  title,
  description,
  usage,
  apiDocs,
  children,
  preview,
  code,
  api,
  props,
  category,
  version
}: ComponentDetailProps) {
  return (
    <div className="space-y-8">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">{title}</h1>
        <p className="text-lg text-muted-foreground">{description}</p>
        {category && version && (
          <div className="flex items-center space-x-4 text-sm text-muted-foreground">
            {category && <div>分类: {category}</div>}
            {version && <div>版本: {version}</div>}
          </div>
        )}
      </div>

      {preview && (
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">预览</h2>
          <div className="rounded-lg border p-6 bg-background">{preview}</div>
          {code && (
            <div className="p-4 border rounded-lg bg-muted overflow-auto">
              <pre className="text-sm">
                <code>{code}</code>
              </pre>
            </div>
          )}
        </div>
      )}

      {usage && (
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">使用说明</h2>
          {typeof usage === 'string' ? (
            <p className="text-muted-foreground">{usage}</p>
          ) : (
            usage
          )}
        </div>
      )}

      {api && (
        <>
          <Separator />
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">API</h2>
            {api}
          </div>
        </>
      )}

      {props && (
        <>
          <Separator />
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">属性</h2>
            {props}
          </div>
        </>
      )}

      {children && (
        <>
          <Separator />
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">示例</h2>
            {children}
          </div>
        </>
      )}

      {apiDocs && (
        <>
          <Separator />
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">API</h2>
            <Card>
              <CardContent className="pt-6">
                <div className="overflow-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="bg-muted text-left">
                        <th className="p-2 border">参数</th>
                        <th className="p-2 border">类型</th>
                        <th className="p-2 border">默认值</th>
                        <th className="p-2 border">说明</th>
                        <th className="p-2 border">必填</th>
                      </tr>
                    </thead>
                    <tbody>
                      {apiDocs.map((api, index) => (
                        <tr key={index}>
                          <td className="p-2 border">{api.name}</td>
                          <td className="p-2 border font-mono text-sm">{api.type}</td>
                          <td className="p-2 border">{api.defaultValue || '-'}</td>
                          <td className="p-2 border">{api.description}</td>
                          <td className="p-2 border text-center">
                            {api.required ? '✓' : '-'}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </div>
        </>
      )}
    </div>
  );
}

export default Detail; 
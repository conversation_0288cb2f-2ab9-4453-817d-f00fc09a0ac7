import RoleManagement from "@/components/pages/roles/role-management";
import { HeaderWithBreadcrumb } from "@/components/custom/breadcrumb";
import RoleEditDialog from "@/components/pages/roles/role-edit-dialog";

export default function RolesPage() {
  // 面包屑数据
  const breadcrumbItems = [
    { label: "设置", href: "/settings" },
    { label: "角色管理", active: true }
  ]

  return (
    <div className="flex flex-col min-h-screen">
      <HeaderWithBreadcrumb items={breadcrumbItems}/>
      <main className="flex-1">
        <div className="container mx-auto px-6 py-6 max-w-7xl">
          <RoleManagement />
        </div>
      </main>
      
      {/* 角色编辑弹窗 */}
      <RoleEditDialog />
    </div>
  )
} 
"use client"

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Building, FileText, MessageSquare, ThumbsUp, UserPlus } from "lucide-react"

const activities = [
  {
    id: 1,
    user: {
      name: "张三",
      avatar: "/placeholder.svg?height=32&width=32",
    },
    action: "添加了新成员",
    target: "李四",
    icon: <UserPlus className="h-4 w-4" />,
    iconBg: "bg-green-500/10",
    iconColor: "text-green-500",
    time: "10分钟前",
  },
  {
    id: 2,
    user: {
      name: "王五",
      avatar: "/placeholder.svg?height=32&width=32",
    },
    action: "完成了任务",
    target: "首页UI设计",
    icon: <FileText className="h-4 w-4" />,
    iconBg: "bg-blue-500/10",
    iconColor: "text-blue-500",
    time: "1小时前",
  },
  {
    id: 3,
    user: {
      name: "赵六",
      avatar: "/placeholder.svg?height=32&width=32",
    },
    action: "评论了",
    target: "后端API文档",
    icon: <MessageSquare className="h-4 w-4" />,
    iconBg: "bg-purple-500/10",
    iconColor: "text-purple-500",
    time: "2小时前",
  },
  {
    id: 4,
    user: {
      name: "钱七",
      avatar: "/placeholder.svg?height=32&width=32",
    },
    action: "创建了子团队",
    target: "UI/UX设计组",
    icon: <Building className="h-4 w-4" />,
    iconBg: "bg-orange-500/10",
    iconColor: "text-orange-500",
    time: "昨天",
  },
  {
    id: 5,
    user: {
      name: "孙八",
      avatar: "/placeholder.svg?height=32&width=32",
    },
    action: "点赞了",
    target: "团队周报",
    icon: <ThumbsUp className="h-4 w-4" />,
    iconBg: "bg-pink-500/10",
    iconColor: "text-pink-500",
    time: "昨天",
  },
]

export function TeamActivityFeed() {
  return (
    <div className="space-y-4">
      {activities.map((activity) => (
        <div key={activity.id} className="flex items-start gap-4">
          <Avatar className="h-8 w-8">
            <AvatarImage src={activity.user.avatar || "/placeholder.svg"} alt={activity.user.name} />
            <AvatarFallback>{activity.user.name.slice(0, 2)}</AvatarFallback>
          </Avatar>
          <div className="flex-1 space-y-1">
            <div className="flex items-center gap-2">
              <span className="font-medium">{activity.user.name}</span>
              <span className="text-sm text-muted-foreground">{activity.action}</span>
              <span className="font-medium">{activity.target}</span>
            </div>
            <div className="text-xs text-muted-foreground">{activity.time}</div>
          </div>
          <div className={`flex h-8 w-8 items-center justify-center rounded-full ${activity.iconBg}`}>
            <div className={activity.iconColor}>{activity.icon}</div>
          </div>
        </div>
      ))}
    </div>
  )
}

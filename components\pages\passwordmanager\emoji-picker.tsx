"use client"

import { useState } from "react"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Button } from "@/components/ui/button"

// 常用的密码类别表情符号
const commonEmojis = [
  "🔑",
  "🔒",
  "🔐",
  "🔓",
  "📱",
  "💻",
  "🖥️",
  "📧",
  "🌐",
  "🏦",
  "💳",
  "💰",
  "🛒",
  "🎮",
  "🎬",
  "🎵",
  "📚",
  "✈️",
  "🏠",
  "🚗",
  "⚙️",
  "📁",
  "📝",
  "🔔",
  "📊",
  "🔍",
  "🔖",
  "📌",
  "🔗",
  "📲",
]

type EmojiPickerProps = {
  value: string
  onChange: (emoji: string) => void
}

export function EmojiPicker({ value, onChange }: EmojiPickerProps) {
  const [open, setOpen] = useState(false)

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" className="w-12 h-12 text-2xl p-0">
          {value}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-64 p-2">
        <div className="grid grid-cols-5 gap-2">
          {commonEmojis.map((emoji) => (
            <Button
              key={emoji}
              variant="ghost"
              className="h-10 w-10 p-0 text-xl"
              onClick={() => {
                onChange(emoji)
                setOpen(false)
              }}
            >
              {emoji}
            </Button>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  )
} 
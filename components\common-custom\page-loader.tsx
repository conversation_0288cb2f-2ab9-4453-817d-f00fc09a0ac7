"use client"

import { useEffect, useState } from "react"
import { Loader2 } from "lucide-react"
import { cn } from "@/lib/utils"

interface PageLoaderProps {
  /**
   * 是否显示加载状态
   */
  loading?: boolean
  
  /**
   * 加载文本
   */
  loadingText?: string
  
  /**
   * 路径变化回调
   */
  onRouteChange?: () => void
  
  /**
   * 自定义类名
   */
  className?: string
  
  /**
   * @deprecated 标记是否为新组件，仅用于界面展示
   */
  isNew?: boolean
}

/**
 * 页面加载状态组件
 * 用于在页面切换时显示加载效果
 */
export function PageLoader({ 
  loading: controlledLoading,
  loadingText = "正在加载页面...",
  onRouteChange,
  className,
  isNew = true 
}: PageLoaderProps) {
  const [internalLoading, setInternalLoading] = useState(false)
  
  // 使用受控或非受控模式
  const loading = controlledLoading !== undefined ? controlledLoading : internalLoading
  
  // 监听路由变化，显示加载状态
  useEffect(() => {
    // 如果是受控模式，不需要内部状态管理
    if (controlledLoading !== undefined) return
    
    const handleStart = () => {
      setInternalLoading(true)
      onRouteChange?.()
    }
    
    const handleComplete = () => {
      setTimeout(() => setInternalLoading(false), 300) // 添加小延迟让动画更流畅
    }

    // 为了在组件挂载后也能触发一次加载状态，通常首次渲染
    let startTimeout: NodeJS.Timeout
    startTimeout = setTimeout(() => {
      handleStart()
      setTimeout(handleComplete, 500)
    }, 0)

    // 监听路由变化
    window.addEventListener("navigationstart", handleStart)
    window.addEventListener("navigationsuccess", handleComplete)
    window.addEventListener("navigationerror", handleComplete)

    return () => {
      clearTimeout(startTimeout)
      window.removeEventListener("navigationstart", handleStart)
      window.removeEventListener("navigationsuccess", handleComplete)
      window.removeEventListener("navigationerror", handleComplete)
    }
  }, [controlledLoading, onRouteChange])

  if (!loading) return null

  return (
    <div className={cn(
      "fixed inset-0 z-50 flex items-center justify-center bg-background/50 backdrop-blur-sm",
      className
    )}>
      <div className="flex flex-col items-center space-y-4">
        <Loader2 className="h-10 w-10 animate-spin text-primary" />
        <p className="text-sm text-muted-foreground">{loadingText}</p>
      </div>
      
      {/* NEW 标识 */}
      {isNew && (
        <span className="absolute bottom-4 right-4 bg-primary text-primary-foreground text-[10px] font-medium px-1 py-0.5 rounded-full">
          NEW
        </span>
      )}
    </div>
  )
}

interface ContentSkeletonProps {
  className?: string
  /**
   * @deprecated 标记是否为新组件，仅用于界面展示
   */
  isNew?: boolean
}

/**
 * 内容骨架屏组件
 * 用于在内容加载时显示占位效果
 */
export function ContentSkeleton({ className, isNew = true }: ContentSkeletonProps) {
  return (
    <div className={cn("space-y-4 animate-pulse relative", className)}>
      <div className="h-8 w-3/4 rounded-md bg-muted"></div>
      <div className="h-4 w-full rounded-md bg-muted"></div>
      <div className="h-4 w-full rounded-md bg-muted"></div>
      <div className="h-4 w-3/4 rounded-md bg-muted"></div>
      <div className="space-y-2">
        <div className="h-64 rounded-md bg-muted"></div>
        <div className="grid grid-cols-3 gap-4">
          <div className="h-20 rounded-md bg-muted"></div>
          <div className="h-20 rounded-md bg-muted"></div>
          <div className="h-20 rounded-md bg-muted"></div>
        </div>
      </div>
      
      {/* NEW 标识 */}
      {isNew && (
        <span className="absolute top-0 right-0 bg-primary text-primary-foreground text-[10px] font-medium px-1 py-0.5 rounded-full">
          NEW
        </span>
      )}
    </div>
  )
} 
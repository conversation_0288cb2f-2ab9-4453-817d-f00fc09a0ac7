"use client"

import { ReactNode } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { ArrowUpCircle, ArrowDownCircle, MinusCircle } from "lucide-react"
import { cn } from "@/lib/utils"

/**
 * 仪表盘卡片组件
 * 用于在仪表盘中展示关键指标
 */
export function DashboardCard({
  data
}: {
  data: {
    title: string
    value: string
    change?: number
    trend?: "increase" | "decrease" | "neutral"
    description?: string
    icon?: ReactNode
  }
}) {
  return (
    <Card className="overflow-hidden">
      <CardContent className="p-6">
        <div className="flex justify-between items-start">
          <div>
            <p className="text-sm font-medium text-muted-foreground mb-1">{data.title}</p>
            <div className="text-2xl font-bold">{data.value}</div>
          </div>
          {data.icon && <div>{data.icon}</div>}
        </div>
        
        {(data.change !== undefined || data.description) && (
          <div className="mt-4 flex items-center">
            {data.change !== undefined && (
              <div className={cn(
                "flex items-center text-sm mr-2",
                data.trend === "increase" ? "text-green-500" : 
                data.trend === "decrease" ? "text-red-500" : 
                "text-muted-foreground"
              )}>
                {data.trend === "increase" ? (
                  <ArrowUpCircle className="h-4 w-4 mr-1" />
                ) : data.trend === "decrease" ? (
                  <ArrowDownCircle className="h-4 w-4 mr-1" />
                ) : (
                  <MinusCircle className="h-4 w-4 mr-1" />
                )}
                
                <span>{data.change > 0 ? '+' : ''}{data.change}%</span>
              </div>
            )}
            
            {data.description && (
              <p className="text-xs text-muted-foreground">{data.description}</p>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
} 
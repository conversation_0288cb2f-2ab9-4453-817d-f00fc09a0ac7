"use client"

import * as React from "react"
import { ReactNode } from "react"
import { Skeleton } from "@/components/ui/skeleton"
import { Button } from "@/components/ui/button"
import { Loader2, RefreshCw, CheckCircle, AlertCircle, LucideIcon } from "lucide-react"
import { cn } from "@/lib/utils"

// ============================================================================
// 类型定义 - 单文件组件类型定义直接在组件文件内
// ============================================================================

/**
 * 加载旋转器组件属性
 */
export interface LoadingSpinnerProps {
  /**
   * 旋转器大小
   * @default "md"
   */
  size?: "xs" | "sm" | "md" | "lg" | "xl"

  /**
   * 旋转器变体
   * @default "default"
   */
  variant?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | "circular"

  /**
   * 自定义类名
   */
  className?: string

  /**
   * 自定义图标
   */
  icon?: LucideIcon

  /**
   * 是否显示文本
   * @default false
   */
  showText?: boolean

  /**
   * 加载文本
   * @default "加载中..."
   */
  text?: string
}

/**
 * 加载按钮组件属性
 */
export interface LoadingButtonProps extends React.ComponentProps<typeof Button> {
  /**
   * 是否加载中
   * @default false
   */
  loading?: boolean

  /**
   * 加载中显示的文本
   * @default "加载中..."
   */
  loadingText?: string

  /**
   * 加载图标
   */
  loadingIcon?: LucideIcon

  /**
   * 成功状态
   * @default false
   */
  success?: boolean

  /**
   * 成功文本
   * @default "成功"
   */
  successText?: string

  /**
   * 成功图标
   */
  successIcon?: LucideIcon

  /**
   * 自动重置成功状态的延迟时间（毫秒）
   */
  successResetDelay?: number
}

/**
 * 骨架屏列表组件属性
 */
export interface SkeletonListProps {
  /**
   * 列表项数量
   * @default 3
   */
  count?: number

  /**
   * 头像大小
   * @default 12
   */
  avatarSize?: number

  /**
   * 是否显示头像
   * @default true
   */
  withAvatar?: boolean

  /**
   * 是否显示操作按钮
   * @default false
   */
  withAction?: boolean

  /**
   * 自定义类名
   */
  className?: string
}

/**
 * 骨架屏卡片组件属性
 */
export interface SkeletonCardProps {
  /**
   * 是否显示操作按钮
   * @default true
   */
  withAction?: boolean

  /**
   * 是否显示图片
   * @default false
   */
  withImage?: boolean

  /**
   * 图片高度
   * @default "h-32"
   */
  imageHeight?: string

  /**
   * 是否显示标题
   * @default true
   */
  withTitle?: boolean

  /**
   * 是否显示描述
   * @default true
   */
  withDescription?: boolean

  /**
   * 自定义类名
   */
  className?: string
}

/**
 * 全页面加载组件属性
 */
export interface FullPageLoaderProps {
  /**
   * 主要文本
   * @default "加载中..."
   */
  text?: string

  /**
   * 副文本
   * @default "正在获取数据，请稍候"
   */
  subText?: string

  /**
   * 加载状态
   * @default "loading"
   */
  status?: "loading" | "success" | "error"

  /**
   * 自定义图标
   */
  icon?: LucideIcon

  /**
   * 是否显示重试按钮（错误状态下）
   * @default false
   */
  showRetry?: boolean

  /**
   * 重试回调
   */
  onRetry?: () => void

  /**
   * 自定义类名
   */
  className?: string
}

/**
 * 进度条加载组件属性
 */
export interface ProgressLoaderProps {
  /**
   * 进度值 (0-100)
   */
  progress: number

  /**
   * 是否显示百分比
   * @default true
   */
  showPercentage?: boolean

  /**
   * 主标签
   */
  label?: string

  /**
   * 副标签
   */
  subLabel?: string

  /**
   * 进度条颜色
   * @default "primary"
   */
  color?: "primary" | "secondary" | "success" | "warning" | "danger"

  /**
   * 进度条高度
   * @default "h-2"
   */
  height?: string

  /**
   * 是否显示动画
   * @default true
   */
  animated?: boolean

  /**
   * 自定义类名
   */
  className?: string
}

/**
 * 加载容器组件属性
 */
export interface LoadingContainerProps {
  /**
   * 是否加载中
   */
  loading: boolean

  /**
   * 子组件
   */
  children: ReactNode

  /**
   * 加载时显示的内容
   */
  fallback?: ReactNode

  /**
   * 加载器类型
   * @default "spinner"
   */
  loaderType?: "spinner" | "skeleton" | "custom"

  /**
   * 最小加载时间（毫秒）
   * @default 0
   */
  minLoadingTime?: number

  /**
   * 自定义类名
   */
  className?: string
}

// ============================================================================
// 组件实现
// ============================================================================

/**
 * 加载旋转器组件
 *
 * 提供多种样式和大小的加载旋转器
 *
 * @example
 * ```tsx
 * // 基础用法
 * <LoadingSpinner />
 *
 * // 自定义大小和颜色
 * <LoadingSpinner size="lg" variant="primary" />
 *
 * // 带文本的加载器
 * <LoadingSpinner showText text="正在加载..." />
 * ```
 */
export function LoadingSpinner({
  size = "md",
  variant = "default",
  className,
  icon,
  showText = false,
  text = "加载中..."
}: LoadingSpinnerProps) {
  const sizeClass = {
    xs: "h-3 w-3",
    sm: "h-4 w-4",
    md: "h-6 w-6",
    lg: "h-8 w-8",
    xl: "h-12 w-12"
  }

  const variantClass = {
    default: "text-muted-foreground",
    primary: "text-primary",
    secondary: "text-secondary",
    success: "text-green-500",
    warning: "text-yellow-500",
    danger: "text-red-500",
    circular: "text-primary"
  }

  const IconComponent = icon || (variant === "circular" ? RefreshCw : Loader2)

  return (
    <div className={cn("flex items-center justify-center", showText && "flex-col gap-2", className)}>
      <IconComponent className={cn("animate-spin", sizeClass[size], variantClass[variant])} />
      {showText && (
        <span className="text-sm text-muted-foreground">{text}</span>
      )}
    </div>
  )
}

/**
 * 加载按钮组件
 *
 * 支持加载状态和成功状态的按钮组件
 *
 * @example
 * ```tsx
 * // 基础用法
 * <LoadingButton loading={isLoading} onClick={handleClick}>
 *   提交
 * </LoadingButton>
 *
 * // 带成功状态
 * <LoadingButton
 *   loading={isLoading}
 *   success={isSuccess}
 *   successText="已保存"
 *   onClick={handleSave}
 * >
 *   保存
 * </LoadingButton>
 * ```
 */
export function LoadingButton({
  children,
  loading = false,
  loadingText = "加载中...",
  loadingIcon = Loader2,
  success = false,
  successText = "成功",
  successIcon = CheckCircle,
  successResetDelay,
  className,
  ...props
}: LoadingButtonProps) {
  const [showSuccess, setShowSuccess] = React.useState(false)

  React.useEffect(() => {
    if (success) {
      setShowSuccess(true)
      if (successResetDelay) {
        const timer = setTimeout(() => {
          setShowSuccess(false)
        }, successResetDelay)
        return () => clearTimeout(timer)
      }
    } else {
      setShowSuccess(false)
    }
  }, [success, successResetDelay])

  const LoadingIcon = loadingIcon
  const SuccessIcon = successIcon

  return (
    <Button
      disabled={loading || showSuccess}
      className={cn(
        showSuccess && "bg-green-500 hover:bg-green-500",
        className
      )}
      {...props}
    >
      {loading && <LoadingIcon className="mr-2 h-4 w-4 animate-spin" />}
      {showSuccess && <SuccessIcon className="mr-2 h-4 w-4" />}
      {loading ? loadingText : showSuccess ? successText : children}
    </Button>
  )
}

/**
 * 骨架屏列表组件
 *
 * 用于列表数据加载时的占位显示
 *
 * @example
 * ```tsx
 * // 基础用法
 * <SkeletonList count={5} />
 *
 * // 不显示头像
 * <SkeletonList count={3} withAvatar={false} />
 *
 * // 带操作按钮
 * <SkeletonList count={4} withAction={true} />
 * ```
 */
export function SkeletonList({
  count = 3,
  avatarSize = 12,
  className,
  withAvatar = true,
  withAction = false
}: SkeletonListProps) {
  return (
    <div className={cn("space-y-4", className)}>
      {Array.from({ length: count }).map((_, i) => (
        <div key={i} className="flex items-center space-x-4">
          {withAvatar && (
            <Skeleton
              className="rounded-full"
              style={{
                height: `${avatarSize * 4}px`,
                width: `${avatarSize * 4}px`
              }}
            />
          )}
          <div className="space-y-2 flex-1">
            <Skeleton className="h-4 w-1/3" />
            <Skeleton className="h-3 w-2/3" />
          </div>
          {withAction && (
            <div className="flex space-x-2">
              <Skeleton className="h-8 w-16" />
              <Skeleton className="h-8 w-16" />
            </div>
          )}
        </div>
      ))}
    </div>
  )
}

/**
 * 骨架屏卡片组件
 *
 * 用于卡片内容加载时的占位显示
 *
 * @example
 * ```tsx
 * // 基础用法
 * <SkeletonCard />
 *
 * // 带图片的卡片
 * <SkeletonCard withImage={true} imageHeight="h-48" />
 *
 * // 简单卡片（无操作按钮）
 * <SkeletonCard withAction={false} />
 * ```
 */
export function SkeletonCard({
  className,
  withAction = true,
  withImage = false,
  imageHeight = "h-32",
  withTitle = true,
  withDescription = true
}: SkeletonCardProps) {
  return (
    <div className={cn("space-y-3", className)}>
      {withImage && (
        <Skeleton className={cn("w-full rounded-lg", imageHeight)} />
      )}
      {withTitle && (
        <>
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-4 w-1/2" />
        </>
      )}
      {withDescription && (
        <Skeleton className="h-20 w-full" />
      )}
      {withAction && (
        <div className="flex space-x-2">
          <Skeleton className="h-8 w-16" />
          <Skeleton className="h-8 w-16" />
        </div>
      )}
    </div>
  )
}

/**
 * 全页面加载组件
 *
 * 用于整个页面或大区域的加载状态显示
 *
 * @example
 * ```tsx
 * // 基础用法
 * <FullPageLoader />
 *
 * // 自定义文本
 * <FullPageLoader
 *   text="正在处理..."
 *   subText="请耐心等待"
 * />
 *
 * // 错误状态带重试
 * <FullPageLoader
 *   status="error"
 *   text="加载失败"
 *   showRetry={true}
 *   onRetry={handleRetry}
 * />
 * ```
 */
export function FullPageLoader({
  text = "加载中...",
  subText = "正在获取数据，请稍候",
  status = "loading",
  icon,
  showRetry = false,
  onRetry,
  className
}: FullPageLoaderProps) {
  const getStatusIcon = () => {
    if (icon) {
      const CustomIcon = icon
      return <CustomIcon className="h-8 w-8" />
    }

    switch (status) {
      case "loading":
        return <Loader2 className="h-8 w-8 animate-spin text-primary" />
      case "success":
        return <CheckCircle className="h-8 w-8 text-green-500" />
      case "error":
        return <AlertCircle className="h-8 w-8 text-red-500" />
      default:
        return <Loader2 className="h-8 w-8 animate-spin text-primary" />
    }
  }

  return (
    <div className={cn("flex flex-col items-center justify-center py-12 space-y-4", className)}>
      {getStatusIcon()}
      <div className="text-center">
        <div className="font-medium">{text}</div>
        <div className="text-sm text-muted-foreground">{subText}</div>
      </div>
      {status === "error" && showRetry && onRetry && (
        <Button onClick={onRetry} variant="outline" size="sm">
          重试
        </Button>
      )}
    </div>
  )
}

/**
 * 进度条加载组件
 *
 * 显示带进度的加载状态
 *
 * @example
 * ```tsx
 * // 基础用法
 * <ProgressLoader progress={60} />
 *
 * // 带标签
 * <ProgressLoader
 *   progress={75}
 *   label="上传中..."
 *   subLabel="正在处理文件"
 * />
 *
 * // 自定义样式
 * <ProgressLoader
 *   progress={90}
 *   color="success"
 *   height="h-3"
 *   animated={true}
 * />
 * ```
 */
export function ProgressLoader({
  progress = 0,
  showPercentage = true,
  label,
  subLabel,
  color = "primary",
  height = "h-2",
  animated = true,
  className
}: ProgressLoaderProps) {
  const colorClass = {
    primary: "bg-primary",
    secondary: "bg-secondary",
    success: "bg-green-500",
    warning: "bg-yellow-500",
    danger: "bg-red-500"
  }

  return (
    <div className={cn("space-y-2", className)}>
      {(label || showPercentage) && (
        <div className="flex justify-between text-sm">
          {label && <span>{label}</span>}
          {showPercentage && <span>{Math.round(progress)}%</span>}
        </div>
      )}
      <div className={cn("w-full bg-muted rounded-full", height)}>
        <div
          className={cn(
            colorClass[color],
            height,
            "rounded-full",
            animated && "transition-all duration-300 ease-out"
          )}
          style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
        />
      </div>
      {subLabel && (
        <div className="text-xs text-muted-foreground">{subLabel}</div>
      )}
    </div>
  )
}

/**
 * 加载容器组件
 *
 * 根据加载状态显示内容或加载器的容器组件
 *
 * @example
 * ```tsx
 * // 基础用法
 * <LoadingContainer loading={isLoading}>
 *   <div>内容</div>
 * </LoadingContainer>
 *
 * // 自定义加载器
 * <LoadingContainer
 *   loading={isLoading}
 *   fallback={<SkeletonCard />}
 * >
 *   <Card>内容</Card>
 * </LoadingContainer>
 * ```
 */
export function LoadingContainer({
  loading = false,
  children,
  fallback,
  loaderType = "spinner",
  minLoadingTime = 0,
  className
}: LoadingContainerProps) {
  const [showLoading, setShowLoading] = React.useState(loading)

  React.useEffect(() => {
    if (loading) {
      setShowLoading(true)
    } else if (minLoadingTime > 0) {
      const timer = setTimeout(() => {
        setShowLoading(false)
      }, minLoadingTime)
      return () => clearTimeout(timer)
    } else {
      setShowLoading(false)
    }
  }, [loading, minLoadingTime])

  if (!showLoading) return <>{children}</>

  if (fallback) return <>{fallback}</>

  const getDefaultLoader = () => {
    switch (loaderType) {
      case "skeleton":
        return <SkeletonCard />
      case "spinner":
      default:
        return <LoadingSpinner />
    }
  }

  return (
    <div className={cn("p-4 flex items-center justify-center", className)}>
      {getDefaultLoader()}
    </div>
  )
}

// ============================================================================
// 类型导出
// ============================================================================

export type {
  LoadingSpinnerProps,
  LoadingButtonProps,
  SkeletonListProps,
  SkeletonCardProps,
  FullPageLoaderProps,
  ProgressLoaderProps,
  LoadingContainerProps
}

// ============================================================================
// 常量导出
// ============================================================================

/**
 * 支持的加载器大小
 */
export const LOADING_SIZES = ['xs', 'sm', 'md', 'lg', 'xl'] as const

/**
 * 支持的加载器变体
 */
export const LOADING_VARIANTS = ['default', 'primary', 'secondary', 'success', 'warning', 'danger', 'circular'] as const

/**
 * 支持的加载状态
 */
export const LOADING_STATUSES = ['loading', 'success', 'error'] as const

/**
 * 支持的进度条颜色
 */
export const PROGRESS_COLORS = ['primary', 'secondary', 'success', 'warning', 'danger'] as const

/**
 * 支持的加载器类型
 */
export const LOADER_TYPES = ['spinner', 'skeleton', 'custom'] as const

/**
 * 默认加载旋转器配置
 */
export const DEFAULT_LOADING_SPINNER_CONFIG = {
  size: 'md' as const,
  variant: 'default' as const,
  showText: false,
  text: '加载中...',
} satisfies Partial<LoadingSpinnerProps>

/**
 * 默认加载按钮配置
 */
export const DEFAULT_LOADING_BUTTON_CONFIG = {
  loading: false,
  loadingText: '加载中...',
  success: false,
  successText: '成功',
} satisfies Partial<LoadingButtonProps>

/**
 * 默认骨架屏列表配置
 */
export const DEFAULT_SKELETON_LIST_CONFIG = {
  count: 3,
  avatarSize: 12,
  withAvatar: true,
  withAction: false,
} satisfies Partial<SkeletonListProps>

/**
 * 默认全页面加载器配置
 */
export const DEFAULT_FULL_PAGE_LOADER_CONFIG = {
  text: '加载中...',
  subText: '正在获取数据，请稍候',
  status: 'loading' as const,
  showRetry: false,
} satisfies Partial<FullPageLoaderProps>

/**
 * 默认进度条加载器配置
 */
export const DEFAULT_PROGRESS_LOADER_CONFIG = {
  showPercentage: true,
  color: 'primary' as const,
  height: 'h-2',
  animated: true,
} satisfies Partial<ProgressLoaderProps>

"use client"

import * as React from "react"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { cn } from "@/lib/utils"

// ============================================================================
// 类型定义 - 单文件组件类型定义直接在组件文件内
// ============================================================================

/**
 * 分页组件属性
 */
export interface PaginationProps {
  /**
   * 当前页索引（从0开始）
   */
  pageIndex: number

  /**
   * 总页数
   */
  pageCount: number

  /**
   * 每页显示数量
   */
  pageSize: number

  /**
   * 每页显示数量选项
   * @default [10, 20, 30, 40, 50]
   */
  pageSizeOptions?: number[]

  /**
   * 总记录数
   */
  totalItems: number

  /**
   * 页码变化回调
   * @param page - 新的页索引（从0开始）
   */
  onPageChange: (page: number) => void

  /**
   * 每页显示数量变化回调
   * @param size - 新的每页显示数量
   */
  onPageSizeChange: (size: number) => void

  /**
   * 自定义类名
   */
  className?: string

  /**
   * 是否禁用
   * @default false
   */
  disabled?: boolean

  /**
   * 是否显示总数信息
   * @default true
   */
  showTotal?: boolean

  /**
   * 是否显示每页数量选择器
   * @default true
   */
  showPageSizeChanger?: boolean

  /**
   * 是否显示快速跳转
   * @default true
   */
  showQuickJumper?: boolean

  /**
   * 自定义总数文本格式化函数
   */
  totalFormatter?: (total: number, range: [number, number]) => string
}

// ============================================================================
// 组件实现
// ============================================================================

/**
 * 分页组件
 *
 * 提供完整的分页功能，包括页码导航、每页数量选择和快速跳转
 *
 * @example
 * ```tsx
 * // 基础用法
 * <Pagination
 *   pageIndex={0}
 *   pageCount={10}
 *   pageSize={20}
 *   totalItems={200}
 *   onPageChange={(page) => console.log('页码变化:', page)}
 *   onPageSizeChange={(size) => console.log('每页数量变化:', size)}
 * />
 *
 * // 自定义配置
 * <Pagination
 *   pageIndex={2}
 *   pageCount={15}
 *   pageSize={10}
 *   totalItems={150}
 *   pageSizeOptions={[5, 10, 20, 50]}
 *   showTotal={true}
 *   showPageSizeChanger={true}
 *   showQuickJumper={true}
 *   onPageChange={handlePageChange}
 *   onPageSizeChange={handlePageSizeChange}
 * />
 * ```
 */
export function Pagination({
  pageIndex,
  pageCount,
  pageSize,
  pageSizeOptions = [10, 20, 30, 40, 50] as readonly number[],
  totalItems,
  onPageChange,
  onPageSizeChange,
  className,
  disabled = false,
  showTotal = true,
  showPageSizeChanger = true,
  showQuickJumper = true,
  totalFormatter,
}: PaginationProps) {
  const [inputValue, setInputValue] = React.useState(pageIndex + 1)

  React.useEffect(() => {
    setInputValue(pageIndex + 1)
  }, [pageIndex])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(Number(e.target.value))
  }

  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      const page = inputValue ? Number(inputValue) - 1 : 0
      if (page >= 0 && page < pageCount) {
        onPageChange(page)
      } else {
        setInputValue(pageIndex + 1)
      }
    }
  }

  const handleInputBlur = () => {
    const page = inputValue ? Number(inputValue) - 1 : 0
    if (page >= 0 && page < pageCount) {
      onPageChange(page)
    } else {
      setInputValue(pageIndex + 1)
    }
  }

  return (
    <div className={cn("flex flex-col sm:flex-row items-center justify-between py-4", className)}>
      {/* 左侧显示总数 */}
      <div className="text-sm text-muted-foreground order-2 sm:order-1 mt-2 sm:mt-0">
        总计 {totalItems} 条
      </div>

      {/* 中间是页码切换 - 小屏居中，大屏靠右 */}
      <div className="flex items-center justify-center sm:justify-end sm:ml-auto sm:mr-4 order-1 sm:order-2 gap-2">
        <div className="flex items-center gap-1">
          {/* 上一页按钮 */}
          <Button
            variant="outline"
            size="icon"
            className="h-9 w-9"
            onClick={() => onPageChange(pageIndex - 1)}
            disabled={disabled || pageIndex === 0}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          
          {/* 页码按钮 */}
          <div className="flex items-center gap-1">
            {(() => {
              const visiblePages = [];
              const totalPages = pageCount;
              const currentPage = pageIndex + 1;
              
              if (totalPages <= 7) {
                // 如果总页数小于等于7，显示所有页码
                for (let i = 1; i <= totalPages; i++) {
                  visiblePages.push(i);
                }
              } else {
                // 始终显示第一页
                visiblePages.push(1);
                
                if (currentPage <= 4) {
                  // 当前页靠近开始
                  for (let i = 2; i <= 5; i++) {
                    visiblePages.push(i);
                  }
                  visiblePages.push(-1); // 省略号
                  visiblePages.push(totalPages);
                } else if (currentPage >= totalPages - 3) {
                  // 当前页靠近结束
                  visiblePages.push(-1); // 省略号
                  for (let i = totalPages - 4; i <= totalPages; i++) {
                    visiblePages.push(i);
                  }
                } else {
                  // 当前页在中间
                  visiblePages.push(-1); // 省略号
                  for (let i = currentPage - 1; i <= currentPage + 1; i++) {
                    visiblePages.push(i);
                  }
                  visiblePages.push(-1); // 省略号
                  visiblePages.push(totalPages);
                }
              }

              return visiblePages.map((pageNum, index) => {
                if (pageNum === -1) {
                  return (
                    <Button
                      key={`ellipsis-${index}`}
                      variant="ghost"
                      size="icon"
                      className="h-9 w-9"
                      disabled
                    >
                      ...
                    </Button>
                  );
                }
                return (
                  <Button
                    key={pageNum}
                    variant={pageNum === currentPage ? "default" : "outline"}
                    size="icon"
                    className="h-9 w-9"
                    onClick={() => onPageChange(pageNum - 1)}
                    disabled={disabled}
                  >
                    {pageNum}
                  </Button>
                );
              });
            })()}
          </div>

          {/* 下一页按钮 */}
          <Button
            variant="outline"
            size="icon"
            className="h-9 w-9"
            onClick={() => onPageChange(pageIndex + 1)}
            disabled={disabled || pageIndex === pageCount - 1}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>

        {/* 跳转和每页条数 */}
        <div className="flex items-center gap-2 ml-2">
          {showPageSizeChanger && (
            <Select
              value={String(pageSize)}
              onValueChange={(value) => onPageSizeChange(Number(value))}
              disabled={disabled}
            >
              <SelectTrigger className="h-9 w-[90px]">
                <SelectValue>{pageSize} 条/页</SelectValue>
              </SelectTrigger>
              <SelectContent>
                {pageSizeOptions.map((option) => (
                  <SelectItem key={option} value={String(option)}>
                    {option} 条/页
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}

          {showQuickJumper && (
            <div className="flex items-center gap-1">
              <span className="text-sm text-muted-foreground">跳至</span>
              <Input
                type="number"
                min={1}
                max={pageCount}
                value={inputValue}
                onChange={handleInputChange}
                onKeyDown={handleInputKeyDown}
                onBlur={handleInputBlur}
                className="h-9 w-[60px]"
                disabled={disabled}
              />
              <span className="text-sm text-muted-foreground">页</span>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

// ============================================================================
// 类型导出
// ============================================================================

export type { PaginationProps }

// ============================================================================
// 常量导出
// ============================================================================

/**
 * 默认每页显示数量选项
 */
export const DEFAULT_PAGE_SIZE_OPTIONS = [10, 20, 30, 40, 50] as const

/**
 * 常用每页显示数量选项
 */
export const COMMON_PAGE_SIZE_OPTIONS = [5, 10, 20, 50, 100] as const

/**
 * 默认分页配置
 */
export const DEFAULT_PAGINATION_CONFIG = {
  pageSize: 20,
  pageSizeOptions: DEFAULT_PAGE_SIZE_OPTIONS,
  disabled: false,
  showTotal: true,
  showPageSizeChanger: true,
  showQuickJumper: true,
} satisfies Partial<PaginationProps>

/**
 * 默认总数格式化函数
 */
export const defaultTotalFormatter = (total: number, range: [number, number]) => {
  return `显示第 ${range[0]}-${range[1]} 条，共 ${total} 条`
}

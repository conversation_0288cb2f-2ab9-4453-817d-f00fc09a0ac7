---
description:
globs:
alwaysApply: false
---
# 设计规范遵循原则

## 核心规则

在实现新功能或修改bug时，必须首先参考并遵循`.doc`目录下的设计规范文档，确保代码实现符合项目设计标准。

## 设计规范文档

项目的设计规范文档按照以下顺序组织：

1. **01-设计哲学.md** - 设计理念、核心原则和价值观
2. **02-色彩系统.md** - 色彩变量、语义化颜色和暗色模式规范
3. **03-排版系统.md** - 字体、文字层级和间距规则
4. **04-布局网格.md** - 网格系统、响应式布局和间距规范
5. **05-组件系统.md** - 基础组件、复合组件和自定义组件规范
6. **06-交互设计.md** - 交互原则、动画系统和状态变化规范
7. **07-主题与暗色模式.md** - 主题配置和暗色模式实现
8. **08-表单设计.md** - 表单组件和交互的设计规范
9. **09-图标系统.md** - 图标使用规范和实现方法
10. **10-通用组件规范.md** - 通用组件的设计和实现规范
11. **11-请求规范.md** - API请求的实现和错误处理
12. **12-数据可视化规范.md** - 图表和数据可视化规范
13. **13-动效与过渡系统.md** - 动画效果和过渡规范
14. **14-图片与媒体规范.md** - 图片和媒体内容的处理规范

## 开发要求

1. **规范优先**: 在开始编码前，必须先阅读相关的设计规范文档
2. **一致性**: 确保新增或修改的代码与现有设计保持一致
3. **问题解决**: 当设计规范与功能需求冲突时，应寻求团队讨论解决
4. **文档更新**: 如发现规范文档需要更新，应同时提出更新建议

## 实施流程

1. 接收任务或修复bug需求
2. 查阅相关设计规范文档
3. 根据规范文档设计解决方案
4. 编码实现并确保符合规范
5. 提交代码前检查是否符合设计规范

## 优先级顺序

当规范之间存在冲突时，遵循以下优先级顺序：
1. 设计哲学与核心原则
2. 组件系统与交互设计规范
3. 其他具体实现规范

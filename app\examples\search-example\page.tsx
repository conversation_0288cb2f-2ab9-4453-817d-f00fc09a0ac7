"use client"

import React from "react"
import { ComponentPreviewContainer } from "@/components/component-detail"
import { allExamples } from "./examples"

// API文档组件
function SearchApiDocs() {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-3">主要组件</h3>
        
        <div className="space-y-4">
          <div className="border rounded-lg p-4">
            <h4 className="font-medium mb-2">SearchInput</h4>
            <p className="text-sm text-muted-foreground mb-2">
              基础搜索输入框组件，支持清除、提交等功能
            </p>
            <div className="space-y-2">
              <div className="text-sm">
                <strong>主要属性：</strong>
                <code className="ml-2 text-xs bg-muted px-2 py-1 rounded">
                  value, onChange, placeholder, onSearch, onClear
                </code>
              </div>
            </div>
          </div>
          
          <div className="border rounded-lg p-4">
            <h4 className="font-medium mb-2">SearchResults</h4>
            <p className="text-sm text-muted-foreground mb-2">
              搜索结果展示组件，支持自定义渲染
            </p>
            <div className="space-y-2">
              <div className="text-sm">
                <strong>主要属性：</strong>
                <code className="ml-2 text-xs bg-muted px-2 py-1 rounded">
                  results, onResultClick, renderResult, loading
                </code>
              </div>
            </div>
          </div>
          
          <div className="border rounded-lg p-4">
            <h4 className="font-medium mb-2">GlobalSearch</h4>
            <p className="text-sm text-muted-foreground mb-2">
              全局搜索对话框，支持快捷键调用
            </p>
            <div className="space-y-2">
              <div className="text-sm">
                <strong>主要属性：</strong>
                <code className="ml-2 text-xs bg-muted px-2 py-1 rounded">
                  open, onOpenChange, results, placeholder
                </code>
              </div>
            </div>
          </div>
          
          <div className="border rounded-lg p-4">
            <h4 className="font-medium mb-2">AdvancedSearch</h4>
            <p className="text-sm text-muted-foreground mb-2">
              高级搜索组件，支持过滤器和搜索历史
            </p>
            <div className="space-y-2">
              <div className="text-sm">
                <strong>主要属性：</strong>
                <code className="ml-2 text-xs bg-muted px-2 py-1 rounded">
                  filters, onFilterChange, history, showHistory
                </code>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div>
        <h3 className="text-lg font-semibold mb-3">使用场景</h3>
        <div className="space-y-2 text-sm text-muted-foreground">
          <p>• <strong>基础搜索</strong>：简单的关键词搜索功能</p>
          <p>• <strong>搜索结果</strong>：展示和交互搜索结果</p>
          <p>• <strong>高级搜索</strong>：复杂的筛选和过滤需求</p>
          <p>• <strong>全局搜索</strong>：应用级别的快速搜索</p>
          <p>• <strong>命令栏</strong>：快捷操作和导航</p>
        </div>
      </div>
      
      <div>
        <h3 className="text-lg font-semibold mb-3">特性</h3>
        <div className="space-y-2 text-sm text-muted-foreground">
          <p>• 支持多种搜索模式和交互方式</p>
          <p>• 完全的 TypeScript 类型支持</p>
          <p>• 可自定义样式和渲染</p>
          <p>• 支持键盘快捷键</p>
          <p>• 响应式设计，适配移动端</p>
          <p>• 支持搜索历史和建议</p>
        </div>
      </div>
    </div>
  )
}

export default function SearchExamplePage() {
  return (
    <ComponentPreviewContainer
      title="搜索组件 Search"
      description="用于搜索和过滤数据的组件集合，包括基础搜索、搜索结果展示、搜索建议和全局搜索等功能"
      whenToUse="当需要在应用中提供搜索功能时使用；适用于数据列表搜索、内容筛选、全局导航、命令面板等场景；支持多种搜索模式和交互方式"
      examples={allExamples}
      apiDocs={<SearchApiDocs />}
    />
  )
}

"use client"

import React from "react"
import { ComponentPreviewContainer } from "@/components/component-detail/preview-container"
import { Pagination } from "@/components/common-custom/pagination"
import { allExamples } from "./examples"

// ============================================================================
// API文档组件
// ============================================================================

function PaginationApiDocs() {
  return (
    <div className="space-y-6">
      {/* 主组件API */}
      <div>
        <h3 className="font-medium text-lg mb-2">Pagination</h3>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">pageIndex</td>
                <td className="p-2 border">number</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">当前页索引（从0开始）</td>
              </tr>
              <tr>
                <td className="p-2 border">pageCount</td>
                <td className="p-2 border">number</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">总页数</td>
              </tr>
              <tr>
                <td className="p-2 border">pageSize</td>
                <td className="p-2 border">number</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">每页显示数量</td>
              </tr>
              <tr>
                <td className="p-2 border">pageSizeOptions</td>
                <td className="p-2 border">number[]</td>
                <td className="p-2 border">[10, 20, 30, 40, 50]</td>
                <td className="p-2 border">每页显示数量选项</td>
              </tr>
              <tr>
                <td className="p-2 border">totalItems</td>
                <td className="p-2 border">number</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">总记录数</td>
              </tr>
              <tr>
                <td className="p-2 border">onPageChange</td>
                <td className="p-2 border">(page: number) =&gt; void</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">页码变化回调函数</td>
              </tr>
              <tr>
                <td className="p-2 border">onPageSizeChange</td>
                <td className="p-2 border">(size: number) =&gt; void</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">每页条数变化回调函数</td>
              </tr>
              <tr>
                <td className="p-2 border">disabled</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">false</td>
                <td className="p-2 border">是否禁用</td>
              </tr>
              <tr>
                <td className="p-2 border">showTotal</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">true</td>
                <td className="p-2 border">是否显示总数信息</td>
              </tr>
              <tr>
                <td className="p-2 border">showPageSizeChanger</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">true</td>
                <td className="p-2 border">是否显示每页数量选择器</td>
              </tr>
              <tr>
                <td className="p-2 border">showQuickJumper</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">true</td>
                <td className="p-2 border">是否显示快速跳转</td>
              </tr>
              <tr>
                <td className="p-2 border">className</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">自定义CSS类名</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* 使用指南 */}
      <div>
        <h3 className="font-medium text-lg mb-2">使用指南</h3>
        <div className="space-y-4 text-sm">
          <div>
            <h4 className="font-medium mb-2">基本用法</h4>
            <p className="text-muted-foreground mb-2">
              最简单的分页组件使用方式：
            </p>
            <pre className="bg-muted p-3 rounded-md overflow-x-auto">
              <code>{`<Pagination
  pageIndex={0}
  pageCount={10}
  pageSize={20}
  totalItems={200}
  onPageChange={(page) => setPageIndex(page)}
  onPageSizeChange={(size) => setPageSize(size)}
/>`}</code>
            </pre>
          </div>

          <div>
            <h4 className="font-medium mb-2">自定义配置</h4>
            <p className="text-muted-foreground mb-2">
              通过配置属性控制显示功能：
            </p>
            <pre className="bg-muted p-3 rounded-md overflow-x-auto">
              <code>{`<Pagination
  pageIndex={pageIndex}
  pageCount={pageCount}
  pageSize={pageSize}
  totalItems={totalItems}
  pageSizeOptions={[5, 10, 20, 50]}
  showTotal={true}
  showPageSizeChanger={true}
  showQuickJumper={true}
  onPageChange={handlePageChange}
  onPageSizeChange={handlePageSizeChange}
/>`}</code>
            </pre>
          </div>

          <div>
            <h4 className="font-medium mb-2">响应式设计</h4>
            <p className="text-muted-foreground mb-2">
              分页组件自动适应不同屏幕尺寸，在小屏幕上隐藏部分功能以保持良好的用户体验。
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

// ============================================================================
// 主预览组件
// ============================================================================

export default function PaginationExamplePage() {
  return (
    <ComponentPreviewContainer
      title="分页 Pagination"
      description="用于数据分页展示的组件，支持页码导航、每页数量选择和快速跳转功能"
      whenToUse="当需要对大量数据进行分页展示时使用；适用于表格数据、列表数据、搜索结果等场景；支持响应式设计，在不同屏幕尺寸下自动调整显示内容"
      examples={allExamples}
      apiDocs={<PaginationApiDocs />}
    />
  )
}

"use client"

import React, { useState } from "react"
import { ComponentPreviewContainer } from "@/components/component-detail/preview-container"
import type { GroupedListItem } from "@/types/group-components"
import { 
  Stepper, 
  FAQGroup,
  GroupedList,
  SettingsGroup,
  CollapsibleGroup,
  CollapsibleGroupItem
} from "@/components/common-custom/group-components"
import { 
  CircleHelp, 
  Laptop, 
  BellRing, 
  ShieldCheck, 
  Settings, 
  User,
  FileText
} from "lucide-react"

// 步骤引导示例代码
const stepperCode = `
import React from "react";
import { Stepper } from "@/components/common-custom/group-components";

function StepperExample() {
  // 步骤示例数据
  const steps = [
    { id: 1, title: "基本信息", description: "填写用户基本信息" },
    { id: 2, title: "联系方式", description: "填写联系方式和地址信息" },
    { id: 3, title: "完成", description: "确认并提交" }
  ];

  return (
    <Stepper 
      steps={steps}
      currentStep={2}
    />
  );
}

render(<StepperExample />);
`;

// 常见问题分组示例代码
const faqGroupCode = `
import React from "react";
import { FAQGroup } from "@/components/common-custom/group-components";
import { CircleHelp } from "lucide-react";

function FAQGroupExample() {
  // FAQ示例数据
  const faqItems = [
    { 
      id: "faq-1", 
      question: "如何重置密码？", 
      answer: "您可以通过登录页面的'忘记密码'链接进行密码重置，系统会发送重置链接到您的邮箱。" 
    },
    { 
      id: "faq-2", 
      question: "如何升级账户？", 
      answer: "登录后前往'账户设置'页面，在'套餐管理'部分可以选择升级您的账户类型。" 
    }
  ];

  return (
    <FAQGroup
      title="常见问题"
      description="关于系统使用的常见问题解答"
      icon={<CircleHelp className="h-5 w-5" />}
      items={faqItems}
    />
  );
}

render(<FAQGroupExample />);
`;

// 分组列表示例代码
const groupedListCode = `
import React from "react";
import { GroupedList } from "@/components/common-custom/group-components";
import { BellRing } from "lucide-react";
import type { GroupedListItem } from "@/types/group-components";

function GroupedListExample() {
  // 分组列表示例数据
  const listItems: GroupedListItem[] = [
    { name: "全部消息", count: 24, status: "active" },
    { name: "未读消息", count: 5, status: "pending" },
    { name: "已归档", count: 18, status: "active" },
    { name: "已删除", count: 2, status: "inactive" }
  ];

  return (
    <GroupedList
      title="消息中心"
      icon={<BellRing className="h-5 w-5" />}
      items={listItems}
    />
  );
}

render(<GroupedListExample />);
`;

// 设置分组示例代码
const settingsGroupCode = `
import React from "react";
import { SettingsGroup } from "@/components/common-custom/group-components";
import { User, BellRing } from "lucide-react";

function SettingsGroupExample() {
  // 设置分组示例数据
  const accountItems = [
    { label: "个人资料", description: "管理您的账户信息" },
    { label: "密码安全", description: "更新您的密码和安全选项" }
  ];

  const notificationItems = [
    { label: "邮件通知", description: "接收重要事件的邮件提醒" },
    { label: "浏览器推送", description: "接收浏览器推送通知" }
  ];

  return (
    <div className="space-y-6 bg-muted/50 p-4 rounded-lg">
      <SettingsGroup
        title="账户"
        icon={<User className="h-5 w-5" />}
        items={accountItems}
      />
      <SettingsGroup
        title="通知"
        icon={<BellRing className="h-5 w-5" />}
        items={notificationItems}
        isLast={true}
      />
    </div>
  );
}

render(<SettingsGroupExample />);
`;

// 可折叠分组示例代码
const collapsibleGroupCode = `
import React, { useState } from "react";
import { CollapsibleGroup, CollapsibleGroupItem } from "@/components/common-custom/group-components";
import { FileText } from "lucide-react";

function CollapsibleGroupExample() {
  const [isOpen, setIsOpen] = useState(true);

  return (
    <CollapsibleGroup
      title="文档管理"
      icon={<FileText className="h-5 w-5" />}
      isOpen={isOpen}
      onToggle={() => setIsOpen(!isOpen)}
    >
      <CollapsibleGroupItem title="用户指南">
        <div className="p-2 text-sm">
          系统使用说明和基本操作教程
        </div>
      </CollapsibleGroupItem>
      <CollapsibleGroupItem title="API文档">
        <div className="p-2 text-sm">
          系统API详细说明和调用方式
        </div>
      </CollapsibleGroupItem>
    </CollapsibleGroup>
  );
}

render(<CollapsibleGroupExample />);
`;

// API文档组件
function GroupComponentsApiDocs() {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="font-medium text-lg mb-2">Stepper</h3>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">steps</td>
                <td className="p-2 border">Array&lt;{'{'}id: number; title: string; description?: string;{'}'}&gt;</td>
                <td className="p-2 border">[]</td>
                <td className="p-2 border">步骤数据数组</td>
              </tr>
              <tr>
                <td className="p-2 border">currentStep</td>
                <td className="p-2 border">number</td>
                <td className="p-2 border">1</td>
                <td className="p-2 border">当前步骤</td>
              </tr>
              <tr>
                <td className="p-2 border">className</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">自定义类名</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      
      <div>
        <h3 className="font-medium text-lg mb-2">FAQGroup</h3>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">title</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">分组标题</td>
              </tr>
              <tr>
                <td className="p-2 border">description</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">分组描述</td>
              </tr>
              <tr>
                <td className="p-2 border">icon</td>
                <td className="p-2 border">ReactNode</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">分组图标</td>
              </tr>
              <tr>
                <td className="p-2 border">items</td>
                <td className="p-2 border">Array&lt;{'{'}id: string; question: string; answer: string;{'}'}&gt;</td>
                <td className="p-2 border">[]</td>
                <td className="p-2 border">FAQ项目数据数组</td>
              </tr>
              <tr>
                <td className="p-2 border">className</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">自定义类名</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      
      <div>
        <h3 className="font-medium text-lg mb-2">GroupedList</h3>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">title</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">列表标题</td>
              </tr>
              <tr>
                <td className="p-2 border">icon</td>
                <td className="p-2 border">ReactNode</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">列表图标</td>
              </tr>
              <tr>
                <td className="p-2 border">items</td>
                <td className="p-2 border">GroupedListItem[]</td>
                <td className="p-2 border">[]</td>
                <td className="p-2 border">列表项数据数组</td>
              </tr>
              <tr>
                <td className="p-2 border">className</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">自定义类名</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

export default function GroupComponentsPreview() {
  const examples = [
    {
      id: "stepper",
      title: "步骤引导",
      description: "用于展示多步操作流程的进度",
      code: stepperCode,
      scope: { 
        Stepper, 
        React 
      }, 
    },
    {
      id: "faq-group",
      title: "常见问题分组",
      description: "用于展示常见问题和解答的分组组件",
      code: faqGroupCode,
      scope: { 
        FAQGroup, 
        CircleHelp,
        React 
      },
    },
    {
      id: "grouped-list",
      title: "分组列表",
      description: "用于展示带有计数和状态的分组列表",
      code: groupedListCode,
      scope: { 
        GroupedList, 
        BellRing,
        React 
      },
    },
    {
      id: "settings-group",
      title: "设置分组",
      description: "用于展示应用设置的分组组件",
      code: settingsGroupCode,
      scope: { 
        SettingsGroup, 
        User,
        BellRing,
        React 
      },
    },
    {
      id: "collapsible-group",
      title: "可折叠分组",
      description: "可展开和折叠的分组组件",
      code: collapsibleGroupCode,
      scope: { 
        CollapsibleGroup,
        CollapsibleGroupItem,
        FileText,
        useState: React.useState,
        React 
      },
    },
  ];

  return (
    <ComponentPreviewContainer
      title="组件分组 GroupComponents"
      description="用于将相关组件进行分组展示的容器组件集合"
      whenToUse="当需要将相关内容或功能进行分组展示时；当需要展示步骤流程时；当需要展示常见问题解答时；当需要展示设置选项时；当需要展示可折叠内容时。"
      examples={examples}
      apiDocs={<GroupComponentsApiDocs />}
    />
  );
} 
 
 
 

 
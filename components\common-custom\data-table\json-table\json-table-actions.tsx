"use client"

import * as React from "react"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Eye, Pencil, Trash2, MoreHorizontal } from "lucide-react"
import { JsonTableAction } from "../types"
import { cn } from "@/lib/utils"

interface JsonTableActionsProps {
  actions: JsonTableAction[]
  row: any
  onAction: (action: string, actionType: string) => void
}

export function JsonTableActions({
  actions,
  row,
  onAction,
}: JsonTableActionsProps) {
  const [confirmDialog, setConfirmDialog] = React.useState<{
    isOpen: boolean
    action: JsonTableAction | null
  }>({
    isOpen: false,
    action: null,
  })
  
  // 获取操作图标
  const getActionIcon = (action: JsonTableAction) => {
    if (action.icon) {
      // 这里可以添加更多图标映射
      switch (action.icon) {
        case "eye":
          return <Eye className="mr-2 h-4 w-4" />
        case "pencil":
          return <Pencil className="mr-2 h-4 w-4" />
        case "trash":
          return <Trash2 className="mr-2 h-4 w-4" />
        default:
          return null
      }
    }
    
    // 根据操作类型返回默认图标
    switch (action.type) {
      case "view":
        return <Eye className="mr-2 h-4 w-4" />
      case "edit":
        return <Pencil className="mr-2 h-4 w-4" />
      case "delete":
        return <Trash2 className="mr-2 h-4 w-4" />
      default:
        return null
    }
  }
  
  // 获取操作变体样式
  const getActionVariant = (action: JsonTableAction): "default" | "destructive" | "outline" | "secondary" | "ghost" => {
    if (action.variant) {
      return action.variant
    }
    
    // 根据操作类型返回默认变体样式
    switch (action.type) {
      case "delete":
        return "destructive"
      case "view":
        return "outline"
      case "edit":
        return "secondary"
      default:
        return "ghost"
    }
  }
  
  // 检查操作是否应该显示
  const shouldShowAction = (action: JsonTableAction) => {
    if (!action.condition) {
      return true
    }
    
    // 简单条件表达式解析（仅支持简单的相等比较）
    try {
      const parts = action.condition.split(/([=!<>]+)/)
      if (parts.length === 3) {
        const field = parts[0].trim()
        const operator = parts[1].trim()
        const value = parts[2].trim().replace(/['"]/g, '')
        
        const fieldValue = row[field]
        
        switch (operator) {
          case "=":
          case "==":
            return fieldValue == value
          case "!=":
            return fieldValue != value
          case ">":
            return fieldValue > value
          case "<":
            return fieldValue < value
          case ">=":
            return fieldValue >= value
          case "<=":
            return fieldValue <= value
          default:
            return true
        }
      }
      return true
    } catch (error) {
      console.error("Condition evaluation error:", error)
      return true
    }
  }
  
  // 处理操作点击
  const handleActionClick = (action: JsonTableAction) => {
    if (action.confirm) {
      setConfirmDialog({
        isOpen: true,
        action,
      })
    } else {
      onAction(action.value || action.type, action.type)
    }
  }
  
  // 处理确认对话框确认
  const handleConfirm = () => {
    if (confirmDialog.action) {
      onAction(
        confirmDialog.action.value || confirmDialog.action.type,
        confirmDialog.action.type
      )
    }
    setConfirmDialog({ isOpen: false, action: null })
  }
  
  // 过滤显示的操作
  const visibleActions = actions.filter(shouldShowAction)
  
  // 如果没有可见操作，不渲染任何内容
  if (visibleActions.length === 0) {
    return null
  }
  
  // 将操作分为主要操作和更多操作
  const primaryActions = visibleActions.slice(0, 2)
  const moreActions = visibleActions.slice(2)
  
  return (
    <>
      <div className="flex items-center justify-end space-x-1">
        {/* 主要操作按钮 */}
        {primaryActions.map((action) => (
          <Button
            key={action.type + (action.value || "")}
            variant={getActionVariant(action)}
            size="sm"
            className="h-8 px-2"
            onClick={() => handleActionClick(action)}
          >
            {getActionIcon(action)}
            <span className="sr-only">{action.label}</span>
          </Button>
        ))}
        
        {/* 更多操作下拉菜单 */}
        {moreActions.length > 0 && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
                <span className="sr-only">更多操作</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-40">
              {moreActions.map((action) => (
                <DropdownMenuItem
                  key={action.type + (action.value || "")}
                  onClick={() => handleActionClick(action)}
                  className={cn(
                    "cursor-pointer",
                    action.type === "delete" && "text-destructive"
                  )}
                >
                  {getActionIcon(action)}
                  {action.label}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
      
      {/* 确认对话框 */}
      <AlertDialog
        open={confirmDialog.isOpen}
        onOpenChange={(isOpen) => {
          if (!isOpen) {
            setConfirmDialog({ isOpen: false, action: null })
          }
        }}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {confirmDialog.action?.confirm?.title || "确认操作"}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {confirmDialog.action?.confirm?.message || "确定要执行此操作吗？"}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>
              {confirmDialog.action?.confirm?.cancelText || "取消"}
            </AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirm}>
              {confirmDialog.action?.confirm?.confirmText || "确认"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
} 
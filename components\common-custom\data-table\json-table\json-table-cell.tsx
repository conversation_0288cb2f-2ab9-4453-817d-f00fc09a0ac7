"use client"

import * as React from "react"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { JsonTableColumn } from "../types"
import { cn } from "@/lib/utils"

interface JsonTableCellProps {
  column: JsonTableColumn
  value: any
  row: any
  renderers?: Record<string, (value: any, row: any) => React.ReactNode>
  formatters?: Record<string, (value: any, row: any) => string>
}

export function JsonTableCell({
  column,
  value,
  row,
  renderers,
  formatters,
}: JsonTableCellProps) {
  // 如果有自定义渲染器，使用自定义渲染器
  if (column.renderer && renderers && renderers[column.renderer]) {
    return renderers[column.renderer](value, row)
  }

  // 根据列类型渲染不同的单元格内容
  switch (column.type) {
    case "badge":
      return renderBadge(value, column)
    case "image":
      return renderImage(value, column)
    case "boolean":
      return renderBoolean(value)
    case "date":
      return renderDate(value, column, formatters)
    case "number":
      return renderNumber(value, column, formatters)
    default:
      return renderText(value, column, formatters)
  }
}

// 渲染文本
function renderText(value: any, column: JsonTableColumn, formatters?: Record<string, (value: any, row: any) => string>) {
  if (value === null || value === undefined) {
    return "-"
  }
  
  // 如果有自定义格式化函数，使用自定义格式化函数
  if (column.format?.formatter && formatters && formatters[column.format.formatter]) {
    return formatters[column.format.formatter](value, {})
  }
  
  return String(value)
}

// 渲染数字
function renderNumber(value: any, column: JsonTableColumn, formatters?: Record<string, (value: any, row: any) => string>) {
  if (value === null || value === undefined) {
    return "-"
  }
  
  // 如果有自定义格式化函数，使用自定义格式化函数
  if (column.format?.formatter && formatters && formatters[column.format.formatter]) {
    return formatters[column.format.formatter](value, {})
  }
  
  // 如果有数字格式，使用数字格式
  if (column.format?.numberFormat) {
    try {
      return new Intl.NumberFormat(undefined, {
        style: column.format.numberFormat.includes("currency") ? "currency" : "decimal",
        currency: column.format.numberFormat.includes("currency") ? column.format.numberFormat.split(":")[1] : undefined,
        minimumFractionDigits: 0,
        maximumFractionDigits: 2,
      }).format(Number(value))
    } catch (error) {
      console.error("Number format error:", error)
      return String(value)
    }
  }
  
  return String(value)
}

// 渲染日期
function renderDate(value: any, column: JsonTableColumn, formatters?: Record<string, (value: any, row: any) => string>) {
  if (value === null || value === undefined) {
    return "-"
  }
  
  // 如果有自定义格式化函数，使用自定义格式化函数
  if (column.format?.formatter && formatters && formatters[column.format.formatter]) {
    return formatters[column.format.formatter](value, {})
  }
  
  // 如果有日期格式，使用日期格式
  if (column.format?.dateFormat) {
    try {
      const date = new Date(value)
      return new Intl.DateTimeFormat(undefined, {
        year: column.format.dateFormat.includes("y") ? "numeric" : undefined,
        month: column.format.dateFormat.includes("M") ? "2-digit" : undefined,
        day: column.format.dateFormat.includes("d") ? "2-digit" : undefined,
        hour: column.format.dateFormat.includes("H") ? "2-digit" : undefined,
        minute: column.format.dateFormat.includes("m") ? "2-digit" : undefined,
        second: column.format.dateFormat.includes("s") ? "2-digit" : undefined,
      }).format(date)
    } catch (error) {
      console.error("Date format error:", error)
      return String(value)
    }
  }
  
  // 默认日期格式
  try {
    return new Date(value).toLocaleString()
  } catch (error) {
    return String(value)
  }
}

// 渲染布尔值
function renderBoolean(value: any) {
  if (value === null || value === undefined) {
    return "-"
  }
  
  return value ? "是" : "否"
}

// 渲染徽章
function renderBadge(value: any, column: JsonTableColumn) {
  if (value === null || value === undefined) {
    return "-"
  }
  
  const stringValue = String(value)
  
  if (column.badge?.mapping && column.badge.mapping[stringValue]) {
    const badgeConfig = column.badge.mapping[stringValue]
    return (
      <Badge
        variant={badgeConfig.variant || "default"}
        className={cn(badgeConfig.color ? `bg-${badgeConfig.color}-500` : "")}
      >
        {badgeConfig.label || stringValue}
      </Badge>
    )
  }
  
  return stringValue
}

// 渲染图片
function renderImage(value: any, column: JsonTableColumn) {
  if (!value) {
    return "-"
  }
  
  const imgConfig = column.image || {}
  const width = imgConfig.width || 40
  const height = imgConfig.height || 40
  
  let roundedClass = ""
  if (imgConfig.rounded === true || imgConfig.rounded === "md") {
    roundedClass = "rounded-md"
  } else if (imgConfig.rounded === "sm") {
    roundedClass = "rounded-sm"
  } else if (imgConfig.rounded === "lg") {
    roundedClass = "rounded-lg"
  } else if (imgConfig.rounded === "full") {
    roundedClass = "rounded-full"
  }
  
  return (
    <Avatar
      className={cn(
        `h-${height / 4} w-${width / 4}`,
        roundedClass
      )}
    >
      <AvatarImage src={value} alt="图片" />
      <AvatarFallback>{imgConfig.placeholder || "图片"}</AvatarFallback>
    </Avatar>
  )
} 
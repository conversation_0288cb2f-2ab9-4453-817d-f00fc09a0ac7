// 基础示例
export { basicExample, rangeExample, multipleExample } from "./basic"

// 事件示例
export { eventsExample, legendExample } from "./events"

// 自定义示例
export { customStyleExample, customRenderExample } from "./customization"

// ============================================================================
// 统一导出所有示例
// ============================================================================

import { basicExample, rangeExample, multipleExample } from "./basic"
import { eventsExample, legendExample } from "./events"
import { customStyleExample, customRenderExample } from "./customization"

export const allExamples = [
  basicExample,
  rangeExample,
  multipleExample,
  eventsExample,
  legendExample,
  customStyleExample,
  customRenderExample,
]

// ============================================================================
// 按类别导出示例
// ============================================================================

export const basicExamples = [basicExample, rangeExample, multipleExample]
export const eventExamples = [eventsExample, legendExample]
export const customizationExamples = [customStyleExample, customRenderExample]

// ============================================================================
// 示例元数据
// ============================================================================

export const exampleMetadata = {
  total: allExamples.length,
  categories: {
    basic: basicExamples.length,
    events: eventExamples.length,
    customization: customizationExamples.length,
  },
  tags: ["calendar", "date", "events", "range", "selection", "customization"],
  lastUpdated: new Date().toISOString().split('T')[0],
}

"use client"

import { useState, useEffect } from "react"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { X, ChevronDown, ChevronUp, Wand2, Eye, EyeOff, Loader2, Globe } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { EmojiPicker } from "@/components/pages/passwordmanager/emoji-picker"
import { createPasswordRequest, passwordConverter, getPasswordDetailRequest, updatePasswordRequest } from "@/services/api/passwordVaultRequestApi"
import { UIPasswordItem } from "@/types/passwordVault"

type CreatePasswordDialogProps = {
  open: boolean
  onOpenChange: (open: boolean) => void
  onCreateSuccess: () => void
  passwordId?: string // 编辑模式的密码ID
}

export function CreatePasswordDialog({ open, onOpenChange, onCreateSuccess, passwordId }: CreatePasswordDialogProps) {
  const [name, setName] = useState("")
  const [account, setAccount] = useState("")
  const [password, setPassword] = useState("")
  const [platform, setPlatform] = useState("")
  const [notes, setNotes] = useState("")
  const [tagInput, setTagInput] = useState("")
  const [tags, setTags] = useState<string[]>([])
  const [passwordStrength, setPasswordStrength] = useState<"weak" | "medium" | "strong">("medium")
  const [password_strength, setPassword_strength] = useState(2) // 1-弱 2-中 3-强
  const [icon, setIcon] = useState("🔑")
  const [isAdvancedOpen, setIsAdvancedOpen] = useState(false)
  const [isGeneratorOpen, setIsGeneratorOpen] = useState(false)
  const [passwordLength, setPasswordLength] = useState(16)
  const [includeUppercase, setIncludeUppercase] = useState(true)
  const [includeLowercase, setIncludeLowercase] = useState(true)
  const [includeNumbers, setIncludeNumbers] = useState(true)
  const [includeSymbols, setIncludeSymbols] = useState(true)
  const [showPassword, setShowPassword] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isEditMode, setIsEditMode] = useState(false)
  const [originalData, setOriginalData] = useState<UIPasswordItem | null>(null)

  // 加载现有密码数据进行编辑
  useEffect(() => {
    const fetchPasswordData = async () => {
      if (!passwordId || !open) return;
      
      try {
        setIsEditMode(true);
        setIsSubmitting(true);
        
        const response = await getPasswordDetailRequest(parseInt(passwordId, 10));
        
        if (response) {
          const passwordData = passwordConverter.toUIFormat(response);
          setOriginalData(passwordData);
          
          // 填充表单
          setName(passwordData.name);
          setAccount(passwordData.account);
          setPassword(passwordData.password);
          setPlatform(passwordData.platform || "");
          setNotes(passwordData.notes || "");
          setTags(passwordData.tags || []);
          setIcon(passwordData.icon || "🔑");
          setPasswordStrength(passwordData.strength);
          
          // 高级选项默认展开
          setIsAdvancedOpen(true);
        } else {
          setError("未找到密码数据");
        }
      } catch (err) {
        console.error("获取密码数据失败:", err);
        setError("获取密码数据失败，请稍后重试");
      } finally {
        setIsSubmitting(false);
      }
    };
    
    fetchPasswordData();
    
    // 重置表单
    return () => {
      if (!passwordId) {
        resetForm();
      }
    };
  }, [passwordId, open]);

  const resetForm = () => {
    setName("");
    setAccount("");
    setPassword("");
    setPlatform("");
    setNotes("");
    setTags([]);
    setIcon("🔑");
    setPasswordStrength("medium");
    setIsAdvancedOpen(false);
    setError(null);
    setIsEditMode(false);
    setOriginalData(null);
  };

  const handleAddTag = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && tagInput.trim() !== "") {
      e.preventDefault()
      if (!tags.includes(tagInput.trim())) {
        setTags([...tags, tagInput.trim()])
      }
      setTagInput("")
    }
  }

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter((tag) => tag !== tagToRemove))
  }

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newPassword = e.target.value
    setPassword(newPassword)
    calculatePasswordStrength(newPassword)
  }

  const calculatePasswordStrength = (password: string) => {
    // Simple password strength calculation
    let strength: "weak" | "medium" | "strong" = "weak"
    let strengthValue = 1 // 默认弱密码

    if (password.length >= 12) {
      const hasUppercase = /[A-Z]/.test(password)
      const hasLowercase = /[a-z]/.test(password)
      const hasNumbers = /[0-9]/.test(password)
      const hasSymbols = /[^A-Za-z0-9]/.test(password)

      const varietyCount = [hasUppercase, hasLowercase, hasNumbers, hasSymbols].filter(Boolean).length

      if (password.length >= 16 && varietyCount >= 3) {
        strength = "strong"
        strengthValue = 3
      } else if (password.length >= 12 && varietyCount >= 2) {
        strength = "medium"
        strengthValue = 2
      }
    }

    setPasswordStrength(strength)
    setPassword_strength(strengthValue)
  }

  const generatePassword = () => {
    const uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
    const lowercase = "abcdefghijklmnopqrstuvwxyz"
    const numbers = "0123456789"
    const symbols = "!@#$%^&*()_+~`|}{[]:;?><,./-="

    let chars = ""
    if (includeUppercase) chars += uppercase
    if (includeLowercase) chars += lowercase
    if (includeNumbers) chars += numbers
    if (includeSymbols) chars += symbols

    if (chars === "") {
      // Default to lowercase if nothing selected
      chars = lowercase
    }

    let newPassword = ""
    for (let i = 0; i < passwordLength; i++) {
      const randomIndex = Math.floor(Math.random() * chars.length)
      newPassword += chars[randomIndex]
    }

    setPassword(newPassword)
    calculatePasswordStrength(newPassword)
    setIsGeneratorOpen(false)
  }

  const validateForm = (): boolean => {
    if (!name.trim()) {
      setError("请输入密码名称");
      return false;
    }
    
    if (!account.trim()) {
      setError("请输入账号");
      return false;
    }
    
    if (!password.trim()) {
      setError("请输入密码");
      return false;
    }
    
    setError(null);
    return true;
  }

  const handleCreatePassword = async () => {
    // 验证表单数据
    if (!validateForm()) {
      return;
    }
    
    try {
      setIsSubmitting(true);
      
      // 构建密码数据
      const passwordData = {
        id: passwordId || "0", // 新建时ID为0，编辑时使用原ID
        name: name,
        account: account,
        password: password,
        platform: platform,
        tags: tags,
        isFavorite: originalData?.isFavorite || false,
        strength: passwordStrength,
        password_strength: password_strength,
        lastUpdated: new Date().toISOString(),
        notes: notes,
        icon: icon,
        createdAt: originalData?.createdAt || new Date().toISOString(),
        breachStatus: "safe" as const,
        searchKeyword: [name, account, platform, ...tags].filter(Boolean).join(" ")
      };
      
      // 将UI数据转换为API格式
      const apiPasswordData = passwordConverter.toAPIFormat(passwordData);
      
      if (isEditMode && passwordId) {
        // 调用更新密码API
        await updatePasswordRequest(parseInt(passwordId, 10), apiPasswordData);
      } else {
        // 调用创建密码API
        await createPasswordRequest(apiPasswordData);
      }
      
      // 重置表单
      resetForm();
      
      // 通知父组件创建成功
      onCreateSuccess();
      
      // 关闭弹窗
      onOpenChange(false);
      
    } catch (err) {
      console.error("保存密码失败:", err);
      setError("保存密码失败，请稍后重试");
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{isEditMode ? "编辑密码" : "创建新密码"}</DialogTitle>
          <DialogDescription>{isEditMode ? "修改密码信息" : "添加一个新的密码到您的密码库"}</DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {error && (
            <div className="bg-destructive/10 p-3 rounded-md text-destructive text-sm mb-4">
              {error}
            </div>
          )}
          
          <div className="grid grid-cols-6 gap-4">
            <div className="col-span-1">
              <Label className="mb-1 block invisible">图标</Label>
              <EmojiPicker value={icon} onChange={setIcon} />
            </div>
            <div className="col-span-5">
              <Label htmlFor="name" className="mb-1 block">
                名称
              </Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="例如: Google, 银行账户"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="account" className="mb-1 block">
              账号
            </Label>
            <Input
              id="account"
              value={account}
              onChange={(e) => setAccount(e.target.value)}
              placeholder="用户名或邮箱"
            />
          </div>

          <div>
            <div className="flex justify-between mb-1">
              <Label htmlFor="password">密码</Label>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 px-2"
                onClick={() => setIsGeneratorOpen(!isGeneratorOpen)}
              >
                <Wand2 className="h-3 w-3 mr-1" />
                生成密码
              </Button>
            </div>
            <div className="relative">
              <Input
                id="password"
                type={showPassword ? "text" : "password"}
                value={password}
                onChange={handlePasswordChange}
                className="pr-16"
              />
              <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center gap-1">
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6 p-0"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
                </Button>
                <Badge
                  variant={
                    passwordStrength === "strong"
                      ? "default"
                      : passwordStrength === "medium"
                        ? "secondary"
                        : "destructive"
                  }
                >
                  {passwordStrength === "strong" ? "强" : passwordStrength === "medium" ? "中" : "弱"}
                </Badge>
              </div>
            </div>
          </div>

          {isGeneratorOpen && (
            <div className="bg-muted p-3 rounded-md space-y-3">
              <div className="flex justify-between items-center">
                <Label>密码长度: {passwordLength}</Label>
                <input
                  type="range"
                  min="8"
                  max="32"
                  value={passwordLength}
                  onChange={(e) => setPasswordLength(Number.parseInt(e.target.value))}
                  className="w-32"
                />
              </div>

              <div className="grid grid-cols-2 gap-2">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="uppercase"
                    checked={includeUppercase}
                    onChange={(e) => setIncludeUppercase(e.target.checked)}
                  />
                  <Label htmlFor="uppercase" className="text-sm">
                    大写字母 (A-Z)
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="lowercase"
                    checked={includeLowercase}
                    onChange={(e) => setIncludeLowercase(e.target.checked)}
                  />
                  <Label htmlFor="lowercase" className="text-sm">
                    小写字母 (a-z)
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="numbers"
                    checked={includeNumbers}
                    onChange={(e) => setIncludeNumbers(e.target.checked)}
                  />
                  <Label htmlFor="numbers" className="text-sm">
                    数字 (0-9)
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="symbols"
                    checked={includeSymbols}
                    onChange={(e) => setIncludeSymbols(e.target.checked)}
                  />
                  <Label htmlFor="symbols" className="text-sm">
                    特殊符号 (!@#$)
                  </Label>
                </div>
              </div>

              <Button onClick={generatePassword} size="sm" className="w-full">
                生成密码
              </Button>
            </div>
          )}

          <Collapsible open={isAdvancedOpen} onOpenChange={setIsAdvancedOpen} className="border rounded-md p-2">
            <CollapsibleTrigger asChild>
              <Button variant="ghost" className="flex w-full justify-between p-0" type="button">
                <span>高级选项</span>
                {isAdvancedOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="space-y-4 pt-4">
              <div>
                <Label htmlFor="platform" className="mb-1 block flex items-center gap-1">
                  <Globe className="h-4 w-4" />
                  <span>平台</span>
                </Label>
                <Input
                  id="platform"
                  value={platform}
                  onChange={(e) => setPlatform(e.target.value)}
                  placeholder="例如: 网站、应用或服务名称"
                />
              </div>

              <div>
                <Label htmlFor="tags" className="mb-1 block">
                  标签
                </Label>
                <div className="flex flex-wrap gap-1 mb-2">
                  {tags.map((tag, index) => (
                    <Badge key={index} variant="secondary" className="flex items-center gap-1">
                      {tag}
                      <X className="h-3 w-3 cursor-pointer" onClick={() => handleRemoveTag(tag)} />
                    </Badge>
                  ))}
                </div>
                <Input
                  id="tags"
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  onKeyDown={handleAddTag}
                  placeholder="输入标签后按回车添加"
                />
              </div>

              <div>
                <Label htmlFor="notes" className="mb-1 block">
                  备注
                </Label>
                <Textarea
                  id="notes"
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  placeholder="添加备注..."
                />
              </div>
            </CollapsibleContent>
          </Collapsible>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isSubmitting}>
            取消
          </Button>
          <Button onClick={handleCreatePassword} disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                保存中...
              </>
            ) : (
              isEditMode ? "保存修改" : "保存密码"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
} 

"use client";

import React from "react";
import { ComponentPreviewContainer } from "@/components/component-detail/preview-container";
import { BackButton } from "@/components/common-custom/back-button";
import { allExamples } from "./examples";

// ============================================================================
// API文档组件
// ============================================================================

function BackButtonApiDocs() {
  return (
    <div className="space-y-6">
      {/* 主组件API */}
      <div>
        <h3 className="font-medium text-lg mb-2">BackButton</h3>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">href</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">跳转链接地址</td>
              </tr>
              <tr>
                <td className="p-2 border">showText</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">false</td>
                <td className="p-2 border">是否显示返回文字</td>
              </tr>
              <tr>
                <td className="p-2 border">size</td>
                <td className="p-2 border">"sm" | "md" | "lg"</td>
                <td className="p-2 border">"md"</td>
                <td className="p-2 border">按钮尺寸</td>
              </tr>
              <tr>
                <td className="p-2 border">rounded</td>
                <td className="p-2 border">"default" | "md" | "full"</td>
                <td className="p-2 border">"default"</td>
                <td className="p-2 border">圆角样式</td>
              </tr>
              <tr>
                <td className="p-2 border">variant</td>
                <td className="p-2 border">"default" | "ghost" | "primary" | "destructive" | "outline"</td>
                <td className="p-2 border">"default"</td>
                <td className="p-2 border">按钮变体样式</td>
              </tr>
              <tr>
                <td className="p-2 border">children</td>
                <td className="p-2 border">React.ReactNode</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">自定义按钮文字内容</td>
              </tr>
              <tr>
                <td className="p-2 border">className</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">自定义CSS类名</td>
              </tr>
              <tr>
                <td className="p-2 border">onClick</td>
                <td className="p-2 border">React.MouseEventHandler&lt;HTMLAnchorElement&gt;</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">点击事件处理函数</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* 使用指南 */}
      <div>
        <h3 className="font-medium text-lg mb-2">使用指南</h3>
        <div className="space-y-4 text-sm">
          <div>
            <h4 className="font-medium mb-2">基本用法</h4>
            <p className="text-muted-foreground mb-2">
              最简单的使用方式，只需要提供跳转链接：
            </p>
            <pre className="bg-muted p-3 rounded-md overflow-x-auto">
              <code>{`<BackButton href="/" />`}</code>
            </pre>
          </div>

          <div>
            <h4 className="font-medium mb-2">显示文字</h4>
            <p className="text-muted-foreground mb-2">
              通过showText属性控制是否显示文字：
            </p>
            <pre className="bg-muted p-3 rounded-md overflow-x-auto">
              <code>{`<BackButton href="/" showText>返回首页</BackButton>`}</code>
            </pre>
          </div>

          <div>
            <h4 className="font-medium mb-2">自定义样式</h4>
            <p className="text-muted-foreground mb-2">
              通过size、variant、rounded属性控制外观：
            </p>
            <pre className="bg-muted p-3 rounded-md overflow-x-auto">
              <code>{`<BackButton
  href="/"
  size="lg"
  variant="primary"
  rounded="full"
  showText
>
  返回主页
</BackButton>`}</code>
            </pre>
          </div>

          <div>
            <h4 className="font-medium mb-2">事件处理</h4>
            <p className="text-muted-foreground mb-2">
              通过onClick属性处理点击事件：
            </p>
            <pre className="bg-muted p-3 rounded-md overflow-x-auto">
              <code>{`<BackButton
  href="/"
  showText
  onClick={(e) => {
    // 可以阻止默认跳转
    e.preventDefault()
    // 自定义处理逻辑
    console.log('返回按钮被点击')
  }}
>
  返回
</BackButton>`}</code>
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
}

// ============================================================================
// 主预览组件
// ============================================================================

export default function BackButtonPreview() {
  return (
    <ComponentPreviewContainer
      title="返回按钮 BackButton"
      description="提供导航返回功能的按钮组件，支持多种显示模式、尺寸、圆角和变体样式"
      whenToUse="当需要提供返回上一页或指定页面的导航功能时使用；适用于详情页、表单页、设置页等需要返回操作的场景；可以根据不同的设计需求选择合适的尺寸和样式；支持自定义点击事件处理，实现复杂的返回逻辑"
      examples={allExamples}
      apiDocs={<BackButtonApiDocs />}
    />
  );
}
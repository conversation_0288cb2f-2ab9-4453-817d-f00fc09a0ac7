"use client"

import React from "react"
import { cn } from "@/lib/utils"
import { CalendarEventProps } from "./types"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

/**
 * 日历事件组件
 * 
 * 用于在日历中显示单个事件
 */
export function CalendarEvent({
  event,
  date,
  onClick,
  className,
  showDetails = true,
  enableTooltip = true,
  renderEvent,
}: CalendarEventProps) {
  
  // 处理事件点击
  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (onClick) {
      onClick(event, date)
    }
  }
  
  // 获取事件颜色样式
  const getEventColorClass = () => {
    if (event.color) {
      return {
        backgroundColor: event.color,
        borderColor: event.color,
      }
    }
    
    switch (event.type) {
      case "primary":
        return "bg-primary border-primary text-primary-foreground"
      case "secondary":
        return "bg-secondary border-secondary text-secondary-foreground"
      case "success":
        return "bg-green-500 border-green-500 text-white"
      case "warning":
        return "bg-yellow-500 border-yellow-500 text-white"
      case "danger":
        return "bg-red-500 border-red-500 text-white"
      default:
        return "bg-blue-500 border-blue-500 text-white"
    }
  }
  
  // 自定义渲染
  if (renderEvent) {
    return (
      <div onClick={handleClick} className={className}>
        {renderEvent(event)}
      </div>
    )
  }
  
  // 事件内容
  const eventContent = (
    <div
      className={cn(
        "calendar-event",
        "px-1 py-0.5 rounded text-xs",
        "border cursor-pointer",
        "hover:opacity-80 transition-opacity",
        "truncate",
        typeof getEventColorClass() === "string" ? getEventColorClass() : "",
        event.className,
        className
      )}
      style={typeof getEventColorClass() === "object" ? getEventColorClass() : undefined}
      onClick={handleClick}
    >
      <div className="font-medium truncate">
        {event.title}
      </div>
      {showDetails && event.description && (
        <div className="text-xs opacity-90 truncate">
          {event.description}
        </div>
      )}
    </div>
  )
  
  // 如果启用了悬浮提示
  if (enableTooltip) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            {eventContent}
          </TooltipTrigger>
          <TooltipContent>
            <div className="space-y-1">
              <div className="font-medium">{event.title}</div>
              {event.description && (
                <div className="text-sm text-muted-foreground">
                  {event.description}
                </div>
              )}
              <div className="text-xs text-muted-foreground">
                {date.toLocaleDateString()}
              </div>
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    )
  }
  
  return eventContent
}

CalendarEvent.displayName = "CalendarEvent"

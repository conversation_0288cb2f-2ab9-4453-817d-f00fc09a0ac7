/**
 * 用户角色关联接口
 * 提供用户角色关联相关的API调用功能
 */
import apiClient from './requestApi';
import { ApiResponse } from '@/types/api';
import { toast } from "sonner";
import { processArrayResponse, processResponse } from '@/lib/request';

// API基础路径
const BASE_URL = '/api/auth/user-role';

/**
 * 为用户分配角色
 * @param params 分配参数
 * @returns 分配结果
 */
export const assignUserRoleRequest = async (params: {
  userId: number;
  teamCode: string;
  roleCodes: string[];
}): Promise<boolean> => {
  try {
    const response = await apiClient.post<ApiResponse<boolean>>(`${BASE_URL}/assign`, params);
    
    return processResponse(response, {
      successMessage: "角色分配成功",
      errorMessage: "角色分配失败",
      showSuccessToast: true,
      showErrorToast: true
    }) || false;
  } catch (error) {
    console.error("角色分配失败", error);
    toast.error("角色分配失败");
    return false;
  }
};

/**
 * 获取用户在团队中的角色列表
 * @param userId 用户ID
 * @param teamCode 团队编码
 * @returns 角色编码列表
 */
export const getUserRolesRequest = async (userId: number, teamCode: string): Promise<string[]> => {
  try {
    const response = await apiClient.get<ApiResponse<string[]>>(
      `${BASE_URL}/list?userId=${userId}&teamCode=${teamCode}`
    );
    
    return processArrayResponse(response, {
      errorMessage: "获取用户角色失败",
      showErrorToast: true
    });
  } catch (error) {
    console.error("获取用户角色失败", error);
    toast.error("获取用户角色失败");
    return [];
  }
};

/**
 * 获取用户角色详情
 * @param userId 用户ID
 * @param teamCode 团队编码
 * @returns 用户角色详情
 */
export const getUserRoleDetailRequest = async (userId: number, teamCode: string): Promise<{
  userId: number;
  userName: string;
  teamCode: string;
  teamName: string;
  roles: Array<{
    roleCode: string;
    roleName: string;
    createTime: string;
  }>;
} | null> => {
  try {
    const response = await apiClient.get<ApiResponse<{
      userId: number;
      userName: string;
      teamCode: string;
      teamName: string;
      roles: Array<{
        roleCode: string;
        roleName: string;
        createTime: string;
      }>;
    }>>(`${BASE_URL}/detail?userId=${userId}&teamCode=${teamCode}`);
    
    return processResponse(response, {
      errorMessage: "获取用户角色详情失败",
      showErrorToast: true
    });
  } catch (error) {
    console.error("获取用户角色详情失败", error);
    toast.error("获取用户角色详情失败");
    return null;
  }
};

/**
 * 移除用户的所有角色
 * @param userId 用户ID
 * @param teamCode 团队编码
 * @returns 移除结果
 */
export const removeUserRolesRequest = async (userId: number, teamCode: string): Promise<boolean> => {
  try {
    const response = await apiClient.delete<ApiResponse<boolean>>(
      `${BASE_URL}/remove?userId=${userId}&teamCode=${teamCode}`
    );
    
    return processResponse(response, {
      successMessage: "角色移除成功",
      errorMessage: "角色移除失败",
      showSuccessToast: true,
      showErrorToast: true
    }) || false;
  } catch (error) {
    console.error("角色移除失败", error);
    toast.error("角色移除失败");
    return false;
  }
};

/**
 * 检查用户是否拥有指定角色
 * @param userId 用户ID
 * @param teamCode 团队编码
 * @param roleCode 角色编码
 * @returns 是否拥有角色
 */
export const hasRoleRequest = async (userId: number, teamCode: string, roleCode: string): Promise<boolean> => {
  try {
    const response = await apiClient.get<ApiResponse<boolean>>(
      `${BASE_URL}/hasRole?userId=${userId}&teamCode=${teamCode}&roleCode=${roleCode}`
    );
    
    return processResponse(response, {
      errorMessage: "检查用户角色失败",
      showErrorToast: true
    }) || false;
  } catch (error) {
    console.error("检查用户角色失败", error);
    toast.error("检查用户角色失败");
    return false;
  }
}; 
"use client"

import { usePathname, useSearchParams } from "next/navigation"
import { PageLoader as CommonPageLoader } from "@/components/common-custom/page-loader"

interface ProjectPageLoaderProps {
  /**
   * 加载文本
   */
  loadingText?: string
  
  /**
   * @deprecated 标记是否为新组件，仅用于界面展示
   */
  isNew?: boolean
}

/**
 * 项目特定的页面加载组件
 * 集成了next/navigation的路由状态
 */
export function PageLoader({ loadingText, isNew = true }: ProjectPageLoaderProps) {
  // 使用next/navigation的路由钩子
  const pathname = usePathname()
  const searchParams = useSearchParams()
  
  return (
    <CommonPageLoader
      loadingText={loadingText}
      // 这里可以根据路由状态控制loading属性
      // 在实际项目中，可能需要更复杂的逻辑来确定是否显示加载状态
      isNew={isNew}
    />
  )
} 
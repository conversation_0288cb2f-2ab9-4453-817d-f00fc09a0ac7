"use client"

import { useState, useEffect, useRef, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { 
  RotateCcw, 
  Loader2, 
  ChevronRight,
  Check,
  Shield,
  FolderTree,
  Settings,
  Folder,
  User,
  BarChart,
  GitBranch,
  BookOpen,
  Lock,
  Users,
  Home,
  FileText,
  Layout,
  Key,
  Eye
} from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { PermissionNode, PermissionGroup } from "@/types/permission";
import { cn } from "@/lib/utils";
import { ScrollArea } from "@/components/ui/scroll-area";
import { fetchPermissionTreeByGroup } from "@/services/api/permissionRequestApi";
import { getRoleByIdRequest } from "@/services/api/roleRequestApi";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";

// 图标映射
type IconComponent = typeof Settings;
const iconMap: Record<string, IconComponent> = {
  settings: Settings,
  folder: Folder,
  user: User,
  "bar-chart": BarChart,
  "git-branch": GitBranch,
  lock: Lock,
  users: Users,
  home: Home,
  "file-text": FileText,
  layout: Layout,
  key: Key,
  shield: Shield
};

interface RolePermissionsProps {
  roleId: string;
  selectedPermissions: string[];
  onPermissionsChange: (permissions: string[]) => void;
  viewOnly?: boolean; // 添加查看模式属性
}

export default function RolePermissions({ 
  roleId,
  selectedPermissions, 
  onPermissionsChange,
  viewOnly = false // 默认为可编辑模式
}: RolePermissionsProps) {
  // 本地状态
  const [isLoading, setIsLoading] = useState(false);
  const [openGroups, setOpenGroups] = useState<Record<string, boolean>>({});
  const [permissionGroups, setPermissionGroups] = useState<PermissionGroup[]>([]);
  const [activeGroup, setActiveGroup] = useState<string>('');
  const [groupPermissionTrees, setGroupPermissionTrees] = useState<Record<string, PermissionNode[]>>({});
  const [initialLoadComplete, setInitialLoadComplete] = useState(false);
  const [roleCode, setRoleCode] = useState<string>('');
  const dataFetchedRef = useRef(false); // 添加引用来跟踪是否已经获取过数据
  const [isPermissionDialogOpen, setIsPermissionDialogOpen] = useState(false);
  
  // 构建权限ID到权限节点的映射关系，用于预览时显示中文名称和类型
  const permissionNodeMap = useMemo(() => {
    const result: Record<string, PermissionNode> = {};
    
    // 递归收集所有权限节点
    const collectNodes = (nodes: PermissionNode[]) => {
      nodes.forEach(node => {
        const nodeId = node.permissionId || node.id;
        result[nodeId] = node;
        
        if (node.children && node.children.length > 0) {
          collectNodes(node.children);
        }
      });
    };
    
    // 遍历所有权限树
    Object.values(groupPermissionTrees).forEach(trees => {
      if (trees && trees.length > 0) {
        collectNodes(trees);
      }
    });
    
    return result;
  }, [groupPermissionTrees]);
  
  // 预处理选中权限数据，按权限组分类
  const permissionsByGroup = useMemo(() => {
    const result: Record<string, { name: string; permissions: string[] }> = {};
    
    // 初始化每个权限组
    permissionGroups.forEach(group => {
      result[group.code] = {
        name: group.name,
        permissions: []
      };
    });
    
    // 遍历所有权限树，收集每个组中被选中的权限
    Object.entries(groupPermissionTrees).forEach(([groupCode, nodes]) => {
      if (!result[groupCode]) {
        return;
      }
      
      // 递归收集选中的权限
      const collectSelectedPermissions = (node: PermissionNode): string[] => {
        const permissions: string[] = [];
        const permissionId = node.permissionId || node.id;
        
        if (selectedPermissions.includes(permissionId)) {
          permissions.push(permissionId);
        }
        
        if (node.children && node.children.length > 0) {
          node.children.forEach(child => {
            permissions.push(...collectSelectedPermissions(child));
          });
        }
        
        return permissions;
      };
      
      // 收集当前组的所有选中权限
      nodes.forEach(node => {
        result[groupCode].permissions.push(...collectSelectedPermissions(node));
      });
    });
    
    return result;
  }, [permissionGroups, groupPermissionTrees, selectedPermissions]);
  
  // 计算选中的权限总数
  const totalSelectedPermissions = useMemo(() => {
    return selectedPermissions.length;
  }, [selectedPermissions]);
  
  // 加载角色数据和权限组
  useEffect(() => {
    // 防止重复获取数据
    if (dataFetchedRef.current) return;
    
    const loadRoleData = async () => {
      setIsLoading(true);
      try {
        dataFetchedRef.current = true; // 标记为已获取，防止多次调用
        
        // 获取角色详情，包含权限组和权限树数据
        const roleData = await getRoleByIdRequest(roleId);
        if (!roleData) {
          console.error("获取角色详情失败");
          return;
        }
        
        setRoleCode(roleData.roleCode);
        
        // 从角色详情中获取权限组数据
        if (roleData.permissionGroups && roleData.permissionGroups.length > 0) {
          setPermissionGroups(roleData.permissionGroups);
          
          // 设置初始激活的权限组
          setActiveGroup(roleData.permissionGroups[0].code);
          
          // 设置权限树数据
          if (roleData.permissionTrees) {
            const trees: Record<string, PermissionNode[]> = {};
            roleData.permissionGroups.forEach(group => {
              const permissionTrees = roleData.permissionTrees || {};
              if (permissionTrees[group.code]) {
                trees[group.code] = permissionTrees[group.code];
              }
            });
            
            setGroupPermissionTrees(trees);
            
            // 初始化折叠状态 - 默认全部展开
            const initialOpenGroups: Record<string, boolean> = {};
            
            // 递归设置所有节点的折叠状态为展开
            const setAllNodesOpen = (nodes: PermissionNode[]) => {
              nodes.forEach(node => {
                initialOpenGroups[node.id] = true;
                if (node.children && node.children.length > 0) {
                  setAllNodesOpen(node.children);
                }
              });
            };
            
            // 对每个权限组的树都设置展开状态
            Object.values(trees).forEach(treeNodes => {
              if (treeNodes && treeNodes.length > 0) {
                setAllNodesOpen(treeNodes);
              }
            });
            
            setOpenGroups(initialOpenGroups);
          }
          
          // 从角色详情中提取已选的权限ID列表
          if (roleData.selectedPermissions && roleData.selectedPermissions.length > 0) {
            onPermissionsChange(roleData.selectedPermissions);
          }
        }
        
        setInitialLoadComplete(true);
        
      } catch (error) {
        console.error("加载角色数据失败", error);
      } finally {
        setIsLoading(false);
      }
    };
    
    if (roleId) {
      loadRoleData();
    }
  }, [roleId, onPermissionsChange]);
  
  // 当活动权限组变化时，不再额外加载权限树，因为已经在角色详情中获取了所有权限树
  useEffect(() => {
    if (!activeGroup || !initialLoadComplete) return;
    
    // 如果没有该组的权限树数据，则设置为空数组
    if (!groupPermissionTrees[activeGroup]) {
      setGroupPermissionTrees(prev => ({
        ...prev,
        [activeGroup]: []
      }));
    }
  }, [activeGroup, groupPermissionTrees, initialLoadComplete]);
  
  // 获取当前显示的权限树
  const currentPermissionTree = activeGroup ? groupPermissionTrees[activeGroup] || [] : [];
  
  // 处理权限切换
  const togglePermission = (permissionId: string) => {
    // 查看模式下不允许修改
    if (viewOnly) return;
    
    const newPermissions = [...selectedPermissions];
    if (newPermissions.includes(permissionId)) {
      onPermissionsChange(newPermissions.filter(id => id !== permissionId));
    } else {
      onPermissionsChange([...newPermissions, permissionId]);
    }
  };
  
  // 切换折叠组
  const toggleGroup = (groupId: string) => {
    setOpenGroups(prev => ({
      ...prev,
      [groupId]: !prev[groupId]
    }));
  };
  
  // 检查权限组是否全部选中
  const isGroupChecked = (node: PermissionNode): boolean => {
    if (!node.children || node.children.length === 0) {
      return selectedPermissions.includes(node.permissionId || node.id);
    }
    
    return node.children.every(child => isGroupChecked(child));
  };
  
  // 检查权限组是否部分选中
  const isGroupIndeterminate = (node: PermissionNode): boolean => {
    if (!node.children || node.children.length === 0) {
      return false;
    }
    
    const allChecked = node.children.every(child => isGroupChecked(child));
    const anyChecked = node.children.some(child => 
      isGroupChecked(child) || isGroupIndeterminate(child)
    );
    
    return !allChecked && anyChecked;
  };
  
  // 切换权限组
  const togglePermissionGroup = (node: PermissionNode) => {
    // 查看模式下不允许修改
    if (viewOnly) return;
    
    const isChecked = isGroupChecked(node);
    const newValue = !isChecked;
    
    // 递归收集所有权限ID
    const collectPermissionIds = (node: PermissionNode): string[] => {
      const ids: string[] = [];
      
      // 确保当前节点的ID也被收集（无论是文件夹、菜单还是按钮）
      if (node.permissionId) {
        ids.push(node.permissionId);
      } else if (node.id) {
        ids.push(node.id);
      }
      
      if (node.children && node.children.length > 0) {
        node.children.forEach(child => {
          ids.push(...collectPermissionIds(child));
        });
      }
      
      return ids;
    };
    
    const permissionIds = collectPermissionIds(node);
    
    if (newValue) {
      // 添加组内所有权限
      const newPermissions = [...selectedPermissions];
      permissionIds.forEach(id => {
        if (!newPermissions.includes(id)) {
          newPermissions.push(id);
        }
      });
      onPermissionsChange(newPermissions);
    } else {
      // 移除组内所有权限
      onPermissionsChange(selectedPermissions.filter(id => !permissionIds.includes(id)));
    }
  };
  
  // 根据权限组代码获取图标
  const getGroupIcon = (code: string) => {
    switch (code) {
      case 'system':
        return <Settings className="h-4 w-4" />;
      case 'project':
        return <Folder className="h-4 w-4" />;
      case 'personal':
        return <User className="h-4 w-4" />;
      case 'report':
        return <BarChart className="h-4 w-4" />;
      case 'workflow':
        return <GitBranch className="h-4 w-4" />;
      case 'admin':
        return <Shield className="h-4 w-4" />;
      case 'guest':
        return <Users className="h-4 w-4" />;
      case 'content':
        return <FileText className="h-4 w-4" />;
      case 'dashboard':
        return <Home className="h-4 w-4" />;
      case 'security':
        return <Lock className="h-4 w-4" />;
      default:
        return <Key className="h-4 w-4" />;
    }
  };
  
  // 根据节点类型获取图标
  const getNodeTypeIcon = (node: PermissionNode) => {
    if (node.icon && typeof node.icon === 'string' && iconMap[node.icon]) {
      const IconComponent = iconMap[node.icon];
      return <IconComponent className="h-4 w-4 text-primary" />;
    }
    
    switch (node.type) {
      case "Directory":
        return <Folder className="h-4 w-4 text-primary" />;
      case "Menu":
        return <FileText className="h-4 w-4 text-primary" />;
      case "Button":
        return <Key className="h-4 w-4 text-primary" />;
      default:
        return <FolderTree className="h-4 w-4 text-primary" />;
    }
  };
  
  // 获取节点类型标签
  const getNodeTypeLabel = (node: PermissionNode) => {
    switch (node.type) {
      case "Directory":
        return <Badge variant="outline" className="ml-1 px-1 py-0 text-[10px]">文件夹</Badge>;
      case "Menu":
        return <Badge variant="outline" className="ml-1 px-1 py-0 text-[10px]">菜单</Badge>;
      case "Button":
        return <Badge variant="outline" className="ml-1 px-1 py-0 text-[10px]">按钮</Badge>;
      default:
        return null;
    }
  };
  
  // 获取权限类型文本
  const getPermissionTypeText = (type?: string) => {
    switch (type) {
      case "Directory":
        return "文件夹";
      case "Menu":
        return "菜单";
      case "Button":
        return "按钮";
      default:
        return "权限";
    }
  };
  
  // 渲染权限节点
  const renderPermissionNode = (node: PermissionNode, level: number = 0) => {
    const hasChildren = node.children && node.children.length > 0;
    const isChecked = isGroupChecked(node);
    const isIndeterminate = isGroupIndeterminate(node);
    const permissionId = node.permissionId || node.id;
    
    // 根据层级获取文字大小类
    const getTextSizeClass = (level: number) => {
      switch (level) {
        case 0: return "text-sm font-medium"; // 根节点
        case 1: return "text-sm";  // 一级子节点
        default: return "text-sm"; // 其他层级
      }
    };
    
    const textSizeClass = getTextSizeClass(level);
    
    if (hasChildren) {
      return (
        <div key={node.id} className={cn("mb-1", level > 0 ? "mt-0.5" : "mt-0")}>
          <Collapsible
            open={openGroups[node.id] || false}
            onOpenChange={() => toggleGroup(node.id)}
            className={cn(
              "overflow-hidden transition-all",
              level > 0 ? "ml-2" : ""
            )}
          >
            <CollapsibleTrigger className="flex items-center justify-between w-full py-2 px-2 hover:bg-muted/30 rounded-md">
              <div className="flex items-center gap-2.5">
                <div 
                  className={cn(
                    "flex items-center justify-center h-5 w-5 rounded-sm border",
                    isChecked 
                      ? "bg-primary border-primary hover:bg-primary hover:border-primary" 
                      : isIndeterminate 
                        ? "border-primary hover:border-primary" 
                        : "border-input hover:border-input",
                    "transition-all",
                    viewOnly ? "opacity-60 cursor-not-allowed" : "cursor-pointer"
                  )}
                  onClick={(e) => {
                    e.stopPropagation();
                    togglePermissionGroup(node);
                  }}
                >
                  {isChecked && <Check className="h-3.5 w-3.5 text-primary-foreground" />}
                  {isIndeterminate && !isChecked && <div className="h-2 w-2 bg-primary rounded-sm"></div>}
                </div>
                <div className="flex items-center">
                  {getNodeTypeIcon(node)}
                  <span className={cn(textSizeClass, "ml-2")}>{node.name}</span>
                  <span className="text-xs text-muted-foreground ml-1.5">
                    ({node.children.length})
                  </span>
                  {getNodeTypeLabel(node)}
                </div>
              </div>
              <ChevronRight
                className={`h-4 w-4 transition-transform duration-200 ${openGroups[node.id] ? "rotate-90" : ""}`}
              />
            </CollapsibleTrigger>
            <CollapsibleContent className="bg-muted/5 pl-2 pr-2">
              <div className="py-1 space-y-0.5">
                {node.children.map(child => renderPermissionNode(child, level + 1))}
              </div>
            </CollapsibleContent>
          </Collapsible>
        </div>
      );
    } else {
      return (
        <div 
          key={node.id} 
          className={cn(
            "flex items-center py-1.5 px-2 rounded-md hover:bg-muted/30 transition-colors",
            level > 0 ? "ml-2" : ""
          )}
        >
          <div className="flex items-center flex-1">
            <div 
              className={cn(
                "flex items-center justify-center h-5 w-5 rounded-sm border",
                selectedPermissions.includes(permissionId) 
                  ? "bg-primary border-primary hover:bg-primary hover:border-primary" 
                  : "border-input hover:border-input",
                "transition-all",
                viewOnly ? "opacity-60 cursor-not-allowed" : "cursor-pointer"
              )}
              onClick={() => togglePermission(permissionId)}
            >
              {selectedPermissions.includes(permissionId) && (
                <Check className="h-3.5 w-3.5 text-primary-foreground" />
              )}
            </div>
            <div className="flex items-center ml-2.5">
              {getNodeTypeIcon(node)}
              <span className={cn(textSizeClass, "ml-2")}>{node.name}</span>
              {getNodeTypeLabel(node)}
            </div>
          </div>
        </div>
      );
    }
  };
  
  // 渲染权限预览对话框
  const renderPermissionPreviewDialog = () => {
    return (
      <Dialog open={isPermissionDialogOpen} onOpenChange={setIsPermissionDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle className="text-lg font-semibold flex items-center gap-2">
              <Shield className="h-5 w-5 text-primary" />
              已选权限预览
            </DialogTitle>
          </DialogHeader>
          
          <div className="flex-1 overflow-hidden">
            <ScrollArea className="h-[60vh]">
              <div className="p-4 space-y-4">
                {Object.entries(permissionsByGroup).map(([groupCode, groupData]) => {
                  if (groupData.permissions.length === 0) return null;
                  
                  return (
                    <div key={groupCode} className="space-y-2">
                      <div className="flex items-center gap-2">
                        {getGroupIcon(groupCode)}
                        <h3 className="font-medium">{groupData.name}</h3>
                        <Badge variant="secondary" className="ml-1">
                          {groupData.permissions.length}
                        </Badge>
                      </div>
                      
                      <div className="bg-muted/10 p-2 rounded-md">
                        <div className="flex flex-wrap gap-1.5">
                          {groupData.permissions.map(permId => {
                            // 获取权限节点数据，用于显示中文名称
                            const node = permissionNodeMap[permId];
                            if (!node) {
                              return (
                                <Badge key={permId} variant="outline" className="py-0.5">
                                  {permId}
                                </Badge>
                              );
                            }
                            
                            return (
                              <Badge key={permId} variant="outline" className="py-0.5 flex items-center gap-1">
                                {getNodeTypeIcon(node)}
                                <span>{node.name}</span>
                                <span className="text-[10px] text-muted-foreground">
                                  ({getPermissionTypeText(node.type)})
                                </span>
                              </Badge>
                            );
                          })}
                        </div>
                      </div>
                    </div>
                  );
                })}
                
                {totalSelectedPermissions === 0 && (
                  <div className="py-8 flex flex-col items-center justify-center text-center">
                    <div className="rounded-full bg-muted/50 p-3 mb-3">
                      <Shield className="h-6 w-6 text-muted-foreground" />
                    </div>
                    <p className="text-sm text-muted-foreground">
                      暂未选择任何权限
                    </p>
                  </div>
                )}
              </div>
            </ScrollArea>
          </div>
        </DialogContent>
      </Dialog>
    );
  };
  
  // 渲染空状态
  const renderEmptyState = () => (
    <div className="py-8 flex flex-col items-center justify-center text-center">
      <div className="rounded-full bg-muted/50 p-3 mb-3">
        <Shield className="h-6 w-6 text-muted-foreground" />
      </div>
      <p className="text-sm text-muted-foreground">
        暂无权限数据
      </p>
    </div>
  );
  
  // 加载中状态
  if (isLoading && !initialLoadComplete) {
    return (
      <div className="flex items-center justify-center h-[300px]">
        <div className="flex flex-col items-center gap-2">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-sm text-muted-foreground">加载权限数据中...</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="flex flex-col md:flex-row gap-6 h-[600px]">
      {renderPermissionPreviewDialog()}
      
      {/* 左侧权限组导航 */}
      <div className="w-full md:w-48 bg-muted/5 rounded-md">
        <div className="p-3 mb-1">
          <h3 className="text-sm font-medium">权限分组</h3>
        </div>
        <ScrollArea className="h-[550px]">
          <nav className="flex flex-col p-2 gap-1">
            {permissionGroups.map(group => (
              <Button
                key={group.code}
                variant={activeGroup === group.code ? "secondary" : "ghost"}
                size="sm"
                className={cn(
                  "justify-start gap-2",
                  activeGroup === group.code ? "bg-secondary" : ""
                )}
                onClick={() => setActiveGroup(group.code)}
              >
                {getGroupIcon(group.code)}
                <span className="text-sm">{group.name}</span>
                {permissionsByGroup[group.code]?.permissions.length > 0 && (
                  <Badge variant="outline" className="ml-auto py-0 h-5 min-w-5 flex items-center justify-center">
                    {permissionsByGroup[group.code].permissions.length}
                  </Badge>
                )}
              </Button>
            ))}
          </nav>
        </ScrollArea>
      </div>
      
      {/* 分割线 */}
      <div className="hidden md:block w-[1px] bg-border h-full self-stretch" />
      
      {/* 右侧权限树 */}
      <div className="flex-1 bg-muted/5 rounded-md">
        <div className="p-3 mb-0 flex items-center justify-between">
          <h3 className="text-sm font-medium">
            {permissionGroups.find(g => g.code === activeGroup)?.name || "所有权限"}
          </h3>
          <div className="flex items-center gap-2">
            {isLoading && initialLoadComplete && (
              <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
            )}
            <Button 
              variant="outline" 
              size="sm" 
              className="h-8 gap-1"
              onClick={() => setIsPermissionDialogOpen(true)}
            >
              <Eye className="h-3.5 w-3.5" />
              <span>权限预览</span>
              <Badge variant="secondary" className="ml-1">
                {totalSelectedPermissions}
              </Badge>
            </Button>
          </div>
        </div>
        <ScrollArea className="h-[550px]">
          <div className="p-2 space-y-1">
            {currentPermissionTree.length > 0 ? (
              currentPermissionTree.map(node => renderPermissionNode(node))
            ) : (
              renderEmptyState()
            )}
          </div>
        </ScrollArea>
      </div>
    </div>
  );
} 
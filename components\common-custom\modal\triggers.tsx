"use client"

import { ReactNode } from "react"
import { DialogTrigger } from "@/components/ui/dialog"
import { AlertDialogTrigger } from "@/components/ui/alert-dialog"

/**
 * 模态框触发器
 */
export function ModalTrigger({
  children,
  asChild = true,
}: {
  children: ReactNode
  asChild?: boolean
}) {
  return <DialogTrigger asChild={asChild}>{children}</DialogTrigger>
}

/**
 * 警告对话框触发器
 */
export function AlertDialogModalTrigger({
  children,
  asChild = true,
}: {
  children: ReactNode
  asChild?: boolean
}) {
  return <AlertDialogTrigger asChild={asChild}>{children}</AlertDialogTrigger>
} 
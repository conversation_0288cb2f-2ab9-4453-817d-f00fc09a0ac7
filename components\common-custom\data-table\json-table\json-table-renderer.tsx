"use client"

import * as React from "react"
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  RowSelectionState,
} from "@tanstack/react-table"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import { Checkbox } from "@/components/ui/checkbox"
import { Skeleton } from "@/components/ui/skeleton"
import { Pagination } from "@/components/common-custom/pagination"
import { JsonTableConfig, JsonTableColumn, JsonTableAction } from "../types"
import { JsonTableCell } from "./json-table-cell"
import { JsonTableActions } from "./json-table-actions"
import { cn } from "@/lib/utils"

interface JsonTableRendererProps {
  columns: JsonTableColumn[]
  data: any[]
  actions?: JsonTableAction[]
  config: JsonTableConfig
  renderers?: Record<string, (value: any, row: any) => React.ReactNode>
  formatters?: Record<string, (value: any, row: any) => string>
  onRowAction?: (row: any, action: string, actionType: string) => void
  onSelectionChange?: (selectedRows: any[]) => void
  loading?: boolean
}

export function JsonTableRenderer({
  columns,
  data,
  actions,
  config,
  renderers,
  formatters,
  onRowAction,
  onSelectionChange,
  loading = false,
}: JsonTableRendererProps) {
  const [sorting, setSorting] = React.useState<SortingState>([])
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({})
  const [rowSelection, setRowSelection] = React.useState<RowSelectionState>({})
  
  // 当行选择变化时通知父组件
  React.useEffect(() => {
    if (onSelectionChange && Object.keys(rowSelection).length > 0) {
      const selectedRows = data.filter((_, index) => rowSelection[index.toString()])
      onSelectionChange(selectedRows)
    }
  }, [rowSelection, data, onSelectionChange])

  // 将JSON列定义转换为TanStack Table列定义
  const tableColumns = React.useMemo(() => {
    const result: ColumnDef<any>[] = []
    
    // 添加选择列
    if (config.selectionMode === "multiple" || config.showSelection) {
      result.push({
        id: "select",
        header: ({ table }) => (
          <Checkbox
            checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")}
            onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
            aria-label="全选"
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="选择行"
          />
        ),
        enableSorting: false,
        enableHiding: false,
      })
    }
    
    // 添加序号列
    if (config.showRowNumbers) {
      result.push({
        id: "rowNumber",
        header: "#",
        cell: ({ row }) => row.index + 1,
        enableSorting: false,
        enableHiding: false,
      })
    }
    
    // 添加数据列
    columns.forEach(column => {
      result.push({
        id: column.field,
        accessorKey: column.field,
        header: column.header,
        enableSorting: column.sortable !== false,
        enableHiding: column.hideable !== false,
        cell: ({ row }) => (
          <JsonTableCell
            column={column}
            value={row.getValue(column.field)}
            row={row.original}
            renderers={renderers}
            formatters={formatters}
          />
        ),
      })
    })
    
    // 添加操作列
    if (actions && actions.length > 0) {
      result.push({
        id: "actions",
        header: "操作",
        cell: ({ row }) => (
          <JsonTableActions
            actions={actions}
            row={row.original}
            onAction={(action: string, actionType: string) => {
              if (onRowAction) {
                onRowAction(row.original, action, actionType)
              }
            }}
          />
        ),
        enableSorting: false,
        enableHiding: false,
      })
    }
    
    return result
  }, [columns, config, actions, renderers, formatters, onRowAction])

  // 创建表格实例
  const table = useReactTable({
    data,
    columns: tableColumns,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    initialState: {
      pagination: {
        pageSize: config.pageSize || 10,
      },
    },
  })

  // 获取表格行高
  const getTableRowHeight = () => {
    switch (config.density) {
      case "compact":
        return "h-8"
      case "comfortable":
        return "h-14"
      default:
        return "h-10"
    }
  }

  // 获取表格单元格内边距
  const getTableCellPadding = () => {
    switch (config.density) {
      case "compact":
        return "py-1"
      case "comfortable":
        return "py-4"
      default:
        return "py-2"
    }
  }

  // 计算总页数
  const pageCount = Math.ceil(data.length / table.getState().pagination.pageSize)

  // 渲染加载中状态
  if (loading) {
    return (
      <div className="w-full overflow-auto">
        <Table className={cn(config.bordered ? "border" : "")}>
          <TableHeader>
            <TableRow>
              {tableColumns.map((column, index) => (
                <TableHead key={index}>
                  {typeof column.header === "string" ? column.header : ""}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {Array.from({ length: 5 }).map((_, rowIndex) => (
              <TableRow key={rowIndex}>
                {tableColumns.map((_, colIndex) => (
                  <TableCell key={colIndex}>
                    <Skeleton className="h-4 w-full" />
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    )
  }

  return (
    <div className="w-full">
      <div className="rounded-md overflow-auto">
        <Table className={cn(config.bordered ? "border" : "")}>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow 
                key={headerGroup.id}
                className={cn(config.striped ? "even:bg-muted/50" : "")}
              >
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                  className={cn(
                    getTableRowHeight(),
                    config.striped ? "even:bg-muted/50" : "",
                    config.hover ? "hover:bg-muted/50" : ""
                  )}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell 
                      key={cell.id}
                      className={cn(getTableCellPadding())}
                    >
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={tableColumns.length}
                  className="h-24 text-center"
                >
                  {config.emptyMessage || "暂无数据"}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      
      {config.showPagination !== false && (
        <div className="py-4 px-2">
          <Pagination
            pageIndex={table.getState().pagination.pageIndex}
            pageCount={pageCount}
            pageSize={table.getState().pagination.pageSize}
            totalItems={data.length}
            onPageChange={(page) => table.setPageIndex(page)}
            onPageSizeChange={(size) => table.setPageSize(size)}
            pageSizeOptions={config.pageSizeOptions || [10, 20, 30, 50, 100]}
          />
        </div>
      )}
    </div>
  )
} 
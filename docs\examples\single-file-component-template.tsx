/**
 * 单文件组件模板
 * 
 * 这是一个单文件组件的标准模板，展示了类型定义、组件实现和导出的最佳实践
 */

"use client"

import { ReactNode, MouseEvent } from "react"
import { cn } from "@/lib/utils"
import { LucideIcon } from "lucide-react"

// ============================================================================
// 类型定义 - 直接在组件文件内定义
// ============================================================================

/**
 * 组件属性接口
 */
export interface ComponentProps {
  /**
   * 组件标题
   * 支持字符串或ReactNode
   */
  title: string | ReactNode
  
  /**
   * 组件描述
   */
  description?: string
  
  /**
   * 组件尺寸
   * @default "md"
   */
  size?: "sm" | "md" | "lg"
  
  /**
   * 组件变体
   * @default "default"
   */
  variant?: "default" | "primary" | "secondary" | "destructive"
  
  /**
   * 是否禁用
   * @default false
   */
  disabled?: boolean
  
  /**
   * 是否显示图标
   * @default true
   */
  showIcon?: boolean
  
  /**
   * 自定义图标
   */
  icon?: LucideIcon
  
  /**
   * 自定义类名
   */
  className?: string
  
  /**
   * 子元素
   */
  children?: ReactNode
  
  /**
   * 点击事件回调
   * @param value - 组件值
   * @param event - 原生事件对象
   */
  onClick?: (value: string, event: MouseEvent<HTMLButtonElement>) => void
  
  /**
   * 自定义数据
   */
  meta?: Record<string, any>
}

// ============================================================================
// 辅助类型定义
// ============================================================================

/**
 * 组件尺寸映射
 */
type SizeClassMap = {
  [K in NonNullable<ComponentProps['size']>]: string
}

/**
 * 组件变体映射
 */
type VariantClassMap = {
  [K in NonNullable<ComponentProps['variant']>]: string
}

// ============================================================================
// 组件实现
// ============================================================================

/**
 * 示例组件
 * 
 * 这是一个示例组件，展示了单文件组件的标准实现方式
 * 
 * @example
 * ```tsx
 * <ExampleComponent 
 *   title="示例标题"
 *   description="这是一个示例组件"
 *   size="md"
 *   variant="primary"
 *   onClick={(value, event) => console.log('clicked', value)}
 * />
 * ```
 */
export function ExampleComponent({
  title,
  description,
  size = "md",
  variant = "default",
  disabled = false,
  showIcon = true,
  icon: Icon,
  className,
  children,
  onClick,
  meta,
}: ComponentProps) {
  // 样式映射
  const sizeClasses: SizeClassMap = {
    sm: "px-3 py-1.5 text-sm",
    md: "px-4 py-2 text-base",
    lg: "px-6 py-3 text-lg",
  }
  
  const variantClasses: VariantClassMap = {
    default: "bg-background text-foreground border border-input hover:bg-accent",
    primary: "bg-primary text-primary-foreground hover:bg-primary/90",
    secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
    destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90",
  }
  
  // 事件处理
  const handleClick = (event: MouseEvent<HTMLButtonElement>) => {
    if (disabled) return
    
    const value = typeof title === 'string' ? title : 'component-value'
    onClick?.(value, event)
  }
  
  return (
    <button
      type="button"
      disabled={disabled}
      onClick={handleClick}
      className={cn(
        // 基础样式
        "inline-flex items-center justify-center rounded-md font-medium transition-colors",
        "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
        "disabled:pointer-events-none disabled:opacity-50",
        
        // 尺寸样式
        sizeClasses[size],
        
        // 变体样式
        variantClasses[variant],
        
        // 自定义类名
        className
      )}
      data-meta={meta ? JSON.stringify(meta) : undefined}
    >
      {/* 图标 */}
      {showIcon && Icon && (
        <Icon className={cn(
          "shrink-0",
          size === "sm" ? "h-3 w-3" : size === "lg" ? "h-5 w-5" : "h-4 w-4",
          title && "mr-2"
        )} />
      )}
      
      {/* 标题 */}
      {title && (
        <span className="truncate">
          {title}
        </span>
      )}
      
      {/* 子元素 */}
      {children}
      
      {/* 描述（如果有的话，显示为tooltip或其他形式） */}
      {description && (
        <span className="sr-only">
          {description}
        </span>
      )}
    </button>
  )
}

// ============================================================================
// 默认导出（可选）
// ============================================================================

/**
 * 默认导出组件
 * 某些情况下可能需要默认导出
 */
export default ExampleComponent

// ============================================================================
// 类型导出
// ============================================================================

/**
 * 导出所有相关类型，方便外部使用
 */
export type { ComponentProps as ExampleComponentProps }

// ============================================================================
// 常量导出（如果需要）
// ============================================================================

/**
 * 组件相关常量
 */
export const COMPONENT_SIZES = ['sm', 'md', 'lg'] as const
export const COMPONENT_VARIANTS = ['default', 'primary', 'secondary', 'destructive'] as const

/**
 * 默认配置
 */
export const DEFAULT_CONFIG = {
  size: 'md' as const,
  variant: 'default' as const,
  disabled: false,
  showIcon: true,
} satisfies Partial<ComponentProps>

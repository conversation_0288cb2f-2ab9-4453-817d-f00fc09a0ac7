"use client"

import * as React from "react"
import { Button } from "@/components/ui/button"
import { Loader2 } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useState, useRef } from "react"

// 加载延迟时间（毫秒）
const LOADING_DELAY = 200

interface NavigationButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  href?: string
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"
  size?: "default" | "sm" | "lg" | "icon"
  asChild?: boolean
  showLoadingIcon?: boolean
}

/**
 * 统一的导航按钮组件
 * 可以是普通按钮或链接按钮，并带有加载状态
 */
export function NavigationButton({
  children,
  className,
  variant,
  size,
  href,
  showLoadingIcon = true,
  asChild = false,
  ...props
}: NavigationButtonProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const loadingTimerRef = useRef<NodeJS.Timeout | null>(null)
  
  // 处理点击事件
  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (props.onClick) {
      props.onClick(e)
    }
    
    if (href) {
      // 清除之前的loading计时器
      if (loadingTimerRef.current) {
        clearTimeout(loadingTimerRef.current)
        loadingTimerRef.current = null
      }
      
      // 显示局部加载状态(延迟500ms)
      if (showLoadingIcon) {
        loadingTimerRef.current = setTimeout(() => {
          setIsLoading(true)
        }, LOADING_DELAY)
        
        // 组件点击后，设置3秒后自动清除loading状态（避免loading无限显示）
        setTimeout(() => {
          setIsLoading(false)
          if (loadingTimerRef.current) {
            clearTimeout(loadingTimerRef.current)
            loadingTimerRef.current = null
          }
        }, 3000)
      }
    }
  }

  // 如果是链接按钮
  if (href) {
    return (
      <Link href={href} passHref>
        <Button
          variant={variant}
          size={size}
          className={className}
          asChild={asChild}
          onClick={handleClick as any}
          disabled={isLoading || props.disabled}
          {...props}
        >
          <span className="inline-flex items-center">
            {children}
            {isLoading && showLoadingIcon && (
              <Loader2 className="ml-1.5 h-3 w-3 animate-spin inline-flex shrink-0" />
            )}
          </span>
        </Button>
      </Link>
    )
  }

  // 普通按钮
  return (
    <Button
      variant={variant}
      size={size}
      className={className}
      asChild={asChild}
      onClick={handleClick}
      {...props}
    >
      {children}
    </Button>
  )
} 
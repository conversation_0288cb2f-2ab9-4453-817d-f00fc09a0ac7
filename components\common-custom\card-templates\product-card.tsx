"use client"

import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ShoppingCart, Star, Heart, Share2 } from "lucide-react"
import { cn } from "@/lib/utils"
import { ProductCardItem } from "@/types/card-templates"

/**
 * 产品卡片组件
 * 用于展示产品信息，支持价格、评分、徽章等
 */
export function ProductCard({
  id,
  title,
  description,
  image,
  price,
  originalPrice,
  rating,
  reviewCount,
  badge,
  badgeVariant = "secondary",
  badgeClassName = "",
  onClick,
  onAddToCart,
  onFavorite,
  onShare,
  isFavorited = false,
  className,
  renderFooter
}: ProductCardItem) {
  return (
    <Card 
      className={cn(
        "overflow-hidden transition-all hover:shadow-md",
        onClick ? "cursor-pointer" : "",
        className
      )} 
      onClick={onClick ? () => onClick() : undefined}
    >
      <div className="aspect-video bg-gradient-to-br from-muted/70 to-muted w-full">
        {image && (
          <img 
            src={image} 
            alt={title} 
            className="w-full h-full object-cover" 
          />
        )}
      </div>
      <CardHeader>
        <div className="flex items-start justify-between">
          <div>
            <CardTitle className="text-lg">{title}</CardTitle>
            {description && <CardDescription>{description}</CardDescription>}
          </div>
          {badge && (
            <Badge 
              variant={badgeVariant} 
              className={badgeClassName}
            >
              {badge}
            </Badge>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <div className="text-2xl font-bold text-primary">
              {typeof price === 'number' ? `¥${price}` : price}
            </div>
            {originalPrice && (
              <div className="text-sm text-muted-foreground line-through">
                {typeof originalPrice === 'number' ? `¥${originalPrice}` : originalPrice}
              </div>
            )}
          </div>
          {typeof rating === 'number' && (
            <div className="flex items-center gap-1">
              <Star className={cn(
                "h-4 w-4", 
                rating > 0 ? "fill-yellow-400 text-yellow-400" : "text-muted"
              )} />
              <span className="text-sm">{rating.toFixed(1)}{reviewCount ? ` (${reviewCount})` : ''}</span>
            </div>
          )}
        </div>

        {/* 自定义底部或默认按钮 */}
        {renderFooter ? renderFooter() : (
          <div className="flex gap-2">
            {onAddToCart && (
              <Button className="flex-1" onClick={(e) => {
                e.stopPropagation();
                onAddToCart();
              }}>
                <ShoppingCart className="h-4 w-4 mr-2" />
                加入购物车
              </Button>
            )}
            <div className="flex gap-1">
              {onFavorite && (
                <Button 
                  variant="outline" 
                  size="icon" 
                  className={isFavorited ? "text-red-500" : ""}
                  onClick={(e) => {
                    e.stopPropagation();
                    onFavorite();
                  }}
                >
                  <Heart className={cn(
                    "h-4 w-4",
                    isFavorited && "fill-current"
                  )} />
                </Button>
              )}
              {onShare && (
                <Button 
                  variant="outline" 
                  size="icon"
                  onClick={(e) => {
                    e.stopPropagation();
                    onShare();
                  }}
                >
                  <Share2 className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
} 
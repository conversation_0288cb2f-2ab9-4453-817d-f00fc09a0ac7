"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Plus, Edit, Trash2, MoreHorizontal, Search, Copy } from "lucide-react"
import type { SystemVariable } from "@/types/notification"

// System variables (read-only)
const systemVariables: SystemVariable[] = [
  { key: "userName", description: "用户名称", example: "张三", category: "用户" },
  { key: "userEmail", description: "用户邮箱", example: "<EMAIL>", category: "用户" },
  { key: "userPhone", description: "用户手机", example: "13800138000", category: "用户" },
  { key: "userId", description: "用户ID", example: "12345", category: "用户" },
  { key: "code", description: "验证码", example: "123456", category: "验证" },
  { key: "codeExpiry", description: "验证码过期时间", example: "5分钟", category: "验证" },
  { key: "orderNumber", description: "订单号", example: "ORD-001", category: "订单" },
  { key: "orderAmount", description: "订单金额", example: "99.00", category: "订单" },
  { key: "orderStatus", description: "订单状态", example: "已支付", category: "订单" },
  { key: "productName", description: "产品名称", example: "iPhone 15", category: "产品" },
  { key: "productPrice", description: "产品价格", example: "5999.00", category: "产品" },
  { key: "timestamp", description: "时间戳", example: "2024-01-20 14:30:00", category: "系统" },
  { key: "serviceName", description: "服务名称", example: "API服务", category: "系统" },
  { key: "errorMessage", description: "错误信息", example: "连接超时", category: "系统" },
  { key: "companyName", description: "公司名称", example: "示例公司", category: "公司" },
  { key: "supportEmail", description: "客服邮箱", example: "<EMAIL>", category: "公司" },
  { key: "websiteUrl", description: "网站地址", example: "https://example.com", category: "公司" },
]

interface VariableManagerInlineProps {
  content: string
  subject?: string
  customVariables: Record<string, string>
  onCustomVariablesChange: (variables: Record<string, string>) => void
  onVariableInsert: (variable: string) => void
}

export function VariableManagerInline({
  content,
  subject = "",
  customVariables,
  onCustomVariablesChange,
  onVariableInsert,
}: VariableManagerInlineProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [showDialog, setShowDialog] = useState(false)
  const [editingVariable, setEditingVariable] = useState<string | null>(null)
  const [formData, setFormData] = useState({ key: "", value: "" })
  const [usedVariables, setUsedVariables] = useState<string[]>([])

  // Extract variables from content and subject
  useEffect(() => {
    const variableRegex = /\$\{([^}]+)\}/g
    const allText = `${content} ${subject}`
    const matches = allText.match(variableRegex) || []
    const extractedVars = matches.map((match) => match.slice(2, -1)) // Remove ${ and }
    const uniqueVars = Array.from(new Set(extractedVars))
    setUsedVariables(uniqueVars)
  }, [content, subject])

  const filteredSystemVariables = systemVariables.filter(
    (variable) =>
      variable.key.toLowerCase().includes(searchTerm.toLowerCase()) ||
      variable.description.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const handleCreateCustomVariable = () => {
    setEditingVariable(null)
    setFormData({ key: "", value: "" })
    setShowDialog(true)
  }

  const handleEditCustomVariable = (key: string) => {
    setEditingVariable(key)
    setFormData({ key, value: customVariables[key] })
    setShowDialog(true)
  }

  const handleSaveCustomVariable = () => {
    if (formData.key && formData.value) {
      const newVariables = { ...customVariables }
      if (editingVariable && editingVariable !== formData.key) {
        delete newVariables[editingVariable]
      }
      newVariables[formData.key] = formData.value
      onCustomVariablesChange(newVariables)
    }
    setShowDialog(false)
    setFormData({ key: "", value: "" })
  }

  const handleDeleteCustomVariable = (key: string) => {
    const newVariables = { ...customVariables }
    delete newVariables[key]
    onCustomVariablesChange(newVariables)
  }

  const copyVariable = (key: string) => {
    const variableText = `\${${key}}`
    navigator.clipboard.writeText(variableText)
    onVariableInsert(variableText)
  }

  const getCategoryColor = (category: string) => {
    const colors = {
      用户: "bg-blue-100 text-blue-800",
      验证: "bg-green-100 text-green-800",
      订单: "bg-purple-100 text-purple-800",
      产品: "bg-orange-100 text-orange-800",
      系统: "bg-red-100 text-red-800",
      公司: "bg-gray-100 text-gray-800",
    }
    return colors[category as keyof typeof colors] || "bg-gray-100 text-gray-800"
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">变量管理</CardTitle>
          <CardDescription>管理通知模板中使用的变量</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="used" className="space-y-4">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="used">已使用变量</TabsTrigger>
              <TabsTrigger value="system">系统变量</TabsTrigger>
              <TabsTrigger value="custom">自定义变量</TabsTrigger>
            </TabsList>

            <TabsContent value="used" className="space-y-4">
              <div className="space-y-3">
                {usedVariables.length > 0 ? (
                  usedVariables.map((variable) => {
                    const systemVar = systemVariables.find((v) => v.key === variable)
                    const customValue = customVariables[variable]
                    const hasValue = systemVar || customValue

                    return (
                      <div
                        key={variable}
                        className={`flex items-center justify-between p-3 rounded-lg border ${
                          hasValue ? "bg-green-50 border-green-200" : "bg-red-50 border-red-200"
                        }`}
                      >
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <code className="text-sm font-mono bg-white px-2 py-1 rounded">
                              ${"{"}${variable}
                              {"}"}
                            </code>
                            {systemVar && (
                              <Badge className={getCategoryColor(systemVar.category)}>{systemVar.category}</Badge>
                            )}
                            {!hasValue && <Badge variant="destructive">未定义</Badge>}
                          </div>
                          <p className="text-sm text-muted-foreground mt-1">
                            {systemVar?.description || customValue || "变量未定义"}
                          </p>
                          {(systemVar?.example || customValue) && (
                            <p className="text-xs text-muted-foreground">示例: {systemVar?.example || customValue}</p>
                          )}
                        </div>
                        {!hasValue && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setFormData({ key: variable, value: "" })
                              setShowDialog(true)
                            }}
                          >
                            定义变量
                          </Button>
                        )}
                      </div>
                    )
                  })
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    内容中暂未使用任何变量
                    <br />
                    <span className="text-sm">
                      使用 ${"{"}
                      {"{"}变量名{"}"} 格式在内容中插入变量
                    </span>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="system" className="space-y-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="搜索系统变量..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              <div className="space-y-2 max-h-64 overflow-y-auto">
                {filteredSystemVariables.map((variable) => (
                  <div
                    key={variable.key}
                    className="flex items-center justify-between p-2 rounded-lg border hover:bg-muted/50 cursor-pointer"
                    onClick={() => copyVariable(variable.key)}
                  >
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <code className="text-sm font-mono bg-muted px-1 rounded">
                          ${"{"}${variable.key}
                          {"}"}
                        </code>
                        <Badge className={getCategoryColor(variable.category)}>{variable.category}</Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mt-1">{variable.description}</p>
                      <p className="text-xs text-muted-foreground">示例: {variable.example}</p>
                    </div>
                    <Button variant="ghost" size="sm">
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="custom" className="space-y-4">
              <div className="flex justify-between items-center">
                <h4 className="text-sm font-medium">自定义变量</h4>
                <Button variant="outline" size="sm" onClick={handleCreateCustomVariable}>
                  <Plus className="h-4 w-4 mr-2" />
                  新建变量
                </Button>
              </div>

              <div className="space-y-2">
                {Object.entries(customVariables).map(([key, value]) => (
                  <div key={key} className="flex items-center justify-between p-2 rounded-lg border">
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <code className="text-sm font-mono bg-muted px-1 rounded">
                          ${"{"}${key}
                          {"}"}
                        </code>
                        <Button variant="ghost" size="sm" onClick={() => copyVariable(key)}>
                          <Copy className="h-3 w-3" />
                        </Button>
                      </div>
                      <p className="text-sm text-muted-foreground">值: {value}</p>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleEditCustomVariable(key)}>
                          <Edit className="h-4 w-4 mr-2" />
                          编辑
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleDeleteCustomVariable(key)} className="text-destructive">
                          <Trash2 className="h-4 w-4 mr-2" />
                          删除
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                ))}

                {Object.keys(customVariables).length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    暂无自定义变量
                    <br />
                    <span className="text-sm">点击上方按钮创建自定义变量</span>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Create/Edit Custom Variable Dialog */}
      <Dialog open={showDialog} onOpenChange={setShowDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{editingVariable ? "编辑自定义变量" : "新建自定义变量"}</DialogTitle>
            <DialogDescription>设置变量名称和默认值</DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="key">变量名</Label>
              <Input
                id="key"
                value={formData.key}
                onChange={(e) => setFormData({ ...formData, key: e.target.value })}
                placeholder="myVariable"
              />
              <p className="text-sm text-muted-foreground">变量名应使用驼峰命名法，不能包含特殊字符</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="value">默认值</Label>
              <Input
                id="value"
                value={formData.value}
                onChange={(e) => setFormData({ ...formData, value: e.target.value })}
                placeholder="默认值"
              />
            </div>

            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowDialog(false)}>
                取消
              </Button>
              <Button onClick={handleSaveCustomVariable}>{editingVariable ? "更新" : "创建"}</Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}

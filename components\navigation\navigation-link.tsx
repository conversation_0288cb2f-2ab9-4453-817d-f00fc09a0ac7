"use client"

import * as React from "react"
import Link from "next/link"
import { Loader2 } from "lucide-react"
import { cn } from "@/lib/utils"
import { buttonVariants } from "@/components/ui/button"
import { useRouter, usePathname } from "next/navigation"
import { useState, useRef, useEffect, useCallback } from "react"

// 加载延迟时间（毫秒）
const LOADING_DELAY = 150

interface NavigationLinkProps extends React.AnchorHTMLAttributes<HTMLAnchorElement> {
  href: string
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link" | "none"
  size?: "default" | "sm" | "lg" | "icon"
  showLoadingIcon?: boolean
  asButton?: boolean
  activeClassName?: string
}

/**
 * 统一的导航链接组件
 * 可以作为普通链接或按钮样式的链接使用，并带有加载状态
 */
export function NavigationLink({
  children,
  href,
  className,
  variant = "none",
  size = "default",
  showLoadingIcon = false,
  asButton = false,
  activeClassName,
  ...props
}: NavigationLinkProps) {
  const router = useRouter()
  const pathname = usePathname()
  const [isLoading, setIsLoading] = useState(false)
  const loadingTimerRef = useRef<NodeJS.Timeout | null>(null)
  const safetyTimerRef = useRef<NodeJS.Timeout | null>(null)
  const currentHrefRef = useRef<string>(href)
  const clickTimeRef = useRef<number>(0)
  
  // 清除所有计时器的函数
  const clearAllTimers = useCallback(() => {
    if (loadingTimerRef.current) {
      clearTimeout(loadingTimerRef.current)
      loadingTimerRef.current = null
    }
    if (safetyTimerRef.current) {
      clearTimeout(safetyTimerRef.current)
      safetyTimerRef.current = null
    }
  }, [])
  
  // 更新当前链接
  useEffect(() => {
    currentHrefRef.current = href
  }, [href])
  
  // 监听路由变化，判断是否为本组件触发的跳转
  useEffect(() => {
    const now = Date.now()
    // 如果处于加载状态且路由变化，且时间在点击后的合理范围内
    if (isLoading && now - clickTimeRef.current < 3000) {
      const urlObj = new URL(currentHrefRef.current, window.location.origin)
      const hrefPathname = urlObj.pathname
      
      // 如果当前路径与链接路径匹配，认为导航已完成
      if (pathname === hrefPathname || 
          (hrefPathname.endsWith('/') && pathname === hrefPathname.slice(0, -1)) ||
          (pathname.endsWith('/') && hrefPathname === pathname.slice(0, -1))) {
        setIsLoading(false)
        clearAllTimers()
      }
    }
  }, [pathname, isLoading, clearAllTimers])
  
  // 组件卸载时清除计时器
  useEffect(() => {
    return clearAllTimers
  }, [clearAllTimers])
  
  // 处理点击事件
  const handleClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    if (props.onClick) {
      props.onClick(e)
    }
    
    // 如果不显示loading图标，不执行后续操作
    if (!showLoadingIcon) return
    
    // 记录点击时间
    clickTimeRef.current = Date.now()
    
    // 清除之前的所有计时器
    clearAllTimers()
    
    // 立即设置loading状态，使用延迟显示
    loadingTimerRef.current = setTimeout(() => {
      setIsLoading(true)
    }, LOADING_DELAY)
    
    // 安全机制：10秒后自动清除loading状态（避免loading无限显示）
    safetyTimerRef.current = setTimeout(() => {
      setIsLoading(false)
      clearAllTimers()
    }, 10000)
  }

  return (
    <Link 
      href={href} 
      className={cn(
        // 如果作为按钮，应用按钮样式
        asButton && variant !== "none" ? buttonVariants({ variant, size }) : "",
        // 确保内容内联显示，不换行
        "inline-flex items-center",
        className
      )} 
      onClick={handleClick}
      {...props}
    >
      {children}
      {isLoading && showLoadingIcon && (
        <Loader2 className="ml-1.5 h-3 w-3 animate-spin inline-flex shrink-0" />
      )}
    </Link>
  )
} 
"use client"

import React from "react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { cn } from "@/lib/utils"

interface AlertDialogModalProps {
  open?: boolean
  onOpenChange?: (open: boolean) => void
  title: string
  description: string
  confirmText?: string
  cancelText?: string
  onConfirm?: () => void
  onCancel?: () => void
  variant?: "default" | "destructive"
  className?: string
  children?: React.ReactNode
}

export function AlertDialogModal({
  open,
  onOpenChange,
  title,
  description,
  confirmText = "确认",
  cancelText = "取消",
  onConfirm,
  onCancel,
  variant = "default",
  className,
  children,
}: AlertDialogModalProps) {
  const handleConfirm = () => {
    onConfirm?.()
    onOpenChange?.(false)
  }

  const handleCancel = () => {
    onCancel?.()
    onOpenChange?.(false)
  }

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      {children}
      <AlertDialogContent className={cn("sm:max-w-[425px]", className)}>
        <AlertDialogHeader>
          <AlertDialogTitle>{title}</AlertDialogTitle>
          <AlertDialogDescription>{description}</AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={handleCancel}>
            {cancelText}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            className={cn(
              variant === "destructive" &&
                "bg-destructive text-destructive-foreground hover:bg-destructive/90"
            )}
          >
            {confirmText}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}

// 预设的确认删除对话框
export function DeleteConfirmModal({
  open,
  onOpenChange,
  onConfirm,
  itemName = "此项",
  className,
}: {
  open?: boolean
  onOpenChange?: (open: boolean) => void
  onConfirm?: () => void
  itemName?: string
  className?: string
}) {
  return (
    <AlertDialogModal
      open={open}
      onOpenChange={onOpenChange}
      title="确认删除"
      description={`您确定要删除${itemName}吗？此操作无法撤销。`}
      confirmText="删除"
      cancelText="取消"
      onConfirm={onConfirm}
      variant="destructive"
      className={className}
    />
  )
}

// 预设的确认操作对话框
export function ConfirmActionModal({
  open,
  onOpenChange,
  onConfirm,
  title = "确认操作",
  description = "您确定要执行此操作吗？",
  confirmText = "确认",
  cancelText = "取消",
  className,
}: {
  open?: boolean
  onOpenChange?: (open: boolean) => void
  onConfirm?: () => void
  title?: string
  description?: string
  confirmText?: string
  cancelText?: string
  className?: string
}) {
  return (
    <AlertDialogModal
      open={open}
      onOpenChange={onOpenChange}
      title={title}
      description={description}
      confirmText={confirmText}
      cancelText={cancelText}
      onConfirm={onConfirm}
      className={className}
    />
  )
}

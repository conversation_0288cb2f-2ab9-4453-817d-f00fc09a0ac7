"use client"

import * as React from "react"
import { ReactNode, useState, useCallback, useMemo } from "react"
import { Button, ButtonProps } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { LucideIcon, MoreHorizontal } from "lucide-react"
import { cn } from "@/lib/utils"

// ============================================================================
// 类型定义 - 单文件组件类型定义直接在组件文件内
// ============================================================================

/**
 * 操作项配置
 */
export interface ActionItem {
  /**
   * 操作项唯一标识
   */
  id: string

  /**
   * 操作项显示标签
   */
  label: string

  /**
   * 操作项图标
   */
  icon?: ReactNode | LucideIcon

  /**
   * 按钮变体样式
   * @default "default"
   */
  variant?: ButtonProps["variant"]

  /**
   * 点击回调函数
   */
  onClick?: () => void | Promise<void>

  /**
   * 是否为危险操作
   * @default false
   */
  destructive?: boolean

  /**
   * 是否禁用
   * @default false
   */
  disabled?: boolean

  /**
   * 是否显示加载状态
   * @default false
   */
  loading?: boolean

  /**
   * 操作项描述（用于提示）
   */
  description?: string

  /**
   * 快捷键
   */
  shortcut?: string

  /**
   * 是否隐藏
   * @default false
   */
  hidden?: boolean

  /**
   * 自定义类名
   */
  className?: string
}

/**
 * 删除确认对话框配置
 */
export interface DeleteConfirmProps {
  /**
   * 对话框标题
   * @default "确认删除"
   */
  title?: string

  /**
   * 对话框描述
   * @default "此操作无法撤销。这将永久删除该项目及其所有相关数据。"
   */
  description?: string

  /**
   * 取消按钮文本
   * @default "取消"
   */
  cancelText?: string

  /**
   * 确认按钮文本
   * @default "确认删除"
   */
  confirmText?: string

  /**
   * 确认删除回调
   */
  onConfirm: () => void | Promise<void>

  /**
   * 是否显示警告图标
   * @default true
   */
  showWarningIcon?: boolean
}

/**
 * 操作按钮组组件属性
 */
export interface ActionButtonsProps {
  /**
   * 操作项列表
   */
  actions: ActionItem[]

  /**
   * 显示变体
   * @default "default"
   */
  variant?: "default" | "compact" | "dropdown" | "table" | "floating"

  /**
   * 删除确认配置
   */
  deleteConfirmProps?: DeleteConfirmProps

  /**
   * 最大显示按钮数（超出时使用下拉菜单）
   * @default 3
   */
  maxVisibleActions?: number

  /**
   * 自定义类名
   */
  className?: string

  /**
   * 按钮尺寸
   * @default "sm"
   */
  size?: "sm" | "lg"

  /**
   * 是否禁用所有操作
   * @default false
   */
  disabled?: boolean

  /**
   * 操作执行前的确认回调
   */
  onBeforeAction?: (action: ActionItem) => boolean | Promise<boolean>

  /**
   * 操作执行后的回调
   */
  onAfterAction?: (action: ActionItem) => void

  /**
   * 是否显示操作标签（紧凑模式下）
   * @default false
   */
  showLabels?: boolean
}

// ============================================================================
// 组件实现
// ============================================================================

/**
 * 操作按钮组组件
 *
 * 提供多种样式的操作按钮组合，支持确认对话框和灵活的布局
 *
 * @example
 * ```tsx
 * // 基础用法
 * <ActionButtons
 *   actions={[
 *     { id: "edit", label: "编辑", icon: Edit, onClick: handleEdit },
 *     { id: "delete", label: "删除", icon: Trash, destructive: true, onClick: handleDelete }
 *   ]}
 * />
 *
 * // 下拉菜单样式
 * <ActionButtons
 *   variant="dropdown"
 *   actions={actions}
 *   deleteConfirmProps={{
 *     title: "确认删除",
 *     description: "删除后无法恢复"
 *   }}
 * />
 * ```
 */
export const ActionButtons = React.memo<ActionButtonsProps>(function ActionButtons({
  actions,
  variant = "default",
  deleteConfirmProps,
  maxVisibleActions = 3,
  className,
  size = "sm",
  disabled = false,
  onBeforeAction,
  onAfterAction,
  showLabels = false
}) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [deletingAction, setDeletingAction] = useState<string | null>(null)
  const [loadingActions, setLoadingActions] = useState<Set<string>>(new Set())

  // 过滤可见的操作项 - 使用 useMemo 优化
  const visibleActions = useMemo(() =>
    actions.filter(action => !action.hidden),
    [actions]
  )

  // 使用 useCallback 优化事件处理函数
  const handleAction = useCallback(async (action: ActionItem) => {
    if (action.disabled || disabled) return

    // 执行前确认
    if (onBeforeAction) {
      const canProceed = await onBeforeAction(action)
      if (!canProceed) return
    }

    // 危险操作需要确认
    if (action.destructive && deleteConfirmProps) {
      setDeletingAction(action.id)
      setShowDeleteDialog(true)
      return
    }

    // 执行操作
    if (action.onClick) {
      try {
        setLoadingActions(prev => new Set(prev).add(action.id))
        await action.onClick()
        onAfterAction?.(action)
      } catch (error) {
        console.error('Action failed:', error)
      } finally {
        setLoadingActions(prev => {
          const newSet = new Set(prev)
          newSet.delete(action.id)
          return newSet
        })
      }
    }
  }, [actions, disabled, onBeforeAction, onAfterAction, deleteConfirmProps, deletingAction])

  const confirmDelete = useCallback(async () => {
    const action = actions.find(a => a.id === deletingAction)
    if (action?.onClick) {
      try {
        await action.onClick()
        onAfterAction?.(action)
      } catch (error) {
        console.error('Delete action failed:', error)
      }
    }
    setShowDeleteDialog(false)
    setDeletingAction(null)
  }, [actions, onAfterAction])

  // 渲染图标 - 使用 useCallback 优化
  const renderIcon = useCallback((icon: ReactNode | LucideIcon) => {
    if (!icon) return null

    if (typeof icon === 'function') {
      const IconComponent = icon as LucideIcon
      return <IconComponent className="h-4 w-4" />
    }

    return icon
  }, [])

  // 基础按钮组
  if (variant === "default") {
    return (
      <>
        <div className={cn("flex gap-2", className)}>
          {visibleActions.map((action) => (
            <Button
              key={action.id}
              size={size}
              variant={action.variant || (action.destructive ? "destructive" : "default")}
              onClick={() => handleAction(action)}
              disabled={action.disabled || disabled || loadingActions.has(action.id)}
              className={action.className}
            >
              {action.icon && <span className="mr-2">{renderIcon(action.icon)}</span>}
              {action.label}
            </Button>
          ))}
        </div>

        {deleteConfirmProps && renderDeleteDialog(showDeleteDialog, setShowDeleteDialog, deleteConfirmProps, confirmDelete)}
      </>
    )
  }

  // 紧凑型按钮
  if (variant === "compact") {
    return (
      <>
        <div className={cn("flex gap-1", className)}>
          {visibleActions.map((action) => (
            <Button
              key={action.id}
              size={size}
              variant={action.variant || "ghost"}
              onClick={() => handleAction(action)}
              disabled={action.disabled || disabled || loadingActions.has(action.id)}
              className={cn(
                action.destructive ? "text-destructive hover:text-destructive" : "",
                action.className
              )}
              title={action.description || action.label}
            >
              {renderIcon(action.icon)}
              {showLabels && <span className="ml-1">{action.label}</span>}
            </Button>
          ))}
        </div>

        {deleteConfirmProps && renderDeleteDialog(showDeleteDialog, setShowDeleteDialog, deleteConfirmProps, confirmDelete)}
      </>
    )
  }

  // 下拉菜单操作
  if (variant === "dropdown") {
    return (
      <>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              size={size}
              disabled={disabled}
              className={className}
            >
              <MoreHorizontal className="w-4 h-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {visibleActions.map((action, idx) => {
              // 如果是最后一个破坏性操作，添加分隔线
              const isLastDestructive = action.destructive &&
                idx === visibleActions.length - 1 &&
                visibleActions.some(a => a.destructive);

              // 如果前一个不是破坏性操作，当前是，添加分隔线
              const needsSeparator = idx > 0 &&
                action.destructive &&
                !visibleActions[idx - 1].destructive;

              return (
                <div key={action.id}>
                  {(isLastDestructive || needsSeparator) && <DropdownMenuSeparator />}
                  <DropdownMenuItem
                    onClick={() => handleAction(action)}
                    disabled={action.disabled || loadingActions.has(action.id)}
                    className={cn(
                      action.destructive ? "text-destructive focus:text-destructive" : "",
                      action.className
                    )}
                  >
                    {action.icon && <span className="mr-2">{renderIcon(action.icon)}</span>}
                    <span>{action.label}</span>
                    {action.shortcut && (
                      <span className="ml-auto text-xs text-muted-foreground">
                        {action.shortcut}
                      </span>
                    )}
                  </DropdownMenuItem>
                </div>
              )
            })}
          </DropdownMenuContent>
        </DropdownMenu>

        {deleteConfirmProps && renderDeleteDialog(showDeleteDialog, setShowDeleteDialog, deleteConfirmProps, confirmDelete)}
      </>
    )
  }

  // 表格行操作
  if (variant === "table") {
    const quickActions = visibleActions.filter(a => !a.destructive && a.icon).slice(0, maxVisibleActions)
    const menuActions = visibleActions.filter(a => !quickActions.includes(a))

    return (
      <>
        <div className={cn("flex gap-1", className)}>
          {quickActions.map((action) => (
            <Button
              key={action.id}
              size={size}
              variant="ghost"
              onClick={() => handleAction(action)}
              disabled={action.disabled || disabled || loadingActions.has(action.id)}
              className={action.className}
              title={action.description || action.label}
            >
              {renderIcon(action.icon)}
            </Button>
          ))}

          {menuActions.length > 0 && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  size={size}
                  variant="ghost"
                  disabled={disabled}
                >
                  <MoreHorizontal className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {menuActions.filter(a => !a.destructive).map((action) => (
                  <DropdownMenuItem
                    key={action.id}
                    onClick={() => handleAction(action)}
                    disabled={action.disabled || loadingActions.has(action.id)}
                    className={action.className}
                  >
                    {action.icon && <span className="mr-2">{renderIcon(action.icon)}</span>}
                    {action.label}
                  </DropdownMenuItem>
                ))}
                {menuActions.some(a => a.destructive) && <DropdownMenuSeparator />}
                {menuActions.filter(a => a.destructive).map((action) => (
                  <DropdownMenuItem
                    key={action.id}
                    onClick={() => handleAction(action)}
                    disabled={action.disabled || loadingActions.has(action.id)}
                    className={cn("text-destructive", action.className)}
                  >
                    {action.icon && <span className="mr-2">{renderIcon(action.icon)}</span>}
                    {action.label}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>

        {deleteConfirmProps && renderDeleteDialog(showDeleteDialog, setShowDeleteDialog, deleteConfirmProps, confirmDelete)}
      </>
    )
  }

  return null
})

const renderDeleteDialog = (
  open: boolean,
  setOpen: (open: boolean) => void,
  props: DeleteConfirmProps,
  onConfirm: () => void
) => {
  return (
    <AlertDialog open={open} onOpenChange={setOpen}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{props.title || "确认删除"}</AlertDialogTitle>
          <AlertDialogDescription>
            {props.description || "此操作无法撤销。这将永久删除该项目及其所有相关数据。"}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>{props.cancelText || "取消"}</AlertDialogCancel>
          <AlertDialogAction
            onClick={onConfirm}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {props.confirmText || "确认删除"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}

// ============================================================================
// 类型导出
// ============================================================================

// 类型已在上面定义时导出

// ============================================================================
// 常量导出
// ============================================================================

/**
 * 支持的操作按钮变体
 */
export const ACTION_BUTTON_VARIANTS = ['default', 'compact', 'dropdown', 'table', 'floating'] as const

/**
 * 支持的按钮尺寸
 */
export const ACTION_BUTTON_SIZES = ['sm', 'lg'] as const

/**
 * 默认操作按钮配置
 */
export const DEFAULT_ACTION_BUTTONS_CONFIG = {
  variant: 'default' as const,
  maxVisibleActions: 3,
  size: 'sm' as const,
  disabled: false,
  showLabels: false,
} satisfies Partial<ActionButtonsProps>

/**
 * 默认删除确认配置
 */
export const DEFAULT_DELETE_CONFIRM_CONFIG = {
  title: '确认删除',
  description: '此操作无法撤销。这将永久删除该项目及其所有相关数据。',
  cancelText: '取消',
  confirmText: '确认删除',
  showWarningIcon: true,
} satisfies Partial<DeleteConfirmProps>

/**
 * 常用操作项模板
 */
export const COMMON_ACTION_TEMPLATES = {
  edit: (onClick: () => void): ActionItem => ({
    id: 'edit',
    label: '编辑',
    onClick,
  }),

  delete: (onClick: () => void): ActionItem => ({
    id: 'delete',
    label: '删除',
    destructive: true,
    onClick,
  }),

  view: (onClick: () => void): ActionItem => ({
    id: 'view',
    label: '查看',
    onClick,
  }),

  copy: (onClick: () => void): ActionItem => ({
    id: 'copy',
    label: '复制',
    onClick,
  }),
} as const
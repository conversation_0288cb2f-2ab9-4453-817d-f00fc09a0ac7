"use client"

import * as React from "react"
import { 
  DropdownMenuButton, 
  MoreMenuButton, 
  ActionMenuButton, 
  ContextMenu,
  MenuItem
} from "@/components/common-custom/menu"
import { Button } from "@/components/ui/button"
import { 
  Settings, 
  User, 
  LogOut, 
  Mail, 
  MessageSquare, 
  Bell, 
  Pencil, 
  Share, 
  Trash, 
  Copy, 
  Eye, 
  FileEdit, 
  FilePlus, 
  FileX, 
  FileText,
  Menu as MenuIcon
} from "lucide-react"
import { Card } from "@/components/ui/card"
import { toast } from "@/hooks/use-toast"

// 菜单项示例
const userMenuItems: MenuItem[] = [
  {
    label: "个人信息",
    icon: User,
    onClick: () => toast({ title: "查看个人信息" })
  },
  {
    label: "设置",
    icon: Settings,
    onClick: () => toast({ title: "打开设置" })
  },
  { type: "separator" },
  {
    label: "退出登录",
    icon: LogOut,
    onClick: () => toast({ title: "退出登录" })
  }
]

// 带子菜单的菜单项
const fileMenuItems: MenuItem[] = [
  {
    label: "新建",
    icon: FilePlus,
    type: "sub",
    items: [
      {
        label: "文档",
        icon: FileText,
        onClick: () => toast({ title: "新建文档" })
      },
      {
        label: "表格",
        icon: FileText,
        onClick: () => toast({ title: "新建表格" })
      },
      {
        label: "演示文稿",
        icon: FileText,
        onClick: () => toast({ title: "新建演示文稿" })
      }
    ]
  },
  {
    label: "打开",
    icon: Eye,
    onClick: () => toast({ title: "打开文件" })
  },
  {
    label: "编辑",
    icon: FileEdit,
    onClick: () => toast({ title: "编辑文件" })
  },
  { type: "separator" },
  {
    label: "删除",
    icon: FileX,
    onClick: () => toast({ title: "删除文件" })
  }
]

// 带复选框的菜单项
const viewMenuItems: MenuItem[] = [
  { type: "label", label: "视图选项" },
  {
    type: "checkbox",
    label: "显示工具栏",
    checked: true,
    onClick: () => toast({ title: "切换工具栏显示" })
  },
  {
    type: "checkbox",
    label: "显示状态栏",
    checked: false,
    onClick: () => toast({ title: "切换状态栏显示" })
  },
  {
    type: "checkbox",
    label: "显示侧边栏",
    checked: true,
    onClick: () => toast({ title: "切换侧边栏显示" })
  },
  { type: "separator" },
  { type: "label", label: "布局" },
  {
    type: "radio",
    label: "紧凑视图",
    value: "compact"
  },
  {
    type: "radio",
    label: "舒适视图",
    value: "comfortable"
  },
  {
    type: "radio",
    label: "宽松视图",
    value: "relaxed"
  }
]

// 带徽章的菜单项
const notificationMenuItems: MenuItem[] = [
  {
    label: "消息",
    icon: Mail,
    badge: {
      content: "3",
      variant: "default"
    },
    onClick: () => toast({ title: "查看消息" })
  },
  {
    label: "评论",
    icon: MessageSquare,
    badge: {
      content: "12",
      variant: "secondary"
    },
    onClick: () => toast({ title: "查看评论" })
  },
  {
    label: "通知",
    icon: Bell,
    badge: {
      content: "5",
      variant: "destructive"
    },
    onClick: () => toast({ title: "查看通知" })
  }
]

// 操作菜单项
const rowActions: MenuItem[] = [
  {
    label: "查看",
    icon: Eye,
    value: "view",
    onClick: () => toast({ title: "查看记录" })
  },
  {
    label: "编辑",
    icon: Pencil,
    value: "edit",
    onClick: () => toast({ title: "编辑记录" })
  },
  {
    label: "删除",
    icon: Trash,
    value: "delete",
    onClick: () => toast({ title: "删除记录" })
  },
  {
    label: "复制",
    icon: Copy,
    value: "copy",
    onClick: () => toast({ title: "复制记录" })
  },
  {
    label: "分享",
    icon: Share,
    value: "share",
    onClick: () => toast({ title: "分享记录" })
  }
]

export default function MenuExamplePage() {
  const [radioValue, setRadioValue] = React.useState("comfortable")
  
  return (
    <div className="container py-10">
      <h1 className="text-2xl font-bold mb-8">菜单组件示例</h1>
      
      <div className="space-y-10">
        {/* 基础菜单示例 */}
        <section>
          <h2 className="text-xl font-semibold mb-4">基础菜单</h2>
          <div className="flex flex-wrap gap-4">
            <DropdownMenuButton
              label="用户"
              icon={User}
              items={userMenuItems}
              variant="outline"
              onSelect={(value) => toast({ title: `选择了: ${value}` })}
            />
            <MoreMenuButton
              items={userMenuItems}
              onSelect={(value) => toast({ title: `选择了: ${value}` })}
            />
          </div>
        </section>
        
        {/* 子菜单示例 */}
        <section>
          <h2 className="text-xl font-semibold mb-4">子菜单</h2>
          <DropdownMenuButton
            label="文件"
            icon={FileText}
            items={fileMenuItems}
            variant="outline"
            onSelect={(value) => toast({ title: `选择了: ${value}` })}
          />
        </section>
        
        {/* 复选框和单选按钮示例 */}
        <section>
          <h2 className="text-xl font-semibold mb-4">复选框和单选按钮</h2>
          <DropdownMenuButton
            label="视图"
            items={viewMenuItems}
            variant="outline"
            radioValue={radioValue}
            onRadioValueChange={(value) => {
              setRadioValue(value)
              toast({ title: `选择了视图: ${value}` })
            }}
          />
        </section>
        
        {/* 带徽章的菜单示例 */}
        <section>
          <h2 className="text-xl font-semibold mb-4">带徽章的菜单</h2>
          <DropdownMenuButton
            label="通知"
            items={notificationMenuItems}
            variant="outline"
            onSelect={(value) => toast({ title: `选择了: ${value}` })}
          />
        </section>
        
        {/* 操作菜单示例 */}
        <section>
          <h2 className="text-xl font-semibold mb-4">操作菜单</h2>
          <ActionMenuButton
            primaryActions={rowActions}
            visibleItemsCount={2}
            onSelect={(value) => toast({ title: `执行操作: ${value}` })}
          />
        </section>
        
        {/* 上下文菜单示例 */}
        <section>
          <h2 className="text-xl font-semibold mb-4">上下文菜单</h2>
          <ContextMenu
            items={fileMenuItems}
            onSelect={(value) => toast({ title: `选择了: ${value}` })}
          >
            <Card className="p-8 cursor-context-menu text-center">
              右键点击此处查看上下文菜单
            </Card>
          </ContextMenu>
        </section>
      </div>
    </div>
  )
} 
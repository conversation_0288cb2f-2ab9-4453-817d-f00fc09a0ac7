"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Loader2, Search, Sparkles, BookOpen, FileText, User, Tag } from "lucide-react"
import { cn } from "@/lib/utils"
import { SearchInput } from "./search-input"
import { SearchResults } from "./search-results"
import type { SearchItem, SearchResultGroup } from "@/types/search"

export interface SemanticSearchProps {
  /**
   * 搜索值
   */
  value: string
  
  /**
   * 搜索值变化回调
   */
  onChange: (value: string) => void
  
  /**
   * 占位符
   */
  placeholder?: string
  
  /**
   * 搜索结果
   */
  results?: SearchItem[] | SearchResultGroup[]
  
  /**
   * 搜索建议
   */
  suggestions?: string[]
  
  /**
   * 是否加载中
   */
  loading?: boolean
  
  /**
   * 结果点击回调
   */
  onResultClick?: (item: SearchItem) => void
  
  /**
   * 搜索提交回调
   */
  onSearch?: (value: string) => void
  
  /**
   * 建议点击回调
   */
  onSuggestionClick?: (suggestion: string) => void
  
  /**
   * 自定义类名
   */
  className?: string
}

/**
 * 语义搜索组件
 * 提供自然语言搜索和搜索建议功能
 */
export function SemanticSearch({
  value,
  onChange,
  placeholder = "使用自然语言搜索...",
  results = [],
  suggestions = [],
  loading = false,
  onResultClick,
  onSearch,
  onSuggestionClick,
  className,
}: SemanticSearchProps) {
  const [activeTab, setActiveTab] = useState<string>("all")
  
  // 提交搜索
  const handleSubmit = () => {
    onSearch?.(value)
  }
  
  // 处理建议点击
  const handleSuggestionClick = (suggestion: string) => {
    onChange(suggestion)
    onSuggestionClick?.(suggestion)
    onSearch?.(suggestion)
  }

  // 根据类型过滤结果
  const getFilteredResults = () => {
    if (activeTab === "all") return results
    
    if (Array.isArray(results) && results.length > 0) {
      // 处理分组结果
      if ('items' in results[0]) {
        const groupedResults = results as SearchResultGroup[]
        
        // 根据标签过滤分组
        return groupedResults.map(group => {
          if (group.title.toLowerCase() === activeTab) {
            return group
          }
          return {
            ...group,
            items: []
          }
        }).filter(group => group.items.length > 0)
      } 
      // 处理非分组结果
      else {
        const items = results as SearchItem[]
        
        // 根据标签过滤项目
        return items.filter(item => {
          if (activeTab === "documents" && item.icon === FileText) return true
          if (activeTab === "people" && item.icon === User) return true
          if (activeTab === "tags" && item.tags && item.tags.length > 0) return true
          return false
        })
      }
    }
    
    return []
  }
  
  // 获取过滤后的结果
  const filteredResults = getFilteredResults()
  
  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex flex-col gap-2">
        <div className="relative">
          <SearchInput
            value={value}
            onChange={onChange}
            placeholder={placeholder}
            onSubmit={handleSubmit}
            icon={<Sparkles className="h-4 w-4" />}
            size="lg"
            loading={loading}
          />
        </div>
        
        {/* 搜索建议 */}
        {suggestions.length > 0 && (
          <div className="flex flex-wrap gap-2 mt-2">
            {suggestions.map((suggestion, index) => (
              <Button
                key={index}
                variant="secondary"
                size="sm"
                className="h-8 text-sm"
                onClick={() => handleSuggestionClick(suggestion)}
              >
                <Sparkles className="mr-1 h-3 w-3" />
                {suggestion}
              </Button>
            ))}
          </div>
        )}
      </div>
      
      {/* 结果分类标签页 */}
      {!loading && results.length > 0 && (
        <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="all" className="flex items-center gap-1">
              <Search className="h-4 w-4" />
              全部
            </TabsTrigger>
            <TabsTrigger value="documents" className="flex items-center gap-1">
              <FileText className="h-4 w-4" />
              文档
            </TabsTrigger>
            <TabsTrigger value="people" className="flex items-center gap-1">
              <User className="h-4 w-4" />
              人员
            </TabsTrigger>
            <TabsTrigger value="tags" className="flex items-center gap-1">
              <Tag className="h-4 w-4" />
              标签
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value={activeTab}>
            <Card>
              <CardContent className="p-4">
                <SearchResults
                  results={filteredResults}
                  onResultClick={onResultClick}
                  grouped={'items' in (results[0] || {})}
                  loading={loading}
                  loadingText="正在进行语义搜索..."
                  noResultsText="没有找到相关结果"
                />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}
      
      {/* 加载中状态 */}
      {loading && (
        <Card>
          <CardContent className="p-6 flex flex-col items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
            <p className="text-center text-muted-foreground">正在进行语义搜索...</p>
            <p className="text-center text-xs text-muted-foreground mt-1">分析查询并查找最相关的结果</p>
          </CardContent>
        </Card>
      )}
      
      {/* 无结果状态 */}
      {!loading && value && results.length === 0 && (
        <Card>
          <CardContent className="p-6 flex flex-col items-center justify-center">
            <BookOpen className="h-8 w-8 text-muted-foreground mb-4" />
            <p className="text-center text-muted-foreground">没有找到相关结果</p>
            <p className="text-center text-xs text-muted-foreground mt-1">尝试使用不同的关键词或更简洁的描述</p>
          </CardContent>
        </Card>
      )}
    </div>
  )
} 
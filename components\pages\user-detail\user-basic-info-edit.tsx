"use client"

import React from "react"
import {
  UserCircle,
  Mail,
  PhoneCall,
  Shield,
  Upload,
  User as UserIcon
} from "lucide-react"

import { User } from "@/types/user"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { 
  Select, 
  SelectContent, 
  SelectGroup, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select"

interface UserBasicInfoEditProps {
  user: User;
  errors: Record<string, string>;
  onChange: (field: string, value: any) => void;
}

export function UserBasicInfoEdit({ user, errors, onChange }: UserBasicInfoEditProps) {
  // 处理头像上传
  const handleAvatarUpload = () => {
    // 这里应该有真实的头像上传逻辑
    alert('头像上传功能未实现')
  }

  return (
    <Card className="border-border/40">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <UserCircle className="h-5 w-5 text-primary" />
          个人资料
        </CardTitle>
        <CardDescription>编辑用户的基本个人信息</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex flex-col sm:flex-row gap-6 items-start">
          <div className="flex flex-col items-center gap-3">
            <Avatar className="h-24 w-24 border">
              <AvatarImage src={user.avatarUrl || ""} alt={user.nickname} />
              <AvatarFallback className="text-2xl">
                {user.nickname?.charAt(0) || <UserIcon className="h-8 w-8" />}
              </AvatarFallback>
            </Avatar>
            <div className="flex flex-col items-center gap-2">
              <Button 
                variant="outline" 
                size="sm" 
                className="w-full"
                onClick={handleAvatarUpload}
              >
                <Upload className="h-4 w-4 mr-2" />
                更换头像
              </Button>
              <p className="text-xs text-muted-foreground text-center w-full">
                推荐使用 200x200 像素的图片
              </p>
            </div>
          </div>
          
          <div className="grid gap-4 flex-1">
            <div className="grid gap-2">
              <Label htmlFor="account" className="flex items-center gap-2">
                <UserIcon className="h-4 w-4 text-muted-foreground" />
                账号
              </Label>
              <Input 
                id="account" 
                value={user.account || ""} 
                onChange={e => onChange("account", e.target.value)} 
                disabled 
                className="bg-muted/50"
              />
              <p className="text-xs text-muted-foreground">账号创建后不可修改</p>
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="nickname" className="flex items-center gap-2">
                <UserIcon className="h-4 w-4 text-muted-foreground" />
                用户名
              </Label>
              <Input 
                id="nickname" 
                value={user.nickname || ""}
                onChange={e => onChange("nickname", e.target.value)}
                className={errors.nickname ? "border-destructive" : ""}
              />
              {errors.nickname && (
                <p className="text-destructive text-sm">{errors.nickname}</p>
              )}
            </div>
          </div>
        </div>
        
        <Separator />
        
        <div className="grid gap-4">
          <div className="grid gap-2">
            <Label htmlFor="email" className="flex items-center gap-2">
              <Mail className="h-4 w-4 text-muted-foreground" />
              邮箱
            </Label>
            <Input 
              id="email" 
              value={user.email || ""}
              onChange={e => onChange("email", e.target.value)} 
              type="email"
              className={errors.email ? "border-destructive" : ""}
            />
            {errors.email && (
              <p className="text-destructive text-sm">{errors.email}</p>
            )}
          </div>
          
          <div className="grid gap-2">
            <Label htmlFor="mobile" className="flex items-center gap-2">
              <PhoneCall className="h-4 w-4 text-muted-foreground" />
              手机号码
            </Label>
            <Input 
              id="mobile" 
              value={user.mobile || ""}
              onChange={e => onChange("mobile", e.target.value)}
              className={errors.mobile ? "border-destructive" : ""}
            />
            {errors.mobile && (
              <p className="text-destructive text-sm">{errors.mobile}</p>
            )}
          </div>
          
          <div className="grid gap-2">
            <Label htmlFor="status" className="flex items-center gap-2">
              <Shield className="h-4 w-4 text-muted-foreground" />
              用户状态
            </Label>
            <Select 
              value={String(user.status)} 
              onValueChange={(value) => onChange("status", parseInt(value, 10))}
            >
              <SelectTrigger>
                <SelectValue placeholder="选择用户状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectItem value="1">正常</SelectItem>
                  <SelectItem value="0">禁用</SelectItem>
                  <SelectItem value="2">锁定</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground">
              {user.status === 1 ? "用户可以正常访问系统" : 
               user.status === 0 ? "禁用后用户将无法登录系统" : 
               user.status === 2 ? "锁定后用户需要解锁才能使用系统" : 
               "请选择用户状态"}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default UserBasicInfoEdit; 
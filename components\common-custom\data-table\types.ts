import { ComponentType, ReactNode } from "react"
import type { LucideIcon } from "lucide-react"
import type { ColumnDef } from "@tanstack/react-table"

// ============================================================================
// 数据表格组件类型定义 - 复杂组件类型定义在组件目录内
// ============================================================================

/**
 * 过滤选项
 */
export interface FilterOption {
  /**
   * 选项标签
   */
  label: string

  /**
   * 选项值
   */
  value: string

  /**
   * 图标
   */
  icon?: LucideIcon | ComponentType<{ className?: string }>

  /**
   * 颜色
   */
  color?: string
}

/**
 * 表格过滤器
 */
export interface TableFilter {
  /**
   * 过滤字段
   */
  key: string
  
  /**
   * 过滤器标题
   */
  title: string
  
  /**
   * 过滤器类型
   */
  type?: "select" | "multi-select" | "date-range" | "search"
  
  /**
   * 过滤选项
   */
  options?: FilterOption[]
  
  /**
   * 占位符
   */
  placeholder?: string
}

/**
 * 表格操作项
 */
export interface TableAction {
  /**
   * 操作标签
   */
  label: string
  
  /**
   * 操作值
   */
  value: string
  
  /**
   * 图标
   */
  icon?: ComponentType<{ className?: string }>
  
  /**
   * 变体样式
   */
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost"
  
  /**
   * 点击回调
   */
  onClick?: (row: any) => void
}

/**
 * 表格配置
 */
export interface TableConfig {
  /**
   * 是否显示选择
   */
  showSelection?: boolean
  
  /**
   * 是否显示分页
   */
  showPagination?: boolean
  
  /**
   * 是否显示搜索
   */
  showSearch?: boolean
  
  /**
   * 是否显示过滤器
   */
  showFilters?: boolean
  
  /**
   * 是否显示列可见性
   */
  showColumnVisibility?: boolean
  
  /**
   * 是否显示行操作
   */
  showRowActions?: boolean
  
  /**
   * 每页数量
   */
  pageSize?: number
  
  /**
   * 分页选项
   */
  pageSizeOptions?: number[]
  
  /**
   * 搜索占位符
   */
  searchPlaceholder?: string
  
  /**
   * 空数据提示
   */
  emptyMessage?: string
  
  /**
   * 加载提示
   */
  loadingMessage?: string
}

/**
 * 高级数据表格属性
 */
export interface AdvancedDataTableProps<TData, TValue> {
  /**
   * 列定义
   */
  columns: any[]
  
  /**
   * 数据源
   */
  data: TData[]
  
  /**
   * 表格配置
   */
  config?: TableConfig
  
  /**
   * 搜索字段
   */
  searchKey?: string
  
  /**
   * 过滤器列表
   */
  filters?: TableFilter[]
  
  /**
   * 主要操作
   */
  primaryActions?: TableAction[]
  
  /**
   * 次要操作
   */
  secondaryActions?: TableAction[]
  
  /**
   * 行操作回调
   */
  onRowAction?: (row: TData, action: string) => void
  
  /**
   * 是否加载中
   */
  loading?: boolean
  
  /**
   * 自定义样式类
   */
  className?: string
  
  /**
   * 刷新回调
   */
  onRefresh?: () => void
}

/**
 * JSON表格列定义
 */
export interface JsonTableColumn {
  /**
   * 列字段名
   */
  field: string
  
  /**
   * 列标题
   */
  header: string
  
  /**
   * 列类型
   */
  type?: "text" | "number" | "date" | "boolean" | "image" | "badge" | "actions" | "custom"
  
  /**
   * 列宽度
   */
  width?: number | string
  
  /**
   * 是否可排序
   */
  sortable?: boolean
  
  /**
   * 是否可过滤
   */
  filterable?: boolean
  
  /**
   * 是否可隐藏
   */
  hideable?: boolean
  
  /**
   * 对齐方式
   */
  align?: "left" | "center" | "right"
  
  /**
   * 格式化选项
   */
  format?: {
    /**
     * 日期格式
     */
    dateFormat?: string
    
    /**
     * 数字格式
     */
    numberFormat?: string
    
    /**
     * 自定义格式化函数名
     */
    formatter?: string
  }
  
  /**
   * 徽章配置
   */
  badge?: {
    /**
     * 徽章映射
     */
    mapping: Record<string, {
      /**
       * 徽章标签
       */
      label?: string
      
      /**
       * 徽章变体
       */
      variant?: "default" | "secondary" | "outline" | "destructive"
      
      /**
       * 徽章颜色
       */
      color?: string
    }>
  }
  
  /**
   * 图片配置
   */
  image?: {
    /**
     * 图片宽度
     */
    width?: number
    
    /**
     * 图片高度
     */
    height?: number
    
    /**
     * 图片圆角
     */
    rounded?: boolean | "sm" | "md" | "lg" | "full"
    
    /**
     * 图片占位符
     */
    placeholder?: string
  }
  
  /**
   * 自定义渲染器名称
   */
  renderer?: string
}

/**
 * JSON表格操作定义
 */
export interface JsonTableAction {
  /**
   * 操作类型
   */
  type: "view" | "edit" | "delete" | "custom"
  
  /**
   * 操作标签
   */
  label: string
  
  /**
   * 操作图标
   */
  icon?: string
  
  /**
   * 操作变体样式
   */
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost"
  
  /**
   * 操作值（自定义操作时使用）
   */
  value?: string
  
  /**
   * 操作条件表达式
   */
  condition?: string
  
  /**
   * 确认对话框配置
   */
  confirm?: {
    /**
     * 确认标题
     */
    title: string
    
    /**
     * 确认消息
     */
    message: string
    
    /**
     * 确认按钮文本
     */
    confirmText?: string
    
    /**
     * 取消按钮文本
     */
    cancelText?: string
  }
}

/**
 * JSON表格配置
 */
export interface JsonTableConfig extends TableConfig {
  /**
   * 表格标题
   */
  title?: string
  
  /**
   * 表格描述
   */
  description?: string
  
  /**
   * 表格密度
   */
  density?: "compact" | "default" | "comfortable"
  
  /**
   * 表格边框
   */
  bordered?: boolean
  
  /**
   * 表格斑马纹
   */
  striped?: boolean
  
  /**
   * 表格悬停效果
   */
  hover?: boolean
  
  /**
   * 行选择模式
   */
  selectionMode?: "single" | "multiple" | "none"
  
  /**
   * 是否显示序号列
   */
  showRowNumbers?: boolean
  
  /**
   * 是否允许调整列宽
   */
  resizableColumns?: boolean
  
  /**
   * 是否允许拖拽列
   */
  reorderableColumns?: boolean
  
  /**
   * 是否显示工具栏
   */
  showToolbar?: boolean
  
  /**
   * 是否显示表头
   */
  showHeader?: boolean
  
  /**
   * 是否显示表尾
   */
  showFooter?: boolean
  
  /**
   * 是否显示导出按钮
   */
  showExport?: boolean
  
  /**
   * 是否显示刷新按钮
   */
  showRefresh?: boolean
  
  /**
   * 是否显示添加按钮
   */
  showAdd?: boolean
  
  /**
   * 添加按钮文本
   */
  addButtonText?: string
  
  /**
   * 导出文件名
   */
  exportFileName?: string
  
  /**
   * 导出格式
   */
  exportFormats?: ("csv" | "excel" | "pdf")[]
}

/**
 * JSON数据表格属性
 */
export interface JsonDataTableProps {
  /**
   * 表格定义JSON
   */
  tableDefinition: {
    /**
     * 表格配置
     */
    config?: JsonTableConfig
    
    /**
     * 列定义
     */
    columns: JsonTableColumn[]
    
    /**
     * 操作定义
     */
    actions?: JsonTableAction[]
    
    /**
     * 过滤器定义
     */
    filters?: TableFilter[]
  }
  
  /**
   * 数据源
   */
  data: any[]
  
  /**
   * 自定义渲染器映射
   */
  renderers?: Record<string, (value: any, row: any) => ReactNode>
  
  /**
   * 自定义格式化函数映射
   */
  formatters?: Record<string, (value: any, row: any) => string>
  
  /**
   * 行操作回调
   */
  onRowAction?: (row: any, action: string, actionType: string) => void
  
  /**
   * 行选择变化回调
   */
  onSelectionChange?: (selectedRows: any[]) => void
  
  /**
   * 是否加载中
   */
  loading?: boolean
  
  /**
   * 刷新回调
   */
  onRefresh?: () => void
  
  /**
   * 添加回调
   */
  onAdd?: () => void
  
  /**
   * 导出回调
   */
  onExport?: (format: "csv" | "excel" | "pdf") => void
  
  /**
   * 自定义样式类
   */
  className?: string
}

// ============================================================================
// 常量导出
// ============================================================================

/**
 * 支持的表格尺寸
 */
export const DATA_TABLE_SIZES = ['sm', 'md', 'lg'] as const

/**
 * 支持的表格变体
 */
export const DATA_TABLE_VARIANTS = ['default', 'striped', 'bordered', 'compact'] as const

/**
 * 支持的过滤器类型
 */
export const FILTER_TYPES = ['select', 'multi-select', 'date-range', 'search'] as const

/**
 * 支持的操作类型
 */
export const ACTION_TYPES = ['view', 'edit', 'delete', 'custom'] as const

/**
 * 支持的导出格式
 */
export const EXPORT_FORMATS = ['csv', 'excel', 'pdf'] as const

/**
 * 默认分页选项
 */
export const DEFAULT_PAGE_SIZE_OPTIONS = [10, 20, 30, 40, 50] as const

/**
 * 默认表格配置
 */
export const DEFAULT_TABLE_CONFIG = {
  showSelection: true,
  showPagination: true,
  showSearch: true,
  showFilters: true,
  showColumnVisibility: true,
  showRowActions: true,
  pageSize: 10,
  pageSizeOptions: DEFAULT_PAGE_SIZE_OPTIONS,
  searchPlaceholder: "搜索...",
  emptyMessage: "暂无数据",
  loadingMessage: "加载中...",
  size: 'md' as const,
  variant: 'default' as const,
  striped: false,
  bordered: false,
  compact: false,
} satisfies Partial<TableConfig>

/**
 * 默认工具栏配置
 */
export const DEFAULT_TOOLBAR_CONFIG = {
  showSearch: true,
  showFilters: true,
  showColumnVisibility: true,
  showExport: false,
  showRefresh: false,
  showAdd: false,
} satisfies Partial<AdvancedDataTableProps<any, any>>

/**
 * 默认操作配置
 */
export const DEFAULT_ACTION_CONFIG = {
  type: 'custom' as const,
  variant: 'ghost' as const,
  size: 'sm' as const,
} satisfies Partial<TableAction<any>>
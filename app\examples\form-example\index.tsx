"use client"

import * as React from "react"
import { useState } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "@/hooks/use-toast"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Checkbox } from "@/components/ui/checkbox"
import { format } from "date-fns"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { CalendarIcon, Plus } from "lucide-react"
import { cn } from "@/lib/utils"
import { Form as CustomForm, FormDialog } from "@/components/common-custom/form"

// 基本表单架构
const basicFormSchema = z.object({
  name: z.string().min(2, {
    message: "名称至少需要2个字符。",
  }),
  email: z.string().email({
    message: "请输入有效的电子邮件地址。",
  }),
  department: z.string({
    required_error: "请选择部门。",
  }),
  bio: z.string().min(10, {
    message: "简介至少需要10个字符。",
  }).max(500, {
    message: "简介不能超过500个字符。",
  }).optional(),
})

// 高级表单架构
const advancedFormSchema = z.object({
  fullName: z.string().min(2, { message: "姓名至少需要2个字符" }),
  email: z.string().email({ message: "请输入有效的电子邮件地址" }),
  dob: z.date({ required_error: "请选择出生日期" }),
  gender: z.string({ required_error: "请选择性别" }),
  address: z.object({
    street: z.string().min(1, { message: "请输入街道地址" }),
    city: z.string().min(1, { message: "请输入城市" }),
    state: z.string().min(1, { message: "请输入省份" }),
    zipCode: z.string().min(1, { message: "请输入邮政编码" }),
  }),
  contactPreference: z.string({ required_error: "请选择联系方式偏好" }),
  notifications: z.boolean().default(false),
  termsAccepted: z.boolean().refine(val => val === true, {
    message: "您必须接受服务条款才能继续",
  }),
})

// 登录表单架构
const loginFormSchema = z.object({
  email: z.string().email({ message: "请输入有效的电子邮件地址" }),
  password: z.string().min(8, { message: "密码至少需要8个字符" }),
  rememberMe: z.boolean().default(false),
})

// 表单对话框架构
const dialogFormSchema = z.object({
  name: z.string().min(2, "姓名至少需要2个字符"),
  email: z.string().email("请输入有效的邮箱地址"),
  role: z.string().min(1, "请选择角色"),
  active: z.boolean().optional(),
  bio: z.string().optional(),
})

export function FormExample() {
  const [activeTab, setActiveTab] = useState("basic")

  // 基本表单状态
  const [basicFormValues, setBasicFormValues] = useState({
      name: "",
      email: "",
    department: "",
      bio: "",
  })
  
  const [basicFormErrors, setBasicFormErrors] = useState<Record<string, string>>({})
  const [isBasicSubmitting, setIsBasicSubmitting] = useState(false)
  const [isBasicSuccess, setIsBasicSuccess] = useState(false)

  // 对话框表单状态
  const [dialogOpen, setDialogOpen] = useState(false)
  const [dialogFormValues, setDialogFormValues] = useState({
    name: "",
      email: "",
    role: "",
    active: false,
    bio: "",
  })
  const [dialogFormErrors, setDialogFormErrors] = useState<Record<string, string>>({})
  const [isDialogSubmitting, setIsDialogSubmitting] = useState(false)
  const [formSubmitted, setFormSubmitted] = useState<Record<string, any> | null>(null)

  // 处理基本表单字段变更
  const handleBasicFieldChange = (name: string, value: any) => {
    setBasicFormValues(prev => ({
      ...prev,
      [name]: value
    }))
    
    // 清除错误
    if (basicFormErrors[name]) {
      setBasicFormErrors(prev => {
        const newErrors = {...prev}
        delete newErrors[name]
        return newErrors
      })
    }
  }

  // 处理对话框表单字段变更
  const handleDialogFieldChange = (name: string, value: any) => {
    setDialogFormValues(prev => ({
      ...prev,
      [name]: value
    }))
    
    // 清除错误
    if (dialogFormErrors[name]) {
      setDialogFormErrors(prev => {
        const newErrors = {...prev}
        delete newErrors[name]
        return newErrors
      })
    }
  }

  // 验证基本表单
  const validateBasicForm = () => {
    try {
      basicFormSchema.parse(basicFormValues)
      setBasicFormErrors({})
      return true
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors: Record<string, string> = {}
        error.errors.forEach(err => {
          if (err.path[0]) {
            errors[err.path[0] as string] = err.message
          }
        })
        setBasicFormErrors(errors)
      }
      return false
    }
  }

  // 验证对话框表单
  const validateDialogForm = () => {
    try {
      dialogFormSchema.parse(dialogFormValues)
      setDialogFormErrors({})
      return true
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors: Record<string, string> = {}
        error.errors.forEach(err => {
          if (err.path[0]) {
            errors[err.path[0] as string] = err.message
          }
        })
        setDialogFormErrors(errors)
      }
      return false
    }
  }

  // 提交基本表单
  const handleBasicSubmit = async (values: any) => {
    if (!validateBasicForm()) return
    
    setIsBasicSubmitting(true)
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      setIsBasicSuccess(true)
    toast({
      title: "基本表单提交成功",
      description: (
        <pre className="mt-2 w-[340px] rounded-md bg-slate-950 p-4">
          <code className="text-white">{JSON.stringify(values, null, 2)}</code>
        </pre>
      ),
    })
      
      // 重置成功状态
      setTimeout(() => {
        setIsBasicSuccess(false)
      }, 2000)
    } catch (error) {
      console.error(error)
    } finally {
      setIsBasicSubmitting(false)
    }
  }

  // 提交对话框表单
  const handleDialogSubmit = async (values: any) => {
    if (!validateDialogForm()) return
    
    setIsDialogSubmitting(true)
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      setFormSubmitted(values)
      setDialogOpen(false)
    toast({
        title: "表单对话框提交成功",
        description: "表单数据已处理完成。",
      })
    } catch (error) {
      console.error(error)
    } finally {
      setIsDialogSubmitting(false)
    }
  }

  return (
    <div className="w-full max-w-4xl mx-auto">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-2 mb-8">
          <TabsTrigger value="basic">基本表单</TabsTrigger>
          <TabsTrigger value="dialog">表单对话框</TabsTrigger>
        </TabsList>
        
        {/* 基本表单 */}
        <TabsContent value="basic">
          <CustomForm
            title="个人信息"
            description="填写您的个人信息用于系统识别。"
            values={basicFormValues}
            errors={basicFormErrors}
            onChange={handleBasicFieldChange}
            onSubmit={handleBasicSubmit}
            isLoading={isBasicSubmitting}
            isSuccess={isBasicSuccess}
            fields={[
              {
                name: "name",
                label: "姓名",
                type: "text",
                placeholder: "张三",
                required: true,
                description: "这将是您的显示名称。"
              },
              {
                name: "email",
                label: "电子邮件",
                type: "email",
                placeholder: "<EMAIL>",
                required: true,
                description: "用于接收系统通知。"
              },
              {
                name: "department",
                label: "部门",
                type: "select",
                required: true,
                options: [
                  { label: "研发部", value: "engineering" },
                  { label: "市场部", value: "marketing" },
                  { label: "销售部", value: "sales" },
                  { label: "客服部", value: "support" }
                ],
                description: "选择您所属的部门。"
              },
              {
                name: "bio",
                label: "个人简介",
                type: "textarea",
                placeholder: "简单介绍一下自己...",
                description: "您可以介绍一下自己的专业背景和技能。"
              }
            ]}
          />
        </TabsContent>
        
        {/* 表单对话框 */}
        <TabsContent value="dialog">
          <div className="space-y-4">
            <Button onClick={() => setDialogOpen(true)}>
              <Plus className="w-4 h-4 mr-2" />
              添加用户
                              </Button>

            <FormDialog
              open={dialogOpen}
              onOpenChange={setDialogOpen}
              title="添加用户"
              description="添加一个新用户到系统中"
              values={dialogFormValues}
              errors={dialogFormErrors}
              onChange={handleDialogFieldChange}
              onSubmit={handleDialogSubmit}
              isLoading={isDialogSubmitting}
              submitLabel="保存"
              cancelLabel="取消"
              onCancel={() => setDialogOpen(false)}
              fields={[
                {
                  name: "name",
                  label: "姓名",
                  type: "text",
                  placeholder: "请输入姓名",
                  required: true,
                },
                {
                  name: "email",
                  label: "邮箱",
                  type: "email",
                  placeholder: "请输入邮箱",
                  required: true,
                },
                {
                  name: "role",
                  label: "角色",
                  type: "select",
                  placeholder: "请选择角色",
                  required: true,
                  options: [
                    { label: "管理员", value: "admin" },
                    { label: "编辑", value: "editor" },
                    { label: "用户", value: "user" },
                  ],
                },
                {
                  name: "active",
                  label: "状态",
                  type: "switch",
                  placeholder: "是否启用该用户",
                  description: "控制用户是否可以登录系统",
                },
                {
                  name: "bio",
                  label: "简介",
                  type: "textarea",
                  placeholder: "请输入简介",
                  rows: 3,
                },
              ]}
            />

            {formSubmitted && (
              <div className="p-4 border rounded-md mt-4">
                <h4 className="font-medium mb-2">提交的数据</h4>
                <pre className="text-sm bg-muted p-2 rounded">
                  {JSON.stringify(formSubmitted, null, 2)}
                </pre>
              </div>
            )}
              </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

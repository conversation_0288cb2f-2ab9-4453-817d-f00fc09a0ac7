/**
 * 团队管理的模拟数据
 */
import { Team } from '@/types/models';

// 模拟团队数据
export const teamsMock: Team[] = [
  {
    id: 1,
    teamCode: "team-1",
    teamName: "研发团队",
    teamLogo: "/logos/dev-team.png",
    teamThemeColor: "#408EF5",
    teamType: "1",
    status: 1,
    description: "负责产品研发和技术实现",
    privateTag: 0,
    createTime: "2024-01-10T09:00:00Z",
    memberCount: 12
  },
  {
    id: 2,
    teamCode: "team-2",
    teamName: "产品团队",
    teamLogo: "/logos/product-team.png",
    teamThemeColor: "#34D399",
    teamType: "1",
    status: 1,
    description: "负责产品规划和需求管理",
    privateTag: 0,
    createTime: "2024-01-12T10:30:00Z",
    memberCount: 8
  },
  {
    id: 3,
    teamCode: "team-3",
    teamName: "设计团队",
    teamLogo: "/logos/design-team.png",
    teamThemeColor: "#A855F7",
    teamType: "1",
    status: 1,
    description: "负责产品UI/UX设计",
    privateTag: 0,
    createTime: "2024-01-15T14:15:00Z",
    memberCount: 6
  },
  {
    id: 4,
    teamCode: "team-4",
    teamName: "测试团队",
    teamLogo: "/logos/qa-team.png",
    teamThemeColor: "#F59E0B",
    teamType: "1",
    status: 1,
    description: "负责质量保证和测试",
    privateTag: 0,
    createTime: "2024-01-20T11:45:00Z",
    memberCount: 7
  },
  {
    id: 5,
    teamCode: "team-5",
    teamName: "运维团队",
    teamLogo: "/logos/ops-team.png", 
    teamThemeColor: "#EC4899",
    teamType: "1",
    status: 1,
    description: "负责系统运维和部署",
    privateTag: 0,
    createTime: "2024-01-25T08:20:00Z",
    memberCount: 5
  }
];

// 获取团队名称的方法，根据ID返回名称
export const getTeamNameById = (teamId: string | number): string => {
  const id = typeof teamId === 'string' ? parseInt(teamId.replace('team-', '')) : teamId;
  const team = teamsMock.find(team => team.id === id);
  return team ? team.teamName : '未知团队';
}; 
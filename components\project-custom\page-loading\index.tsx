"use client"

import { useNavigation } from "@/components/providers/navigation-provider"
import { useSidebar } from "@/components/ui/sidebar"
import { usePathname, useSearchParams } from "next/navigation"
import { PageLoading as CommonPageLoading } from "@/components/common-custom/page-loading"

interface ProjectPageLoadingProps {
  /**
   * @deprecated 标记是否为新组件，仅用于界面展示
   */
  isNew?: boolean
}

/**
 * 项目特定的页面加载组件
 * 集成了navigation-provider和sidebar状态
 */
export function PageLoading({ isNew = true }: ProjectPageLoadingProps) {
  const { isPageTransition, shouldShowTransition, visitedPaths } = useNavigation()
  const { state, isMobile } = useSidebar()
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const currentPath = pathname + searchParams.toString()
  
  // 检查当前路径是否已访问过
  const isCurrentPathVisited = visitedPaths.has(currentPath)
  
  // 确定是否应该显示加载状态
  const isLoading = shouldShowTransition && isPageTransition && !isCurrentPathVisited
  
  return (
    <CommonPageLoading
      isLoading={isLoading}
      considerSidebar={true}
      sidebarState={state as "expanded" | "collapsed" | "none"}
      isMobile={isMobile}
      isNew={isNew}
    />
  )
} 
"use client"

import React from "react"
import { cn } from "@/lib/utils"
import { CalendarDayProps } from "./types"
import { CalendarEvent } from "./calendar-event"

/**
 * 日历日期组件
 * 
 * 用于渲染日历中的单个日期，支持事件显示和交互
 */
export function CalendarDay({
  date,
  events = [],
  selected,
  disabled,
  isToday,
  isCurrentMonth,
  onClick,
  onEventClick,
  maxEvents = 3,
  className,
  renderEvent,
  ...props
}: CalendarDayProps) {

  // 检查日期是否有效
  if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
    return null
  }

  // 处理日期点击
  const handleDateClick = (e: React.MouseEvent) => {
    e.preventDefault()
    if (!disabled && onClick) {
      onClick(date)
    }
  }
  
  // 处理事件点击
  const handleEventClick = (event: React.MouseEvent, calendarEvent: any) => {
    event.stopPropagation()
    if (onEventClick) {
      onEventClick(calendarEvent, date)
    }
  }
  
  // 显示的事件列表（限制数量）
  const displayEvents = events.slice(0, maxEvents)
  const hasMoreEvents = events.length > maxEvents
  
  return (
    <div
      className={cn(
        "calendar-day relative",
        "h-9 w-9 p-0 font-normal",
        "flex flex-col items-center justify-start",
        "cursor-pointer transition-colors",
        {
          "bg-primary text-primary-foreground": selected,
          "bg-accent text-accent-foreground": isToday && !selected,
          "text-muted-foreground": !isCurrentMonth,
          "opacity-50 cursor-not-allowed": disabled,
          "hover:bg-accent hover:text-accent-foreground": !disabled && !selected,
        },
        className
      )}
      onClick={handleDateClick}
      {...props}
    >
      {/* 日期数字 */}
      <span className={cn(
        "text-sm leading-none",
        {
          "text-xs": events.length > 0,
        }
      )}>
        {date.getDate()}
      </span>
      
      {/* 事件列表 */}
      {events.length > 0 && (
        <div className="absolute top-6 left-0 right-0 space-y-0.5 px-0.5">
          {displayEvents.map((event, index) => (
            <CalendarEvent
              key={`${event.id}-${index}`}
              event={event}
              date={date}
              onClick={handleEventClick}
              className="text-xs"
              showDetails={false}
              enableTooltip={true}
              renderEvent={renderEvent}
            />
          ))}
          
          {/* 更多事件指示器 */}
          {hasMoreEvents && (
            <div className="text-xs text-muted-foreground text-center">
              +{events.length - maxEvents}
            </div>
          )}
        </div>
      )}
    </div>
  )
}

CalendarDay.displayName = "CalendarDay"

"use client";

import { useState, useEffect, useRef } from "react";
import { notFound, useRouter, useSearchParams } from "next/navigation";
import { use } from "react";
import { HeaderWithBreadcrumb } from "@/components/project-custom/breadcrumb";
import { BackButton } from "@/components/common-custom/back-button";
import { 
  ShieldCheck, 
  Calendar, 
  Clock, 
  FileText, 
  Users, 
  Tag,
  Save,
  RotateCcw,
  Loader2,
  Pencil,
  UserPlus,
  ToggleRight,
  Ban,
  Settings
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { formatDate } from "@/lib/utils";
import { toast } from "sonner";
import { 
  getRoleByIdRequest, 
  getRolePermissionsRequest, 
  getRoleUsersRequest,
  updateRoleRequest
} from "@/services/api/roleRequestApi";
import RolePermissions from "@/components/pages/roles/role-permissions";
import RoleUsers from "@/components/pages/roles/role-users";
import { Role } from "@/types/models";

interface RoleDetailPageProps {
  params: Promise<{ id: string }> | { id: string };
}

export default function RoleDetailPage({ params }: RoleDetailPageProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const mode = searchParams.get("mode") === "edit" ? "edit" : "view";
  
  // 使用React.use解包params
  const resolvedParams = params instanceof Promise ? use(params) : params;
  const roleId = resolvedParams.id;
  
  const [role, setRole] = useState<Role | null>(null);
  const [users, setUsers] = useState<any[]>([]);
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  const [originalPermissions, setOriginalPermissions] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const dataFetchedRef = useRef(false); // 添加引用来跟踪是否已经获取过数据
  
  // 表单状态
  const [formData, setFormData] = useState({
    roleName: "",
    description: "",
    roleCode: "",
    status: 1
  });
  
  // 处理表单输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  // 重置表单
  const handleReset = () => {
    if (role) {
      setFormData({
        roleName: role.roleName || "",
        description: role.description || "",
        roleCode: role.roleCode || "",
        status: role.status
      });
      setSelectedPermissions([...originalPermissions]);
    }
    toast.info("已重置所有更改");
  };
  
  // 加载角色和权限数据
  useEffect(() => {
    // 防止重复获取数据
    if (dataFetchedRef.current) return;
    dataFetchedRef.current = true; // 立即标记为已获取，防止多次调用
    
    const loadData = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        // 获取角色信息
        const roleData = await getRoleByIdRequest(roleId);
        if (!roleData) {
          notFound();
        }
        setRole(roleData);
        
        // 设置表单数据
        setFormData({
          roleName: roleData.roleName || "",
          description: roleData.description || "",
          roleCode: roleData.roleCode || "",
          status: roleData.status
        });
        
        // 获取角色权限
        const permissionIds = roleData.selectedPermissions || [];
        setSelectedPermissions(permissionIds);
        setOriginalPermissions(permissionIds);
        
        // 获取角色关联的用户
        const userData = await getRoleUsersRequest(roleId);
        setUsers(userData);
      } catch (err) {
        console.error("加载数据失败", err);
        setError("加载数据失败，请稍后重试");
        dataFetchedRef.current = false; // 加载失败时重置标记，允许重试
      } finally {
        setIsLoading(false);
      }
    };
    
    if (roleId) {
      loadData();
    }
  }, [roleId]);
  
  // 是否有变更
  const hasChanges = () => {
    if (!role) return false;
    
    const hasPermissionChanges = 
      selectedPermissions.length !== originalPermissions.length ||
      selectedPermissions.some(id => !originalPermissions.includes(id)) ||
      originalPermissions.some(id => !selectedPermissions.includes(id));
    
    const hasFormChanges = 
      formData.roleName !== role.roleName ||
      formData.description !== role.description ||
      formData.status !== role.status;
    
    return hasPermissionChanges || hasFormChanges;
  };
  
  // 更新选中的权限
  const updateSelectedPermissions = (permissions: string[]) => {
    setSelectedPermissions(permissions);
  };
  
  // 保存所有更改
  const handleSaveChanges = async () => {
    if (!role) return;
    
    // 验证表单数据
    if (!formData.roleName.trim()) {
      toast.error("角色名称不能为空");
      return;
    }
    
    setIsSaving(true);
    
    try {
      // 更新角色信息和权限
      await updateRoleRequest({
        id: role.id,
        teamCode: role.teamCode,
        roleName: formData.roleName.trim(),
        roleCode: role.roleCode,
        description: formData.description.trim(),
        status: formData.status,
        permissionCodes: selectedPermissions // 一并提交权限数据
      });
      
      setOriginalPermissions([...selectedPermissions]);
      
      toast.success("角色信息已成功保存");
      
      // 更新本地角色数据
      setRole({
        ...role,
        roleName: formData.roleName.trim(),
        description: formData.description.trim(),
        status: formData.status
      });
      
      // 如果当前是编辑模式，保存后切换到查看模式
      if (mode === "edit") {
        router.push(`/settings/roles/${role.id}`);
      } else {
        // 不要使用router.refresh()，因为它会导致页面重新渲染并再次调用API
        // 而是重置数据获取标志，以便下次导航到此页面时重新获取数据
        dataFetchedRef.current = false;
      }
    } catch (error) {
      console.error("保存失败", error);
      toast.error("保存失败，请稍后重试");
    } finally {
      setIsSaving(false);
    }
  };
  
  // 用户列表刷新
  const handleUsersChange = async () => {
    if (!role) return;
    
    try {
      const userData = await getRoleUsersRequest(String(role.id));
      setUsers(userData);
    } catch (error) {
      console.error("获取用户列表失败", error);
      toast.error("获取用户列表失败，请稍后重试");
    }
  };
  
  // 切换到编辑模式
  const switchToEditMode = () => {
    router.push(`/settings/roles/${roleId}?mode=edit`);
  };
  
  // 返回角色列表
  const goBack = () => {
    router.push("/settings/roles");
  };
  
  if (isLoading) {
    return (
      <div className="flex flex-col min-h-screen">
        <HeaderWithBreadcrumb items={[
          { label: "设置", href: "/settings" },
          { label: "角色管理", href: "/settings/roles" },
          { label: "加载中..." }
        ]} />
        <main className="flex-1">
          <div className="container mx-auto px-6 py-6 max-w-7xl">
            <div className="flex items-center justify-center h-[400px]">
              <div className="flex flex-col items-center gap-2">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <p className="text-sm text-muted-foreground">正在加载角色信息...</p>
              </div>
            </div>
          </div>
        </main>
      </div>
    );
  }
  
  if (!role) {
    notFound();
  }
  
  // 面包屑数据
  const breadcrumbItems = [
    { label: "设置", href: "/settings" },
    { label: "角色管理", href: "/settings/roles" },
    { label: role.roleName }
  ];

  return (
    <div className="flex flex-col min-h-screen">
      <HeaderWithBreadcrumb items={breadcrumbItems} />
      <main className="flex-1 bg-muted/55">
        <div className="container mx-auto py-4 max-w-7xl">
          {/* 页面头部 */}
          <div className="bg-background rounded-lg shadow-sm mb-4 p-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-3">
                <BackButton
                  href="/settings/roles"
                  onNavigate={(href) => router.push(href)}
                />
                <div>
                  <h1 className="text-xl font-semibold flex items-center gap-2">
                    {mode === "edit" ? "编辑角色" : role.roleName}
                  </h1>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                {mode === "edit" ? (
                  <>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={handleReset}
                      disabled={isSaving || !hasChanges()}
                      className="cursor-pointer"
                    >
                      <RotateCcw className="h-4 w-4 mr-2" />
                      重置更改
                    </Button>
                    <Button 
                      size="sm"
                      onClick={handleSaveChanges}
                      disabled={isSaving || !hasChanges()}
                      className="cursor-pointer"
                    >
                      {isSaving ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          保存中...
                        </>
                      ) : (
                        <>
                          <Save className="mr-2 h-4 w-4" />
                          保存更改
                        </>
                      )}
                    </Button>
                  </>
                ) : (
                  <>
                    <Button 
                      size="sm"
                      onClick={switchToEditMode}
                      className="cursor-pointer"
                    >
                      <Pencil className="h-4 w-4 mr-2" />
                      编辑角色
                    </Button>
                  </>
                )}
              </div>
            </div>
          </div>
          
          {/* 主要内容区域 */}
          <div className="space-y-4 ">
            {/* 基本信息区域 */}
            <div className="bg-background rounded-lg shadow-sm p-4 mb-4 px-8">
              <h2 className="text-xl font-semibold mb-3 flex items-center gap-2">
                <Settings className="h-5 w-5 text-primary" />
                基本信息
              </h2>
              <div className="space-y-3">

                <div className="space-y-2">
                  <Label htmlFor="roleCode">
                    角色编码 <span className="text-destructive">*</span>
                  </Label>
                  {mode === "edit" ? (
                    <Input
                      id="roleCode"
                      name="roleCode"
                      value={formData.roleCode}
                      readOnly
                      placeholder="角色编码（系统自动生成）"
                      className="max-w-md bg-muted/50"
                    />
                  ) : (
                    <div className="text-base pl-3 py-2 max-w-md bg-muted/30 rounded-md">
                      {role.roleCode || "--"}
                    </div>
                  )}
                  <p className="text-xs text-muted-foreground">
                    角色编码为系统内部标识，无法修改
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="roleName">
                    角色名称 <span className="text-destructive">*</span>
                  </Label>
                  {mode === "edit" ? (
                    <Input
                      id="roleName"
                      name="roleName"
                      value={formData.roleName}
                      onChange={handleInputChange}
                      placeholder="请输入角色名称"
                      required
                      className="max-w-md"
                    />
                  ) : (
                    <div className="text-base pl-3 py-2 max-w-md bg-muted/30 rounded-md">
                      {role.roleName || "--"}
                    </div>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="status">
                    角色状态 <span className="text-destructive">*</span>
                  </Label>
                  {mode === "edit" ? (
                    <div className="flex items-center gap-2 mt-1">
                      <Switch
                        id="status"
                        checked={formData.status === 1}
                        onCheckedChange={(checked) =>
                          setFormData(prev => ({
                            ...prev,
                            status: checked ? 1 : 0
                          }))
                        }
                        className="h-5 w-10"
                      />
                      <span className="text-sm text-muted-foreground">
                        {formData.status === 1 ? "启用" : "禁用"}
                      </span>
                    </div>
                  ) : (
                    <Badge 
                      variant={role.status === 1 ? "default" : "secondary"}
                      className="ml-1"
                    >
                      {role.status === 1 ? "已启用" : "已禁用"}
                    </Badge>
                  )}
                  <p className="text-xs text-muted-foreground">
                    禁用后，拥有此角色的用户将失去相关权限
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">
                    角色描述 <span className="text-destructive">*</span>
                  </Label>
                  {mode === "edit" ? (
                    <Textarea
                      id="description"
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      placeholder="请输入角色描述"
                      rows={4}
                      className="max-w-2xl"
                    />
                  ) : (
                    <div className="text-base pl-3 py-2 max-w-2xl min-h-[80px] bg-muted/30 rounded-md">
                      {role.description || "该角色暂无描述信息"}
                    </div>
                  )}
                </div>
                
                {mode !== "edit" && (
                  <div className="mt-3 flex items-center gap-2 text-xs text-muted-foreground">
                    <Clock className="h-3.5 w-3.5"/>
                    <span>创建时间: {formatDate(role.createTime || '')}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Tab 内容区域 */}
            <div className="bg-background rounded-lg shadow-sm overflow-hidden">
              <Tabs defaultValue="permissions" className="w-full">
                <div className="px-8 pt-4">
                  <TabsList className="grid w-full max-w-md grid-cols-2">
                    <TabsTrigger value="permissions" className="cursor-pointer">
                      <ShieldCheck className="h-4 w-4 mr-2" />
                      权限设置
                    </TabsTrigger>
                    <TabsTrigger value="users" className="cursor-pointer">
                      <Users className="h-4 w-4 mr-2" />
                      关联用户
                    </TabsTrigger>
                  </TabsList>
                </div>
                
                <TabsContent value="permissions" className="mt-4 px-4 pb-4">
                  <RolePermissions 
                    roleId={roleId}
                    selectedPermissions={selectedPermissions}
                    onPermissionsChange={updateSelectedPermissions}
                    viewOnly={mode !== "edit"} // 非编辑模式下只读
                  />
                </TabsContent>
                
                <TabsContent value="users" className="mt-4 px-4 pb-4">
                  <RoleUsers 
                    roleId={String(role.id)} 
                    users={users}
                    readOnly={mode !== "edit"}
                    onUsersChange={handleUsersChange}
                  />
                </TabsContent>
              </Tabs>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
} 
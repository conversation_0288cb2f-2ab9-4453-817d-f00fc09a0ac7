"use client"

import { ReactNode } from "react"
import { But<PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { X } from "lucide-react"
import { cn } from "@/lib/utils"
import type { ModalProps, ModalButtonProps } from "@/types/modal"

// 按钮处理组件，封装事件处理
function FooterButton({ button, index }: { button: ModalButtonProps, index: number }) {
  return (
    <Button
      key={index}
      variant={button.variant || "outline"}
      onClick={(e) => {
        e.stopPropagation();
        if (typeof button.onClick === 'function') {
          button.onClick(e);
        }
      }}
      disabled={button.disabled}
      className={button.className}
    >
      {button.icon && <button.icon className="mr-2 h-4 w-4" />}
      {button.label}
    </Button>
  )
}

/**
 * 模态框组件
 */
export function Modal({
  open,
  onOpenChange,
  title,
  description,
  children,
  footerButtons,
  showCloseButton = true,
  onClose,
  confirmText = "确认",
  cancelText = "取消",
  onConfirm,
  onCancel,
  className,
  maxWidth = "sm:max-w-[425px]",
  icon,
  renderFooter,
}: ModalProps) {
  const handleClose = () => {
    if (onClose) {
      onClose()
    }
    if (onOpenChange) {
      onOpenChange(false)
    }
  }

  const handleConfirm = async () => {
    if (onConfirm) {
      await onConfirm()
    }
    if (onOpenChange) {
      onOpenChange(false)
    }
  }

  const handleCancel = () => {
    if (onCancel) {
      onCancel()
    }
    if (onOpenChange) {
      onOpenChange(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className={cn(maxWidth, className)}>
        {showCloseButton && (
          <button
            onClick={handleClose}
            className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="h-4 w-4" />
            <span className="sr-only">关闭</span>
          </button>
        )}

        {(title || description) && (
          <DialogHeader>
            {icon && <div className="flex justify-center mb-4">{icon}</div>}
            {title && <DialogTitle>{title}</DialogTitle>}
            {description && <DialogDescription>{description}</DialogDescription>}
          </DialogHeader>
        )}

        <div>{children}</div>

        {renderFooter ? (
          renderFooter()
        ) : (
          <DialogFooter>
            {footerButtons ? (
              footerButtons.map((button, index) => (
                <FooterButton key={index} button={button} index={index} />
              ))
            ) : (
              <>
                {cancelText && (
                  <Button variant="outline" onClick={handleCancel}>
                    {cancelText}
                  </Button>
                )}
                {confirmText && (
                  <Button onClick={handleConfirm}>
                    {confirmText}
                  </Button>
                )}
              </>
            )}
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  )
} 
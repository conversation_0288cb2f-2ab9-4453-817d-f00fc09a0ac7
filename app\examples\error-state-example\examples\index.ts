/**
 * 错误状态组件示例代码集合
 * 
 * 按照新规范管理示例代码，避免在页面文件中硬编码
 */

import React from "react"
import { ErrorCard, PageErrorState } from "@/components/common-custom/error-state"
import { AlertOctagon, RefreshCw, Home, ArrowLeft, Wifi, Server } from "lucide-react"

// ============================================================================
// 基础错误卡片示例
// ============================================================================

export const basicErrorCardExample = {
  id: "basic-error-card",
  title: "基础错误卡片",
  description: "展示基础的错误状态卡片",
  code: `
import React from "react";
import { ErrorCard } from "@/components/common-custom/error-state";
import { AlertOctagon, RefreshCw } from "lucide-react";

function BasicErrorCardExample() {
  return (
    <div className="space-y-6">
      <ErrorCard
        title="加载失败"
        description="无法加载数据，请稍后再试"
        actions={[
          {
            label: "重试",
            onClick: () => console.log("重试")
          }
        ]}
      />
      
      <ErrorCard
        icon={<AlertOctagon className="h-12 w-12 text-destructive" />}
        title="服务器错误"
        description="服务器暂时不可用，我们正在努力修复中"
        severity="high"
        actions={[
          {
            label: "返回首页",
            onClick: () => console.log("返回首页")
          }
        ]}
      />
    </div>
  );
}

render(<BasicErrorCardExample />);
  `,
  scope: { ErrorCard, AlertOctagon, RefreshCw, React },
}

// ============================================================================
// 页面级错误状态示例
// ============================================================================

export const pageErrorStateExample = {
  id: "page-error-state",
  title: "页面级错误状态",
  description: "用于整个页面的错误状态展示",
  code: `
import React from "react";
import { PageErrorState } from "@/components/common-custom/error-state";
import { Server, Home } from "lucide-react";

function PageErrorStateExample() {
  return (
    <PageErrorState
      icon={<Server className="h-16 w-16 text-muted-foreground" />}
      title="页面加载失败"
      description="抱歉，页面暂时无法访问，请稍后重试"
      actions={[
        {
          label: "返回首页",
          onClick: () => console.log("返回首页"),
          variant: "default"
        },
        {
          label: "重新加载",
          onClick: () => console.log("重新加载"),
          variant: "outline"
        }
      ]}
    />
  );
}

render(<PageErrorStateExample />);
  `,
  scope: { PageErrorState, Server, Home, React },
}



// ============================================================================
// 网络错误示例
// ============================================================================

export const networkErrorExample = {
  id: "network-error",
  title: "网络错误状态",
  description: "网络连接问题的错误状态",
  code: `
import React from "react";
import { ErrorCard } from "@/components/common-custom/error-state";
import { Wifi, RefreshCw } from "lucide-react";

function NetworkErrorExample() {
  return (
    <ErrorCard
      icon={<Wifi className="h-12 w-12 text-muted-foreground" />}
      title="网络连接失败"
      description="请检查您的网络连接，然后重试"
      severity="medium"
      actions={[
        {
          label: "重新连接",
          onClick: () => console.log("重新连接")
        }
      ]}
    />
  );
}

render(<NetworkErrorExample />);
  `,
  scope: { ErrorCard, Wifi, RefreshCw, React },
}

// ============================================================================
// 统一导出所有示例
// ============================================================================

export const allExamples = [
  basicErrorCardExample,
  pageErrorStateExample,
  networkErrorExample,
]

// ============================================================================
// 按类别导出示例
// ============================================================================

export const basicExamples = [basicErrorCardExample]
export const pageExamples = [pageErrorStateExample]
export const scenarioExamples = [networkErrorExample]

// ============================================================================
// 示例元数据
// ============================================================================

export const exampleMetadata = {
  total: allExamples.length,
  categories: {
    basic: basicExamples.length,
    page: pageExamples.length,
    scenarios: scenarioExamples.length,
  },
  tags: ["error", "state", "feedback", "network", "server", "failure"],
  lastUpdated: new Date().toISOString().split('T')[0],
}

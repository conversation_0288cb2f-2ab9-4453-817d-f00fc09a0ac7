/**
 * 菜单组件示例代码集合
 * 
 * 按照新规范管理示例代码，避免在页面文件中硬编码
 */

import React from "react"
import { 
  DropdownMenuButton, 
  MoreMenuButton, 
  ActionMenuButton, 
  ContextMenu,
  MenuItem
} from "@/components/common-custom/menu"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { 
  Settings, 
  User, 
  LogOut, 
  Mail, 
  MessageSquare, 
  Check, 
  CreditCard, 
  Download, 
  Pencil, 
  Share, 
  Trash, 
  Copy, 
  MoreHorizontal, 
  Eye, 
  FileEdit, 
  FilePlus, 
  FileX, 
  FileText,
  Bell,
  Menu as MenuIcon
} from "lucide-react"

// ============================================================================
// 基础下拉菜单示例
// ============================================================================

export const basicDropdownExample = {
  id: "basic-dropdown",
  title: "基础下拉菜单",
  description: "展示基础的下拉菜单功能",
  code: `
import React from "react";
import { DropdownMenuButton } from "@/components/common-custom/menu";
import { User, Settings, LogOut } from "lucide-react";

function BasicDropdownExample() {
  const userMenuItems = [
    {
      label: "个人信息",
      icon: User,
      onClick: () => console.log("查看个人信息")
    },
    {
      label: "设置",
      icon: Settings,
      onClick: () => console.log("打开设置")
    },
    { type: "separator" },
    {
      label: "退出登录",
      icon: LogOut,
      onClick: () => console.log("退出登录")
    }
  ];

  return (
    <div className="space-y-4">
      <DropdownMenuButton
        label="用户菜单"
        icon={User}
        items={userMenuItems}
        variant="outline"
        onSelect={(value) => console.log("选择了:", value)}
      />
      
      <DropdownMenuButton
        label="操作"
        items={userMenuItems}
        variant="default"
        onSelect={(value) => console.log("选择了:", value)}
      />
    </div>
  );
}

render(<BasicDropdownExample />);
  `,
  scope: { DropdownMenuButton, User, Settings, LogOut, React },
}

// ============================================================================
// 更多菜单示例
// ============================================================================

export const moreMenuExample = {
  id: "more-menu",
  title: "更多菜单",
  description: "展示更多选项菜单的使用",
  code: `
import React from "react";
import { MoreMenuButton } from "@/components/common-custom/menu";
import { Pencil, Share, Trash, Copy, Eye } from "lucide-react";

function MoreMenuExample() {
  const moreMenuItems = [
    {
      label: "查看",
      icon: Eye,
      value: "view",
      onClick: () => console.log("查看")
    },
    {
      label: "编辑",
      icon: Pencil,
      value: "edit",
      onClick: () => console.log("编辑")
    },
    {
      label: "复制",
      icon: Copy,
      value: "copy",
      onClick: () => console.log("复制")
    },
    { type: "separator" },
    {
      label: "分享",
      icon: Share,
      value: "share",
      onClick: () => console.log("分享")
    },
    {
      label: "删除",
      icon: Trash,
      value: "delete",
      onClick: () => console.log("删除")
    }
  ];

  return (
    <div className="flex gap-4">
      <MoreMenuButton
        items={moreMenuItems}
        onSelect={(value) => console.log("选择了:", value)}
      />
      
      <MoreMenuButton
        items={moreMenuItems}
        variant="outline"
        onSelect={(value) => console.log("选择了:", value)}
      />
    </div>
  );
}

render(<MoreMenuExample />);
  `,
  scope: { MoreMenuButton, Pencil, Share, Trash, Copy, Eye, React },
}

// ============================================================================
// 操作菜单示例
// ============================================================================

export const actionMenuExample = {
  id: "action-menu",
  title: "操作菜单",
  description: "展示操作菜单的功能，显示常用操作并将其余操作放入更多菜单",
  code: `
import React from "react";
import { ActionMenuButton } from "@/components/common-custom/menu";
import { Pencil, Share, Trash, Copy, Eye, Download } from "lucide-react";

function ActionMenuExample() {
  const primaryActions = [
    {
      label: "查看",
      icon: Eye,
      value: "view",
      onClick: () => console.log("查看")
    },
    {
      label: "编辑",
      icon: Pencil,
      value: "edit",
      onClick: () => console.log("编辑")
    },
    {
      label: "复制",
      icon: Copy,
      value: "copy",
      onClick: () => console.log("复制")
    },
    {
      label: "下载",
      icon: Download,
      value: "download",
      onClick: () => console.log("下载")
    }
  ];

  const secondaryActions = [
    {
      label: "分享",
      icon: Share,
      value: "share",
      onClick: () => console.log("分享")
    },
    {
      label: "删除",
      icon: Trash,
      value: "delete",
      onClick: () => console.log("删除")
    }
  ];

  return (
    <div className="space-y-4">
      <ActionMenuButton
        primaryActions={primaryActions}
        secondaryActions={secondaryActions}
        visibleItemsCount={2}
        onSelect={(value) => console.log("选择了:", value)}
      />
      
      <ActionMenuButton
        primaryActions={primaryActions}
        secondaryActions={secondaryActions}
        visibleItemsCount={3}
        variant="outline"
        onSelect={(value) => console.log("选择了:", value)}
      />
    </div>
  );
}

render(<ActionMenuExample />);
  `,
  scope: { ActionMenuButton, Pencil, Share, Trash, Copy, Eye, Download, React },
}

// ============================================================================
// 上下文菜单示例
// ============================================================================

export const contextMenuExample = {
  id: "context-menu",
  title: "上下文菜单",
  description: "展示右键上下文菜单的功能",
  code: `
import React from "react";
import { ContextMenu } from "@/components/common-custom/menu";
import { Card } from "@/components/ui/card";
import { Pencil, Share, Trash, Copy, Eye } from "lucide-react";

function ContextMenuExample() {
  const contextMenuItems = [
    {
      label: "查看详情",
      icon: Eye,
      value: "view",
      onClick: () => console.log("查看详情")
    },
    {
      label: "编辑",
      icon: Pencil,
      value: "edit",
      onClick: () => console.log("编辑")
    },
    {
      label: "复制",
      icon: Copy,
      value: "copy",
      onClick: () => console.log("复制")
    },
    { type: "separator" },
    {
      label: "分享",
      icon: Share,
      value: "share",
      onClick: () => console.log("分享")
    },
    {
      label: "删除",
      icon: Trash,
      value: "delete",
      onClick: () => console.log("删除")
    }
  ];

  return (
    <div className="space-y-4">
      <ContextMenu
        items={contextMenuItems}
        onSelect={(value) => console.log("选择了:", value)}
      >
        <Card className="p-6 cursor-context-menu text-center border-dashed">
          <p className="text-muted-foreground">右键点击此处查看上下文菜单</p>
        </Card>
      </ContextMenu>
      
      <ContextMenu
        items={contextMenuItems}
        onSelect={(value) => console.log("选择了:", value)}
      >
        <Card className="p-4 cursor-context-menu">
          <h3 className="font-medium">文档标题</h3>
          <p className="text-sm text-muted-foreground mt-1">
            右键点击此卡片查看可用操作
          </p>
        </Card>
      </ContextMenu>
    </div>
  );
}

render(<ContextMenuExample />);
  `,
  scope: { ContextMenu, Card, Pencil, Share, Trash, Copy, Eye, React },
}

// ============================================================================
// 统一导出所有示例
// ============================================================================

export const allExamples = [
  basicDropdownExample,
  moreMenuExample,
  actionMenuExample,
  contextMenuExample,
]

// ============================================================================
// 按类别导出示例
// ============================================================================

export const basicExamples = [basicDropdownExample, moreMenuExample]
export const advancedExamples = [actionMenuExample, contextMenuExample]

// ============================================================================
// 示例元数据
// ============================================================================

export const exampleMetadata = {
  total: allExamples.length,
  categories: {
    basic: basicExamples.length,
    advanced: advancedExamples.length,
  },
  tags: ["menu", "dropdown", "context", "action", "navigation"],
  lastUpdated: "2024-01-01",
}

import apiClient from './requestApi';
import { ApiResponse } from '@/types/api';
import { 
  PasswordVaultItem, 
  CreatePasswordParams, 
  PasswordListParams, 
  UIPasswordItem,
  CollectParams
} from '@/types/passwordVault';

/**
 * 获取密码库列表
 * @param params 查询参数
 */
export const passwordListRequest = async (params: PasswordListParams): Promise<PasswordVaultItem[]> => {
  try {
    const response = await apiClient.post<ApiResponse<PasswordVaultItem[]>>(
      '/password-vault/list',
      { data: params }
    );
    return response.data || [];
  } catch (error) {
    console.error('获取密码列表失败:', error);
    throw error;
  }
};

/**
 * 创建新密码
 * @param params 密码创建参数
 */
export const createPasswordRequest = async (params: CreatePasswordParams): Promise<boolean> => {
  try {
    await apiClient.post<ApiResponse<any>>(
      '/password-vault/create',
      { data: params }
    );
    return true;
  } catch (error) {
    console.error('创建密码失败:', error);
    throw error;
  }
};

/**
 * 更新密码
 * @param id 密码ID
 * @param params 密码更新参数
 */
export const updatePasswordRequest = async (id: number, params: CreatePasswordParams): Promise<boolean> => {
  try {
    await apiClient.put<ApiResponse<any>>(
      `/password-vault/update/${id}`,
      { data: params }
    );
    return true;
  } catch (error) {
    console.error('更新密码失败:', error);
    throw error;
  }
};

/**
 * 删除密码
 * @param id 密码ID
 */
export const deletePasswordRequest = async (id: number): Promise<boolean> => {
  try {
    await apiClient.delete<ApiResponse<any>>(
      `/password-vault/delete/${id}`
    );
    return true;
  } catch (error) {
    console.error('删除密码失败:', error);
    throw error;
  }
};

/**
 * 根据ID获取密码详情
 * @param id 密码ID
 */
export const getPasswordDetailRequest = async (id: number): Promise<PasswordVaultItem | null> => {
  try {
    const response = await apiClient.get<ApiResponse<PasswordVaultItem>>(
      `/password-vault/get/${id}`
    );
    return response.data || null;
  } catch (error) {
    console.error('获取密码详情失败:', error);
    throw error;
  }
};

/**
 * 添加收藏
 * @param params 收藏参数
 */
export const toggleFavoriteRequest = async (params: CollectParams): Promise<boolean> => {
  try {
    await apiClient.post<ApiResponse<any>>(
      '/collect/add',
      { data: params }
    );
    return true;
  } catch (error) {
    console.error('添加收藏失败:', error);
    throw error;
  }
};

/**
 * 取消收藏
 * @param params 收藏参数
 */
export const cancelFavoriteRequest = async (params: CollectParams): Promise<boolean> => {
  try {
    await apiClient.post<ApiResponse<any>>(
      '/collect/cancel',
      { data: params }
    );
    return true;
  } catch (error) {
    console.error('取消收藏失败:', error);
    throw error;
  }
};

/**
 * 将API响应的密码项转换为UI使用的格式
 */
export const passwordConverter = {
  toUIFormat: (item: PasswordVaultItem): UIPasswordItem => {
    // 将API数据格式转换为UI组件使用的格式
    let strength: "weak" | "medium" | "strong" = "medium";
    let password_strength = 2; // 默认为中等强度
    
    // 根据passwordStrength设置值
    switch (item.passwordStrength) {
      case 1: // 弱
        strength = "weak";
        password_strength = 1;
        break;
      case 2: // 中
        strength = "medium";
        password_strength = 2;
        break;
      case 3: // 强
        strength = "strong";
        password_strength = 3;
        break;
    }
    
    // 生成搜索关键词
    const searchKeyword = [
      item.keyName,
      item.checkPassword,
      item.platform,
      ...(item.tagNames || [])
    ].filter(Boolean).join(" ");
    
    return {
      id: item.id.toString(),
      name: item.keyName,
      account: item.checkPassword,
      password: item.encryptedPassword,
      platform: item.platform || "",
      tags: item.tagNames || [],
      isFavorite: item.isCollected,
      strength: strength,
      password_strength: password_strength,
      lastUpdated: item.updateTime,
      notes: item.remarks || "",
      icon: item.passwordIcon || "🔑",
      createdAt: item.createTime,
      breachStatus: "safe",
      searchKeyword: searchKeyword
    };
  },
  
  toAPIFormat: (item: UIPasswordItem): CreatePasswordParams => {
    // 将UI组件使用的格式转换为API请求格式
    let strengthValue;
    
    // 优先使用password_strength
    if (item.password_strength) {
      strengthValue = item.password_strength;
    } else {
      // 兼容旧版数据
      switch (item.strength) {
        case "weak":
          strengthValue = 1;
          break;
        case "medium":
          strengthValue = 2;
          break;
        case "strong":
          strengthValue = 3;
          break;
        default:
          strengthValue = 2;
      }
    }
    
    return {
      userId: 1,
      checkPassword: item.account,
      passwordIcon: item.icon,
      keyName: item.name,
      platform: item.platform || "",
      encryptedPassword: item.password,
      passwordStrength: strengthValue,
      remarks: item.notes,
      tagNames: item.tags
    };
  }
}; 

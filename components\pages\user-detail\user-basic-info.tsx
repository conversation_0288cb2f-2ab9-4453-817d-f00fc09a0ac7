"use client"

import React from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import {
  UserCircle,
  Mail,
  PhoneCall,
  Shield,
  Clock,
  CalendarClock,
  Activity,
  Edit,
  LucideIcon
} from "lucide-react"

import { User } from "@/types/user"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"

interface InfoItemProps {
  icon: LucideIcon;
  label: string;
  value: React.ReactNode;
}

function InfoItem({ icon: Icon, label, value }: InfoItemProps) {
  return (
    <div className="flex items-start gap-2">
      <div className="p-1.5 rounded-md bg-muted mt-0.5">
        <Icon className="h-3.5 w-3.5 text-muted-foreground" />
      </div>
      <div>
        <p className="text-xs text-muted-foreground">{label}</p>
        <p className="text-sm font-medium mt-0.5">{value || "-"}</p>
      </div>
    </div>
  );
}

// 详细日期格式化 (年月日时分)
function formatDate(date: string | undefined) {
  if (!date) return "-";
  try {
    return new Date(date).toLocaleString("zh-CN", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    });
  } catch (error) {
    console.error("日期格式化错误", error);
    return date;
  }
}

// 简短日期格式化 (仅年月日)
function formatShortDate(date: string | undefined) {
  if (!date) return "-";
  try {
    return new Date(date).toLocaleDateString("zh-CN", {
      year: "numeric",
      month: "numeric", 
      day: "numeric"
    });
  } catch (error) {
    console.error("日期格式化错误", error);
    return date;
  }
}

// 获取用户状态标签样式
const getStatusBadgeStyle = (status: number | undefined) => {
  if (status === undefined) return "";
  
  switch (status) {
    case 1: // 正常
      return 'bg-green-500 text-white';
    case 0: // 禁用
      return 'bg-gray-500 text-white';
    case 2: // 锁定
      return 'bg-red-500 text-white';
    default:
      return 'bg-muted text-muted-foreground';
  }
};

// 获取用户状态标签文本
const getStatusLabel = (status?: number) => {
  switch (status) {
    case 1:
      return '正常';
    case 0:
      return '禁用';
    case 2:
      return '锁定';
    default:
      return '未知';
  }
};

interface UserBasicInfoProps {
  user: User | null;
}

export function UserBasicInfo({ user }: UserBasicInfoProps) {
  const router = useRouter();

  if (!user) {
    return (
      <Card className="border-border/40">
        <CardContent className="py-4">
          <div className="text-center">
            <p className="text-muted-foreground">用户信息不可用</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="mb-6 border-border/40">
      <CardHeader className="pb-2 pt-4 px-4">
        <CardTitle className="text-base flex items-center gap-2">
          <UserCircle className="h-4 w-4 text-primary" />
          用户信息
        </CardTitle>
        <CardDescription className="text-xs">基本信息和状态</CardDescription>
      </CardHeader>
      <CardContent className="px-4 py-2 space-y-3">
        <div className="flex items-center gap-3">
          <Avatar className="h-14 w-14 border">
            <AvatarImage src={user.avatarUrl || "/avatars/placeholder.png"} alt={user.nickname || "用户头像"} />
            <AvatarFallback className="text-sm">{(user.nickname || "U")[0].toUpperCase()}</AvatarFallback>
          </Avatar>
          <div>
            <div className="flex items-center gap-2">
              <h2 className="font-semibold">{user.nickname || "未知用户"}</h2>
              <Badge variant="outline" className={`text-xs py-0 ${getStatusBadgeStyle(user.status)}`}>
                {user.statusLabel || getStatusLabel(user.status)}
              </Badge>
            </div>
            <div className="flex items-center text-xs text-muted-foreground mt-1">
              <Mail className="h-3 w-3 mr-1" />
              <span>{user.email || "无邮箱信息"}</span>
            </div>
            <Button 
              variant="outline"
              size="sm"
              onClick={() => router.push(`/user/detail/${user.id}/edit`)}
              className="gap-1 mt-2 h-7 text-xs"
            >
              <Edit className="h-3 w-3" />
              编辑资料
            </Button>
          </div>
        </div>
        
        <Separator className="my-2" />
        
        <div className="grid grid-cols-2 gap-3">
          <InfoItem
            icon={UserCircle}
            label="账号"
            value={user.account || "未设置"}
          />
          <InfoItem
            icon={PhoneCall}
            label="手机号码"
            value={user.mobile || "未设置"}
          />
        </div>
        
        <div className="grid grid-cols-2 gap-3">
          <InfoItem
            icon={CalendarClock}
            label="创建时间"
            value={formatShortDate(user.createTime)}
          />
          <InfoItem
            icon={Activity}
            label="最后登录"
            value={user.lastLoginTime ? formatShortDate(user.lastLoginTime) : "从未登录"}
          />
        </div>
      </CardContent>
      <CardFooter className="pt-0 px-4 pb-3 flex-col items-stretch">
        <div className="pt-1 text-[10px] text-center text-muted-foreground">
          用户ID: {user.id}
        </div>
      </CardFooter>
    </Card>
  );
}

export default UserBasicInfo; 
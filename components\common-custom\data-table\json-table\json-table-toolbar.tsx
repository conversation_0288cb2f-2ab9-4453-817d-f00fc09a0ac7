"use client"

import * as React from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuCheckboxItem
} from "@/components/ui/dropdown-menu"
import { 
  Search, 
  RefreshCw, 
  Plus, 
  Download, 
  FileSpreadsheet, 
  FileText,
  Filter,
  Check,
  Calendar
} from "lucide-react"
import { cn } from "@/lib/utils"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { format } from "date-fns"
import { Badge } from "@/components/ui/badge"

export interface TableFilter {
  key: string
  title: string
  type: "select" | "multi-select" | "date-range"
  options?: FilterOption[]
}

export interface FilterOption {
  label: string
  value: string | number
}

export interface JsonTableConfig {
  showToolbar?: boolean
  showSearch?: boolean
  showExport?: boolean
  showPagination?: boolean
  showSelection?: boolean
  pageSize?: number
  pageSizeOptions?: number[]
  searchDebounce?: number
}

interface JsonTableToolbarProps {
  config: JsonTableConfig
  filters?: TableFilter[]
  onRefresh?: () => void
  onAdd?: () => void
  onExport?: (format: "csv" | "excel" | "pdf") => void
  onFilterChange?: (key: string, value: any) => void
  activeFilters?: Record<string, any>
  className?: string
}

export function JsonTableToolbar({
  config: {
    showSearch = true,
    showExport = true,
    ...restConfig
  },
  filters = [],
  onRefresh,
  onAdd,
  onExport,
  onFilterChange,
  activeFilters = {},
  className,
}: JsonTableToolbarProps) {
  const [searchValue, setSearchValue] = React.useState("")
  
  // 处理搜索输入变化
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value)
    // 这里可以添加防抖搜索逻辑
  }
  
  // 处理导出点击
  const handleExport = (format: "csv" | "excel" | "pdf") => {
    onExport?.(format)
  }
  
  // 获取导出格式图标
  const getExportFormatIcon = (format: "csv" | "excel" | "pdf") => {
    switch (format) {
      case "csv":
        return <FileText className="mr-2 h-4 w-4" />
      case "excel":
        return <FileSpreadsheet className="mr-2 h-4 w-4" />
      case "pdf":
        return <FileText className="mr-2 h-4 w-4" />
    }
  }
  
  // 获取导出格式标签
  const getExportFormatLabel = (format: "csv" | "excel" | "pdf") => {
    switch (format) {
      case "csv":
        return "导出为 CSV"
      case "excel":
        return "导出为 Excel"
      case "pdf":
        return "导出为 PDF"
    }
  }

  // 计算已激活的筛选器数量
  const activeFilterCount = Object.keys(activeFilters).length;
  
  // 渲染筛选器菜单项
  const renderFilterMenuItem = (filter: TableFilter) => {
    switch (filter.type) {
      case "select":
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 justify-start w-full">
                {filter.title}
                <span className="ml-auto opacity-70">
                  {activeFilters[filter.key] ? 
                    filter.options?.find(opt => opt.value === activeFilters[filter.key])?.label || activeFilters[filter.key] : 
                    "全部"}
                </span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start" className="w-48">
              {filter.options?.map((option) => (
                <DropdownMenuItem 
                  key={option.value}
                  onClick={() => onFilterChange?.(filter.key, option.value)}
                  className="cursor-pointer"
                >
                  <span>{option.label}</span>
                  {activeFilters[filter.key] === option.value && (
                    <Check className="ml-auto h-4 w-4" />
                  )}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        );
        
      case "multi-select":
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 justify-start w-full">
                {filter.title}
                <span className="ml-auto opacity-70">
                  {Array.isArray(activeFilters[filter.key]) && activeFilters[filter.key]?.length > 0 ? 
                    `已选 ${activeFilters[filter.key]?.length}` : 
                    "全部"}
                </span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start" className="w-48">
              {filter.options?.map((option) => (
                <DropdownMenuCheckboxItem 
                  key={option.value}
                  checked={Array.isArray(activeFilters[filter.key]) && activeFilters[filter.key]?.includes(option.value)}
                  onCheckedChange={(checked) => {
                    const currentValues = Array.isArray(activeFilters[filter.key]) ? [...activeFilters[filter.key]] : [];
                    if (checked) {
                      onFilterChange?.(filter.key, [...currentValues, option.value]);
                    } else {
                      onFilterChange?.(filter.key, currentValues.filter(v => v !== option.value));
                    }
                  }}
                  className="cursor-pointer"
                >
                  {option.label}
                </DropdownMenuCheckboxItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        );
        
      case "date-range":
        return (
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 justify-start w-full">
                {filter.title}
                <span className="ml-auto opacity-70">
                  {activeFilters[filter.key] ? 
                    (Array.isArray(activeFilters[filter.key]) ? 
                      `${format(new Date(activeFilters[filter.key][0]), 'yyyy-MM-dd')} 至 ${format(new Date(activeFilters[filter.key][1]), 'yyyy-MM-dd')}` : 
                      "已选择") : 
                    "选择日期"}
                </span>
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <CalendarComponent
                mode="range"
                selected={activeFilters[filter.key] ? {
                  from: new Date(activeFilters[filter.key][0]),
                  to: new Date(activeFilters[filter.key][1])
                } : undefined}
                onSelect={(range) => {
                  if (range?.from && range?.to) {
                    onFilterChange?.(filter.key, [range.from, range.to]);
                  }
                }}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        );
        
      default:
        return null;
    }
  };
  
  return (
    <div className={cn("flex flex-col gap-4 p-4 sm:flex-row sm:items-center sm:justify-between", className)}>
      {/* 左侧过滤器和搜索 */}
      <div className="flex flex-1 items-center gap-4">
        {/* 筛选按钮 */}
        {filters.length > 0 && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="outline" 
                size="default"
                className="h-9 gap-2"
              >
                <Filter className="h-4 w-4" />
                筛选
                {activeFilterCount > 0 && (
                  <Badge variant="secondary" className="ml-1">
                    {activeFilterCount}
                  </Badge>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start" className="w-56">
              {filters.map((filter, index) => (
                <React.Fragment key={filter.key}>
                  {index > 0 && <DropdownMenuSeparator />}
                  {renderFilterMenuItem(filter)}
                </React.Fragment>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        )}

        {/* 搜索框 */}
        {showSearch && (
          <div className="flex-1 max-w-sm">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索..."
                value={searchValue}
                onChange={handleSearchChange}
                className="pl-8 h-9"
              />
            </div>
          </div>
        )}
      </div>

      {/* 右侧操作按钮组 */}
      <div className="flex items-center gap-2">
        {/* 刷新按钮 */}
        {onRefresh && (
          <Button
            variant="outline"
            size="icon"
            onClick={onRefresh}
            className="h-9 w-9"
          >
            <RefreshCw className="h-4 w-4" />
          </Button>
        )}

        {/* 添加按钮 */}
        {onAdd && (
          <Button
            variant="default"
            size="default"
            onClick={onAdd}
            className="h-9 gap-2"
          >
            <Plus className="h-4 w-4" />
            添加
          </Button>
        )}

        {/* 导出按钮 */}
        {onExport && showExport && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                size="default"
                className="h-9 gap-2"
              >
                <Download className="h-4 w-4" />
                导出
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {["csv", "excel", "pdf"].map((format) => (
                <DropdownMenuItem
                  key={format}
                  onClick={() => handleExport(format as "csv" | "excel" | "pdf")}
                  className="cursor-pointer"
                >
                  {getExportFormatIcon(format as "csv" | "excel" | "pdf")}
                  <span>{getExportFormatLabel(format as "csv" | "excel" | "pdf")}</span>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
    </div>
  )
}
/**
 * 图表组件示例代码集合
 * 
 * 按照新规范管理示例代码，避免在页面文件中硬编码
 */

import React from "react"
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"
import {
  Bar,
  BarChart,
  Line,
  LineChart,
  Area,
  AreaChart,
  Pie,
  PieChart,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
} from "recharts"

// ============================================================================
// 柱状图示例
// ============================================================================

export const barChartExample = {
  id: "bar-chart",
  title: "柱状图",
  description: "展示分类数据的柱状图组件",
  code: `
import React from "react";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import {
  Bar,
  BarChart,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
} from "recharts";

function BarChartExample() {
  const data = [
    { name: "一月", value: 400 },
    { name: "二月", value: 300 },
    { name: "三月", value: 500 },
    { name: "四月", value: 280 },
    { name: "五月", value: 390 },
    { name: "六月", value: 320 },
  ];

  const chartConfig = {
    value: {
      label: "销售额",
      color: "hsl(var(--chart-1))",
    },
  };

  return (
    <ChartContainer config={chartConfig} className="h-[300px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={data}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="name" />
          <YAxis />
          <ChartTooltip content={<ChartTooltipContent />} />
          <Bar dataKey="value" fill="var(--color-value)" />
        </BarChart>
      </ResponsiveContainer>
    </ChartContainer>
  );
}

render(<BarChartExample />);
  `,
  scope: {
    ChartContainer,
    ChartTooltip,
    ChartTooltipContent,
    Bar,
    BarChart,
    XAxis,
    YAxis,
    CartesianGrid,
    ResponsiveContainer,
    React,
  },
}

// ============================================================================
// 折线图示例
// ============================================================================

export const lineChartExample = {
  id: "line-chart",
  title: "折线图",
  description: "展示趋势变化的折线图组件",
  code: `
import React from "react";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import {
  Line,
  LineChart,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
} from "recharts";

function LineChartExample() {
  const data = [
    { name: "一月", value: 400, target: 350 },
    { name: "二月", value: 300, target: 320 },
    { name: "三月", value: 500, target: 480 },
    { name: "四月", value: 280, target: 300 },
    { name: "五月", value: 390, target: 380 },
    { name: "六月", value: 320, target: 340 },
  ];

  const chartConfig = {
    value: {
      label: "实际值",
      color: "hsl(var(--chart-1))",
    },
    target: {
      label: "目标值",
      color: "hsl(var(--chart-2))",
    },
  };

  return (
    <ChartContainer config={chartConfig} className="h-[300px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={data}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="name" />
          <YAxis />
          <ChartTooltip content={<ChartTooltipContent />} />
          <Line 
            type="monotone" 
            dataKey="value" 
            stroke="var(--color-value)" 
            strokeWidth={2}
          />
          <Line 
            type="monotone" 
            dataKey="target" 
            stroke="var(--color-target)" 
            strokeWidth={2}
            strokeDasharray="5 5"
          />
        </LineChart>
      </ResponsiveContainer>
    </ChartContainer>
  );
}

render(<LineChartExample />);
  `,
  scope: {
    ChartContainer,
    ChartTooltip,
    ChartTooltipContent,
    Line,
    LineChart,
    XAxis,
    YAxis,
    CartesianGrid,
    ResponsiveContainer,
    React,
  },
}

// ============================================================================
// 面积图示例
// ============================================================================

export const areaChartExample = {
  id: "area-chart",
  title: "面积图",
  description: "展示数据变化趋势的面积图组件",
  code: `
import React from "react";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import {
  Area,
  AreaChart,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
} from "recharts";

function AreaChartExample() {
  const data = [
    { name: "一月", value: 400 },
    { name: "二月", value: 300 },
    { name: "三月", value: 500 },
    { name: "四月", value: 280 },
    { name: "五月", value: 390 },
    { name: "六月", value: 320 },
  ];

  const chartConfig = {
    value: {
      label: "访问量",
      color: "hsl(var(--chart-1))",
    },
  };

  return (
    <ChartContainer config={chartConfig} className="h-[300px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart data={data}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="name" />
          <YAxis />
          <ChartTooltip content={<ChartTooltipContent />} />
          <Area
            type="monotone"
            dataKey="value"
            stroke="var(--color-value)"
            fill="var(--color-value)"
            fillOpacity={0.3}
          />
        </AreaChart>
      </ResponsiveContainer>
    </ChartContainer>
  );
}

render(<AreaChartExample />);
  `,
  scope: {
    ChartContainer,
    ChartTooltip,
    ChartTooltipContent,
    Area,
    AreaChart,
    XAxis,
    YAxis,
    CartesianGrid,
    ResponsiveContainer,
    React,
  },
}

// ============================================================================
// 饼图示例
// ============================================================================

export const pieChartExample = {
  id: "pie-chart",
  title: "饼图",
  description: "展示数据占比的饼图组件",
  code: `
import React from "react";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import {
  Pie,
  PieChart,
  Cell,
  ResponsiveContainer,
} from "recharts";

function PieChartExample() {
  const data = [
    { name: "桌面端", value: 400, fill: "hsl(var(--chart-1))" },
    { name: "移动端", value: 300, fill: "hsl(var(--chart-2))" },
    { name: "平板端", value: 200, fill: "hsl(var(--chart-3))" },
    { name: "其他", value: 100, fill: "hsl(var(--chart-4))" },
  ];

  const chartConfig = {
    desktop: {
      label: "桌面端",
      color: "hsl(var(--chart-1))",
    },
    mobile: {
      label: "移动端",
      color: "hsl(var(--chart-2))",
    },
    tablet: {
      label: "平板端",
      color: "hsl(var(--chart-3))",
    },
    other: {
      label: "其他",
      color: "hsl(var(--chart-4))",
    },
  };

  return (
    <ChartContainer config={chartConfig} className="h-[300px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <ChartTooltip content={<ChartTooltipContent />} />
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            outerRadius={80}
            dataKey="value"
            label={({ name, percent }) => \`\${name} \${(percent * 100).toFixed(0)}%\`}
          >
            {data.map((entry, index) => (
              <Cell key={\`cell-\${index}\`} fill={entry.fill} />
            ))}
          </Pie>
        </PieChart>
      </ResponsiveContainer>
    </ChartContainer>
  );
}

render(<PieChartExample />);
  `,
  scope: {
    ChartContainer,
    ChartTooltip,
    ChartTooltipContent,
    Pie,
    PieChart,
    Cell,
    ResponsiveContainer,
    React,
  },
}

// ============================================================================
// 统一导出所有示例
// ============================================================================

export const allExamples = [
  barChartExample,
  lineChartExample,
  areaChartExample,
  pieChartExample,
]

// ============================================================================
// 按类别导出示例
// ============================================================================

export const basicExamples = [barChartExample, lineChartExample]
export const advancedExamples = [areaChartExample, pieChartExample]

// ============================================================================
// 示例元数据
// ============================================================================

export const exampleMetadata = {
  total: allExamples.length,
  categories: {
    basic: basicExamples.length,
    advanced: advancedExamples.length,
  },
  tags: ["chart", "data-visualization", "recharts", "ui"],
  lastUpdated: "2024-01-01",
}

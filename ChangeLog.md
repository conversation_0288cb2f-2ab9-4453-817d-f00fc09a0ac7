# 变更日志

## [2.3.1] - 2024-07-22

### 修复
- 修复 Stagewise 工具栏初始化错误：`Uncaught Error: ReactPlugin is not a constructor`
- 优化 ReactPlugin 导入和初始化逻辑，增强兼容性
- 添加更健壮的错误处理，确保在不同环境下正常工作

## [2.3.0] - 2024-07-21

### 新增
- 集成stagewise工具栏，提供AI辅助编辑功能
- 添加@stagewise-plugins/react插件支持，增强React组件交互能力
- 优化工具栏加载逻辑，确保只在开发环境中加载

### 优化
- 更新stagewise工具栏至最新版本(0.4.6)
- 改进工具栏配置，支持更多自定义选项
- 优化错误处理和加载状态显示

## [2.2.0] - 2024-07-20

### 新增
- 角色详情页增加查看/编辑模式切换
- 角色详情页使用Tab页展示权限和关联用户
- 新增角色关联用户列表组件，支持查看和移除功能
- 添加回退按钮，方便导航返回角色列表

### 优化
- 统一角色详情页的信息展示和编辑界面
- 查看按钮和编辑按钮进入同一页面，通过模式区分
- 编辑模式下实时修改并统一保存基础信息和权限设置
- 优化数据加载和提交流程，减少页面重复加载

## [2.1.1] - 2024-07-19

### 优化
- 改进角色管理界面交互体验
- 角色列表页添加查看按钮和编辑按钮，分别跳转到对应页面
- 角色详情页添加统一保存和重置按钮，移除重复按钮
- 重构角色权限组件，集成到详情页面中统一保存

### 修复
- 修复服务器组件中使用事件处理函数导致的报错
- 统一角色管理相关组件结构，提高代码质量
- 优化权限管理界面操作流程，减少页面跳转

## [2.1.0] - 2024-07-10

### 优化
- 角色管理功能整合到系统设置目录下
- 改进角色编辑流程，使用弹窗进行编辑而非单独页面
- 权限配置整合到角色详情页，简化工作流程
- 角色编码只允许在创建时设置，添加自动生成功能

### 修复
- 修复了服务器组件中使用事件处理函数的错误
- 组织角色管理相关组件，将其移至 components/pages/roles 目录
- 优化组件结构，将客户端交互逻辑分离为独立组件

## [2.0.0] - 2024-07-05

### 新增
- 创建项目架构基础
- 添加角色管理页面
- 添加权限管理页面
- 实现角色权限配置功能
- 添加用户管理页面

### 优化
- 角色权限树状结构优化
- 角色列表页面UI改进
- 添加面包屑导航
- 优化整体布局和响应式设计

### 修复
- 修复角色管理页面按钮无效问题
- 修复弹窗关闭后状态未重置问题

## 2024-07-18 角色管理目录结构优化

### 优化内容
- 合并了角色管理的目录结构，将所有角色管理功能统一维护在系统设置目录下
  - 将`/app/roles/[id]`路径下的页面移动到`/app/settings/roles/[id]`
  - 包括角色详情、编辑和权限设置页面
- 重构了相关页面中的路径引用
  - 更新了面包屑导航中的路径
  - 修改了成功操作后的跳转路径
- 保留了旧路径的重定向支持，确保兼容性
  - 为原有路径添加了重定向页面，自动跳转到新路径
- 添加了角色详情组件，优化数据展示

### 技术细节
- 在角色列表组件中添加了权限设置入口
- 优化了页面组件结构，减少代码重复
- 确保用户体验的一致性和流畅性

## 2023-05-26

### 团队管理页面字段映射优化

- 修复团队列表Logo主题色显示问题，确保图标正确应用主题色
- 更新字段映射关系，遵循后端API返回的标准字段命名：
  - `teamCode` → 团队编号
  - `teamName` → 团队名称
  - `parentTeamName` → 父级团队名称
  - `statusLabel` → 团队状态
  - `description` → 团队描述
  - `createTime` → 创建时间
  - `updateTime` → 更新时间
- 优化团队卡片视图布局，调整字段显示顺序与内容
- 为卡片视图中的描述和时间字段添加标签，提高可读性
- 统一表格表头命名，与字段映射保持一致

## 2024-07-12 权限管理模块实现

### 新增功能
- 实现了基于树形结构的Keel Cloud
- 支持多级权限管理：目录、菜单和按钮三种权限类型
- 权限节点支持拖拽排序和移动
- 权限表单支持完整的权限配置功能

### 技术细节
- 基于React和TypeScript实现权限树和权限表单组件
- 使用shadcn UI组件库构建用户界面
- 实现了完整的权限节点CRUD操作
- 添加了丰富的交互功能，如拖拽排序、节点展开折叠
- 实现了权限联动规则和表单验证逻辑

### 后续计划
- 考虑添加权限导入导出功能
- 实现权限模板功能，支持批量权限创建
- 优化权限搜索和过滤功能
- 添加权限历史记录功能

## 2024-07-10 面包屑组件优化

### 修改内容
- 优化了面包屑组件的高度，从16px减小到12px
- 移除了面包屑组件的底部边框，避免与其他组件重叠
- 调整了dashboard页面布局，增加了适当的上边距(pt-2)，确保与面包屑组件协调

### 技术细节
- 修改了header-with-breadcrumb.tsx组件中的header样式
- 移除了border-b类，使界面更加清爽
- 调整了页面内容与面包屑之间的间距，优化了整体视觉体验

## 2024-07-10 全局UI样式优化

### 修改内容
- 移除了所有UI组件的边框阴影效果
- 移除了所有组件的聚焦状态阴影
- 优化了按钮、卡片和对话框等组件的视觉风格

### 技术细节
- 在全局CSS中添加了覆盖规则，移除了所有box-shadow和focus-ring样式
- 修改了button.tsx组件，移除了shadow-xs类
- 修改了card.tsx组件，移除了shadow-sm类
- 修改了dialog.tsx组件，移除了shadow-lg类和focus-ring相关样式

## 2023-12-16 API集成问题修复

### 修复内容
- 调整API响应类型定义，匹配实际服务返回格式
- 修复动态API服务中对分页数据的处理逻辑
- 更新团队服务中处理嵌套data结构的方式
- 添加API测试页面，方便后续调试和验证

### 技术细节
- 将API响应中的success/message字段改为code/msg
- 支持嵌套的分页数据结构格式 
- 提高数据处理的健壮性，确保数组类型安全

## 2023-12-15 团队管理模块API集成

### 新增功能
- 创建了通用动态API服务，支持MongoDB集合的CRUD操作
- 封装了专用的团队服务层，用于处理团队管理相关的业务逻辑
- 集成Sonner提示库，提供友好的操作反馈

### 优化项
- 团队列表页替换为实际API调用，支持分页和搜索
- 团队详情页使用API获取数据，支持实际的团队创建、编辑和删除
- 团队成员管理使用API实现，支持添加和移除成员
- 添加了加载状态和错误处理，提升用户体验

### 待优化项
- 考虑添加状态管理库以优化数据流和性能
- 添加数据缓存策略减少请求次数
- 完善错误处理机制，提供更多的错误恢复选项

# 更新日志

## 2024-05-10

### 密码管理模块真实API实现

- 新增`types/passwordVault.ts`，定义密码管理相关的类型
- 新增`services/api/passwordVaultRequestApi.ts`，实现密码管理的API请求方法
- 重构密码列表组件`components/pages/passwordmanager/password-list.tsx`，使用真实API替换mock数据
- 重构密码详情组件`components/pages/passwordmanager/password-detail-dialog.tsx`，使用真实API替换mock数据
- 重构密码创建组件`components/pages/passwordmanager/create-password-dialog.tsx`，使用真实API提交表单数据
- 更新README.md文件，添加密码管理模块的说明和API使用示例

### 优化内容

- 为所有API请求添加了错误处理逻辑
- 增加了加载状态和错误状态的UI反馈
- 实现了表单验证
- 实现了API数据格式和UI数据格式的转换

### 待优化项

- 密码收藏功能尚未连接到后端API
- 密码编辑功能尚未实现
- 密码健康检测功能仍使用静态数据
- 考虑添加乐观更新以提升用户体验
- 考虑使用React Query进行数据获取和缓存管理

## 2024-05-11

### 修复密码管理模块API集成问题

- 修复了密码列表组件无限刷新的问题，移除了useEffect依赖数组中的toast
- 修复了密码详情组件无限刷新的问题，优化了依赖项
- 修正了API接口路径，确保使用正确的路径前缀
- 删除了遗留的`services/mock/passwordsMock.ts`模拟数据文件
- 优化了API响应处理逻辑

## [未发布]

### 新增
- 优化请求库，增加简化的JSON请求方法
  - 新增 `postJSON` 方法，简化POST JSON数据的请求
  - 新增 `putJSON` 方法，简化PUT JSON数据的请求
  - 更新请求库文档和示例代码
- 修复类型错误，确保请求参数类型安全

### 修复
- 修复 `createTeamRequest` 方法中的类型错误
- 优化请求库中的错误处理，改进错误提示
- 修复 `Cancel` 类的构造函数，增强对各种参数类型的支持

### 优化
- 改进请求库的类型定义，确保类型安全
- 优化团队API接口实现，使用新的JSON请求方法
- 简化API调用代码，提高开发效率

## 2023-10-10

### 团队管理模块更新
- 移除组织架构树和相关逻辑、文件
- 简化团队管理逻辑
- 优化团队列表展示，按照接口实际返回结构进行展示更新
- 删除复制ID、导出数据等冗余菜单项

## 2023-05-23

### 团队创建页面优化

- 优化团队代码输入功能
  - 添加失去焦点时自动校验团队代码是否存在
  - 使用API接口`/team/getByCode`进行实时验证
  - 显示加载状态和错误提示
- 统一表单输入框和按钮高度为9px
- 优化团队预览卡片样式，与列表视图保持一致
  - 调整卡片布局和排版
  - 修改图标尺寸和位置
  - 优化文本显示

## 2023-05-24

### 团队模块更新

- 在团队创建表单中添加团队代码(team_code)字段
  - 支持自动生成，使用TEAM-XXXXX-YYYY格式
  - 支持手动修改，提供自动生成按钮
  - 增加表单验证
- 在团队卡片和列表视图中显示团队代码
- 更新团队预览功能，展示团队代码信息
- 优化卡片视图布局，放大logo并减少整体尺寸

## 2023-05-25

### 团队管理页面优化

- 优化团队管理页面，移除分页逻辑，改为前端过滤和排序
- 更新团队列表视图，显示更多详细信息：
  - 添加团队代码字段显示
  - 添加团队描述字段（过长时显示省略号并提供tooltip）
  - 添加创建时间和更新时间字段
  - 添加父级团队信息
- 优化卡片视图样式：
  - 减少边框粗细，优化卡片阴影
  - 调整卡片布局，提高信息展示效率
  - 添加悬停效果
- 改进表格视图：
  - 增加团队代码列
  - 优化列宽配置
  - 添加表格内空状态和加载状态
- 调整排序选项：
  - 移除成员数排序选项
  - 添加创建时间排序选项
- 优化团队筛选和搜索体验

## 2023-05-22

### 修复团队管理错误

- 修复团队列表排序功能中的错误
  - 修正 `localeCompare` 调用导致的 Uncaught Error 问题
  - 增强搜索和排序功能中的空值处理机制
  - 优化数据转换函数，确保所有数据字段都有合理的默认值
  - 增强API返回数据的验证和过滤
- 提高系统健壮性
  - 增加数据过滤器，确保无效数据不会导致页面崩溃
  - 改进类型定义，避免TypeScript编译错误
  - 增强页面容错能力，即使API返回格式不正确也能正常显示

## 2023-05-23

## 2024-07-15 角色管理功能实现

### 新增功能
- 新增角色管理模块，提供完整的角色管理功能
  - 角色列表页面，支持搜索、排序和筛选
  - 角色详情页面，展示角色基本信息和关联用户
  - 角色创建、编辑和删除功能
  - 角色权限分配功能，支持权限树形选择
- 在系统设置中集成角色管理，并与权限管理联动
  - 在权限管理页面添加角色管理入口
  - 优化系统设置布局，提高导航效率

### 技术细节
- 创建了`roleRequestApi.ts`服务层，封装角色相关API请求
- 扩展了`Role`类型定义，增加了更多字段支持
- 实现了角色与权限的关联管理
- 使用树形结构展示权限数据，支持批量选择
- 添加了角色管理的数据验证和错误处理

### 后续计划
- 完善角色与用户的关联管理功能
- 添加角色复制功能，便于快速创建相似角色
- 考虑添加角色模板功能
- 优化角色权限展示，增加分类筛选

## 2023-10-19
- 将角色管理目录结构合并到系统设置目录下
  - 创建了新目录结构`/app/settings/roles/[id]`及子目录
  - 将角色编辑、权限设置等页面从`/app/roles/[id]`移至新目录
  - 更新了所有相关路径引用
  - 为旧路径添加重定向支持
  - 创建了角色详情组件
- 调整权限管理页面的按钮组位置
  - 将"权限结构"和"角色管理"按钮组从页面下方移至标题同行右侧
  - 优化了按钮高度和间距，使其与标题协调
  - 删除了右上角多余的角色管理按钮

## 2023-10-20
- 角色管理功能升级
  - 添加角色管理模拟数据，支持角色的增删改查操作
  - 创建了完整的角色详情页面，展示角色信息、用户数量和权限数量
  - 重构角色编辑页面，使用客户端组件实现表单提交和数据验证
  - 重构角色权限设置页面，使用树形结构展示权限，支持权限选择和保存
  - 添加权限树模拟数据，支持角色权限的分配和保存
  - 添加日期格式化工具函数，用于格式化创建时间和更新时间

## 2023-10-21
- 角色管理界面优化
  - 删除角色操作和状态卡片，简化界面
  - 将权限配置整合到角色详情页，不再使用独立页面
  - 编辑角色改为使用弹窗形式，提升用户体验
  - 角色编码只允许在创建时设置，添加自动生成功能
  - 删除冗余的角色编辑和权限设置页面
  - 优化角色详情页面布局和交互

## 2023-11-15

### 改进
- 角色管理页面UI优化
  - 去除卡片进行布局，改用直接的区域划分，简化页面结构
  - 汇总字段放在tab页上展示，提高信息获取效率
  - 权限树UI改进，使用更美观的折叠树结构
  - 添加统一的回退按钮组件，改进导航体验
  - 添加用户数量、权限数量和创建时间的汇总信息
- 角色权限列表样式优化
  - 改进权限树的视觉层次感
  - 增加图标指示更直观
  - 添加动效提升交互体验
- 新增回退按钮统一组件
  - 支持自定义返回目标和行为
  - 统一样式提升一致性体验
  - 添加悬停动画效果

## 2023-11-16

### 改进
- 角色管理页面布局进一步优化
  - 将统计信息移至用户列表和权限设置Tab内部，更符合信息分类逻辑
  - 减少模块间上下边距，使页面更紧凑
  - 统一边距和内边距，增强视觉一致性
  - 优化用户列表中的垂直居中对齐
  - 在查看模式下添加创建时间信息到角色描述区域

## 2023-11-17

### 改进
- 角色管理页面继续优化
  - 调整统计信息位置，统计数据直接显示在标题旁边
  - 删除底部多余的统计信息区块和分割线
  - 移除权限设置页面的权限选中统计条
  - 精简页面布局，减少视觉干扰
  - 提高信息展示的直观性和简洁性

## 2023-11-18

### 改进
- 角色管理页面文字层级优化
  - 调整标题使用更大字体和更粗字重，增强层级感
  - 修改主要标题为font-semibold，增强视觉层次
  - 缩小创建时间和统计信息字体至text-xs，降低视觉权重
  - 权限树中父节点使用text-base加粗显示，子项使用text-xs显示
  - 整体层级区分更加明确，改善信息层次结构

## 2023-11-19

### 功能新增
- 角色启用/停用功能
  - 增加角色状态切换功能，支持启用和停用操作
  - 编辑页面增加状态开关，可直接修改角色状态
  - 查看页面增加启用/停用按钮，便于快速切换状态
  - 添加状态变更提示，提升用户体验

### 改进
- UI细节优化
  - 更换基本信息图标为Settings，更符合功能含义
  - 更换角色描述图标为FileText，提高语义匹配度
  - 调整权限树字体为普通样式，减小字体大小，提高可读性
  - 优化启用/禁用按钮样式，使用合适图标提升直观性

## 变更日志

### [新增功能] - 2023-07-14

- 添加用户管理功能
  - 实现用户列表展示
  - 支持新增用户
  - 支持编辑用户信息
  - 支持删除用户
  - 用户列表支持按名称、邮箱和角色排序
  - 在导航菜单添加用户管理入口

## 2023-05-16

### 修复
- 修复用户列表样式异常问题，解决表格单元格内容溢出问题
- 修复导入路径错误：将`@/components/ui/header-with-breadcrumb`修改为`@/components/custom/breadcrumb`
- 分离用户详情页和编辑页，创建独立的编辑页面路由

### 优化
- 优化用户详情页展示内容和布局，提高可读性
- 设计更合理的页面跳转流程，改善用户体验

## 2023-05-17

### 优化
- 全面优化用户详情页界面设计和布局
- 添加用户信息选项卡展示，包含概览、团队和权限三个部分
- 丰富用户信息展示，添加账户完整度、活跃状态、角色详情等信息
- 优化权限和团队信息展示，提高可读性和易用性
- 改进用户界面体验，统一风格与其他页面保持一致

## 2023-05-18

### 修复
- 修复`roleRequestApi.ts`中的函数重复定义问题，删除了重复的`getRoleByIdRequest`函数定义

## 用户详情页和编辑页重构更新日志 (2025-06-23)

### 重构内容

1. **组件化重构**
   - 将用户详情页和编辑页拆分为更小、可复用的组件
   - 新增组件位于 `components/pages/user-detail/` 目录下
   - 实现了业务逻辑与UI展示的分离

2. **新增组件**
   - `UserBasicInfo`: 用户基本信息展示组件
   - `UserBasicInfoEdit`: 用户基本信息编辑组件
   - `UserRoles`: 用户角色信息展示组件
   - `UserPermissions`: 用户权限信息展示组件
   - `UserOrganization`: 用户组织信息展示组件
   - `UserActivity`: 用户活动信息展示组件
   - `RoleSelector`: 角色选择器组件

3. **功能优化**
   - 新增用户角色管理功能，支持添加和移除角色
   - 新增用户权限展示功能，以树形结构展示用户权限
   - 新增组织部门管理功能
   - 新增用户活动历史记录展示
   - 改进表单验证和错误提示
   - 使用模态对话框进行角色和部门选择

4. **UI/UX 改进**
   - 统一了详情页和编辑页的设计风格
   - 优化了数据加载状态展示
   - 使用选项卡组织更多信息，提高页面整洁度
   - 增强了表单交互体验
   - 添加了更多的视觉反馈和状态提示

5. **代码质量提升**
   - 减少代码重复，提高可复用性
   - 增强错误处理和边缘情况处理
   - 优化数据加载和状态管理
   - 改进类型定义，提高代码可靠性

### 后续优化建议

1. 进一步完善用户权限实时获取功能
2. 实现头像上传功能
3. 添加更详细的用户活动日志
4. 实现组织架构选择器
5. 优化移动端适配和响应式设计

## 2023-06-20

### 功能优化
- 优化了用户详情页面的设计和布局
  - 删除了所属组织卡片，简化页面结构
  - 缩小了用户信息卡片尺寸，提高信息密度
  - 删除了权限tab页，减少冗余信息
  - 将活动tab页修改为设置tab，添加了常用设置选项
  - 统一了编辑页和详情页的设计风格

### 功能修复
- 修复了用户详情页面的类型错误
- 优化了页面加载状态的显示

## 2023-06-18

### 功能新增
- 完成了用户管理页面的基本功能
- 添加了用户详情页面
- 添加了用户编辑页面

### 功能优化
- 优化了表单验证逻辑
- 改进了错误提示的显示方式

"use client"

import React from "react"
import { ComponentPreviewContainer } from "@/components/component-detail/preview-container"
import { TruncateText } from "@/components/common-custom/truncate-text"
import { allExamples } from "./examples"

// ============================================================================
// API文档组件
// ============================================================================

function TruncateTextApiDocs() {
  return (
    <div className="space-y-6">
      {/* 主组件API */}
      <div>
        <h3 className="font-medium text-lg mb-2">TruncateText</h3>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">text</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">要截断显示的文本内容</td>
              </tr>
              <tr>
                <td className="p-2 border">lines</td>
                <td className="p-2 border">number</td>
                <td className="p-2 border">1</td>
                <td className="p-2 border">显示行数</td>
              </tr>
              <tr>
                <td className="p-2 border">maxWidth</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">容器最大宽度</td>
              </tr>
              <tr>
                <td className="p-2 border">showTooltip</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">true</td>
                <td className="p-2 border">是否显示提示框</td>
              </tr>
              <tr>
                <td className="p-2 border">position</td>
                <td className="p-2 border">"start" | "middle" | "end"</td>
                <td className="p-2 border">"end"</td>
                <td className="p-2 border">截断位置</td>
              </tr>
              <tr>
                <td className="p-2 border">ellipsis</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">"..."</td>
                <td className="p-2 border">自定义省略符号</td>
              </tr>
              <tr>
                <td className="p-2 border">expandable</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">false</td>
                <td className="p-2 border">是否显示展开/收起按钮</td>
              </tr>
              <tr>
                <td className="p-2 border">onExpandChange</td>
                <td className="p-2 border">(expanded: boolean) =&gt; void</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">展开状态变化回调</td>
              </tr>
              <tr>
                <td className="p-2 border">tooltipSide</td>
                <td className="p-2 border">"top" | "bottom" | "left" | "right"</td>
                <td className="p-2 border">"top"</td>
                <td className="p-2 border">提示框位置</td>
              </tr>
              <tr>
                <td className="p-2 border">disabled</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">false</td>
                <td className="p-2 border">是否禁用</td>
              </tr>
              <tr>
                <td className="p-2 border">className</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">自定义CSS类名</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* 使用指南 */}
      <div>
        <h3 className="font-medium text-lg mb-2">使用指南</h3>
        <div className="space-y-4 text-sm">
          <div>
            <h4 className="font-medium mb-2">基本用法</h4>
            <p className="text-muted-foreground mb-2">
              最简单的文本截断使用方式：
            </p>
            <pre className="bg-muted p-3 rounded-md overflow-x-auto">
              <code>{`<TruncateText text="长文本内容" maxWidth="200px" />`}</code>
            </pre>
          </div>

          <div>
            <h4 className="font-medium mb-2">多行截断</h4>
            <p className="text-muted-foreground mb-2">
              控制显示的最大行数：
            </p>
            <pre className="bg-muted p-3 rounded-md overflow-x-auto">
              <code>{`<TruncateText
  text="长文本内容"
  lines={3}
  maxWidth="300px"
/>`}</code>
            </pre>
          </div>

          <div>
            <h4 className="font-medium mb-2">可展开文本</h4>
            <p className="text-muted-foreground mb-2">
              允许用户展开查看完整内容：
            </p>
            <pre className="bg-muted p-3 rounded-md overflow-x-auto">
              <code>{`<TruncateText
  text="长文本内容"
  expandable={true}
  onExpandChange={(expanded) => console.log(expanded)}
/>`}</code>
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
}

// ============================================================================
// 主预览组件
// ============================================================================

export default function TruncateTextExamplePage() {
  return (
    <ComponentPreviewContainer
      title="文本截断 TruncateText"
      description="用于处理长文本的显示，当文本内容超出容器宽度或指定行数时，自动截断并显示省略号"
      whenToUse="当需要在有限空间内显示可能过长的文本内容时使用；适用于卡片、表格单元格、列表项等场景；当需要确保UI布局的一致性，避免因文本长度不一致导致的布局错乱；支持可展开功能，为用户提供查看完整内容的方式"
      examples={allExamples}
      apiDocs={<TruncateTextApiDocs />}
    />
  )
}
"use client"

import * as React from "react"
import { JsonDataTableProps } from "../types"
import { JsonTableRenderer } from "./json-table-renderer"
import { JsonTableToolbar } from "./json-table-toolbar"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { cn } from "@/lib/utils"

/**
 * JSON数据表格组件
 * 
 * 该组件通过JSON配置定义表格结构和功能，更加灵活和可配置
 */
export function JsonDataTable({
  tableDefinition,
  data,
  renderers,
  formatters,
  onRowAction,
  onSelectionChange,
  loading = false,
  onRefresh,
  onAdd,
  onExport,
  className,
}: JsonDataTableProps) {
  // 获取配置
  const config = tableDefinition.config || {}
  const { title, description } = config
  
  // 筛选器状态
  const [activeFilters, setActiveFilters] = React.useState<Record<string, any>>({});
  
  // 处理筛选器变更
  const handleFilterChange = (key: string, value: any) => {
    setActiveFilters(prev => {
      if (value === undefined) {
        const newFilters = { ...prev };
        delete newFilters[key];
        return newFilters;
      }
      return { ...prev, [key]: value };
    });
  };
  
  // 筛选数据
  const filteredData = React.useMemo(() => {
    if (Object.keys(activeFilters).length === 0) return data;
    
    return data.filter(item => {
      // 检查每个激活的筛选器
      for (const [key, value] of Object.entries(activeFilters)) {
        if (value === undefined) continue;
        
        // 根据筛选器类型进行不同的比较
        const filter = tableDefinition.filters?.find(f => f.key === key);
        if (!filter) continue;
        
        switch (filter.type) {
          case "select":
            if (value !== "all" && item[key] !== value) return false;
            break;
            
          case "multi-select":
            if (Array.isArray(value) && value.length > 0 && !value.includes(item[key])) return false;
            break;
            
          case "date-range":
            if (Array.isArray(value) && value.length === 2) {
              const itemDate = new Date(item[key]);
              const fromDate = new Date(value[0]);
              const toDate = new Date(value[1]);
              if (itemDate < fromDate || itemDate > toDate) return false;
            }
            break;
            
          case "search":
            if (value && !String(item[key]).toLowerCase().includes(String(value).toLowerCase())) return false;
            break;
        }
      }
      
      return true;
    });
  }, [data, activeFilters, tableDefinition.filters]);

  return (
    <Card className={cn("w-full", className)}>
      {(title || description) && (
        <CardHeader className="pb-3">
          {title && <CardTitle>{title}</CardTitle>}
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>
      )}
      
      {(config.showToolbar !== false) && (
        <JsonTableToolbar
          config={config}
          filters={tableDefinition.filters}
          onRefresh={onRefresh}
          onAdd={onAdd}
          onExport={onExport}
          onFilterChange={handleFilterChange}
          activeFilters={activeFilters}
        />
      )}
      
      <CardContent className="p-0">
        <JsonTableRenderer
          columns={tableDefinition.columns}
          data={filteredData}
          actions={tableDefinition.actions}
          config={config}
          renderers={renderers}
          formatters={formatters}
          onRowAction={onRowAction}
          onSelectionChange={onSelectionChange}
          loading={loading}
        />
      </CardContent>
    </Card>
  )
} 
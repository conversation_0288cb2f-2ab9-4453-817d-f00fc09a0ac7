"use client"

import React from "react"
import { ComponentPreviewContainer } from "@/components/component-detail/preview-container"
import { 
  Timeline, 
  ActivityTimeline, 
  StageTimeline 
} from "@/components/common-custom/timeline"
import { 
  Calendar, 
  CheckCircle, 
  Clock, 
  AlertCircle,
  FileText,
  MessageSquare,
  User,
  ArrowRight,
  Zap,
  Star
} from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"

// 基础时间轴示例代码
const basicTimelineCode = `
import React from "react";
import { Timeline } from "@/components/common-custom/timeline";
import { CheckCircle, Clock, AlertCircle } from "lucide-react";

function BasicTimeline() {
  const timelineItems = [
    {
      id: "1",
      title: "项目创建",
      description: "项目初始化并设置基本参数",
      time: "2023-10-01",
      status: "success",
      icon: <CheckCircle className="h-4 w-4 text-white" />,
      content: (
        <div className="text-sm text-muted-foreground">
          由系统管理员创建
        </div>
      )
    },
    {
      id: "2",
      title: "需求分析",
      description: "收集并分析用户需求",
      time: "2023-10-05",
      status: "success",
      icon: <CheckCircle className="h-4 w-4 text-white" />,
      content: (
        <div className="text-sm text-muted-foreground">
          完成需求文档编写
        </div>
      )
    },
    {
      id: "3",
      title: "设计阶段",
      description: "UI设计和架构设计",
      time: "2023-10-10",
      status: "primary",
      icon: <Clock className="h-4 w-4 text-white" />,
      content: (
        <div className="text-sm text-muted-foreground">
          设计稿审核中
        </div>
      )
    },
    {
      id: "4",
      title: "开发阶段",
      description: "前后端功能开发",
      time: "预计 2023-10-20",
      status: "warning",
      icon: <AlertCircle className="h-4 w-4 text-white" />,
      content: (
        <div className="text-sm text-muted-foreground">
          等待设计稿完成
        </div>
      )
    }
  ];

  return <Timeline items={timelineItems} />;
}

render(<BasicTimeline />);
`;

// 时间轴样式变体示例代码
const timelineVariantsCode = `
import React from "react";
import { Timeline } from "@/components/common-custom/timeline";
import { Star } from "lucide-react";

function TimelineVariants() {
  const timelineItems = [
    {
      id: "1",
      title: "创建项目",
      description: "项目初始化完成",
      time: "2023-10-01",
      status: "success"
    },
    {
      id: "2",
      title: "发布版本",
      description: "版本 1.0 发布",
      time: "2023-10-15",
      status: "success"
    },
    {
      id: "3",
      title: "功能更新",
      description: "新增核心功能",
      time: "2023-10-30",
      status: "primary"
    }
  ];

  return (
    <div className="space-y-8">
      <div>
        <h3 className="text-sm font-medium mb-2">虚线连接器</h3>
        <Timeline 
          items={timelineItems} 
          connectorStyle="dashed"
        />
      </div>
      
      <div>
        <h3 className="text-sm font-medium mb-2">点状连接器</h3>
        <Timeline 
          items={timelineItems} 
          connectorStyle="dotted"
          connectorColor="#6366f1"
        />
      </div>
      
      <div>
        <h3 className="text-sm font-medium mb-2">方形点标记</h3>
        <Timeline 
          items={timelineItems} 
          dotShape="square"
        />
      </div>
      
      <div>
        <h3 className="text-sm font-medium mb-2">自定义图标</h3>
        <Timeline 
          items={timelineItems.map(item => ({
            ...item,
            icon: <Star className="h-4 w-4 text-white" />
          }))} 
        />
      </div>
    </div>
  );
}

render(<TimelineVariants />);
`;

// 活动时间轴示例代码
const activityTimelineCode = `
import React from "react";
import { ActivityTimeline } from "@/components/common-custom/timeline";
import { FileText, MessageSquare, CheckCircle, AlertCircle } from "lucide-react";
import { Badge } from "@/components/ui/badge";

function ActivityTimelineExample() {
  const activities = [
    {
      id: "1",
      user: { name: "张三", avatar: "ZS" },
      action: "创建了任务",
      target: "首页设计",
      time: "1小时前",
      icon: <FileText className="h-4 w-4" />,
      status: "primary",
      content: (
        <div className="mt-1 text-sm bg-muted p-2 rounded-md">
          需要完成首页的响应式设计，包括桌面端和移动端
        </div>
      )
    },
    {
      id: "2",
      user: { name: "李四", avatar: "LS" },
      action: "评论了任务",
      target: "首页设计",
      time: "45分钟前",
      icon: <MessageSquare className="h-4 w-4" />,
      status: "primary",
      content: (
        <div className="mt-1 text-sm bg-muted p-2 rounded-md">
          建议增加暗色模式支持
        </div>
      )
    },
    {
      id: "3",
      user: { name: "王五", avatar: "WW" },
      action: "完成了任务",
      target: "登录页面",
      time: "30分钟前",
      icon: <CheckCircle className="h-4 w-4" />,
      status: "success"
    },
    {
      id: "4",
      user: { name: "系统", avatar: "SYS" },
      action: "部署了新版本",
      target: "v1.2.0",
      time: "10分钟前",
      icon: <AlertCircle className="h-4 w-4" />,
      status: "warning",
      content: (
        <div className="mt-1">
          <Badge variant="outline" className="mr-1">修复</Badge>
          <Badge variant="outline">性能优化</Badge>
        </div>
      )
    }
  ];

  return (
    <ActivityTimeline 
      activities={activities}
      showAvatar={true}
      hasMore={true}
      onLoadMore={() => console.log("加载更多")}
    />
  );
}

render(<ActivityTimelineExample />);
`;

// 阶段时间轴示例代码
const stageTimelineCode = `
import React, { useState } from "react";
import { StageTimeline } from "@/components/common-custom/timeline";
import { User, FileText, Calendar, Clock, CheckCircle } from "lucide-react";
import { Button } from "@/components/ui/button";

function StageTimelineExample() {
  const [currentStage, setCurrentStage] = useState("stage3");

  const stages = [
    {
      id: "stage1",
      name: "信息收集",
      description: "收集基本信息",
      status: "completed",
      icon: <User className="h-4 w-4" />
    },
    {
      id: "stage2",
      name: "需求确认",
      description: "确认项目需求和范围",
      status: "completed",
      icon: <FileText className="h-4 w-4" />
    },
    {
      id: "stage3",
      name: "方案设计",
      description: "设计解决方案",
      status: "current",
      icon: <Calendar className="h-4 w-4" />
    },
    {
      id: "stage4",
      name: "开发实施",
      description: "开发和测试",
      status: "pending",
      icon: <Clock className="h-4 w-4" />
    },
    {
      id: "stage5",
      name: "交付使用",
      description: "项目交付和验收",
      status: "pending",
      icon: <CheckCircle className="h-4 w-4" />
    }
  ];

  const handlePrevious = () => {
    const currentIndex = stages.findIndex(stage => stage.id === currentStage);
    if (currentIndex > 0) {
      setCurrentStage(stages[currentIndex - 1].id);
    }
  };

  const handleNext = () => {
    const currentIndex = stages.findIndex(stage => stage.id === currentStage);
    if (currentIndex < stages.length - 1) {
      setCurrentStage(stages[currentIndex + 1].id);
    }
  };

  return (
    <div>
            <StageTimeline 
              stages={stages}
        currentStage={currentStage}
        onChange={setCurrentStage}
            />
            <div className="mt-4">
        <Button variant="outline" size="sm" onClick={handlePrevious}>
          上一步
        </Button>
        <Button className="ml-2" size="sm" onClick={handleNext}>
          下一步
        </Button>
            </div>
          </div>
  );
}

render(<StageTimelineExample />);
`;

// 水平时间轴示例代码
const horizontalTimelineCode = `
import React from "react";
import { Timeline } from "@/components/common-custom/timeline";
import { Zap, ArrowRight } from "lucide-react";

function HorizontalTimeline() {
  const timelineItems = [
    {
      id: "1",
      title: "第一步",
      description: "创建项目",
      time: "1小时",
      status: "success",
      icon: <Zap className="h-4 w-4 text-white" />
    },
    {
      id: "2",
      title: "第二步",
      description: "配置环境",
      time: "2小时",
      status: "success",
      icon: <Zap className="h-4 w-4 text-white" />
    },
    {
      id: "3",
      title: "第三步",
      description: "编写代码",
      time: "8小时",
      status: "primary",
      icon: <ArrowRight className="h-4 w-4 text-white" />
    },
    {
      id: "4",
      title: "第四步",
      description: "测试部署",
      time: "4小时",
      status: "warning",
      icon: <ArrowRight className="h-4 w-4 text-white" />
    }
  ];

  return (
    <div className="w-full overflow-x-auto pb-4">
      <div className="min-w-max">
        <Timeline 
          items={timelineItems}
          mode="horizontal"
        />
      </div>
    </div>
  );
}

render(<HorizontalTimeline />);
`;

// API文档组件
function TimelineApiDocs() {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="font-medium text-lg mb-2">Timeline</h3>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">items</td>
                <td className="p-2 border">TimelineItem[]</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">时间线项列表</td>
              </tr>
              <tr>
                <td className="p-2 border">mode</td>
                <td className="p-2 border">vertical | horizontal</td>
                <td className="p-2 border">vertical</td>
                <td className="p-2 border">时间线方向</td>
              </tr>
              <tr>
                <td className="p-2 border">reverse</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">false</td>
                <td className="p-2 border">是否反转顺序</td>
              </tr>
              <tr>
                <td className="p-2 border">align</td>
                <td className="p-2 border">left | right | center | alternate</td>
                <td className="p-2 border">left</td>
                <td className="p-2 border">对齐方式</td>
              </tr>
              <tr>
                <td className="p-2 border">connectorStyle</td>
                <td className="p-2 border">solid | dashed | dotted</td>
                <td className="p-2 border">solid</td>
                <td className="p-2 border">连接线样式</td>
              </tr>
              <tr>
                <td className="p-2 border">connectorColor</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">连接线颜色</td>
              </tr>
              <tr>
                <td className="p-2 border">dotSize</td>
                <td className="p-2 border">sm | md | lg</td>
                <td className="p-2 border">md</td>
                <td className="p-2 border">点标记大小</td>
              </tr>
              <tr>
                <td className="p-2 border">dotShape</td>
                <td className="p-2 border">circle | square</td>
                <td className="p-2 border">circle</td>
                <td className="p-2 border">点标记形状</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div>
        <h3 className="font-medium text-lg mb-2">ActivityTimeline</h3>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">activities</td>
                <td className="p-2 border">Activity[]</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">活动列表</td>
              </tr>
              <tr>
                <td className="p-2 border">showAvatar</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">true</td>
                <td className="p-2 border">是否显示用户头像</td>
              </tr>
              <tr>
                <td className="p-2 border">dotSize</td>
                <td className="p-2 border">sm | md | lg</td>
                <td className="p-2 border">md</td>
                <td className="p-2 border">点标记大小</td>
              </tr>
              <tr>
                <td className="p-2 border">showConnector</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">true</td>
                <td className="p-2 border">是否显示连接线</td>
              </tr>
              <tr>
                <td className="p-2 border">onLoadMore</td>
                <td className="p-2 border">{"() => void"}</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">加载更多回调</td>
              </tr>
              <tr>
                <td className="p-2 border">hasMore</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">false</td>
                <td className="p-2 border">是否有更多数据</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div>
        <h3 className="font-medium text-lg mb-2">StageTimeline</h3>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">stages</td>
                <td className="p-2 border">Stage[]</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">阶段列表</td>
              </tr>
              <tr>
                <td className="p-2 border">currentStage</td>
                <td className="p-2 border">string | number</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">当前阶段ID</td>
              </tr>
              <tr>
                <td className="p-2 border">onChange</td>
                <td className="p-2 border">{`(stageId: string | number) => void`}</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">阶段变更回调</td>
              </tr>
              <tr>
                <td className="p-2 border">readonly</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">false</td>
                <td className="p-2 border">是否只读</td>
              </tr>
              <tr>
                <td className="p-2 border">compact</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">false</td>
                <td className="p-2 border">是否简洁模式</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

export default function TimelinePreview() {
  const examples = [
    {
      id: "basic-timeline",
      title: "基础时间轴",
      description: "展示时间顺序的流程信息",
      code: basicTimelineCode,
      scope: { 
        Timeline, 
        CheckCircle, 
        Clock, 
        AlertCircle, 
        React 
      },
    },
    {
      id: "timeline-variants",
      title: "时间轴样式变体",
      description: "不同样式的时间轴连接器和点标记",
      code: timelineVariantsCode,
      scope: { 
        Timeline, 
        Star,
        React 
      },
    },
    {
      id: "activity-timeline",
      title: "活动时间轴",
      description: "展示用户活动和系统事件",
      code: activityTimelineCode,
      scope: { 
        ActivityTimeline, 
        FileText, 
        MessageSquare, 
        CheckCircle, 
        AlertCircle, 
        Badge,
        React 
      },
    },
    {
      id: "stage-timeline",
      title: "阶段时间轴",
      description: "展示流程步骤和当前进度",
      code: stageTimelineCode,
      scope: { 
        StageTimeline, 
        User, 
        FileText, 
        Calendar, 
        Clock, 
        CheckCircle, 
        Button,
        useState: React.useState,
        React 
      },
    },
    {
      id: "horizontal-timeline",
      title: "水平时间轴",
      description: "水平方向展示的时间轴",
      code: horizontalTimelineCode,
      scope: { 
        Timeline, 
        Zap, 
        ArrowRight,
        React 
      },
    },
  ];

  return (
    <ComponentPreviewContainer
      title="时间轴 Timeline"
      description="用于展示时间流程、活动记录或步骤进度的组件"
      whenToUse="当需要展示一系列事件、活动或步骤的时间顺序时；当需要呈现用户活动历史记录时；当需要显示工作流程或任务进度时；当需要展示项目里程碑或重要时间节点时。"
      examples={examples}
      apiDocs={<TimelineApiDocs />}
    />
  );
} 
export enum PermissionType {
  Directory = "Directory",  // 目录
  Menu = "Menu",       // 菜单
  Button = "Button",     // 按钮
}

// 新增权限组类型定义
export interface PermissionGroup {
  id: string;
  code: string;
  name: string;
  description?: string;
  icon?: string;
  order?: number;
}

export interface PermissionNode {
  id: string
  name: string
  type: PermissionType
  children: PermissionNode[]
  enabled?: boolean
  permissionId?: string
  icon?: string
  parentId?: string
  order?: number
  path?: string
  isExternalLink?: boolean
  hideInMenu?: boolean
  target?: string
  component?: string
  groupCode?: string  // 所属权限组的code
  componentPath?: string  // 组件路径
  openInNewTab?: boolean  // 是否新页面打开
}

export interface PermissionConfig {
  id: string
  code: string
  name: string
  description?: string
  permissions: PermissionNode[]
}

// 角色权限关联类型
export interface RolePermission {
  id?: string;
  roleCode: string;
  permissionCode: string;
  createTime?: string;
}

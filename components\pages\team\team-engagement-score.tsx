"use client"

import { useEffect, useState } from "react"
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, ResponsiveContainer, Tooltip } from "recharts"

const data = [
  { name: "高度参与", value: 45, color: "hsl(var(--primary))" },
  { name: "中度参与", value: 30, color: "hsl(var(--orange-500, 24 95% 53%))" },
  { name: "低度参与", value: 15, color: "hsl(var(--purple-500, 269 94% 57%))" },
  { name: "未参与", value: 10, color: "hsl(var(--muted-foreground))" },
]

export function TeamEngagementScore() {
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
  }, [])

  if (!isMounted) {
    return <div className="h-[300px] flex items-center justify-center">加载中...</div>
  }

  return (
    <div className="flex flex-col items-center">
      <div className="relative h-[300px] w-full">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie data={data} cx="50%" cy="50%" innerRadius={60} outerRadius={80} paddingAngle={2} dataKey="value">
              {data.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Pie>
            <Tooltip
              content={({ active, payload }) => {
                if (active && payload && payload.length) {
                  return (
                    <div className="rounded-lg border bg-background p-2 shadow-sm">
                      <div className="grid grid-cols-2 gap-2">
                        <div className="flex flex-col">
                          <span className="text-[0.70rem] uppercase text-muted-foreground">类型</span>
                          <span className="font-bold text-sm">{payload[0].name}</span>
                        </div>
                        <div className="flex flex-col">
                          <span className="text-[0.70rem] uppercase text-muted-foreground">人数</span>
                          <span className="font-bold text-sm">{payload[0].value}人</span>
                        </div>
                      </div>
                    </div>
                  )
                }
                return null
              }}
            />
          </PieChart>
        </ResponsiveContainer>
        <div className="absolute inset-0 flex flex-col items-center justify-center">
          <span className="text-3xl font-bold">85%</span>
          <span className="text-sm text-muted-foreground">总体参与度</span>
        </div>
      </div>
      <div className="mt-4 grid grid-cols-2 gap-4">
        {data.map((item, index) => (
          <div key={index} className="flex items-center gap-2">
            <div className="h-3 w-3 rounded-full" style={{ backgroundColor: item.color }} />
            <span className="text-sm">
              {item.name}: {item.value}人
            </span>
          </div>
        ))}
      </div>
    </div>
  )
}

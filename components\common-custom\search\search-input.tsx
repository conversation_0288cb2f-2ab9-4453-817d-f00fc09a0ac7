"use client"

import { KeyboardEvent } from "react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Search, X, Loader2 } from "lucide-react"
import { cn } from "@/lib/utils"
import type { SearchInputProps } from "@/types/search"

/**
 * 搜索输入框组件
 */
export function SearchInput({
  value,
  onChange,
  placeholder = "搜索...",
  disabled = false,
  showClear = true,
  onClear,
  onSubmit,
  loading = false,
  icon,
  className,
  size = "md",
}: SearchInputProps) {
  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && onSubmit) {
      onSubmit()
    }
  }

  const handleClear = () => {
    onChange("")
    onClear?.()
  }

  const sizeClasses = {
    sm: "h-8 text-sm",
    md: "h-10",
    lg: "h-12 text-lg"
  }

  return (
    <div className={cn("relative", className)}>
      {icon ? (
        <div className={cn(
          "absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground", 
          size === "sm" ? "w-3.5 h-3.5" : size === "lg" ? "w-5 h-5" : "w-4 h-4"
        )}>
          {typeof icon === "function" ? 
            icon({ className: "w-full h-full" }) : 
            icon}
        </div>
      ) : (
        <Search 
          className={cn(
            "absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground", 
            size === "sm" ? "w-3.5 h-3.5" : size === "lg" ? "w-5 h-5" : "w-4 h-4"
          )} 
        />
      )}
      <Input
        type="text"
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        onKeyDown={handleKeyDown}
        disabled={disabled || loading}
        className={cn(
          "pl-10", 
          showClear || loading ? "pr-10" : "pr-4",
          sizeClasses[size]
        )}
      />
      {(loading || (showClear && value)) && (
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
          {loading ? (
            <Loader2 className={cn(
              "animate-spin text-muted-foreground",
              size === "sm" ? "w-3.5 h-3.5" : size === "lg" ? "w-5 h-5" : "w-4 h-4"
            )} />
          ) : (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={handleClear}
              className="h-6 w-6 p-0 hover:bg-transparent"
            >
              <X className={cn(
                "text-muted-foreground",
                size === "sm" ? "w-3.5 h-3.5" : size === "lg" ? "w-5 h-5" : "w-4 h-4"
              )} />
            </Button>
          )}
        </div>
      )}
    </div>
  )
} 
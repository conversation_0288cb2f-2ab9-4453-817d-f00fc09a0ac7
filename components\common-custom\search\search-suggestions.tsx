"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Clock, Search, X } from "lucide-react"
import { cn } from "@/lib/utils"
import type { SearchSuggestionsProps } from "@/types/search"

/**
 * 搜索建议组件
 * 显示搜索建议和最近搜索记录
 */
export function SearchSuggestions({
  searchTerm,
  suggestions = [],
  recentSearches = [],
  onSearch,
  onClearRecentSearch,
  className,
}: SearchSuggestionsProps) {
  if (!searchTerm && (!recentSearches || recentSearches.length === 0) && (!suggestions || suggestions.length === 0)) {
    return null
  }

  return (
    <div className={cn("bg-background border rounded-md shadow-md mt-1 p-2", className)}>
      {searchTerm && suggestions && suggestions.length > 0 && (
        <div className="mb-2">
          <div className="flex items-center px-2 py-1.5 text-sm text-muted-foreground">
            <Search className="w-3.5 h-3.5 mr-2" />
            <span>搜索建议</span>
          </div>
          <div className="mt-1">
            {suggestions.map((suggestion, index) => (
              <Button
                key={index}
                variant="ghost"
                className="w-full justify-start text-sm h-8 px-2 py-1"
                onClick={() => onSearch(suggestion)}
              >
                <span className="truncate">{suggestion}</span>
              </Button>
            ))}
          </div>
        </div>
      )}

      {recentSearches && recentSearches.length > 0 && (
        <div>
          <div className="flex items-center px-2 py-1.5 text-sm text-muted-foreground">
            <Clock className="w-3.5 h-3.5 mr-2" />
            <span>最近搜索</span>
          </div>
          <div className="mt-1">
            {recentSearches.map((term, index) => (
              <div key={index} className="flex items-center">
                <Button
                  variant="ghost"
                  className="flex-1 justify-start text-sm h-8 px-2 py-1"
                  onClick={() => onSearch(term)}
                >
                  <span className="truncate">{term}</span>
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 hover:bg-muted"
                  onClick={() => onClearRecentSearch(term)}
                >
                  <X className="h-3.5 w-3.5 text-muted-foreground" />
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
} 
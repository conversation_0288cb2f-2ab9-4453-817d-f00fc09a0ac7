"use client"

import { useEffect, useState } from "react"
import { Line, LineChart, ResponsiveContainer, Tooltip, <PERSON>A<PERSON>s, <PERSON><PERSON><PERSON><PERSON> } from "recharts"

const data = [
  {
    date: "1月",
    efficiency: 65,
    collaboration: 58,
    satisfaction: 70,
  },
  {
    date: "2月",
    efficiency: 68,
    collaboration: 62,
    satisfaction: 73,
  },
  {
    date: "3月",
    efficiency: 75,
    collaboration: 65,
    satisfaction: 75,
  },
  {
    date: "4月",
    efficiency: 72,
    collaboration: 68,
    satisfaction: 78,
  },
  {
    date: "5月",
    efficiency: 78,
    collaboration: 70,
    satisfaction: 80,
  },
  {
    date: "6月",
    efficiency: 82,
    collaboration: 73,
    satisfaction: 82,
  },
  {
    date: "7月",
    efficiency: 87,
    collaboration: 78,
    satisfaction: 85,
  },
]

export function TeamMetricsChart() {
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
  }, [])

  if (!isMounted) {
    return <div className="h-[300px] flex items-center justify-center">加载中...</div>
  }

  return (
    <ResponsiveContainer width="100%" height={300}>
      <LineChart data={data}>
        <XAxis dataKey="date" stroke="#888888" fontSize={12} tickLine={false} axisLine={false} />
        <YAxis
          stroke="#888888"
          fontSize={12}
          tickLine={false}
          axisLine={false}
          tickFormatter={(value) => `${value}%`}
        />
        <Tooltip
          content={({ active, payload }) => {
            if (active && payload && payload.length) {
              return (
                <div className="rounded-lg border bg-background p-2 shadow-sm">
                  <div className="grid grid-cols-2 gap-2">
                    <div className="flex flex-col">
                      <span className="text-[0.70rem] uppercase text-muted-foreground">日期</span>
                      <span className="font-bold text-sm">{payload[0].payload.date}</span>
                    </div>
                    {payload.map((item) => (
                      <div key={item.dataKey} className="flex flex-col">
                        <span className="text-[0.70rem] uppercase text-muted-foreground">
                          {item.dataKey === "efficiency"
                            ? "效率"
                            : item.dataKey === "collaboration"
                              ? "协作"
                              : "满意度"}
                        </span>
                        <span className="font-bold text-sm">{item.value}%</span>
                      </div>
                    ))}
                  </div>
                </div>
              )
            }
            return null
          }}
        />
        <Line type="monotone" dataKey="efficiency" stroke="hsl(var(--primary))" strokeWidth={2} activeDot={{ r: 6 }} />
        <Line
          type="monotone"
          dataKey="collaboration"
          stroke="hsl(var(--orange-500, 24 95% 53%))"
          strokeWidth={2}
          activeDot={{ r: 6 }}
        />
        <Line
          type="monotone"
          dataKey="satisfaction"
          stroke="hsl(var(--green-500, 142 71% 45%))"
          strokeWidth={2}
          activeDot={{ r: 6 }}
        />
      </LineChart>
    </ResponsiveContainer>
  )
}

import PermissionManagementSystem from "@/components/pages/permission/permission-management-system"
import {HeaderWithBreadcrumb} from "@/components/custom/breadcrumb";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { Shield, ShieldAlert } from "lucide-react";
import { NavigationLink } from "@/components/custom";

export default function Home() {

    // 面包屑数据
    const breadcrumbItems = [
        { label: "设置", href: "/settings" },
        { label: "权限管理", active: true }
    ]

    return (
        <div className="flex flex-col min-h-screen">
            <HeaderWithBreadcrumb items={breadcrumbItems}/>
            <main className="flex-1">
                <div className="container mx-auto px-6 py-6 max-w-7xl">
                    <div className="mb-6">
                        <div className="flex items-center justify-between">
                            <h1 className="text-2xl font-bold tracking-tight">权限管理</h1>
                            <div className="flex flex-wrap gap-2">
                                <NavigationLink 
                                    href="/settings/permission" 
                                    className={`inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input hover:bg-accent hover:text-accent-foreground h-9 px-4 bg-primary text-primary-foreground hover:bg-primary/90`}
                                >
                                    <ShieldAlert className="h-4 w-4 mr-2" />
                                    权限结构
                                </NavigationLink>
                                
                                <NavigationLink 
                                    href="/settings/roles" 
                                    className={`inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-4`}
                                >
                                    <Shield className="h-4 w-4 mr-2" />
                                    角色管理
                                </NavigationLink>
                            </div>
                        </div>
                        <p className="text-muted-foreground mt-2">管理系统权限结构和角色分配</p>
                    </div>
                    
                    <PermissionManagementSystem/>
                </div>
            </main>
        </div>
    )
}

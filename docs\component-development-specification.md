# 组件开发与管理规范

本文档整理了当前项目的组件预览、管理的最优实现方案和开发规范。

> 📋 **组件清单参考**：开发前请查阅 [`组件清单.md`](./组件清单.md) 了解当前94个可用组件及其分类。

## 1. 项目架构概览

### 1.1 目录结构
```
components/
├── ui/                     # shadcn/ui 基础组件
├── common-custom/          # 通用业务组件（可跨项目复用）
├── project-custom/         # 项目特定组件（集成项目依赖）
└── component-detail/       # 组件预览和文档系统

app/examples/              # 组件示例页面
types/                     # 类型定义
```

### 1.2 组件分层设计
1. **基础层 (ui/)**: shadcn/ui 组件，提供基础UI能力（42个组件）
2. **通用层 (common-custom/)**: 业务组件，无项目特定依赖（45个组件）
3. **项目层 (project-custom/)**: 集成项目特定依赖的组件（3个组件）
4. **系统层 (component-detail/)**: 组件预览和文档系统（4个组件）
5. **展示层 (examples/)**: 组件示例和文档

> 💡 **组件复用原则**：优先使用现有组件构建新功能，避免重复开发。详见[组件清单](./组件清单.md)中的依赖关系图。

## 2. 组件开发规范

### 2.1 组件结构规范

#### 简单组件（单文件）
```
components/common-custom/timeline.tsx
```

#### 复杂组件（多文件）
```
components/common-custom/data-table/
├── index.tsx              # 主导出文件
├── toolbar.tsx            # 子组件
├── column-header.tsx      # 子组件
└── types.ts              # 组件特定类型（可选）
```

### 2.2 组件命名规范
- 组件文件使用 kebab-case: `timeline.tsx`, `data-table.tsx`
- 组件名使用 PascalCase: `Timeline`, `DataTable`
- 类型定义使用 PascalCase + Props/Interface 后缀: `TimelineProps`, `TimelineItem`

### 2.3 导出规范
```tsx
// 主组件导出
export function Timeline({ ... }: TimelineProps) { ... }

// 子组件导出
export function ActivityTimeline({ ... }: ActivityTimelineProps) { ... }
export function StageTimeline({ ... }: StageTimelineProps) { ... }

// 容器组件导出
export function TimelineContainer({ children }: { children: ReactNode }) { ... }

// 类型导出（在组件文件末尾或单独的types文件中）
export type { TimelineProps, TimelineItem, ActivityTimelineProps, StageTimelineProps }
```

### 2.4 复杂组件的index.tsx规范
```tsx
"use client"

/**
 * 组件集合名称
 * 重新导出所有相关组件
 */

// 基础组件
export { MainComponent } from "./main-component"
export { SubComponent1 } from "./sub-component1"
export { SubComponent2 } from "./sub-component2"

// 导出类型
export * from "@/types/component-name"
// 或者
export * from "./types"
```

## 3. 类型定义规范

### 3.1 类型定义策略

#### 3.1.1 单文件组件类型定义
对于简单的单文件组件，类型定义应该直接在组件文件内部维护：

```tsx
"use client"

import { ReactNode } from "react"
import { LucideIcon } from "lucide-react"

// 类型定义直接在组件文件内
export interface BackButtonProps {
  /**
   * 跳转链接
   */
  href: string
  /**
   * 自定义类名
   */
  className?: string
  /**
   * 按钮内容
   */
  children?: ReactNode
  /**
   * 是否显示"返回"文字
   * @default false
   */
  showText?: boolean
  /**
   * 按钮尺寸
   * @default "md"
   */
  size?: "sm" | "md" | "lg"
}

export function BackButton({
  href,
  className,
  children,
  showText = false,
  size = "md",
  // ... 其他属性
}: BackButtonProps) {
  // 组件实现
}
```

**适用场景**：
- 组件逻辑相对简单，单个文件即可完成
- 类型定义不超过 100 行
- 类型不需要在其他地方复用
- 组件是独立的功能单元

#### 3.1.2 复杂组件类型定义
对于复杂的多文件组件，类型定义应该独立管理：

```
components/common-custom/data-table/
├── index.tsx              # 主导出文件
├── toolbar.tsx            # 工具栏组件
├── column-header.tsx      # 列头组件
├── types.ts              # 类型定义文件
└── json-table/           # 子功能模块
    ├── json-table.tsx
    └── json-table-toolbar.tsx
```

**types.ts 示例**：
```tsx
import { ComponentType, ReactNode } from "react"
import type { LucideIcon } from "lucide-react"

/**
 * 过滤选项
 */
export interface FilterOption {
  /**
   * 选项标签
   */
  label: string
  /**
   * 选项值
   */
  value: string
  /**
   * 图标
   */
  icon?: LucideIcon | ComponentType<{ className?: string }>
}

/**
 * 表格过滤器
 */
export interface TableFilter {
  /**
   * 过滤字段
   */
  key: string
  /**
   * 过滤器标题
   */
  title: string
  /**
   * 过滤器类型
   * @default "select"
   */
  type?: "select" | "multi-select" | "date-range" | "search"
  /**
   * 过滤选项
   */
  options?: FilterOption[]
}

// 更多复杂类型定义...
```

**适用场景**：
- 组件包含多个子组件或模块
- 类型定义超过 100 行
- 类型需要在多个文件中复用
- 组件具有复杂的数据结构

#### 3.1.3 全局通用类型定义
对于跨组件复用的通用类型，应该放在全局 `types/` 目录：

```tsx
// types/component-preview.ts
export interface ComponentExample {
  id: string
  title: string
  description?: string
  code: string
  extraFiles?: CodeFile[]
  scope?: Record<string, any>
}

export interface ComponentPreviewContainerProps {
  title: string
  description?: string
  whenToUse?: string
  examples: ComponentExample[]
  apiDocs?: React.ReactNode
  className?: string
}
```

**适用场景**：
- 类型被多个组件使用
- 系统级别的通用类型定义
- 与业务逻辑无关的基础类型

### 3.2 类型定义最佳实践

#### 3.2.1 命名规范
- 组件属性接口：`ComponentNameProps`
- 数据项接口：`ComponentNameItem`
- 配置接口：`ComponentNameConfig`
- 事件回调接口：`ComponentNameHandler`

#### 3.2.2 注释规范
```tsx
export interface ComponentProps {
  /**
   * 组件标题
   * 支持字符串或 ReactNode
   */
  title: string | ReactNode

  /**
   * 组件尺寸
   * @default "md"
   */
  size?: "sm" | "md" | "lg"

  /**
   * 点击事件回调
   * @param value - 点击的值
   * @param event - 原生事件对象
   */
  onClick?: (value: string, event: MouseEvent) => void

  /**
   * 是否禁用
   * @default false
   */
  disabled?: boolean
}
```

#### 3.2.3 类型导出规范
```tsx
// 单文件组件
export interface ComponentProps { ... }
export function Component(props: ComponentProps) { ... }

// 复杂组件 - index.tsx
export { MainComponent } from "./main-component"
export { SubComponent } from "./sub-component"
export * from "./types"

// 复杂组件 - types.ts
export interface MainComponentProps { ... }
export interface SubComponentProps { ... }
export type ComponentVariant = "default" | "primary" | "secondary"
```

## 4. 组件预览系统规范

### 4.1 预览系统架构

#### 4.1.1 预览系统组成
```
components/component-detail/     # 预览系统核心
├── preview-container.tsx       # 预览容器组件
├── preview.tsx                # 代码预览组件
└── index.tsx                  # 统一导出

app/examples/                   # 组件示例页面
├── component-example/
│   ├── page.tsx               # 示例页面
│   └── examples/              # 示例代码文件（推荐）
│       ├── basic.tsx
│       ├── advanced.tsx
│       └── index.ts

app/preview/[exampleId]/        # 独立预览系统
├── page.tsx                   # 预览页面路由
├── examples.ts                # 示例代码集合
└── client-preview-component.tsx # 客户端预览组件
```

#### 4.1.2 预览代码管理策略

**方案一：独立文件管理（推荐）**
```
app/examples/timeline-example/
├── page.tsx                   # 主页面
└── examples/                  # 示例代码目录
    ├── basic-timeline.tsx     # 基础时间轴示例
    ├── activity-timeline.tsx  # 活动时间轴示例
    ├── stage-timeline.tsx     # 阶段时间轴示例
    └── index.ts              # 示例导出文件
```

**examples/index.ts**：
```tsx
// 导出所有示例代码和配置
export const basicTimelineExample = {
  id: "basic-timeline",
  title: "基础时间轴",
  description: "展示时间顺序的流程信息",
  code: `// 从独立文件导入
import React from "react";
import { Timeline } from "@/components/common-custom/timeline";
import { CheckCircle, Clock, AlertCircle } from "lucide-react";

function BasicTimeline() {
  const timelineItems = [
    {
      id: "1",
      title: "项目创建",
      description: "项目初始化并设置基本参数",
      time: "2023-10-01",
      status: "success",
      icon: <CheckCircle className="h-4 w-4 text-white" />,
    },
    // 更多数据...
  ];

  return <Timeline items={timelineItems} />;
}

render(<BasicTimeline />);`,
  scope: { Timeline, CheckCircle, Clock, AlertCircle, React },
}

export const activityTimelineExample = {
  // 活动时间轴示例配置...
}

// 统一导出所有示例
export const allExamples = [
  basicTimelineExample,
  activityTimelineExample,
  // 更多示例...
]
```

**方案二：JSON配置管理**
```
app/examples/timeline-example/
├── page.tsx
└── config/
    ├── examples.json         # 示例元数据
    └── code/                # 代码文件
        ├── basic-timeline.ts
        ├── activity-timeline.ts
        └── stage-timeline.ts
```

**examples.json**：
```json
{
  "examples": [
    {
      "id": "basic-timeline",
      "title": "基础时间轴",
      "description": "展示时间顺序的流程信息",
      "codeFile": "basic-timeline.ts",
      "scope": ["Timeline", "CheckCircle", "Clock", "AlertCircle", "React"]
    }
  ]
}
```

### 4.2 组件预览分类实现

#### 4.2.1 单文件组件预览
适用于简单组件，如 `back-button`、`rating` 等：

```tsx
// app/examples/back-button-example/page.tsx
"use client"

import React from "react"
import { ComponentPreviewContainer } from "@/components/component-detail/preview-container"
import { BackButton } from "@/components/common-custom/back-button"
import { allExamples } from "./examples"

export default function BackButtonPreview() {
  return (
    <ComponentPreviewContainer
      title="返回按钮 BackButton"
      description="用于页面导航的返回按钮组件"
      whenToUse="当需要提供返回上一页或指定页面的导航功能时使用"
      examples={allExamples}
      apiDocs={<BackButtonApiDocs />}
    />
  );
}

function BackButtonApiDocs() {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="font-medium text-lg mb-2">BackButton</h3>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">href</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">跳转链接</td>
              </tr>
              <tr>
                <td className="p-2 border">showText</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">false</td>
                <td className="p-2 border">是否显示"返回"文字</td>
              </tr>
              <tr>
                <td className="p-2 border">size</td>
                <td className="p-2 border">"sm" | "md" | "lg"</td>
                <td className="p-2 border">"md"</td>
                <td className="p-2 border">按钮尺寸</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
```

#### 4.2.2 复杂组件预览
适用于多功能组件，如 `data-table`、`search` 等：

```tsx
// app/examples/data-table-example/page.tsx
"use client"

import React from "react"
import { ComponentPreviewContainer } from "@/components/component-detail/preview-container"
import {
  AdvancedDataTable,
  DataTableToolbar,
  DataTableColumnHeader
} from "@/components/common-custom/data-table"
import { allExamples } from "./examples"

export default function DataTablePreview() {
  return (
    <ComponentPreviewContainer
      title="数据表格 DataTable"
      description="功能丰富的数据表格组件，支持排序、筛选、分页等功能"
      whenToUse="当需要展示大量结构化数据，并提供交互功能时使用"
      examples={allExamples}
      apiDocs={<DataTableApiDocs />}
    />
  );
}

function DataTableApiDocs() {
  return (
    <div className="space-y-6">
      {/* 主组件API */}
      <div>
        <h3 className="font-medium text-lg mb-2">AdvancedDataTable</h3>
        {/* API表格 */}
      </div>

      {/* 子组件API */}
      <div>
        <h3 className="font-medium text-lg mb-2">DataTableToolbar</h3>
        {/* API表格 */}
      </div>

      <div>
        <h3 className="font-medium text-lg mb-2">DataTableColumnHeader</h3>
        {/* API表格 */}
      </div>
    </div>
  );
}
```

### 4.3 独立预览窗口实现

#### 4.3.1 路由设计
```
app/preview/[exampleId]/
├── page.tsx                   # 预览页面入口
├── preview-client.tsx         # 客户端预览组件
├── client-preview-component.tsx # 预览组件实现
├── live-preview.tsx          # Live预览包装器
├── examples.ts               # 示例代码集合
└── types.ts                  # 预览相关类型
```

#### 4.3.2 预览页面实现
```tsx
// app/preview/[exampleId]/page.tsx
import React from "react";
import exampleCollection, { ExampleKey } from "./examples";
import PreviewClient from "./preview-client";

interface PreviewPageProps {
  params: {
    exampleId: string;
  };
}

export default function PreviewPage({ params }: PreviewPageProps) {
  const { exampleId } = params;
  const exampleData = exampleCollection[exampleId as ExampleKey];

  if (!exampleData) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center space-y-4">
          <h1 className="text-xl font-semibold">示例不存在</h1>
          <p className="text-muted-foreground">找不到ID为 "{exampleId}" 的组件示例</p>
        </div>
      </div>
    );
  }

  return <PreviewClient code={exampleData.code} title={exampleData.title} />;
}
```

#### 4.3.3 示例代码集合管理
```tsx
// app/preview/[exampleId]/examples.ts
const examples = {
  "basic-timeline": {
    title: "基础时间轴",
    code: `
import React from "react";
import { Timeline } from "@/components/common-custom/timeline";
import { CheckCircle, Clock, AlertCircle } from "lucide-react";

function BasicTimeline() {
  const timelineItems = [
    {
      id: "1",
      title: "项目创建",
      description: "项目初始化并设置基本参数",
      time: "2023-10-01",
      status: "success",
      icon: <CheckCircle className="h-4 w-4 text-white" />,
    },
    // 更多数据...
  ];

  return <Timeline items={timelineItems} />;
}

render(<BasicTimeline />);
    `,
  },
  // 更多示例...
} as const;

export type ExampleKey = keyof typeof examples;
export default examples;
```

### 4.4 预览系统最佳实践

#### 4.4.1 代码示例编写规范
- 示例代码要完整可运行
- 包含必要的导入语句
- 使用 `render()` 函数渲染组件
- 提供合适的 scope 对象
- 示例数据要真实有意义

#### 4.4.2 预览性能优化
- 使用动态导入减少初始加载时间
- 合理使用 React.memo 避免不必要的重渲染
- 大型示例考虑代码分割
- 预览组件使用 `ssr: false` 避免服务端渲染问题

## 5. 项目特定组件规范

### 5.1 common-custom vs project-custom
- **common-custom**: 不依赖项目特定的hooks、providers等，可跨项目复用
- **project-custom**: 集成项目特定依赖，如 `use-toast`、`next/navigation` 等

### 5.2 project-custom组件模板
```tsx
"use client"

import { projectSpecificHook } from "@/hooks/project-hook"
import { 
  CommonComponent,
  type CommonComponentProps 
} from "@/components/common-custom/common-component"

/**
 * 项目特定的组件实现
 * 集成了项目特定的依赖
 */
export function ProjectComponent(props: CommonComponentProps) {
  const projectFeature = projectSpecificHook()
  
  return (
    <CommonComponent 
      {...props}
      // 注入项目特定功能
      onAction={(data) => {
        projectFeature.handle(data)
        props.onAction?.(data)
      }}
    />
  )
}

// 重新导出通用组件和类型
export { CommonComponent }
export type { CommonComponentProps }
```

## 6. 导航和路由规范

### 6.1 导航配置
在 `components/navigation/main-layout.tsx` 中配置组件导航：

```tsx
export const componentSections: NavSection[] = [
  {
    title: "分类名称",
    items: [
      {
        title: "组件名称",
        href: "/examples/component-example",
        isNew: true, // 新组件标识
      },
    ],
  },
]
```

### 6.2 路由规范
- 示例页面路由: `/examples/component-example`
- 路由名称使用 kebab-case
- 与组件文件名保持一致

## 7. 开发流程规范

### 7.1 新组件开发流程

#### 7.1.1 单文件组件开发流程
1. **需求分析**: 确定组件功能和API设计
2. **组件实现**: 在 `components/common-custom/` 创建组件文件
   - 在组件文件内直接定义类型
   - 实现组件逻辑
   - 添加完整的JSDoc注释
3. **示例开发**: 在 `app/examples/` 创建示例页面
   - 创建示例目录和页面文件
   - 编写多个使用场景的示例
   - 配置示例代码和scope
4. **导航配置**: 在 `components/navigation/main-layout.tsx` 中添加组件入口
5. **文档完善**: 完善API文档和使用说明
6. **测试验证**: 确保组件在各种场景下正常工作

#### 7.1.2 复杂组件开发流程
1. **需求分析**: 确定组件功能和API设计
2. **架构设计**: 规划组件的模块结构和文件组织
3. **类型定义**: 在组件目录下创建 `types.ts` 文件
4. **组件实现**:
   - 创建组件目录结构
   - 实现主组件和子组件
   - 创建 `index.tsx` 统一导出
5. **示例开发**: 创建综合性示例展示各种功能
6. **导航配置**: 添加组件导航入口
7. **文档完善**: 为主组件和子组件分别编写API文档
8. **测试验证**: 进行全面的功能测试

### 7.2 组件更新流程

#### 7.2.1 向后兼容更新
1. **API设计**: 新增功能时保持现有API不变
2. **类型更新**:
   - 单文件组件：直接在组件文件中更新类型
   - 复杂组件：在 `types.ts` 中更新类型定义
3. **示例更新**: 添加新功能的示例，保留原有示例
4. **文档更新**: 更新API文档，标注新增功能

#### 7.2.2 破坏性更新
1. **版本规划**: 制定迁移计划和时间表
2. **类型更新**: 更新类型定义，添加废弃标记
3. **示例更新**: 更新所有相关示例代码
4. **文档更新**: 详细说明变更内容和迁移指南
5. **通知机制**: 通过changelog等方式通知使用者

### 7.3 组件维护最佳实践

#### 7.3.1 代码质量保证
- 定期进行代码审查
- 保持类型定义的准确性
- 及时更新依赖和修复安全漏洞
- 监控组件性能表现

#### 7.3.2 文档维护
- 保持示例代码与组件实现同步
- 定期检查API文档的准确性
- 收集用户反馈并改进文档
- 维护组件使用统计和分析

## 8. 质量保证规范

### 8.1 代码质量
- 使用TypeScript严格模式
- 遵循ESLint和Prettier配置
- 组件要有完整的类型定义
- 重要逻辑要有注释说明

### 8.2 可访问性
- 使用语义化HTML标签
- 提供合适的ARIA属性
- 支持键盘导航
- 考虑屏幕阅读器兼容性

### 8.3 性能优化
- 合理使用React.memo
- 避免不必要的重渲染
- 大型组件考虑代码分割
- 图标等资源使用懒加载

## 9. 最佳实践总结

### 9.1 组件设计原则
- **单一职责**: 每个组件只负责一个功能
- **可组合性**: 组件可以灵活组合使用
- **可扩展性**: 预留扩展接口和自定义能力
- **一致性**: 保持API设计和视觉风格一致

### 9.2 API设计原则
- **直观性**: API命名要直观易懂
- **灵活性**: 提供合理的配置选项
- **默认值**: 为常用场景提供合理默认值
- **类型安全**: 充分利用TypeScript类型系统

### 9.3 文档编写原则
- **完整性**: 覆盖所有API和使用场景
- **实用性**: 提供实际可用的示例代码
- **清晰性**: 说明要简洁明了
- **及时性**: 与代码保持同步更新

## 10. 实施计划与迁移指南

### 10.1 现有项目优化建议

#### 10.1.1 渐进式类型定义优化
**阶段一：单文件组件优化（优先级：高）**
- 将简单组件的类型定义迁移到组件文件内部
- 目标组件：`back-button`、`rating`、`pagination` 等
- 预计工作量：1-2周
- 迁移成本：低

**阶段二：复杂组件类型整理（优先级：中）**
- 整理复杂组件的类型定义结构
- 目标组件：`data-table`、`search`、`dashboard` 等
- 预计工作量：2-3周
- 迁移成本：中等

**阶段三：全局类型优化（优先级：低）**
- 清理和优化全局类型定义
- 移除未使用的类型定义
- 预计工作量：1周
- 迁移成本：低

#### 10.1.2 预览代码管理优化
**阶段一：示例代码结构化（优先级：高）**
- 将硬编码的示例代码提取到独立文件
- 建立统一的示例代码管理机制
- 预计工作量：2-3周
- 迁移成本：中等

**阶段二：预览系统增强（优先级：中）**
- 完善独立预览窗口功能
- 优化预览性能和用户体验
- 预计工作量：1-2周
- 迁移成本：低

### 10.2 新组件开发指南

#### 10.2.1 组件类型判断标准
**单文件组件标准**：
- 组件逻辑少于200行代码
- 类型定义少于50行
- 不包含子组件
- 功能相对独立

**复杂组件标准**：
- 组件逻辑超过200行代码
- 包含多个子组件或模块
- 类型定义复杂且需要复用
- 具有多种使用模式

#### 10.2.2 开发检查清单
**组件实现检查**：
- [ ] 组件具有完整的TypeScript类型定义
- [ ] 所有属性都有JSDoc注释
- [ ] 组件支持className属性用于样式扩展
- [ ] 组件具有合理的默认值
- [ ] 组件通过了基本的可访问性测试

**示例和文档检查**：
- [ ] 至少包含3个不同场景的使用示例
- [ ] 示例代码可以独立运行
- [ ] API文档完整且准确
- [ ] 包含"何时使用"的说明
- [ ] 在导航中正确配置

**质量保证检查**：
- [ ] 组件在不同屏幕尺寸下正常显示
- [ ] 组件支持暗色模式（如果适用）
- [ ] 组件性能表现良好
- [ ] 代码通过ESLint和Prettier检查

### 10.3 团队协作规范

#### 10.3.1 代码审查要点
- 检查类型定义的完整性和准确性
- 验证示例代码的可运行性
- 确认API设计的一致性
- 评估组件的可复用性

#### 10.3.2 文档维护责任
- 组件开发者：负责初始文档编写
- 团队Lead：负责文档质量审查
- 产品经理：负责使用场景验证
- 设计师：负责视觉规范确认

### 10.4 工具和自动化

#### 10.4.1 开发工具推荐
- **类型检查**：TypeScript strict mode
- **代码格式化**：Prettier + ESLint
- **组件测试**：Jest + React Testing Library
- **文档生成**：基于JSDoc的自动化文档生成

#### 10.4.2 自动化流程
```bash
# 组件开发脚本
npm run component:create <component-name>  # 创建组件模板
npm run component:example <component-name> # 创建示例页面
npm run component:docs <component-name>    # 生成API文档
npm run component:test <component-name>    # 运行组件测试
```

### 10.5 成功指标

#### 10.5.1 开发效率指标
- 新组件开发时间减少30%
- 组件复用率提升50%
- 文档维护成本降低40%
- 代码审查时间减少25%

#### 10.5.2 质量指标
- 组件API一致性达到95%
- 文档准确性达到98%
- 组件测试覆盖率达到80%
- 用户满意度达到90%

## 11. 总结

这套组件开发与管理规范基于时间轴模块的成功实践，结合项目实际需求，提供了完整的组件开发指导方针。通过规范化的类型定义、预览系统和开发流程，能够显著提升组件库的质量和开发效率。

### 11.1 核心价值
- **一致性**：统一的开发规范确保组件库的一致性
- **可维护性**：清晰的结构和文档降低维护成本
- **可扩展性**：灵活的架构支持未来功能扩展
- **开发效率**：标准化流程提升开发效率

### 11.2 持续改进
规范文档将根据项目发展和团队反馈持续优化，确保始终符合最佳实践和团队需求。建议每季度进行一次规范评审和更新。

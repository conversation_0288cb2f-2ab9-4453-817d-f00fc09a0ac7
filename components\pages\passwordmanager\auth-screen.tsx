"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Lock } from "lucide-react"
import { PasswordDashboard } from "./password-dashboard"

export function AuthScreen() {
  const [authenticated, setAuthenticated] = useState(false)
  const [password, setPassword] = useState("")
  const [error, setError] = useState("")

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    // 在实际应用中，这里应该进行真正的密码验证
    // 这里仅作为演示，使用任何非空密码都可以通过
    if (password.trim() === "") {
      setError("请输入主密码")
      return
    }

    setAuthenticated(true)
  }

  if (authenticated) {
    return <PasswordDashboard />
  }

  return (
    <div className="flex justify-center items-center min-h-[70vh]">
      <Card className="w-full max-w-md border border-border/30 rounded-xl bg-card/95 ring-1 ring-border/5">
        <CardHeader className="space-y-4">
          <div className="flex justify-center mb-6">
            <div className="bg-primary/10 p-4 rounded-full">
              <Lock className="h-8 w-8 text-primary" />
            </div>
          </div>
          <CardTitle className="text-2xl text-center">欢迎回来</CardTitle>
          <CardDescription className="text-center text-base">请输入您的主密码以访问密码库</CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-6">
            <div className="space-y-3">
              <Label htmlFor="master-password" className="text-center block">主密码</Label>
              <Input
                id="master-password"
                type="password"
                placeholder="输入您的主密码"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="text-center h-11"
              />
              {error && <p className="text-sm text-destructive text-center">{error}</p>}
            </div>
          </CardContent>
          <CardFooter className="pt-4 pb-6 px-6">
            <Button type="submit" className="w-full h-11 text-base font-medium">
              解锁
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  )
} 
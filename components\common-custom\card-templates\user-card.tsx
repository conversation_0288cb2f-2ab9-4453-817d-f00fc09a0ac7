"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import { Eye, MessageCircle, Mail, Share2 } from "lucide-react"
import { cn } from "@/lib/utils"
import { UserCardItem } from "@/types/card-templates"

/**
 * 用户卡片组件
 * 用于展示用户信息，支持进度、状态等
 */
export function UserCard({
  id,
  name,
  avatar,
  avatarFallback,
  position,
  status,
  statusText,
  contactInfo,
  progress,
  lastActive,
  completedTasks,
  totalTasks,
  teamRating,
  onViewDetails,
  onSendMessage,
  onSendEmail,
  onShare,
  renderCustomContent,
  renderFooter,
  className
}: UserCardItem) {
  // 根据状态获取对应的徽章样式
  const getStatusBadge = () => {
    if (!status) return null;

    const badgeStyles = {
      online: "bg-green-500",
      busy: "bg-red-500",
      away: "bg-yellow-500",
      offline: "bg-gray-500",
      active: "bg-blue-500",
      pending: "bg-amber-500",
      blocked: "bg-slate-500"
    };

    const styles = status && status in badgeStyles 
      ? badgeStyles[status as keyof typeof badgeStyles]
      : "bg-gray-500";
    
    return (
      <Badge
        variant="outline"
        className="ml-2 px-2 py-0.5 text-xs"
      >
        <div className={cn("w-2 h-2 rounded-full mr-1", styles)} />
        {statusText || status}
      </Badge>
    );
  }
  
  return (
    <Card className={cn("transition-all hover:shadow-md", className)}>
      <CardHeader className="pb-2">
        <div className="flex items-start justify-between">
          <div className="flex items-center">
            <Avatar className="h-10 w-10 mr-3">
              {avatar && <AvatarImage src={avatar} alt={name} />}
              {avatarFallback && <AvatarFallback>{avatarFallback}</AvatarFallback>}
            </Avatar>
            <div>
              <div className="flex items-center">
                <CardTitle className="text-base">{name}</CardTitle>
                {getStatusBadge()}
              </div>
              {position && <CardDescription>{position}</CardDescription>}
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pb-4">
        {renderCustomContent ? (
          renderCustomContent()
        ) : (
          <>
            {(progress !== undefined || (completedTasks !== undefined && totalTasks !== undefined)) && (
              <div className="mb-3">
                {progress !== undefined ? (
                  <>
                    <div className="flex items-center justify-between mb-1 text-sm">
                      <span>进度</span>
                      <span>{Math.round(progress)}%</span>
                    </div>
                    <Progress value={progress} className="h-1.5" />
                  </>
                ) : (
                  <>
                    <div className="flex items-center justify-between mb-1 text-sm">
                      <span>完成任务</span>
                      <span>{completedTasks || 0}/{totalTasks || 0}</span>
                    </div>
                    <Progress 
                      value={totalTasks && totalTasks > 0 ? ((completedTasks || 0) / totalTasks) * 100 : 0}
                      className="h-1.5"
                    />
                  </>
                )}
              </div>
            )}
            
            {(contactInfo || lastActive || teamRating !== undefined) && (
              <div className="space-y-2 mb-3 text-sm">
                {contactInfo && (
                  <div className="text-muted-foreground">
                    {contactInfo.email && <div>邮箱: {contactInfo.email}</div>}
                    {contactInfo.phone && <div>电话: {contactInfo.phone}</div>}
                    {contactInfo.location && <div>地址: {contactInfo.location}</div>}
                    {Object.entries(contactInfo).map(([key, value]) => {
                      if (key !== 'email' && key !== 'phone' && key !== 'location' && value) {
                        return <div key={key}>{key}: {value}</div>
                      }
                      return null;
                    })}
                  </div>
                )}
                {lastActive && (
                  <div className="text-muted-foreground">
                    最近活动: {lastActive}
                  </div>
                )}
                {teamRating !== undefined && (
                  <div className="text-muted-foreground">
                    团队评分: {teamRating}
                  </div>
                )}
              </div>
            )}
            
            {renderFooter ? (
              renderFooter()
            ) : (
              <div className="flex gap-2 justify-end">
                {onViewDetails && (
                  <Button 
                    size="sm" 
                    variant="secondary"
                    onClick={() => onViewDetails?.()}
                  >
                    <Eye className="h-4 w-4 mr-1" />
                    详情
                  </Button>
                )}
                {onSendMessage && (
                  <Button 
                    size="sm" 
                    variant="outline"
                    onClick={() => onSendMessage?.()}
                  >
                    <MessageCircle className="h-4 w-4 mr-1" />
                    消息
                  </Button>
                )}
                {onSendEmail && (
                  <Button 
                    size="sm" 
                    variant="outline"
                    onClick={() => onSendEmail?.()}
                  >
                    <Mail className="h-4 w-4 mr-1" />
                    邮件
                  </Button>
                )}
                {onShare && (
                  <Button 
                    size="sm" 
                    variant="outline"
                    onClick={() => onShare?.()}
                  >
                    <Share2 className="h-4 w-4" />
                  </Button>
                )}
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  )
} 
"use client"

import React from "react"
import { ComponentPreviewContainer } from "@/components/component-detail/preview-container"
import { allExamples } from "./examples"

// ============================================================================
// API文档组件
// ============================================================================

function DashboardApiDocs() {
  return (
    <div className="space-y-6">
      {/* StatCard 组件API */}
      <div>
        <h3 className="font-medium text-lg mb-2">StatCard</h3>
        <p className="text-muted-foreground mb-4">用于展示关键指标的统计卡片组件</p>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">title</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">统计项标题</td>
              </tr>
              <tr>
                <td className="p-2 border">value</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">统计数值</td>
              </tr>
              <tr>
                <td className="p-2 border">change</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">变化百分比</td>
              </tr>
              <tr>
                <td className="p-2 border">trend</td>
                <td className="p-2 border">"up" | "down" | "neutral"</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">趋势方向</td>
              </tr>
              <tr>
                <td className="p-2 border">description</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">描述信息</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* QuickActions 组件API */}
      <div>
        <h3 className="font-medium text-lg mb-2">QuickActions</h3>
        <p className="text-muted-foreground mb-4">快速操作按钮组件</p>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">actions</td>
                <td className="p-2 border">Action[]</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">操作按钮列表</td>
              </tr>
              <tr>
                <td className="p-2 border">title</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">"快速操作"</td>
                <td className="p-2 border">组件标题</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* ActivityList 组件API */}
      <div>
        <h3 className="font-medium text-lg mb-2">ActivityList</h3>
        <p className="text-muted-foreground mb-4">活动列表组件，显示最近的操作记录</p>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">activities</td>
                <td className="p-2 border">Activity[]</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">活动记录列表</td>
              </tr>
              <tr>
                <td className="p-2 border">title</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">"最近活动"</td>
                <td className="p-2 border">组件标题</td>
              </tr>
              <tr>
                <td className="p-2 border">maxItems</td>
                <td className="p-2 border">number</td>
                <td className="p-2 border">10</td>
                <td className="p-2 border">最大显示条数</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* SystemStatus 组件API */}
      <div>
        <h3 className="font-medium text-lg mb-2">SystemStatus</h3>
        <p className="text-muted-foreground mb-4">系统状态监控组件</p>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">statuses</td>
                <td className="p-2 border">SystemStatusItem[]</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">系统状态列表</td>
              </tr>
              <tr>
                <td className="p-2 border">title</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">"系统状态"</td>
                <td className="p-2 border">组件标题</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* AnalyticsDashboard 组件API */}
      <div>
        <h3 className="font-medium text-lg mb-2">AnalyticsDashboard</h3>
        <p className="text-muted-foreground mb-4">综合仪表板组件，整合多个数据展示模块</p>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">title</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">"仪表板"</td>
                <td className="p-2 border">仪表板标题</td>
              </tr>
              <tr>
                <td className="p-2 border">data</td>
                <td className="p-2 border">DashboardData</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">仪表板数据</td>
              </tr>
              <tr>
                <td className="p-2 border">refreshInterval</td>
                <td className="p-2 border">number</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">自动刷新间隔(毫秒)</td>
              </tr>
              <tr>
                <td className="p-2 border">onRefresh</td>
                <td className="p-2 border">() =&gt; void</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">刷新回调函数</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* 使用指南 */}
      <div>
        <h3 className="font-medium text-lg mb-2">使用指南</h3>
        <div className="space-y-4 text-sm">
          <div>
            <h4 className="font-medium mb-2">基本用法</h4>
            <p className="text-muted-foreground mb-2">
              最简单的统计卡片使用方式：
            </p>
            <pre className="bg-muted p-3 rounded-md overflow-x-auto">
              <code>{`<StatCard
  title="总用户数"
  value="12,345"
  change="+12%"
  trend="up"
  description="较上月增长"
/>`}</code>
            </pre>
          </div>
          
          <div>
            <h4 className="font-medium mb-2">组合使用</h4>
            <p className="text-muted-foreground mb-2">
              组合多个组件创建完整的仪表板：
            </p>
            <pre className="bg-muted p-3 rounded-md overflow-x-auto">
              <code>{`<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
  {stats.map((stat, index) => (
    <StatCard key={index} {...stat} />
  ))}
</div>

<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
  <ActivityList activities={activities} />
  <SystemStatus statuses={systemStatus} />
</div>`}</code>
            </pre>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function DashboardExamplePage() {
  return (
    <ComponentPreviewContainer
      title="仪表板 Dashboard"
      description="用于构建数据仪表板的组件集合，包括统计卡片、活动列表、系统状态监控等功能模块。"
      whenToUse="当需要构建数据分析仪表板、管理后台首页、业务监控面板时使用。适用于展示关键业务指标、系统状态、用户活动等场景，帮助用户快速了解业务概况和系统运行状态。"
      examples={allExamples}
      apiDocs={<DashboardApiDocs />}
    />
  )
}

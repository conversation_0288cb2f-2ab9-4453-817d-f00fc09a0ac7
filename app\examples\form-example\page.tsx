"use client"

import React from "react"
import { ComponentPreviewContainer } from "@/components/component-detail/preview-container"
import { Form as CustomForm } from "@/components/common-custom/form"
import { Button } from "@/components/ui/button"
import { 
  Form, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormControl, 
  FormDescription, 
  FormMessage 
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Switch } from "@/components/ui/switch"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { 
  Popover, 
  PopoverContent, 
  PopoverTrigger 
} from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { CalendarIcon } from "lucide-react"
import { cn } from "@/lib/utils"
import { format } from "date-fns"

// 基础表单代码示例
const basicFormCode = `
import React, { useState } from "react"
import { Form as CustomForm } from "@/components/common-custom/form"

function BasicFormExample() {
  const [formValues, setFormValues] = useState({
    name: "",
    email: "",
    department: "",
    bio: ""
  })
  
  const [formErrors, setFormErrors] = useState({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSuccess, setIsSuccess] = useState(false)
  
  const handleFieldChange = (name, value) => {
    setFormValues(prev => ({
      ...prev,
      [name]: value
    }))
    
    // 清除错误
    if (formErrors[name]) {
      setFormErrors(prev => {
        const newErrors = {...prev}
        delete newErrors[name]
        return newErrors
      })
    }
  }
  
  const handleSubmit = async (values) => {
    setIsSubmitting(true)
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      setIsSuccess(true)
      console.log("表单提交成功:", values)
      
      // 重置成功状态
      setTimeout(() => {
        setIsSuccess(false)
      }, 2000)
    } catch (error) {
      console.error(error)
    } finally {
      setIsSubmitting(false)
    }
  }
  
  // 表单字段配置
  const fields = [
    {
      name: "name",
      label: "姓名",
      type: "text",
      placeholder: "请输入您的姓名",
      required: true,
      description: "这将是您的显示名称"
    },
    {
      name: "email",
      label: "电子邮件",
      type: "email",
      placeholder: "<EMAIL>",
      required: true,
      description: "用于接收系统通知"
    },
    {
      name: "department",
      label: "部门",
      type: "select",
      placeholder: "请选择部门",
      required: true,
      options: [
        { label: "研发部", value: "engineering" },
        { label: "市场部", value: "marketing" },
        { label: "销售部", value: "sales" },
        { label: "客服部", value: "support" }
      ]
    },
    {
      name: "bio",
      label: "个人简介",
      type: "textarea",
      placeholder: "简单介绍一下自己...",
      required: false,
      description: "您可以介绍一下自己的专业背景和技能"
    }
  ]
  
  return (
    <CustomForm
      title="个人信息"
      description="填写您的个人信息用于系统识别"
      fields={fields}
      values={formValues}
      errors={formErrors}
      onChange={handleFieldChange}
      onSubmit={handleSubmit}
      isLoading={isSubmitting}
      isSuccess={isSuccess}
      successMessage="提交成功!"
    />
  )
}

render(<BasicFormExample />)
`

// 分组表单代码示例
const sectionFormCode = `
import React, { useState } from "react"
import { Form as CustomForm } from "@/components/common-custom/form"

function SectionFormExample() {
  const [formValues, setFormValues] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    address: "",
    city: "",
    state: "",
    zipCode: "",
    company: "",
    jobTitle: "",
    department: ""
  })
  
  const [formErrors, setFormErrors] = useState({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  const handleFieldChange = (name, value) => {
    setFormValues(prev => ({
      ...prev,
      [name]: value
    }))
    
    // 清除错误
    if (formErrors[name]) {
      setFormErrors(prev => {
        const newErrors = {...prev}
        delete newErrors[name]
        return newErrors
      })
    }
  }
  
  const handleSubmit = async (values) => {
    setIsSubmitting(true)
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      console.log("表单提交成功:", values)
    } catch (error) {
      console.error(error)
    } finally {
      setIsSubmitting(false)
    }
  }
  
  // 表单分组配置
  const sections = [
    {
      title: "个人信息",
      description: "您的基本个人信息",
      fields: [
        {
          name: "firstName",
          label: "名",
          type: "text",
          placeholder: "请输入名",
          required: true
        },
        {
          name: "lastName",
          label: "姓",
          type: "text",
          placeholder: "请输入姓",
          required: true
        },
        {
          name: "email",
          label: "电子邮件",
          type: "email",
          placeholder: "<EMAIL>",
          required: true
        },
        {
          name: "phone",
          label: "电话号码",
          type: "tel",
          placeholder: "请输入电话号码",
          required: false
        }
      ]
    },
    {
      title: "联系地址",
      description: "您的邮寄地址信息",
      fields: [
        {
          name: "address",
          label: "街道地址",
          type: "text",
          placeholder: "请输入街道地址",
          required: true
        },
        {
          name: "city",
          label: "城市",
          type: "text",
          placeholder: "请输入城市",
          required: true
        },
        {
          name: "state",
          label: "省份",
          type: "text",
          placeholder: "请输入省份",
          required: true
        },
        {
          name: "zipCode",
          label: "邮政编码",
          type: "text",
          placeholder: "请输入邮政编码",
          required: true
        }
      ]
    },
    {
      title: "工作信息",
      description: "您的职业相关信息",
      fields: [
        {
          name: "company",
          label: "公司名称",
          type: "text",
          placeholder: "请输入公司名称",
          required: false
        },
        {
          name: "jobTitle",
          label: "职位",
          type: "text",
          placeholder: "请输入职位",
          required: false
        },
        {
          name: "department",
          label: "部门",
          type: "select",
          placeholder: "请选择部门",
          required: false,
          options: [
            { label: "研发部", value: "engineering" },
            { label: "市场部", value: "marketing" },
            { label: "销售部", value: "sales" },
            { label: "客服部", value: "support" }
          ]
        }
      ]
    }
  ]
  
  return (
    <CustomForm
      title="完整个人资料"
      description="请完善您的个人资料信息"
      sections={sections}
      values={formValues}
      errors={formErrors}
      onChange={handleFieldChange}
      onSubmit={handleSubmit}
      isLoading={isSubmitting}
    />
  )
}

render(<SectionFormExample />)
`

// 水平布局表单代码示例
const horizontalFormCode = `
import React, { useState } from "react"
import { Form as CustomForm } from "@/components/common-custom/form"

function HorizontalFormExample() {
  const [formValues, setFormValues] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    bio: ""
  })
  
  const [formErrors, setFormErrors] = useState({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  const handleFieldChange = (name, value) => {
    setFormValues(prev => ({
      ...prev,
      [name]: value
    }))
    
    // 清除错误
    if (formErrors[name]) {
      setFormErrors(prev => {
        const newErrors = {...prev}
        delete newErrors[name]
        return newErrors
      })
    }
  }
  
  const handleSubmit = async (values) => {
    setIsSubmitting(true)
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      console.log("表单提交成功:", values)
    } catch (error) {
      console.error(error)
    } finally {
      setIsSubmitting(false)
    }
  }
  
  // 表单字段配置
  const fields = [
    {
      name: "firstName",
      label: "名",
      type: "text",
      placeholder: "请输入名",
      required: true
    },
    {
      name: "lastName",
      label: "姓",
      type: "text",
      placeholder: "请输入姓",
      required: true
    },
    {
      name: "email",
      label: "电子邮件",
      type: "email",
      placeholder: "<EMAIL>",
      required: true
    },
    {
      name: "phone",
      label: "电话号码",
      type: "tel",
      placeholder: "请输入电话号码",
      required: false
    },
    {
      name: "bio",
      label: "个人简介",
      type: "textarea",
      placeholder: "简单介绍一下自己...",
      required: false
    }
  ]
  
  return (
    <CustomForm
      title="个人信息"
      description="填写您的个人信息用于系统识别"
      fields={fields}
      values={formValues}
      errors={formErrors}
      onChange={handleFieldChange}
      onSubmit={handleSubmit}
      isLoading={isSubmitting}
      layout="horizontal"
    />
  )
}

render(<HorizontalFormExample />)
`

// 表单对话框代码示例
const formDialogCode = `
import React, { useState } from "react"
import { Form as CustomForm } from "@/components/common-custom/form"
import { Button } from "@/components/ui/button"

function FormDialogExample() {
  const [dialogOpen, setDialogOpen] = useState(false)
  const [formValues, setFormValues] = useState({
    name: "",
    email: "",
    role: "",
    active: false,
    bio: ""
  })
  
  const [formErrors, setFormErrors] = useState({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  const handleFieldChange = (name, value) => {
    setFormValues(prev => ({
      ...prev,
      [name]: value
    }))
    
    // 清除错误
    if (formErrors[name]) {
      setFormErrors(prev => {
        const newErrors = {...prev}
        delete newErrors[name]
        return newErrors
      })
    }
  }
  
  const handleSubmit = async (values) => {
    setIsSubmitting(true)
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      console.log("表单提交成功:", values)
      setDialogOpen(false)
    } catch (error) {
      console.error(error)
    } finally {
      setIsSubmitting(false)
    }
  }
  
  // 表单字段配置
  const fields = [
    {
      name: "name",
      label: "姓名",
      type: "text",
      placeholder: "请输入姓名",
      required: true
    },
    {
      name: "email",
      label: "电子邮件",
      type: "email",
      placeholder: "<EMAIL>",
      required: true
    },
    {
      name: "role",
      label: "角色",
      type: "select",
      placeholder: "请选择角色",
      required: true,
      options: [
        { label: "管理员", value: "admin" },
        { label: "编辑", value: "editor" },
        { label: "用户", value: "user" }
      ]
    },
    {
      name: "active",
      label: "激活状态",
      type: "switch",
      description: "是否激活此用户"
    },
    {
      name: "bio",
      label: "个人简介",
      type: "textarea",
      placeholder: "简单介绍一下...",
      required: false
    }
  ]
  
  return (
    <div>
      <Button onClick={() => setDialogOpen(true)}>
        打开表单对话框
      </Button>
      
      <CustomForm
        title="创建用户"
        description="添加新用户到系统"
        fields={fields}
        values={formValues}
        errors={formErrors}
        onChange={handleFieldChange}
        onSubmit={handleSubmit}
        isLoading={isSubmitting}
        isDialog={true}
        dialogOpen={dialogOpen}
        onDialogOpenChange={setDialogOpen}
        dialogMaxWidth="md"
      />
    </div>
  )
}

render(<FormDialogExample />)
`

// Shadcn/ui表单代码示例
const shadcnFormCode = `
import React from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import { 
  Form, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormControl, 
  FormDescription, 
  FormMessage 
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card"

// 定义表单验证架构
const formSchema = z.object({
  name: z.string().min(2, {
    message: "名称至少需要2个字符。",
  }),
  email: z.string().email({
    message: "请输入有效的电子邮件地址。",
  }),
  department: z.string({
    required_error: "请选择部门。",
  }),
  bio: z.string().min(10, {
    message: "简介至少需要10个字符。",
  }).max(500, {
    message: "简介不能超过500个字符。",
  }).optional(),
})

function ShadcnFormExample() {
  // 初始化表单
  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      email: "",
      department: "",
      bio: "",
    },
  })

  // 表单提交处理
  function onSubmit(values) {
    console.log(values)
    // 模拟表单提交
    setTimeout(() => {
      alert("表单提交成功！")
    }, 500)
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>个人信息</CardTitle>
        <CardDescription>填写您的个人信息用于系统识别。</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>姓名</FormLabel>
                  <FormControl>
                    <Input placeholder="张三" {...field} />
                  </FormControl>
                  <FormDescription>
                    这将是您的显示名称。
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>电子邮件</FormLabel>
                  <FormControl>
                    <Input placeholder="<EMAIL>" {...field} />
                  </FormControl>
                  <FormDescription>
                    用于接收系统通知。
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="department"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>部门</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="选择部门" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="engineering">研发部</SelectItem>
                      <SelectItem value="marketing">市场部</SelectItem>
                      <SelectItem value="sales">销售部</SelectItem>
                      <SelectItem value="support">客服部</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    选择您所属的部门。
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="bio"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>个人简介</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="简单介绍一下自己..."
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    您可以介绍一下自己的专业背景和技能。
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button type="submit" className="w-full">提交</Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}

render(<ShadcnFormExample />)
`

// API文档组件
function FormApiDocs() {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="font-medium text-lg mb-2">Form 组件</h3>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">title</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">表单标题</td>
              </tr>
              <tr>
                <td className="p-2 border">description</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">表单描述</td>
              </tr>
              <tr>
                <td className="p-2 border">fields</td>
                <td className="p-2 border">FormFieldProps[]</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">表单字段配置</td>
              </tr>
              <tr>
                <td className="p-2 border">values</td>
                <td className="p-2 border">Record&lt;string, any&gt;</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">表单字段值</td>
              </tr>
              <tr>
                <td className="p-2 border">errors</td>
                <td className="p-2 border">Record&lt;string, string&gt;</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">表单字段错误</td>
              </tr>
              <tr>
                <td className="p-2 border">onChange</td>
                <td className="p-2 border">(name: string, value: any) =&gt; void</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">字段变更回调</td>
              </tr>
              <tr>
                <td className="p-2 border">onSubmit</td>
                <td className="p-2 border">(values: T) =&gt; void</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">表单提交回调</td>
              </tr>
              <tr>
                <td className="p-2 border">submitLabel</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">"提交"</td>
                <td className="p-2 border">提交按钮文本</td>
              </tr>
              <tr>
                <td className="p-2 border">cancelLabel</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">"取消"</td>
                <td className="p-2 border">取消按钮文本</td>
              </tr>
              <tr>
                <td className="p-2 border">onCancel</td>
                <td className="p-2 border">() =&gt; void</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">取消按钮回调</td>
              </tr>
              <tr>
                <td className="p-2 border">isLoading</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">false</td>
                <td className="p-2 border">加载状态</td>
              </tr>
              <tr>
                <td className="p-2 border">isSuccess</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">false</td>
                <td className="p-2 border">成功状态</td>
              </tr>
              <tr>
                <td className="p-2 border">layout</td>
                <td className="p-2 border">"vertical" | "horizontal"</td>
                <td className="p-2 border">"vertical"</td>
                <td className="p-2 border">表单布局方式</td>
              </tr>
              <tr>
                <td className="p-2 border">sections</td>
                <td className="p-2 border">{`{ title: string, description?: string, fields: FormFieldProps[] }[]`}</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">表单分组配置</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div>
        <h3 className="font-medium text-lg mb-2">FormField 字段属性</h3>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">name</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">字段名称</td>
              </tr>
              <tr>
                <td className="p-2 border">label</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">字段标签</td>
              </tr>
              <tr>
                <td className="p-2 border">type</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">"text"</td>
                <td className="p-2 border">字段类型</td>
              </tr>
              <tr>
                <td className="p-2 border">placeholder</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">占位文本</td>
              </tr>
              <tr>
                <td className="p-2 border">required</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">false</td>
                <td className="p-2 border">是否必填</td>
              </tr>
              <tr>
                <td className="p-2 border">disabled</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">false</td>
                <td className="p-2 border">是否禁用</td>
              </tr>
              <tr>
                <td className="p-2 border">description</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">字段描述</td>
              </tr>
              <tr>
                <td className="p-2 border">options</td>
                <td className="p-2 border">{`{ label: string, value: string }[]`}</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">选项列表(用于select等类型)</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

export default function FormPreview() {
  const examples = [
    {
      id: "basic-form",
      title: "基础表单",
      description: "基本的表单组件实现，包含多种输入类型",
      code: basicFormCode,
      scope: { 
        React, 
        useState: React.useState,
        CustomForm 
      },
    },
    {
      id: "section-form",
      title: "分组表单",
      description: "将表单内容分成多个逻辑部分",
      code: sectionFormCode,
      scope: { 
        React, 
        useState: React.useState,
        CustomForm 
      },
    },
    {
      id: "horizontal-form",
      title: "水平布局表单",
      description: "使用水平布局的表单组件",
      code: horizontalFormCode,
      scope: { 
        React, 
        useState: React.useState,
        CustomForm 
      },
    },
    {
      id: "form-dialog",
      title: "表单对话框",
      description: "在对话框中展示的表单",
      code: formDialogCode,
      scope: { 
        React, 
        useState: React.useState,
        CustomForm,
        Button 
      },
    },
  ];

  return (
    <ComponentPreviewContainer
      title="表单 Form"
      description="用于收集、验证和提交用户输入数据的表单组件。"
      whenToUse="当需要收集用户输入信息时使用，适用于数据录入、信息提交、设置面板等场景。表单组件提供了多种布局方式和字段类型，可以满足各种表单交互需求。"
      examples={examples}
      apiDocs={<FormApiDocs />}
    />
  );
} 
"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON>, EyeOff, Star, MoreHorizontal, Trash2, <PERSON>, Key, Shield, Globe } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import {useToast} from "@/components/ui/use-toast";
import { PasswordDetailDialog } from "./password-detail-dialog"
import { passwordListRequest, passwordConverter, toggleFavoriteRequest, cancelFavoriteRequest, deletePasswordRequest } from "@/services/api/passwordVaultRequestApi"
import { PasswordQueryType, UIPasswordItem } from "@/types/passwordVault"
import { CreatePasswordDialog } from "./create-password-dialog"

type PasswordListProps = {
  searchQuery: string
  category: "all" | "favorites" | "weak" | "medium" | "strong"
  refreshKey?: number
  onDelete?: () => void
  onEdit?: () => void
}

export function PasswordList({ searchQuery, category, refreshKey = 0, onDelete, onEdit }: PasswordListProps) {
  const [visiblePasswords, setVisiblePasswords] = useState<Record<string, boolean>>({})
  const [selectedPassword, setSelectedPassword] = useState<string | null>(null)
  const [editPasswordId, setEditPasswordId] = useState<string | null>(null)
  const [favorites, setFavorites] = useState<Record<string, boolean>>({})
  const [passwords, setPasswords] = useState<UIPasswordItem[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { toast } = useToast()

  useEffect(() => {
    const fetchPasswords = async () => {
      try {
        setLoading(true)
        setError(null)
        
        // 根据类别确定查询类型
        let queryType = PasswordQueryType.ALL
        if (category === "favorites") {
          queryType = PasswordQueryType.FAVORITES
        } else if (category === "weak") {
          queryType = PasswordQueryType.WEAK
        } else if (category === "medium") {
          queryType = PasswordQueryType.MEDIUM
        } else if (category === "strong") {
          queryType = PasswordQueryType.STRONG
        }
        
        // 调用API获取密码列表
        const response = await passwordListRequest({
          userId: 1, // 默认用户ID，实际应从用户会话中获取
          queryType: queryType
        })
        
        // 转换为UI格式
        const uiPasswords = response.map(passwordConverter.toUIFormat)
        
        setPasswords(uiPasswords)
        
        // 初始化收藏状态
        const favoritesMap: Record<string, boolean> = {}
        uiPasswords.forEach(password => {
          favoritesMap[password.id] = password.isFavorite
        })
        setFavorites(favoritesMap)
        
      } catch (err) {
        console.error('获取密码列表失败', err)
        setError('获取密码列表失败，请稍后重试')
        toast({
          title: "获取失败",
          description: "无法加载密码列表，请稍后重试",
          variant: "destructive"
        })
      } finally {
        setLoading(false)
      }
    }
    
    fetchPasswords()
  }, [category, refreshKey])

  const handleToggleVisibility = (id: string) => {
    setVisiblePasswords((prev) => ({
      ...prev,
      [id]: !prev[id],
    }))
  }

  const handleCopyPassword = (password: string) => {
    try {
      // 使用document.execCommand作为备选方案
      const textArea = document.createElement("textarea");
      textArea.value = password;
      document.body.appendChild(textArea);
      textArea.select();
      const success = document.execCommand('copy');
      document.body.removeChild(textArea);
      
      if (success) {
        toast({
          title: "已复制",
          description: "密码已复制到剪贴板",
          duration: 2000,
        });
      } else {
        throw new Error("复制失败");
      }
    } catch (error) {
      console.error('复制失败:', error);
      toast({
        title: "复制失败",
        description: "无法复制密码到剪贴板，请手动选择并复制",
        variant: "destructive"
      });
    }
  }

  const handleViewDetails = (id: string) => {
    setSelectedPassword(id)
  }
  
  const handleEditPassword = (id: string) => {
    setEditPasswordId(id)
  }
  
  const handleEditSuccess = () => {
    setEditPasswordId(null)
    if (onEdit) onEdit()
  }
  
  const handleDeletePassword = async (id: string) => {
    try {
      await deletePasswordRequest(parseInt(id, 10))
      
      toast({
        title: "已删除",
        description: "密码已成功删除",
        duration: 2000,
      })
      
      if (onDelete) onDelete()
    } catch (err) {
      console.error('删除密码失败', err)
      toast({
        title: "删除失败",
        description: "无法删除密码，请稍后重试",
        variant: "destructive"
      })
    }
  }

  const handleToggleFavorite = async (id: string) => {
    try {
      const isFavorite = favorites[id] || false
      
      if (isFavorite) {
        // 取消收藏
        await cancelFavoriteRequest({
          moduleCode: "TOOL_PASSWORD_VAULT",
          sourceId: 1, // 当前用户ID
          targetId: parseInt(id, 10)
        })
      } else {
        // 添加收藏
        await toggleFavoriteRequest({
          moduleCode: "TOOL_PASSWORD_VAULT",
          sourceId: 1, // 当前用户ID
          targetId: parseInt(id, 10)
        })
      }
      
      // 更新本地状态
      setFavorites(prev => {
        const newState = {
          ...prev,
          [id]: !prev[id]
        }
        return newState
      })
      
      toast({
        title: !isFavorite ? "已添加到收藏" : "已从收藏中移除",
        duration: 2000,
      })
    } catch (err) {
      console.error('收藏操作失败', err)
      toast({
        title: "操作失败",
        description: "收藏操作失败，请稍后重试",
        variant: "destructive"
      })
    }
  }

  // 筛选符合搜索条件的密码
  const filteredPasswords = passwords.filter((password) => {
    // 应用搜索筛选
    return searchQuery === "" || password.searchKeyword?.toLowerCase().includes(searchQuery.toLowerCase())
  })

  // 获取默认图标
  const getDefaultIcon = (name: string): string => {
    // 如果有图标则使用
    if (name && typeof name === 'string' && name.trim() !== '') {
      return name;
    }
    
    // 默认返回锁图标
    return "🔑";
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center py-14">
        <div className="animate-pulse flex flex-col items-center">
          <Shield className="h-9 w-9 text-primary/30 mb-3" />
          <p className="text-muted-foreground font-medium">加载密码库中...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-10 space-y-4">
        <p className="text-destructive font-medium">{error}</p>
        <Button onClick={() => window.location.reload()} variant="outline" size="sm" className="mt-2">
          重新加载
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-2 mt-2">
      {filteredPasswords.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-10 text-muted-foreground">
          <Key className="h-9 w-9 opacity-30 mb-3" />
          <p className="font-medium">没有找到匹配的密码</p>
          <p className="text-sm mt-1">尝试使用其他搜索条件或添加新密码</p>
        </div>
      ) : (
        filteredPasswords.map((password) => {
          const isFavorite = favorites[password.id] ?? password.isFavorite
          const iconToDisplay = getDefaultIcon(password.icon)

          return (
            <Card
              key={password.id}
              className="overflow-hidden hover:bg-accent/50 transition-all !py-2"
            >
              <CardContent className="p-0">
                <div className="flex items-center justify-between py-2.5 px-3">
                  <div className="flex items-center gap-2">
                    <div className="text-xl w-9 h-9 rounded-full flex items-center justify-center">
                      {iconToDisplay}
                    </div>
                    <div>
                      <div className="flex items-center">
                        <span className="font-medium text-base">{password.name}</span>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleToggleFavorite(password.id)}
                          className="ml-1 h-6 w-6 p-0 text-yellow-400"
                        >
                          <Star className={`h-4 w-4 ${isFavorite ? "fill-yellow-400" : ""}`} />
                        </Button>
                      </div>
                      <div className="flex items-center gap-1 text-sm text-muted-foreground">
                        <span>{password.account}</span>
                        {password.platform && (
                          <div className="flex items-center gap-1">
                            <span className="text-xs opacity-50">•</span>
                            <Globe className="h-3 w-3 opacity-50" />
                            <span>{password.platform}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-1">
                    <Badge
                      variant={
                        password.password_strength === 3
                          ? "outline"
                          : password.password_strength === 2
                            ? "secondary"
                            : "destructive"
                      }
                      className="hidden sm:inline-flex mr-1 text-xs py-0 px-2"
                    >
                      {password.password_strength === 3 ? "强" : password.password_strength === 2 ? "中" : "弱"}
                    </Badge>

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleToggleVisibility(password.id)}
                      className="h-8 w-8"
                      title={visiblePasswords[password.id] ? "隐藏密码" : "显示密码"}
                    >
                      {visiblePasswords[password.id] ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleCopyPassword(password.password)}
                      className="h-8 w-8"
                      title="复制密码"
                    >
                      <Copy className="h-4 w-4" />
                    </Button>

                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-40">
                        <DropdownMenuItem onClick={() => handleViewDetails(password.id)}>
                          <Eye className="mr-2 h-4 w-4" />
                          <span>查看详情</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEditPassword(password.id)}>
                          <Edit className="mr-2 h-4 w-4" />
                          <span>编辑</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          className="text-destructive focus:text-destructive" 
                          onClick={() => handleDeletePassword(password.id)}
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          <span>删除</span>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>

                {visiblePasswords[password.id] && (
                  <div className="p-2 bg-muted border-t flex flex-wrap items-center justify-between gap-2">
                    <div className="font-mono text-sm bg-background rounded px-3 py-1 border flex-1 overflow-x-auto">
                      {password.password}
                    </div>
                    {password.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        {password.tags.map((tag, index) => (
                          <Badge key={index} variant="outline" className="text-xs py-0">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          )
        })
      )}

      {selectedPassword && (
        <PasswordDetailDialog
          passwordId={selectedPassword}
          open={!!selectedPassword}
          onOpenChange={(open) => {
            if (!open) setSelectedPassword(null)
          }}
          onDelete={onDelete}
        />
      )}

      {editPasswordId && (
        <CreatePasswordDialog
          open={!!editPasswordId}
          onOpenChange={(open) => {
            if (!open) setEditPasswordId(null)
          }}
          passwordId={editPasswordId}
          onCreateSuccess={handleEditSuccess}
        />
      )}
    </div>
  )
} 

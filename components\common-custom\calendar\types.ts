import { ReactNode } from "react"
import { DayPickerProps } from "react-day-picker"

/**
 * 日历事件接口
 */
export interface CalendarEvent {
  /**
   * 事件唯一标识
   */
  id: string
  
  /**
   * 事件日期
   */
  date: Date
  
  /**
   * 事件标题
   */
  title: string
  
  /**
   * 事件描述
   */
  description?: string
  
  /**
   * 事件类型，用于确定样式
   */
  type?: "default" | "primary" | "secondary" | "success" | "warning" | "danger"
  
  /**
   * 事件颜色（优先级高于type）
   */
  color?: string
  
  /**
   * 事件自定义类名
   */
  className?: string
  
  /**
   * 自定义事件数据
   */
  data?: Record<string, any>
}

/**
 * 日历图例项接口
 */
export interface CalendarLegendItem {
  /**
   * 图例标识
   */
  id: string
  
  /**
   * 图例标签
   */
  label: string
  
  /**
   * 图例颜色
   */
  color: string
  
  /**
   * 图例类型
   */
  type?: "default" | "primary" | "secondary" | "success" | "warning" | "danger"
  
  /**
   * 图例描述
   */
  description?: string
}

/**
 * 日历选择模式类型
 */
export type CalendarMode = "single" | "multiple" | "range"

/**
 * 日历选择值类型
 */
export type CalendarValue<T extends CalendarMode = CalendarMode> = 
  T extends "single" ? Date | undefined :
  T extends "multiple" ? Date[] | undefined :
  T extends "range" ? { from: Date; to?: Date } | undefined :
  never

/**
 * 日历选择回调类型
 */
export type CalendarSelectHandler<T extends CalendarMode = CalendarMode> = 
  (value: CalendarValue<T>) => void

/**
 * 日历事件回调类型
 */
export interface CalendarEventHandlers {
  /**
   * 日期选择回调
   */
  onSelect?: CalendarSelectHandler
  
  /**
   * 事件点击回调
   */
  onEventClick?: (event: CalendarEvent, date: Date) => void
  
  /**
   * 日期点击回调
   */
  onDateClick?: (date: Date) => void
  
  /**
   * 月份变化回调
   */
  onMonthChange?: (month: Date) => void
}

/**
 * 日历样式配置
 */
export interface CalendarStyleConfig {
  /**
   * 日历容器类名
   */
  className?: string
  
  /**
   * 日历内容类名
   */
  calendarClassName?: string
  
  /**
   * 事件容器类名
   */
  eventClassName?: string
  
  /**
   * 图例容器类名
   */
  legendClassName?: string
  
  /**
   * 是否显示边框
   * @default true
   */
  showBorder?: boolean
  
  /**
   * 是否显示阴影
   * @default false
   */
  showShadow?: boolean
}

/**
 * 日历功能配置
 */
export interface CalendarFeatureConfig {
  /**
   * 是否显示事件
   * @default true
   */
  showEvents?: boolean
  
  /**
   * 是否显示图例
   * @default false
   */
  showLegend?: boolean
  
  /**
   * 图例位置
   * @default "bottom"
   */
  legendPosition?: "top" | "bottom" | "left" | "right"
  
  /**
   * 是否显示选中信息
   * @default true
   */
  showSelectedInfo?: boolean
  
  /**
   * 是否显示事件详情
   * @default true
   */
  showEventDetails?: boolean
  
  /**
   * 最大显示事件数量
   * @default 3
   */
  maxEventsPerDay?: number
  
  /**
   * 是否启用事件悬浮提示
   * @default true
   */
  enableEventTooltip?: boolean
}

/**
 * 日历组件主要属性接口
 */
export interface CalendarProps<T extends CalendarMode = "single"> {
  /**
   * 日历选择模式
   * @default "single"
   */
  mode?: T
  
  /**
   * 选中的值
   */
  value?: CalendarValue<T>
  
  /**
   * 默认选中的值
   */
  defaultValue?: CalendarValue<T>
  
  /**
   * 日历事件列表
   */
  events?: CalendarEvent[]
  
  /**
   * 图例配置
   */
  legends?: CalendarLegendItem[]
  
  /**
   * 事件回调配置
   */
  handlers?: CalendarEventHandlers
  
  /**
   * 样式配置
   */
  style?: CalendarStyleConfig
  
  /**
   * 功能配置
   */
  features?: CalendarFeatureConfig
  
  /**
   * react-day-picker 原生属性
   */
  dayPickerProps?: Omit<DayPickerProps, "mode" | "selected" | "onSelect">
  
  /**
   * 自定义日期渲染函数
   */
  renderDay?: (date: Date, events: CalendarEvent[]) => ReactNode
  
  /**
   * 自定义事件渲染函数
   */
  renderEvent?: (event: CalendarEvent) => ReactNode
  
  /**
   * 自定义图例渲染函数
   */
  renderLegend?: (legend: CalendarLegendItem) => ReactNode
}

/**
 * 日历事件组件属性
 */
export interface CalendarEventProps {
  /**
   * 事件数据
   */
  event: CalendarEvent
  
  /**
   * 事件所属日期
   */
  date: Date
  
  /**
   * 点击回调
   */
  onClick?: (event: CalendarEvent, date: Date) => void
  
  /**
   * 自定义类名
   */
  className?: string
  
  /**
   * 是否显示详情
   * @default true
   */
  showDetails?: boolean
  
  /**
   * 是否启用悬浮提示
   * @default true
   */
  enableTooltip?: boolean
}

/**
 * 日历图例组件属性
 */
export interface CalendarLegendProps {
  /**
   * 图例列表
   */
  legends: CalendarLegendItem[]
  
  /**
   * 图例位置
   * @default "bottom"
   */
  position?: "top" | "bottom" | "left" | "right"
  
  /**
   * 自定义类名
   */
  className?: string
  
  /**
   * 自定义渲染函数
   */
  renderItem?: (legend: CalendarLegendItem) => ReactNode
}

/**
 * 日历日期组件属性
 */
export interface CalendarDayProps {
  /**
   * 日期
   */
  date: Date
  
  /**
   * 该日期的事件列表
   */
  events: CalendarEvent[]
  
  /**
   * 是否选中
   */
  selected?: boolean
  
  /**
   * 是否禁用
   */
  disabled?: boolean
  
  /**
   * 是否是今天
   */
  isToday?: boolean
  
  /**
   * 是否在当前月份
   */
  isCurrentMonth?: boolean
  
  /**
   * 点击回调
   */
  onClick?: (date: Date) => void
  
  /**
   * 事件点击回调
   */
  onEventClick?: (event: CalendarEvent, date: Date) => void
  
  /**
   * 最大显示事件数量
   * @default 3
   */
  maxEvents?: number
  
  /**
   * 自定义类名
   */
  className?: string
  
  /**
   * 自定义渲染函数
   */
  renderEvent?: (event: CalendarEvent) => ReactNode
}

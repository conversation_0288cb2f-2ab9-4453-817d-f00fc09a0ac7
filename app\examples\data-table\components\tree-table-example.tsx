"use client"

import * as React from "react"
import { 
  ColumnDef,
  ExpandedState, 
  flexRender, 
  getCoreRowModel, 
  getExpandedRowModel,
  useReactTable,
  getPaginationRowModel,
} from "@tanstack/react-table"

import { Button } from "@/components/ui/button"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { ChevronDown, ChevronRight } from "lucide-react"
import { User } from "../data"

interface Department {
  id: string
  name: string
  manager: string
  employeeCount: number
  children?: User[]
}

interface TreeNode {
  id: string
  name: string
  type: "department" | "employee"
  email?: string
  role?: string
  status?: string
  manager?: string
  employeeCount?: number
  subRows?: TreeNode[]
}

// 使用React.memo包装单元格组件以减少重渲染
const ExpandCell = React.memo(({ row }: { row: any }) => {
  return (
    <div className="flex items-center">
      {row.getCanExpand() ? (
        <Button
          variant="ghost"
          size="icon"
          className="p-0 mr-2 h-6 w-6"
          onClick={() => {
            row.toggleExpanded()
          }}
        >
          {row.getIsExpanded() ? (
            <ChevronDown className="h-4 w-4" />
          ) : (
            <ChevronRight className="h-4 w-4" />
          )}
        </Button>
      ) : (
        <div className="w-6 mr-2"></div>
      )}
      <span className="font-medium">
        {row.original.name}
      </span>
      {row.original.type === "department" && (
        <span className="ml-2 text-xs text-muted-foreground">
          (部门)
        </span>
      )}
    </div>
  )
})
ExpandCell.displayName = "ExpandCell"

// 使用React.memo包装状态单元格组件
const StatusCell = React.memo(({ row }: { row: any }) => {
  if (row.original.type === "department") {
    return <div>{row.original.employeeCount} 位员工</div>
  }

  const status = row.original.status
  if (!status) return null

  return (
    <div className="flex items-center">
      <span
        className={`mr-2 h-2 w-2 rounded-full ${
          status === "活跃" ? "bg-green-500" : 
          status === "未活跃" ? "bg-gray-400" : "bg-yellow-500"
        }`}
      />
      {status}
    </div>
  )
})
StatusCell.displayName = "StatusCell"

// 使用React.memo包装角色单元格组件
const RoleCell = React.memo(({ row }: { row: any }) => {
  if (row.original.type === "department") {
    return <div>部门经理: {row.original.manager}</div>
  } else {
    return row.original.role
  }
})
RoleCell.displayName = "RoleCell"

export function TreeTableExample() {
  // 减少数据量，只保留必要的部门和员工
  const departments: Department[] = [
    {
      id: "dept-1",
      name: "技术部",
      manager: "张三",
      employeeCount: 2,
      children: [
        {
          id: "u-001",
          name: "张三",
          email: "<EMAIL>",
          role: "管理员",
          status: "活跃",
          lastLogin: "2023-12-01 10:30:00",
        },
        {
          id: "u-003",
          name: "王五",
          email: "<EMAIL>",
          role: "编辑",
          status: "未活跃",
          lastLogin: "2023-11-28 09:45:00",
        }
      ]
    },
    {
      id: "dept-2",
      name: "市场部",
      manager: "李四",
      employeeCount: 1,
      children: [
        {
          id: "u-002",
          name: "李四",
          email: "<EMAIL>",
          role: "用户",
          status: "活跃",
          lastLogin: "2023-12-03 14:20:00",
        }
      ]
    }
  ]

  // 构建树形数据结构
  const treeData = React.useMemo<TreeNode[]>(() => departments.map(dept => ({
    id: dept.id,
    name: dept.name,
    type: "department" as const,
    manager: dept.manager,
    employeeCount: dept.employeeCount,
    subRows: dept.children?.map(employee => ({
      id: employee.id,
      name: employee.name,
      type: "employee" as const,
      email: employee.email,
      role: employee.role,
      status: employee.status
    }))
  })), [])

  const [expanded, setExpanded] = React.useState<ExpandedState>({})

  const columns = React.useMemo<ColumnDef<TreeNode>[]>(() => [
    {
      id: "name",
      header: "名称",
      accessorKey: "name",
      cell: ({ row }) => <ExpandCell row={row} />
    },
    {
      id: "role",
      header: "角色/职位",
      cell: ({ row }) => <RoleCell row={row} />
    },
    {
      id: "email",
      header: "邮箱",
      accessorKey: "email",
    },
    {
      id: "status",
      header: "状态",
      accessorKey: "status",
      cell: ({ row }) => <StatusCell row={row} />
    },
  ], [])

  const table = useReactTable({
    data: treeData,
    columns,
    state: {
      expanded,
    },
    onExpandedChange: setExpanded,
    getSubRows: row => row.subRows,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
    initialState: {
      pagination: {
        pageSize: 10,
      },
    },
  })

  return (
    <div className="space-y-4">
      <div className="text-sm text-muted-foreground mb-2">
        <p>树形表格示例，展示部门与员工的层级结构。点击箭头展开部门查看员工信息。</p>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                  className={row.original.type === "department" ? "bg-muted/50" : ""}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  无数据
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
} 
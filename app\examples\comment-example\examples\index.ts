/**
 * 评论组件示例代码集合
 * 
 * 按照新规范管理示例代码，避免在页面文件中硬编码
 */

import React from "react"
import { Comment, CommentList, CommentInput } from "@/components/common-custom/comment"
import { Heart, MessageCircle, Share, MoreHorizontal } from "lucide-react"

// ============================================================================
// 基础评论示例
// ============================================================================

export const basicCommentExample = {
  id: "basic-comment",
  title: "基础评论",
  description: "展示基础的评论组件",
  code: `
import React from "react";
import { Comment } from "@/components/common-custom/comment";
import { Heart, MessageCircle, Share } from "lucide-react";

function BasicCommentExample() {
  const comment = {
    id: "1",
    author: "张三",
    avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face",
    content: "这是一条很棒的评论内容，感谢分享！",
    time: "2小时前",
    likes: 12,
    replies: [],
  };

  return (
    <Comment
      comment={comment}
      onLike={() => console.log("点赞")}
      onReply={() => console.log("回复")}
      onShare={() => console.log("分享")}
    />
  );
}

render(<BasicCommentExample />);
  `,
  scope: { Comment, Heart, MessageCircle, Share, React },
}

// ============================================================================
// 评论列表示例
// ============================================================================

export const commentListExample = {
  id: "comment-list",
  title: "评论列表",
  description: "展示评论列表组件",
  code: `
import React from "react";
import { CommentList } from "@/components/common-custom/comment";

function CommentListExample() {
  const comments = [
    {
      id: "1",
      author: "张三",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face",
      content: "这是第一条评论，内容很精彩！",
      time: "2小时前",
      likes: 12,
      replies: [
        {
          id: "1-1",
          author: "李四",
          avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=32&h=32&fit=crop&crop=face",
          content: "我也觉得很不错！",
          time: "1小时前",
          likes: 5,
          replies: 0,
        }
      ]
    },
    {
      id: "2",
      author: "王五",
      avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=32&h=32&fit=crop&crop=face",
      content: "感谢分享，学到了很多东西。",
      time: "3小时前",
      likes: 8,
      replies: [],
    }
  ];

  return (
    <CommentList
      comments={comments}
      onLike={(commentId) => console.log("点赞评论:", commentId)}
      onReply={(commentId) => console.log("回复评论:", commentId)}
      onDelete={(commentId) => console.log("删除评论:", commentId)}
    />
  );
}

render(<CommentListExample />);
  `,
  scope: { CommentList, React },
}

// ============================================================================
// 评论输入示例
// ============================================================================

export const commentInputExample = {
  id: "comment-input",
  title: "评论输入",
  description: "展示评论输入组件",
  code: `
import React, { useState } from "react";
import { CommentInput } from "@/components/common-custom/comment";

function CommentInputExample() {
  const [value, setValue] = useState("");

  const handleSubmit = (content: string) => {
    console.log("提交评论:", content);
    setValue("");
  };

  return (
    <div className="space-y-4">
      <CommentInput
        value={value}
        onChange={setValue}
        onSubmit={handleSubmit}
        placeholder="写下你的评论..."
        maxLength={500}
        showCounter
      />
      
      <CommentInput
        placeholder="回复评论..."
        size="sm"
        onSubmit={(content) => console.log("回复:", content)}
      />
    </div>
  );
}

render(<CommentInputExample />);
  `,
  scope: { CommentInput, React, useState: React.useState },
}

// ============================================================================
// 完整评论系统示例
// ============================================================================

export const fullCommentSystemExample = {
  id: "full-comment-system",
  title: "完整评论系统",
  description: "展示完整的评论系统",
  code: `
import React, { useState } from "react";
import { CommentList, CommentInput } from "@/components/common-custom/comment";

function FullCommentSystemExample() {
  const [comments, setComments] = useState([
    {
      id: "1",
      author: "张三",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face",
      content: "这是一个很棒的功能！",
      time: "刚刚",
      likes: 0,
      replies: [],
    }
  ]);

  const handleAddComment = (content: string) => {
    const newComment = {
      id: Date.now().toString(),
      author: "当前用户",
      avatar: "https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=32&h=32&fit=crop&crop=face",
      content,
      time: "刚刚",
      likes: 0,
      replies: [],
    };
    setComments([newComment, ...comments]);
  };

  return (
    <div className="space-y-6">
      <CommentInput
        placeholder="分享你的想法..."
        onSubmit={handleAddComment}
        showCounter
        maxLength={500}
      />
      
      <CommentList
        comments={comments}
        onLike={(commentId) => console.log("点赞:", commentId)}
        onReply={(commentId) => console.log("回复:", commentId)}
      />
    </div>
  );
}

render(<FullCommentSystemExample />);
  `,
  scope: { CommentList, CommentInput, React, useState: React.useState },
}

// ============================================================================
// 统一导出所有示例
// ============================================================================

export const allExamples = [
  basicCommentExample,
  commentListExample,
  commentInputExample,
  fullCommentSystemExample,
]

// ============================================================================
// 按类别导出示例
// ============================================================================

export const basicExamples = [basicCommentExample]
export const listExamples = [commentListExample]
export const inputExamples = [commentInputExample]
export const systemExamples = [fullCommentSystemExample]

// ============================================================================
// 示例元数据
// ============================================================================

export const exampleMetadata = {
  total: allExamples.length,
  categories: {
    basic: basicExamples.length,
    list: listExamples.length,
    input: inputExamples.length,
    system: systemExamples.length,
  },
  tags: ["comment", "social", "interaction", "feedback", "discussion"],
  lastUpdated: new Date().toISOString().split('T')[0],
}

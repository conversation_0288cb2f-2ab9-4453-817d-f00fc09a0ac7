"use client"

/**
 * 仪表板组件集合
 *
 * 按照新规范重新导出所有仪表板相关组件和类型
 */

// ============================================================================
// 主组件导出
// ============================================================================

export { AnalyticsDashboard } from "./analytics-dashboard"

// ============================================================================
// 子组件导出
// ============================================================================

export { StatCard } from "./stat-card"
export { QuickActions } from "./quick-actions"
export { ActivityList } from "./activity-list"
export { ProductRanking } from "./product-ranking"
export { TaskList } from "./task-list"
export { SystemStatus } from "./system-status"
export { ChartCard } from "./chart-card"

// ============================================================================
// 类型导出
// ============================================================================

// 从本地types文件导出所有类型（需要创建）
// export * from "./types"

// 从本地types文件导出所有类型
export * from "./types"

// ============================================================================
// 常量导出
// ============================================================================

/**
 * 支持的统计卡片尺寸
 */
export const STAT_CARD_SIZES = ['sm', 'md', 'lg'] as const

/**
 * 支持的统计卡片变体
 */
export const STAT_CARD_VARIANTS = ['default', 'outline', 'filled'] as const

/**
 * 支持的图表类型
 */
export const CHART_TYPES = ['line', 'bar', 'pie', 'area', 'donut'] as const

/**
 * 支持的系统状态
 */
export const SYSTEM_STATUSES = ['online', 'offline', 'warning', 'error'] as const

/**
 * 默认仪表板配置
 */
export const DEFAULT_DASHBOARD_CONFIG = {
  refreshInterval: 30000, // 30秒
  autoRefresh: true,
  showLastUpdated: true,
} as const
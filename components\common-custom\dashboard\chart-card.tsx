"use client"

import { ReactNode } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Download, RefreshCw } from "lucide-react"

/**
 * 图表卡片组件
 */
export function ChartCard({ 
  title, 
  description, 
  children,
  showControls = true
}: { 
  title: ReactNode, 
  description?: ReactNode, 
  children?: ReactNode,
  showControls?: boolean
}) {
  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>{title}</CardTitle>
            {description && <CardDescription>{description}</CardDescription>}
          </div>
          {showControls && (
            <div className="flex items-center gap-2">
              <Button variant="outline" size="icon">
                <RefreshCw className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="icon">
                <Download className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>{children}</CardContent>
    </Card>
  )
} 
"use client"

import React from "react"
import { ComponentPreviewContainer } from "@/components/component-detail/preview-container"
import { Tabs, CardTabs, StepTabs } from "@/components/common-custom/tabs"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { User, Mail, Settings, Home, FileText, Bell, Check } from "lucide-react"
import { allExamples } from "./examples"

// ============================================================================
// API文档组件
// ============================================================================

function TabsApiDocs() {
  return (
    <div className="space-y-6">
      {/* Tabs 组件API */}
      <div>
        <h3 className="font-medium text-lg mb-2">Tabs</h3>
        <p className="text-muted-foreground mb-4">基础标签页组件，支持多种样式和配置</p>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">tabs</td>
                <td className="p-2 border">TabItem[]</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">标签项列表</td>
              </tr>
              <tr>
                <td className="p-2 border">defaultValue</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">默认激活的标签ID</td>
              </tr>
              <tr>
                <td className="p-2 border">value</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">当前激活的标签ID（受控模式）</td>
              </tr>
              <tr>
                <td className="p-2 border">onValueChange</td>
                <td className="p-2 border">(value: string) =&gt; void</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">标签变更回调</td>
              </tr>
              <tr>
                <td className="p-2 border">orientation</td>
                <td className="p-2 border">"horizontal" | "vertical"</td>
                <td className="p-2 border">"horizontal"</td>
                <td className="p-2 border">标签页方向</td>
              </tr>
              <tr>
                <td className="p-2 border">variant</td>
                <td className="p-2 border">"default" | "outline" | "underline" | "pills"</td>
                <td className="p-2 border">"default"</td>
                <td className="p-2 border">标签页变体样式</td>
              </tr>
              <tr>
                <td className="p-2 border">size</td>
                <td className="p-2 border">"sm" | "md" | "lg"</td>
                <td className="p-2 border">"md"</td>
                <td className="p-2 border">标签页大小</td>
              </tr>
              <tr>
                <td className="p-2 border">closable</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">false</td>
                <td className="p-2 border">是否可关闭标签</td>
              </tr>
              <tr>
                <td className="p-2 border">addable</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">false</td>
                <td className="p-2 border">是否可添加标签</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* CardTabs 组件API */}
      <div>
        <h3 className="font-medium text-lg mb-2">CardTabs</h3>
        <p className="text-muted-foreground mb-4">带卡片容器的标签页组件</p>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">title</td>
                <td className="p-2 border">ReactNode</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">卡片标题</td>
              </tr>
              <tr>
                <td className="p-2 border">description</td>
                <td className="p-2 border">ReactNode</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">卡片描述</td>
              </tr>
              <tr>
                <td className="p-2 border">actions</td>
                <td className="p-2 border">ReactNode</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">卡片操作区域</td>
              </tr>
              <tr>
                <td className="p-2 border">showDivider</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">true</td>
                <td className="p-2 border">是否显示分割线</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* StepTabs 组件API */}
      <div>
        <h3 className="font-medium text-lg mb-2">StepTabs</h3>
        <p className="text-muted-foreground mb-4">步骤引导标签页组件</p>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">backable</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">true</td>
                <td className="p-2 border">是否可返回上一步</td>
              </tr>
              <tr>
                <td className="p-2 border">onNext</td>
                <td className="p-2 border">(currentIndex: number) =&gt; boolean | Promise&lt;boolean&gt;</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">下一步回调，返回false阻止继续</td>
              </tr>
              <tr>
                <td className="p-2 border">onComplete</td>
                <td className="p-2 border">() =&gt; void</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">完成回调</td>
              </tr>
              <tr>
                <td className="p-2 border">nextButtonText</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">"下一步"</td>
                <td className="p-2 border">下一步按钮文本</td>
              </tr>
              <tr>
                <td className="p-2 border">completeButtonText</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">"完成"</td>
                <td className="p-2 border">完成按钮文本</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* 使用指南 */}
      <div>
        <h3 className="font-medium text-lg mb-2">使用指南</h3>
        <div className="space-y-4 text-sm">
          <div>
            <h4 className="font-medium mb-2">基本用法</h4>
            <p className="text-muted-foreground mb-2">
              最简单的标签页使用方式：
            </p>
            <pre className="bg-muted p-3 rounded-md overflow-x-auto">
              <code>{`const tabs = [
  {
    id: "tab1",
    title: "标签1",
    content: <div>内容1</div>,
  },
  {
    id: "tab2",
    title: "标签2",
    content: <div>内容2</div>,
  },
];

<Tabs tabs={tabs} defaultValue="tab1" />`}</code>
            </pre>
          </div>

          <div>
            <h4 className="font-medium mb-2">卡片标签页</h4>
            <p className="text-muted-foreground mb-2">
              带有卡片容器的标签页：
            </p>
            <pre className="bg-muted p-3 rounded-md overflow-x-auto">
              <code>{`<CardTabs
  title="工作台"
  description="管理您的工作内容"
  tabs={tabs}
  actions={<Button>操作</Button>}
/>`}</code>
            </pre>
          </div>

          <div>
            <h4 className="font-medium mb-2">步骤标签页</h4>
            <p className="text-muted-foreground mb-2">
              用于引导用户完成多步骤流程：
            </p>
            <pre className="bg-muted p-3 rounded-md overflow-x-auto">
              <code>{`<StepTabs
  tabs={stepTabs}
  onNext={(index) => validateStep(index)}
  onComplete={() => handleComplete()}
  nextButtonText="下一步"
  completeButtonText="完成"
/>`}</code>
            </pre>
          </div>
        </div>
      </div>
    </div>
  )
}

// ============================================================================
// 示例代码字符串
// ============================================================================

// 基础标签页示例代码
const basicTabsCode = `
import React from "react";
import { Tabs } from "@/components/common-custom/tabs";
import { User, Bell, Settings } from "lucide-react";

function BasicTabs() {
  const tabs = [
    {
      id: "account",
      title: "账户",
      icon: <User className="h-4 w-4" />,
      content: (
        <div className="p-4 border rounded-md">
          <h3 className="text-lg font-medium mb-2">账户信息</h3>
          <p className="text-muted-foreground">管理您的账户设置和偏好</p>
        </div>
      )
    },
    {
      id: "notifications",
      title: "通知",
      icon: <Bell className="h-4 w-4" />,
      content: (
        <div className="p-4 border rounded-md">
          <h3 className="text-lg font-medium mb-2">通知设置</h3>
          <p className="text-muted-foreground">设置通知首选项</p>
        </div>
      ),
      badge: 3
    },
    {
      id: "settings",
      title: "设置",
      icon: <Settings className="h-4 w-4" />,
      content: (
        <div className="p-4 border rounded-md">
          <h3 className="text-lg font-medium mb-2">系统设置</h3>
          <p className="text-muted-foreground">配置系统参数与偏好</p>
        </div>
      )
    }
  ];

  return <Tabs tabs={tabs} defaultValue="account" />;
}

render(<BasicTabs />);
`;

// 标签页变体示例代码
const tabVariantsCode = `
import React from "react";
import { Tabs } from "@/components/common-custom/tabs";
import { User, Mail } from "lucide-react";

function TabVariants() {
  const tabs = [
    {
      id: "account",
      title: "账号",
      icon: <User className="h-4 w-4" />,
      content: (
        <div className="p-4 border rounded-md">
          <h3 className="text-lg font-medium mb-2">账号设置</h3>
          <p className="text-muted-foreground">管理您的账号信息与个人资料</p>
        </div>
      )
    },
    {
      id: "notifications",
      title: "通知",
      icon: <Mail className="h-4 w-4" />,
      content: (
        <div className="p-4 border rounded-md">
          <h3 className="text-lg font-medium mb-2">通知设置</h3>
          <p className="text-muted-foreground">设置通知首选项</p>
        </div>
      ),
      badge: 3
    }
  ];

  return (
    <div className="space-y-8">
      <div>
        <h3 className="text-sm font-medium mb-2">轮廓标签页</h3>
        <Tabs tabs={tabs} defaultValue="account" variant="outline" />
      </div>
      
      <div>
        <h3 className="text-sm font-medium mb-2">下划线标签页</h3>
        <Tabs tabs={tabs} defaultValue="account" variant="underline" />
      </div>
      
      <div>
        <h3 className="text-sm font-medium mb-2">胶囊标签页</h3>
        <Tabs tabs={tabs} defaultValue="account" variant="pills" />
      </div>
    </div>
  );
}

render(<TabVariants />);
`;

// 可关闭和可添加标签页示例代码
const closableTabsCode = `
import React, { useState } from "react";
import { Tabs } from "@/components/common-custom/tabs";
import { Home, FileText } from "lucide-react";

function ClosableTabs() {
  const [tabs, setTabs] = useState([
    {
      id: "tab1",
      title: "主页",
      icon: <Home className="h-4 w-4" />,
      content: (
        <div className="p-4 border rounded-md">
          <h3 className="text-lg font-medium">主页内容</h3>
          <p className="text-muted-foreground">欢迎访问主页</p>
        </div>
      )
    },
    {
      id: "tab2",
      title: "文档",
      icon: <FileText className="h-4 w-4" />,
      content: (
        <div className="p-4 border rounded-md">
          <h3 className="text-lg font-medium">文档内容</h3>
          <p className="text-muted-foreground">这里是文档页面</p>
        </div>
      )
    }
  ]);

  // 处理关闭标签
  const handleCloseTab = (id) => {
    setTabs(tabs => tabs.filter(tab => tab.id !== id));
  };
  
  // 处理添加标签
  const handleAddTab = () => {
    const newId = \`tab\${tabs.length + 1}\`;
    setTabs([
      ...tabs,
      {
        id: newId,
        title: \`标签 \${tabs.length + 1}\`,
        icon: <FileText className="h-4 w-4" />,
        content: (
          <div className="p-4 border rounded-md">
            <h3 className="text-lg font-medium">新标签页</h3>
            <p className="text-muted-foreground">这是新添加的标签页内容</p>
          </div>
        )
      }
    ]);
  };

  return (
    <Tabs
      tabs={tabs}
      defaultValue="tab1"
      closable
      onClose={handleCloseTab}
      addable
      onAdd={handleAddTab}
      addButtonText="添加标签"
    />
  );
}

render(<ClosableTabs />);
`;

// 卡片标签页示例代码
const cardTabsCode = `
import React from "react";
import { CardTabs } from "@/components/common-custom/tabs";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

function CardTabsExample() {
  const tabs = [
    {
      id: "personal",
      title: "个人信息",
      content: (
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">姓名</Label>
              <Input id="name" placeholder="请输入姓名" />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">邮箱</Label>
              <Input id="email" type="email" placeholder="请输入邮箱" />
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="address">地址</Label>
            <Input id="address" placeholder="请输入地址" />
          </div>
        </div>
      )
    },
    {
      id: "payment",
      title: "支付信息",
      content: (
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="cardType">卡类型</Label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="选择卡类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="credit">信用卡</SelectItem>
                  <SelectItem value="debit">借记卡</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="cardNumber">卡号</Label>
              <Input id="cardNumber" placeholder="请输入卡号" />
            </div>
          </div>
        </div>
      )
    }
  ];

  return (
    <CardTabs
      tabs={tabs}
      defaultValue="personal"
      title="用户信息"
      description="请填写以下信息完成注册"
    />
  );
}

render(<CardTabsExample />);
`;

// 步骤标签页示例代码
const stepTabsCode = `
import React from "react";
import { StepTabs } from "@/components/common-custom/tabs";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Check } from "lucide-react";

function StepTabsExample() {
  const tabs = [
    {
      id: "step1",
      title: "步骤 1",
      content: (
        <div className="space-y-4">
          <h3 className="text-lg font-medium">基本信息</h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="firstName">姓名</Label>
              <Input id="firstName" placeholder="请输入姓名" />
            </div>
            <div className="space-y-2">
              <Label htmlFor="stepEmail">邮箱</Label>
              <Input id="stepEmail" type="email" placeholder="请输入邮箱" />
            </div>
          </div>
        </div>
      )
    },
    {
      id: "step2",
      title: "步骤 2",
      content: (
        <div className="space-y-4">
          <h3 className="text-lg font-medium">详细信息</h3>
          <div className="space-y-2">
            <Label htmlFor="bio">个人简介</Label>
            <textarea 
              id="bio" 
              className="w-full min-h-[100px] border rounded-md p-2"
              placeholder="请输入个人简介"
            ></textarea>
          </div>
        </div>
      )
    },
    {
      id: "step3",
      title: "步骤 3",
      content: (
        <div className="space-y-4">
          <h3 className="text-lg font-medium">完成</h3>
          <p className="text-muted-foreground">所有步骤已完成，请点击"完成"按钮提交数据。</p>
          <div className="flex items-center gap-2 text-green-600">
            <Check className="h-5 w-5" />
            <span>信息已准备就绪</span>
          </div>
        </div>
      )
    }
  ];

  return (
    <StepTabs
      tabs={tabs}
      defaultValue="step1"
      onComplete={() => alert('步骤完成！')}
    />
  );
}

render(<StepTabsExample />);
`;

// ============================================================================
// 主预览组件
// ============================================================================

export default function TabsExamplePage() {
  const examples = [
    {
      id: "basic-tabs",
      title: "基础标签页",
      description: "带有图标和徽章的标准标签页",
      code: basicTabsCode,
      scope: { Tabs, User, Mail, Settings, React },
    },
    {
      id: "tab-variants",
      title: "标签页变体",
      description: "不同样式的标签页：轮廓、下划线和胶囊",
      code: tabVariantsCode,
      scope: { Tabs, User, Mail, React },
    },
    {
      id: "closable-tabs",
      title: "可关闭和可添加标签页",
      description: "支持动态添加和关闭的标签页",
      code: closableTabsCode,
      scope: {
        Tabs,
        Home,
        FileText,
        useState: React.useState,
        React 
      },
    },
    {
      id: "card-tabs",
      title: "卡片标签页",
      description: "包含在卡片容器中的标签页",
      code: cardTabsCode,
      scope: { 
        CardTabs, 
        Label,
        Input,
        Select,
        SelectContent,
        SelectItem,
        SelectTrigger,
        SelectValue,
        React 
      },
    },
    {
      id: "step-tabs",
      title: "步骤标签页",
      description: "用于分步操作流程的标签页",
      code: stepTabsCode,
      scope: { 
        StepTabs,
        Label,
        Input,
        Check,
        React 
      },
    },
  ];

  return (
    <ComponentPreviewContainer
      title="标签页 Tabs"
      description="用于在同一区域内组织和切换不同内容的组件，支持多种样式和交互方式。"
      whenToUse="当需要在同一区域内切换不同内容时；当需要将内容分类展示时；当需要减少页面空间占用，将多个视图组织在一起时；当需要构建分步操作流程时。"
      examples={examples}
      apiDocs={<TabsApiDocs />}
    />
  );
}

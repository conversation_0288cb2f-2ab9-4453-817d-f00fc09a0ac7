# MCP工具使用规范

本文档详细说明了如何合理、高效地使用MCP（Model Context Protocol）工具集来解决问题和实现功能。

## 📋 已配置的MCP服务器

当前项目已配置以下MCP服务器：

| 服务器名称 | 包名 | 版本 | 主要功能 |
|-----------|------|------|----------|
| context7 | @upstash/context7-mcp | v1.0.14 | 代码上下文检索和分析 |
| filesystem | @modelcontextprotocol/server-filesystem | latest | 本地文件系统操作 |
| sequential-thinking | @modelcontextprotocol/server-sequential-thinking | 0.6.2 | 结构化思考和推理 |
| fetch | fetch-mcp | 0.0.5 | 网络请求和内容获取 |
| playwright | @executeautomation/playwright-mcp-server | 1.0.6 | 浏览器自动化和测试 |
| memory | @modelcontextprotocol/server-memory | 2025.4.25 | 持久化记忆存储 |
| feedback-enhanced | mcp-feedback-collector-md | 2.2.1 | 用户交互和反馈收集 |

## 🎯 核心原则

在解决问题或实现功能时，应该合理、高效地使用MCP工具，遵循以下原则：

### 1. 工具优先级原则
- **优先使用专门的MCP工具**，而不是通用方法
- **组合使用多个工具**以实现复杂功能
- **避免重复造轮子**，充分利用现有工具能力

### 2. 效率最大化原则
- **批量操作优于单次操作**
- **并行处理优于串行处理**
- **缓存结果优于重复计算**

### 3. 用户体验原则
- **重要操作前必须确认**
- **提供清晰的进度反馈**
- **支持操作撤销和回滚**

## 🛠️ 各工具详细使用规范

### context7 - 代码上下文检索
**核心功能：**
- 智能代码搜索和上下文分析
- 相关代码片段检索
- 代码库结构理解

**配置要求：**
```json
{
  "env": {
    "UPSTASH_REDIS_REST_URL": "your_redis_url",
    "UPSTASH_REDIS_REST_TOKEN": "your_redis_token"
  }
}
```

**使用场景：**
- 查找相似的组件实现
- 理解代码库架构和模式
- 获取API使用示例
- 分析代码依赖关系
- 维护组件清单和文档

**最佳实践：**
```javascript
// ✅ 正确：查找相关组件实现
resolve_library_id("react button component")
get_library_docs("/facebook/react", {
  topic: "button components",
  tokens: 5000
})

// ✅ 正确：获取特定功能的实现示例
resolve_library_id("typescript interface patterns")
get_library_docs("/microsoft/typescript", {
  topic: "interface design patterns",
  tokens: 3000
})
```

**注意事项：**
- 需要配置Upstash Redis连接
- 查询时使用具体的技术术语
- 合理控制返回的token数量

### filesystem - 文件系统操作
**核心功能：**
- 本地文件和目录的完整操作
- 文件内容搜索和分析
- 目录结构管理

**配置要求：**
```json
{
  "args": ["@modelcontextprotocol/server-filesystem", "d:\\430\\business-components"],
  "env": {}
}
```

**使用场景：**
- 读取、写入、编辑本地文件
- 目录结构分析和管理
- 文件内容搜索和检索
- 批量文件操作
- 组件清单维护和更新

**最佳实践：**
```javascript
// ✅ 正确：批量读取相关文件
read_multiple_files_filesystem({
  paths: [
    "src/components/Button.tsx",
    "src/components/Input.tsx",
    "src/styles/components.css"
  ]
})

// ✅ 正确：使用正则搜索文件内容
view({
  path: "src/components/Button.tsx",
  type: "file",
  search_query_regex: "interface.*Props",
  case_sensitive: false
})

// ✅ 正确：递归搜索文件
search_files_filesystem({
  path: "src",
  pattern: "*.tsx",
  excludePatterns: ["node_modules", "dist"]
})

// ❌ 错误：逐个读取文件（应使用批量操作）
read_file_filesystem({path: "src/components/Button.tsx"})
read_file_filesystem({path: "src/components/Input.tsx"})
```

**注意事项：**
- 优先使用批量操作提高效率
- 使用正则搜索精确定位代码
- 注意文件路径的正确性

### sequential-thinking - 结构化思考
**核心功能：**
- 复杂问题的结构化分析
- 多步骤解决方案规划
- 逻辑推理和决策支持

**配置要求：**
```json
{
  "args": ["@modelcontextprotocol/server-sequential-thinking"],
  "env": {}
}
```

**使用场景：**
- 复杂技术问题分析
- 架构设计决策
- 多步骤实施计划
- 代码重构策略

**最佳实践：**
```javascript
// ✅ 正确：用于复杂问题分解
sequentialthinking_sequential_thinking({
  thought: "需要设计一个可扩展的组件库架构，首先分析现有组件结构",
  nextThoughtNeeded: true,
  thoughtNumber: 1,
  totalThoughts: 5
})

// ✅ 正确：用于架构决策
sequentialthinking_sequential_thinking({
  thought: "比较不同状态管理方案：Redux vs Zustand vs Context API",
  nextThoughtNeeded: true,
  thoughtNumber: 1,
  totalThoughts: 4
})

// ❌ 错误：用于简单问题（应直接解决）
// 不要为简单的样式修改使用sequential-thinking
```

**注意事项：**
- 仅用于需要深度思考的复杂问题
- 合理估计所需的思考步骤数量
- 可以根据思考过程调整总步骤数

### fetch - 网络请求和内容获取
**核心功能：**
- HTTP请求和API调用
- 网页内容抓取和分析
- 在线资源访问

**配置要求：**
```json
{
  "args": ["fetch-mcp"],
  "env": {}
}
```

**使用场景：**
- 获取外部API数据
- 下载技术文档和教程
- 访问在线代码示例
- 获取最新的库版本信息

**最佳实践：**
```javascript
// ✅ 正确：获取最新技术文档
fetch_url_fetch({
  url: "https://api.github.com/repos/facebook/react/releases/latest",
  max_length: 5000
})

// ✅ 正确：获取网页内容进行分析
fetch_url_fetch({
  url: "https://developer.mozilla.org/en-US/docs/Web/CSS/flexbox",
  max_length: 10000,
  raw: false
})

// ✅ 正确：获取YouTube视频转录
fetch_youtube_transcript_fetch({
  url: "https://www.youtube.com/watch?v=example"
})
```

**注意事项：**
- 合理设置max_length避免内容过长
- 对于HTML内容，raw=false会转换为Markdown
- 遵守网站的robots.txt和使用条款

### playwright - 浏览器自动化
**核心功能：**
- 浏览器自动化和测试
- 动态网页内容抓取
- 用户界面交互模拟

**配置要求：**
```json
{
  "args": ["@executeautomation/playwright-mcp-server"],
  "env": {}
}
```

**使用场景：**
- 端到端测试自动化
- 动态内容抓取（需要JavaScript渲染）
- 用户界面交互测试
- 网页截图和PDF生成

**最佳实践：**
```javascript
// ✅ 正确：测试完整用户流程
playwright_navigate_playwright({
  url: "http://localhost:3000",
  browserType: "chromium",
  headless: false
})
playwright_fill_playwright({
  selector: "#username",
  value: "testuser"
})
playwright_click_playwright({
  selector: "#login-button"
})
playwright_screenshot_playwright({
  name: "login-success",
  fullPage: true
})

// ✅ 正确：生成代码测试
start_codegen_session_playwright({
  options: {
    outputPath: "d:\\430\\business-components\\tests",
    testNamePrefix: "ComponentTest"
  }
})

// ✅ 正确：获取动态内容
playwright_get_visible_html_playwright({
  cleanHtml: true,
  removeScripts: true
})

// ❌ 错误：仅用于静态内容获取（应使用fetch）
// 不要用playwright获取静态HTML页面
```

**注意事项：**
- 优先用于需要JavaScript渲染的页面
- 静态内容获取应使用fetch工具
- 合理设置浏览器类型和headless模式
- 使用代码生成功能提高测试效率

### memory - 持久化记忆存储
**核心功能：**
- 知识图谱构建和管理
- 实体关系存储
- 长期记忆保持

**配置要求：**
```json
{
  "args": ["@modelcontextprotocol/server-memory"],
  "env": {}
}
```

**使用场景：**
- 存储项目相关信息和配置
- 记录用户偏好和习惯
- 缓存重要的分析结果
- 构建代码库知识图谱

**最佳实践：**
```javascript
// ✅ 正确：创建项目实体
create_entities_memory({
  entities: [{
    name: "business-components",
    entityType: "project",
    observations: [
      "React组件库项目",
      "使用TypeScript和Tailwind CSS",
      "包含shadcn/ui基础组件"
    ]
  }]
})

// ✅ 正确：建立实体关系
create_relations_memory({
  relations: [{
    from: "business-components",
    to: "React",
    relationType: "uses_framework"
  }]
})

// ✅ 正确：搜索相关信息
search_nodes_memory({
  query: "React组件开发模式"
})

// ✅ 正确：添加观察记录
add_observations_memory({
  observations: [{
    entityName: "business-components",
    contents: ["新增了MCP工具集成", "更新了开发规范文档"]
  }]
})
```

**注意事项：**
- 使用有意义的实体名称和类型
- 建立清晰的实体关系
- 定期清理过时的信息

### feedback-enhanced - 用户交互和反馈
**核心功能：**
- 用户反馈收集和展示
- 交互式确认和选择
- Markdown渲染和代码高亮

**配置要求：**
```json
{
  "args": ["mcp-feedback-collector-md"],
  "env": {
    "PORT": "3001"
  }
}
```

**使用场景：**
- 重要操作前的用户确认
- 收集用户偏好和选择
- 展示工作汇报和中间结果
- 代码审核和反馈收集

**最佳实践：**
```javascript
// ✅ 正确：重要操作前确认
collect_feedback_feedback_enhanced({
  work_summary: `
## 即将执行的操作

### 文件删除
- old-component.tsx
- unused-styles.css

### 影响范围
- 可能影响现有的导入引用
- 需要更新相关测试文件

请确认是否继续执行？
  `
})

// ✅ 正确：展示工作汇报
collect_feedback_feedback_enhanced({
  work_summary: `
## 组件开发完成汇报

### 已完成的工作
1. ✅ 创建了新的Button组件
2. ✅ 添加了TypeScript类型定义
3. ✅ 编写了单元测试
4. ✅ 更新了文档和示例

### 代码示例
\`\`\`tsx
export interface ButtonProps {
  variant: 'primary' | 'secondary'
  size: 'sm' | 'md' | 'lg'
  disabled?: boolean
}
\`\`\`

### 下一步计划
- 添加更多变体样式
- 集成到组件库主页

请审核并提供反馈。
  `
})
```

**注意事项：**
- 支持Markdown格式，可以包含代码块
- 用于需要用户参与决策的场景
- 提供清晰的操作说明和选项

## 🔄 工具组合使用模式

### 模式1：分析-思考-实施
```javascript
// 1. 分析现状
const files = filesystem.read_multiple_files(["src/**/*.tsx"])

// 2. 结构化思考
const plan = sequential_thinking({
  problem: "重构组件库",
  context: files
})

// 3. 用户确认
const approval = mcp_feedback_enhanced.request_feedback({
  message: "重构计划如下，是否继续？",
  details: plan
})

// 4. 执行实施
if (approval.confirmed) {
  filesystem.write_file("refactor-plan.md", plan)
}
```

### 模式2：研究-验证-应用
```javascript
// 1. 研究最佳实践
const docs = fetch_mcp.get("https://react.dev/learn/thinking-in-react")

// 2. 查找相关代码
const examples = context7.search({
  query: "react component patterns",
  type: "examples"
})

// 3. 验证实现
playwright.navigate("http://localhost:3000/components")
playwright.screenshot("current-implementation")

// 4. 记录发现
memory.store({
  key: "research_findings",
  value: { docs, examples, screenshot: "current-implementation" }
})
```

### 模式3：迭代-反馈-优化
```javascript
// 1. 实现初版
filesystem.write_file("src/components/NewComponent.tsx", initialCode)

// 2. 获取反馈
const feedback = mcp_feedback_enhanced.request_feedback({
  message: "请审核新组件实现",
  code: initialCode,
  options: ["通过", "需要修改", "重新设计"]
})

// 3. 根据反馈优化
if (feedback.action === "需要修改") {
  const improvements = sequential_thinking({
    problem: "优化组件实现",
    feedback: feedback.details
  })
  
  // 4. 应用改进
  filesystem.write_file("src/components/NewComponent.tsx", improvedCode)
}
```

## ⚠️ 注意事项

### 避免的反模式

1. **过度使用工具**
   - 不要为了使用工具而使用工具
   - 简单问题用简单方法解决

2. **忽略用户体验**
   - 重要操作必须使用mcp-feedback-enhanced确认
   - 提供清晰的操作说明和选项

3. **低效的工具组合**
   - 避免不必要的重复操作
   - 合理安排工具调用顺序

4. **忽略错误处理**
   - 每个工具调用都应该有错误处理
   - 提供备选方案

### 性能优化建议

1. **批量操作**
   - 使用filesystem的批量读写功能
   - 合并相关的网络请求

2. **缓存利用**
   - 使用memory存储常用数据
   - 避免重复的expensive操作

3. **并行处理**
   - 独立的操作可以并行执行
   - 合理利用异步特性

## 📋 检查清单

在使用MCP工具前，请检查：

- [ ] 是否选择了最合适的工具？
- [ ] 是否可以批量处理？
- [ ] 是否需要用户确认？
- [ ] 是否需要缓存结果？
- [ ] 是否有错误处理？
- [ ] 是否考虑了用户体验？

## 🔧 组件清单维护专项指南

### 使用MCP工具维护组件清单的标准流程

#### 1. 组件清单更新流程
```javascript
// 步骤1：分析组件结构变化
search_files_filesystem({
  path: "components",
  pattern: "*.tsx",
  excludePatterns: ["node_modules", "dist", "test"]
})

// 步骤2：检查新增或修改的组件
view({
  path: "components/common-custom",
  type: "directory"
})

// 步骤3：更新组件清单文档
edit_file_filesystem({
  path: "docs/组件清单.md",
  edits: [{
    oldText: "旧的组件信息",
    newText: "新的组件信息"
  }]
})

// 步骤4：记录变更到memory系统
create_entities_memory({
  entities: [{
    name: "component-inventory-update",
    entityType: "maintenance_record",
    observations: ["更新组件清单", "新增XX组件", "修改XX组件状态"]
  }]
})

// 步骤5：用户确认变更
collect_feedback_feedback_enhanced({
  work_summary: `
## 组件清单更新汇报

### 变更内容
- 新增组件：XX个
- 更新组件：XX个
- 状态变更：XX个

### 影响范围
- 文档更新：组件清单.md
- 相关文档：implementation-guide.md

请确认变更内容是否正确。
  `
})
```

#### 2. 组件状态监控
```javascript
// 定期检查组件完整性
const checkComponentIntegrity = () => {
  // 检查组件文件是否存在
  read_multiple_files_filesystem({
    paths: [
      "components/common-custom/back-button.tsx",
      "components/common-custom/data-table.tsx",
      // ... 其他关键组件
    ]
  })

  // 检查示例页面是否存在
  search_files_filesystem({
    path: "app/examples",
    pattern: "*-example",
    excludePatterns: []
  })
}
```

#### 3. 组件依赖关系分析
```javascript
// 分析组件间的依赖关系
const analyzeComponentDependencies = () => {
  // 搜索组件导入关系
  search_files_filesystem({
    path: "components",
    pattern: "*.tsx"
  }).then(files => {
    files.forEach(file => {
      view({
        path: file,
        type: "file",
        search_query_regex: "import.*from.*@/components",
        case_sensitive: false
      })
    })
  })
}
```

#### 4. 最佳实践
- **定期更新**：每次新增或修改组件后及时更新清单
- **状态跟踪**：准确标记组件的维护状态
- **依赖管理**：维护组件间的依赖关系图
- **用户确认**：重要变更使用feedback工具确认
- **记录保存**：使用memory工具保存重要的维护记录

---

遵循这些规范，可以最大化MCP工具的效用，提供更好的开发体验和更高的工作效率。

"use client"

import React from "react"
import { ComponentPreviewContainer } from "@/components/component-detail/preview-container"
import { allExamples } from "./examples"

// ============================================================================
// API文档组件
// ============================================================================

function EmptyStateApiDocs() {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-3">EmptyState</h3>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b">
                <th className="text-left py-2 px-3 font-medium">属性</th>
                <th className="text-left py-2 px-3 font-medium">类型</th>
                <th className="text-left py-2 px-3 font-medium">默认值</th>
                <th className="text-left py-2 px-3 font-medium">说明</th>
              </tr>
            </thead>
            <tbody className="text-muted-foreground">
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">icon</td>
                <td className="py-2 px-3">ReactNode</td>
                <td className="py-2 px-3">-</td>
                <td className="py-2 px-3">空状态图标</td>
              </tr>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">title</td>
                <td className="py-2 px-3">string</td>
                <td className="py-2 px-3">-</td>
                <td className="py-2 px-3">空状态标题</td>
              </tr>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">description</td>
                <td className="py-2 px-3">string</td>
                <td className="py-2 px-3">-</td>
                <td className="py-2 px-3">空状态描述</td>
              </tr>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">actionLabel</td>
                <td className="py-2 px-3">string</td>
                <td className="py-2 px-3">-</td>
                <td className="py-2 px-3">操作按钮文本</td>
              </tr>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">actionIcon</td>
                <td className="py-2 px-3">ReactNode</td>
                <td className="py-2 px-3">-</td>
                <td className="py-2 px-3">操作按钮图标</td>
              </tr>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">onAction</td>
                <td className="py-2 px-3">() =&gt; void</td>
                <td className="py-2 px-3">-</td>
                <td className="py-2 px-3">操作按钮点击回调</td>
              </tr>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">className</td>
                <td className="py-2 px-3">string</td>
                <td className="py-2 px-3">-</td>
                <td className="py-2 px-3">自定义样式类名</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

// ============================================================================
// 页面组件
// ============================================================================

export default function EmptyStateExamplePage() {
  return (
    <ComponentPreviewContainer
      title="空状态 EmptyState"
      description="当页面或区域没有内容时显示的占位组件，提供友好的用户体验和引导操作"
      whenToUse="当列表、表格、搜索结果为空时使用；当页面加载失败或网络错误时使用；当用户首次使用功能时提供引导"
      examples={allExamples}
      apiDocs={<EmptyStateApiDocs />}
    />
  )
}
"use client"

import React from "react"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ArrowRight, Database, FormInput, Layout, MousePointer, FileText, Wrench } from "lucide-react"
import { MainLayout, componentSections } from "@/components/navigation/main-layout"

// 分组图标映射
const sectionIcons = {
  "数据展示": Database,
  "表单与输入": FormInput,
  "导航与布局": Layout,
  "交互与反馈": MousePointer,
  "内容与状态": FileText,
  "工具与扩展": Wrench,
}

export default function Home() {
  return (
    <MainLayout pageTitle="业务组件库">
      <div className="space-y-8">
        {/* 页面标题和描述 */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold tracking-tight">业务组件库</h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            基于 Next.js 15 和 shadcn/ui 构建的高质量业务组件库，提供丰富的组件和完整的示例代码
          </p>
        </div>

        {/* 组件分组展示 */}
        <div className="space-y-12">
          {componentSections.map((section) => {
            const IconComponent = sectionIcons[section.title as keyof typeof sectionIcons]
            
            return (
              <section key={section.title} className="space-y-6">
                <div className="flex items-center gap-3">
                  {IconComponent && <IconComponent className="h-6 w-6 text-primary" />}
                  <h2 className="text-2xl font-bold">{section.title}</h2>
                  <Badge variant="outline" className="text-xs">
                    {section.items.filter(item => !item.disabled).length} 个组件
                  </Badge>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                  {section.items.map((item) => (
                    <Card 
                      key={item.title} 
                      className={`group transition-all duration-200 hover:shadow-md hover:scale-[1.02] ${
                        item.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
                      }`}
                    >
                      <Link href={item.disabled ? '#' : item.href} className={item.disabled ? 'pointer-events-none' : ''}>
                        <CardHeader className="pb-3">
                          <div className="flex items-center justify-between">
                            <CardTitle className="text-lg font-medium group-hover:text-primary transition-colors">
                              {item.title}
                            </CardTitle>
                            <div className="flex items-center gap-2">
                              {item.isNew && (
                                <Badge variant="secondary" className="text-xs">新增</Badge>
                              )}
                              {item.disabled && (
                                <Badge variant="outline" className="text-xs">即将推出</Badge>
                              )}
                              {!item.disabled && (
                                <ArrowRight className="h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors" />
                              )}
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <CardDescription className="text-sm">
                            {getComponentDescription(item.title)}
                          </CardDescription>
                        </CardContent>
                      </Link>
                    </Card>
                  ))}
                </div>
              </section>
            )
          })}
        </div>
      </div>
    </MainLayout>
  )
}

// 组件描述映射
function getComponentDescription(title: string): string {
  const descriptions: Record<string, string> = {
    "数据表格": "功能完整的数据表格，支持排序、筛选、分页等特性",
    "统计卡片": "用于展示关键业务指标和统计数据的卡片组件",
    "列表视图": "多种样式的列表展示组件，支持不同的数据格式",
    "时间轴": "展示时间相关的活动或事件序列",
    "图表": "数据可视化图表组件（即将推出）",
    "基础表单": "用于数据输入的表单组件，支持验证和各种输入类型",
    "表单对话框": "弹出式表单，用于快速创建和编辑操作",
    "搜索组件": "搜索输入框组件，支持自动完成和建议",
    "筛选器": "数据筛选组件，支持多条件组合筛选",
    "文件上传": "文件上传组件，支持拖拽、预览和进度显示",
    "面包屑导航": "面包屑导航组件，显示页面层次结构",
    "标签页": "标签页组件，用于内容分组展示",
    "分页": "分页组件，支持多种分页样式",
    "返回按钮": "返回上一页的按钮组件",
    "卡片模板": "各种卡片布局模板，适用于不同场景",
    "仪表板": "仪表板布局组件，展示关键业务数据",
    "操作按钮": "各种操作按钮组件，支持不同的交互状态",
    "模态框": "模态对话框组件，用于重要信息展示和确认",
    "菜单": "下拉菜单和上下文菜单组件",
    "通知": "系统通知和消息提醒组件",
    "工具提示": "鼠标悬停时显示的提示信息组件",
    "加载状态": "各种加载状态和占位符组件",
    "骨架屏": "内容加载时的骨架屏占位组件",
    "标签": "标签组件，用于分类和标记",
    "状态徽章": "状态标识组件，显示不同的状态信息",
    "评分": "评分组件，支持星级评分和数字评分",
    "评论": "评论组件，支持嵌套回复和点赞",
    "文本截断": "长文本截断组件，支持展开和收起",
    "空状态": "没有数据时的空状态展示组件",
    "错误状态": "错误发生时的状态展示组件",
    "图标选择器": "图标选择组件，提供丰富的图标库",
    "日历": "日历组件，支持日期选择和事件展示",
    "组合组件": "多个组件的组合使用示例",
  }
  
  return descriptions[title] || "高质量的业务组件，提供完整的功能和示例"
}

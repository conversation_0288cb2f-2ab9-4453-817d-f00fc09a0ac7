"use client"

// 认证相关组件
export { AuthProvider } from "../providers/auth-provider"

// 数据表格组件 - 使用标准化组件
export { AdvancedDataTable as DataTable } from "@/components/common-custom/data-table"

// 表单组件 - 使用标准化组件
export { FormDialog } from "@/components/common-custom/form"

// 加载相关组件 - 使用标准化组件
export { GlobalLoading, globalLoading } from "@/components/common-custom/global-loading"
export { PageLoading } from "@/components/project-custom/page-loading"

// 导航相关组件 - 从navigation目录导出
export { NavigationLink } from "../navigation/navigation-link"
export { NavigationButton } from "../navigation/navigation-button"

// 侧边栏相关组件 - 从navigation目录导出
export {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarInset,
  SidebarProvider,
  SidebarRail,
  useSidebar
} from "../navigation/sidebar"

// 其他组件 - 使用标准化组件
export { BackButton } from "@/components/common-custom/back-button"
export { HeaderWithBreadcrumb } from "@/components/project-custom/breadcrumb"
"use client"

import React from "react"
import { ComponentPreviewContainer } from "@/components/component-detail/preview-container"
import { allExamples } from "./examples"

// ============================================================================
// API文档组件
// ============================================================================

function ApiDocs() {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium mb-2">图表组件</h3>
        <p className="text-muted-foreground">
          基于 Recharts 构建的图表组件集合，提供多种数据可视化方案。支持柱状图、折线图、饼图、面积图等常用图表类型。
        </p>
      </div>

      {/* 图表组件通用属性 */}
      <div>
        <h4 className="text-md font-medium mb-3">通用属性</h4>
        <div className="overflow-x-auto">
          <table className="w-full text-sm border-collapse border border-border">
            <thead>
              <tr className="border-b bg-muted/50">
                <th className="py-2 px-3 text-left font-medium">属性名</th>
                <th className="py-2 px-3 text-left font-medium">类型</th>
                <th className="py-2 px-3 text-left font-medium">默认值</th>
                <th className="py-2 px-3 text-left font-medium">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">data</td>
                <td className="py-2 px-3">Array</td>
                <td className="py-2 px-3">[]</td>
                <td className="py-2 px-3">图表数据</td>
              </tr>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">width</td>
                <td className="py-2 px-3">number</td>
                <td className="py-2 px-3">400</td>
                <td className="py-2 px-3">图表宽度</td>
              </tr>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">height</td>
                <td className="py-2 px-3">number</td>
                <td className="py-2 px-3">300</td>
                <td className="py-2 px-3">图表高度</td>
              </tr>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">className</td>
                <td className="py-2 px-3">string</td>
                <td className="py-2 px-3">-</td>
                <td className="py-2 px-3">自定义样式类名</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* 使用指南 */}
      <div>
        <h4 className="text-md font-medium mb-3">使用指南</h4>
        <div className="space-y-2 text-sm text-muted-foreground">
          <p>• 图表组件基于 Recharts 库构建，提供响应式设计</p>
          <p>• 支持深色模式自动适配</p>
          <p>• 数据格式需要符合对应图表类型的要求</p>
          <p>• 可通过 className 属性自定义样式</p>
          <p>• 支持交互功能如悬停提示、点击事件等</p>
        </div>
      </div>
    </div>
  )
}

export default function ChartExamplePage() {
  return (
    <ComponentPreviewContainer
      title="图表组件 Charts"
      description="基于 Recharts 的数据可视化组件集合，提供柱状图、折线图、饼图、面积图等多种图表类型，支持响应式设计和深色模式。"
      whenToUse="当需要展示数据趋势和统计信息时使用；当需要可视化数值数据时使用；当需要创建仪表板和报表时使用；适用于销售数据、用户统计、性能监控、财务报表等场景。"
      examples={allExamples}
      apiDocs={<ApiDocs />}
    />
  )
}

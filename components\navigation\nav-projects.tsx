﻿"use client"

import {
  <PERSON><PERSON><PERSON>,
  Forward,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Trash2,
  Chevron<PERSON><PERSON>,
  type LucideIcon,
} from "lucide-react"
import Link from "next/link"
import { usePathname } from "next/navigation"

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuAction,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/navigation/sidebar"
import { NavigationLink } from "./navigation-link"

interface Project {
  title: string
  url: string
  icon?: LucideIcon
}

export function NavProjects({
  projects,
}: {
  projects: Project[]
}) {
  const { state, isMobile } = useSidebar()
  const pathname = usePathname()

  // 检查项目是否活动状态
  const isProjectActive = (url: string): boolean => {
    return pathname === url || pathname.startsWith(`${url}/`)
  }

  if (!projects.length) {
    return null
  }
  
  return (
    <SidebarGroup className={state === "collapsed" && !isMobile ? "" : "group-data-[collapsible=icon]:hidden"}>
      <SidebarGroupLabel>Projects</SidebarGroupLabel>
      <SidebarMenu>
        {projects.map((project) => (
          <SidebarMenuItem key={project.title}>
            {state === "collapsed" && !isMobile ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <SidebarMenuButton 
                    tooltip={project.title}
                    isActive={isProjectActive(project.url)}
                  >
                    {project.icon && <project.icon />}
                    <span className="flex-1">{project.title}</span>
                    <ChevronRight className="ml-auto opacity-0 group-hover:opacity-100 transition-opacity" />
                  </SidebarMenuButton>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  side="right"
                  align="start"
                  className="w-48 rounded-lg py-2"
                >
                  <DropdownMenuItem asChild>
                    <NavigationLink href={project.url} className="flex w-full items-center" showLoadingIcon={true}>
                      <Folder className="mr-2 size-4 text-muted-foreground" />
                      <span>View Project</span>
                    </NavigationLink>
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Forward className="mr-2 size-4 text-muted-foreground" />
                    <span>Share Project</span>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem>
                    <Trash2 className="mr-2 size-4 text-muted-foreground" />
                    <span>Delete Project</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <>
                <SidebarMenuButton 
                  asChild
                  isActive={isProjectActive(project.url)}
                >
                  <NavigationLink href={project.url} showLoadingIcon={true}>
                    {project.icon && <project.icon />}
                    <span className="flex-1">{project.title}</span>
                  </NavigationLink>
                </SidebarMenuButton>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <SidebarMenuAction showOnHover>
                      <MoreHorizontal />
                      <span className="sr-only">More</span>
                    </SidebarMenuAction>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent
                    className="w-48 rounded-lg"
                    side={isMobile ? "bottom" : "right"}
                    align={isMobile ? "end" : "start"}
                  >
                    <DropdownMenuItem>
                      <Folder className="mr-2 size-4 text-muted-foreground" />
                      <span>View Project</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Forward className="mr-2 size-4 text-muted-foreground" />
                      <span>Share Project</span>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem>
                      <Trash2 className="mr-2 size-4 text-muted-foreground" />
                      <span>Delete Project</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </>
            )}
          </SidebarMenuItem>
        ))}
        <SidebarMenuItem>
          <SidebarMenuButton className="text-sidebar-foreground/70">
            <MoreHorizontal className="text-sidebar-foreground/70" />
            <span>More</span>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    </SidebarGroup>
  )
}


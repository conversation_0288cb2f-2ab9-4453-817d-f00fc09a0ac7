﻿"use client"

import { ChevronRight, type LucideIcon } from "lucide-react"
import Link from "next/link"
import { usePathname } from "next/navigation"

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  useSidebar,
} from "@/components/navigation/sidebar"
import { NavigationLink } from "./navigation-link"

export function NavMain({
  items,
}: {
  items: {
    title: string
    url?: string
    icon?: LucideIcon
    isActive?: boolean
    items?: {
      title: string
      url: string
    }[]
  }[]
}) {
  const { state, isMobile } = useSidebar();
  const pathname = usePathname();

  // 检查子项是否包含当前路径
  const isSubItemActive = (url: string): boolean => {
    return pathname === url || pathname.startsWith(`${url}/`);
  };

  return (
    <SidebarGroup>
      <SidebarGroupLabel>Platform</SidebarGroupLabel>
      <SidebarMenu>
        {items.map((item) => {
          // 判断是否有子项
          const hasSubItems = item.items && item.items.length > 0;
          
          // 检查主项是否活动 - 只有直接点击主项时才激活
          const isMainItemActive = item.url ? isSubItemActive(item.url) : false;
          
          // 检查是否有活动的子项，用于展开父菜单，但不高亮父菜单
          const hasActiveSubItem = hasSubItems && item.items?.some(subItem => isSubItemActive(subItem.url));
          
          // 决定是否展开菜单（布尔值）
          const shouldExpand = Boolean(item.isActive || isMainItemActive || hasActiveSubItem);
          
          return state === "collapsed" && !isMobile && hasSubItems ? (
            <SidebarMenuItem key={item.title}>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <SidebarMenuButton tooltip={item.title} isActive={isMainItemActive}>
                    {item.icon && <item.icon />}
                    {item.url ? (
                      <NavigationLink href={item.url} className="flex-1" showLoadingIcon={true}>
                        <span>{item.title}</span>
                      </NavigationLink>
                    ) : (
                      <span className="flex-1">{item.title}</span>
                    )}
                    <ChevronRight className="ml-auto" />
                  </SidebarMenuButton>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  side="right"
                  align="start"
                  className="w-48 rounded-lg py-2"
                >
                  {item.items?.map((subItem) => {
                    const isSubActive = isSubItemActive(subItem.url);
                    return (
                      <NavigationLink
                        key={subItem.title}
                        href={subItem.url}
                        showLoadingIcon={true}
                        className={`flex w-full cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent focus:text-accent-foreground hover:bg-accent hover:text-accent-foreground ${isSubActive ? 'bg-accent text-accent-foreground font-medium' : ''}`}
                      >
                        <span>{subItem.title}</span>
                      </NavigationLink>
                    );
                  })}
                </DropdownMenuContent>
              </DropdownMenu>
            </SidebarMenuItem>
          ) : hasSubItems ? (
            // 有子项时显示可折叠组件
            <Collapsible
              key={item.title}
              asChild
              defaultOpen={shouldExpand}
              className="group/collapsible"
            >
              <SidebarMenuItem>
                <CollapsibleTrigger asChild>
                  <SidebarMenuButton tooltip={item.title} isActive={isMainItemActive}>
                    {item.icon && <item.icon />}
                    {item.url ? (
                      <NavigationLink href={item.url} className="flex-1" showLoadingIcon={true}>
                        <span>{item.title}</span>
                      </NavigationLink>
                    ) : (
                      <span className="flex-1">{item.title}</span>
                    )}
                    <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                  </SidebarMenuButton>
                </CollapsibleTrigger>
                <CollapsibleContent>
                  <SidebarMenuSub>
                    {item.items?.map((subItem) => {
                      const isSubActive = isSubItemActive(subItem.url);
                      return (
                        <SidebarMenuSubItem key={subItem.title}>
                          <SidebarMenuSubButton asChild isActive={isSubActive}>
                            <NavigationLink href={subItem.url} showLoadingIcon={true}>
                              <span>{subItem.title}</span>
                            </NavigationLink>
                          </SidebarMenuSubButton>
                        </SidebarMenuSubItem>
                      );
                    })}
                  </SidebarMenuSub>
                </CollapsibleContent>
              </SidebarMenuItem>
            </Collapsible>
          ) : (
            // 没有子项时显示普通菜单项
            <SidebarMenuItem key={item.title}>
              <SidebarMenuButton tooltip={item.title} isActive={isMainItemActive}>
                {item.icon && <item.icon />}
                {item.url ? (
                  <NavigationLink href={item.url} className="flex-1" showLoadingIcon={true}>
                    <span>{item.title}</span>
                  </NavigationLink>
                ) : (
                  <span className="flex-1">{item.title}</span>
                )}
              </SidebarMenuButton>
            </SidebarMenuItem>
          );
        })}
      </SidebarMenu>
    </SidebarGroup>
  )
}


"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
  DropdownMenuGroup,
  DropdownMenuCheckboxItem,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuPortal,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { MoreHorizontal, ChevronDown, ChevronRight, Check } from "lucide-react"
import { Badge } from "@/components/ui/badge"

/**
 * 基础菜单项接口
 */
interface BaseMenuItem {
  /**
   * 菜单项值
   */
  value?: string

  /**
   * 菜单项是否禁用
   */
  disabled?: boolean

  /**
   * 菜单项CSS类名
   */
  className?: string
}

/**
 * 标准菜单项接口
 */
interface StandardMenuItem extends BaseMenuItem {
  /**
   * 菜单项类型
   */
  type?: "item"

  /**
   * 菜单项标签
   */
  label: string

  /**
   * 菜单项图标
   */
  icon?: React.ComponentType<{ className?: string }>

  /**
   * 菜单项点击处理函数
   */
  onClick?: () => void

  /**
   * 菜单项徽章
   */
  badge?: {
    /**
     * 徽章内容
     */
    content: React.ReactNode

    /**
     * 徽章变体
     */
    variant?: "default" | "secondary" | "outline" | "destructive"
  }

  /**
   * 菜单项描述
   */
  description?: string

  /**
   * 菜单项快捷键
   */
  shortcut?: string
}

/**
 * 分隔符菜单项接口
 */
interface SeparatorMenuItem extends BaseMenuItem {
  /**
   * 菜单项类型
   */
  type: "separator"
  
  /**
   * 菜单项标签 (可选)
   */
  label?: string
}

/**
 * 标签菜单项接口
 */
interface LabelMenuItem extends BaseMenuItem {
  /**
   * 菜单项类型
   */
  type: "label"
  
  /**
   * 菜单项标签
   */
  label: string
  
  /**
   * 菜单项图标
   */
  icon?: React.ComponentType<{ className?: string }>
}

/**
 * 复选框菜单项接口
 */
interface CheckboxMenuItem extends BaseMenuItem {
  /**
   * 菜单项类型
   */
  type: "checkbox"
  
  /**
   * 菜单项标签
   */
  label: string
  
  /**
   * 菜单项图标
   */
  icon?: React.ComponentType<{ className?: string }>
  
  /**
   * 菜单项状态
   */
  checked?: boolean
  
  /**
   * 菜单项点击处理函数
   */
  onClick?: () => void
  
  /**
   * 菜单项徽章
   */
  badge?: {
    /**
     * 徽章内容
     */
    content: React.ReactNode

    /**
     * 徽章变体
     */
    variant?: "default" | "secondary" | "outline" | "destructive"
  }
}

/**
 * 单选菜单项接口
 */
interface RadioMenuItem extends BaseMenuItem {
  /**
   * 菜单项类型
   */
  type: "radio"
  
  /**
   * 菜单项标签
   */
  label: string
  
  /**
   * 菜单项图标
   */
  icon?: React.ComponentType<{ className?: string }>
  
  /**
   * 菜单项值
   */
  value: string
  
  /**
   * 菜单项徽章
   */
  badge?: {
    /**
     * 徽章内容
     */
    content: React.ReactNode

    /**
     * 徽章变体
     */
    variant?: "default" | "secondary" | "outline" | "destructive"
  }
}

/**
 * 子菜单项接口
 */
interface SubMenuItem extends BaseMenuItem {
  /**
   * 菜单项类型
   */
  type: "sub"
  
  /**
   * 菜单项标签
   */
  label: string
  
  /**
   * 菜单项图标
   */
  icon?: React.ComponentType<{ className?: string }>
  
  /**
   * 子菜单项
   */
  items: MenuItem[]
  
  /**
   * 菜单项徽章
   */
  badge?: {
    /**
     * 徽章内容
     */
    content: React.ReactNode

    /**
     * 徽章变体
     */
    variant?: "default" | "secondary" | "outline" | "destructive"
  }
}

/**
 * 菜单项联合类型
 */
export type MenuItem = StandardMenuItem | SeparatorMenuItem | LabelMenuItem | CheckboxMenuItem | RadioMenuItem | SubMenuItem

/**
 * 下拉菜单属性接口
 */
export interface DropdownMenuButtonProps {
  /**
   * 触发器标签
   */
  label?: string

  /**
   * 触发器图标
   */
  icon?: React.ComponentType<{ className?: string }>

  /**
   * 菜单项
   */
  items: MenuItem[]

  /**
   * 触发器变体
   */
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"

  /**
   * 触发器尺寸
   */
  size?: "default" | "sm" | "lg" | "icon"

  /**
   * 菜单对齐方式
   */
  align?: "center" | "start" | "end"

  /**
   * 菜单宽度
   */
  menuWidth?: string | number

  /**
   * 菜单CSS类名
   */
  menuClassName?: string

  /**
   * 触发器CSS类名
   */
  triggerClassName?: string

  /**
   * 菜单打开状态变更回调
   */
  onOpenChange?: (open: boolean) => void

  /**
   * 菜单项选择回调
   */
  onSelect?: (value: string) => void

  /**
   * 单选组值
   */
  radioValue?: string

  /**
   * 单选组值变更回调
   */
  onRadioValueChange?: (value: string) => void

  /**
   * 是否显示图标
   */
  showIcons?: boolean
}

/**
 * 更多菜单按钮属性接口
 */
export interface MoreMenuButtonProps {
  /**
   * 菜单项
   */
  items: MenuItem[]

  /**
   * 菜单对齐方式
   */
  align?: "center" | "start" | "end"

  /**
   * 菜单宽度
   */
  menuWidth?: string | number

  /**
   * 菜单CSS类名
   */
  menuClassName?: string

  /**
   * 触发器CSS类名
   */
  triggerClassName?: string

  /**
   * 触发器变体
   */
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"

  /**
   * 触发器尺寸
   */
  size?: "default" | "sm" | "lg" | "icon"

  /**
   * 菜单打开状态变更回调
   */
  onOpenChange?: (open: boolean) => void

  /**
   * 菜单项选择回调
   */
  onSelect?: (value: string) => void

  /**
   * 是否显示图标
   */
  showIcons?: boolean
}

/**
 * 操作菜单按钮属性接口
 */
export interface ActionMenuButtonProps {
  /**
   * 主要操作
   */
  primaryActions: MenuItem[]

  /**
   * 次要操作
   */
  secondaryActions?: MenuItem[]

  /**
   * 是否显示更多按钮的阈值
   */
  visibleItemsCount?: number

  /**
   * 菜单对齐方式
   */
  align?: "center" | "start" | "end"

  /**
   * 菜单宽度
   */
  menuWidth?: string | number

  /**
   * 菜单CSS类名
   */
  menuClassName?: string

  /**
   * 触发器CSS类名
   */
  triggerClassName?: string

  /**
   * 触发器变体
   */
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"

  /**
   * 触发器尺寸
   */
  size?: "default" | "sm" | "lg" | "icon"

  /**
   * 菜单打开状态变更回调
   */
  onOpenChange?: (open: boolean) => void

  /**
   * 菜单项选择回调
   */
  onSelect?: (value: string) => void

  /**
   * 是否显示图标
   */
  showIcons?: boolean
}

/**
 * 渲染菜单项
 */
const renderMenuItem = (item: MenuItem, onSelect?: (value: string) => void, showIcons: boolean = true, index?: number, items?: MenuItem[]) => {
  switch (item.type) {
    case "separator":
      // 避免在菜单顶部显示分割线
      if (index === 0) return null
      // 避免连续的分割线
      if (items && index && items[index - 1]?.type === "separator") return null
      return <DropdownMenuSeparator key={item.label || `separator-${index}`} />
    
    case "label": {
      const labelItem = item as LabelMenuItem;
      return (
        <DropdownMenuLabel key={labelItem.label} className={labelItem.className}>
          {labelItem.icon && showIcons && <labelItem.icon className="mr-2 h-4 w-4" />}
          {labelItem.label}
        </DropdownMenuLabel>
      )
    }
    
    case "checkbox": {
      const checkboxItem = item as CheckboxMenuItem;
      return (
        <DropdownMenuCheckboxItem
          key={checkboxItem.label}
          checked={checkboxItem.checked}
          disabled={checkboxItem.disabled}
          onCheckedChange={() => {
            checkboxItem.onClick?.()
            if (checkboxItem.value && onSelect) {
              onSelect(checkboxItem.value)
            }
          }}
          className={cn("cursor-pointer", checkboxItem.className)}
        >
          {checkboxItem.icon && showIcons && <checkboxItem.icon className="mr-2 h-4 w-4" />}
          {checkboxItem.label}
          {checkboxItem.badge && (
            <Badge variant={checkboxItem.badge.variant} className="ml-auto">
              {checkboxItem.badge.content}
            </Badge>
          )}
        </DropdownMenuCheckboxItem>
      )
    }
    
    case "radio": {
      const radioItem = item as RadioMenuItem;
      return (
        <DropdownMenuRadioItem
          key={radioItem.label}
          value={radioItem.value}
          disabled={radioItem.disabled}
          className={cn("cursor-pointer", radioItem.className)}
        >
          {radioItem.icon && showIcons && <radioItem.icon className="mr-2 h-4 w-4" />}
          {radioItem.label}
          {radioItem.badge && (
            <Badge variant={radioItem.badge.variant} className="ml-auto">
              {radioItem.badge.content}
            </Badge>
          )}
        </DropdownMenuRadioItem>
      )
    }
    
    case "sub": {
      const subItem = item as SubMenuItem;
      return (
        <DropdownMenuSub key={subItem.label}>
          <DropdownMenuSubTrigger className={cn("cursor-pointer", subItem.disabled && "opacity-50 pointer-events-none", subItem.className)}>
            {subItem.icon && showIcons && <subItem.icon className="mr-2 h-4 w-4" />}
            {subItem.label}
            {subItem.badge && (
              <Badge variant={subItem.badge.variant} className="ml-auto">
                {subItem.badge.content}
              </Badge>
            )}
          </DropdownMenuSubTrigger>
          <DropdownMenuPortal>
            <DropdownMenuSubContent>
              {subItem.items.map((subItem) => renderMenuItem(subItem, onSelect, showIcons))}
            </DropdownMenuSubContent>
          </DropdownMenuPortal>
        </DropdownMenuSub>
      )
    }
    
    default: {
      // 处理标准菜单项
      const standardItem = item as StandardMenuItem;
      return (
        <DropdownMenuItem
          key={standardItem.label}
          disabled={standardItem.disabled}
          onClick={() => {
            standardItem.onClick?.()
            if (standardItem.value && onSelect) {
              onSelect(standardItem.value)
            }
          }}
          className={cn("cursor-pointer", standardItem.className)}
        >
          {standardItem.icon && showIcons && <standardItem.icon className="mr-2 h-4 w-4" />}
          <span className="flex-1">{standardItem.label}</span>
          {standardItem.description && (
            <span className="text-xs text-muted-foreground ml-2">{standardItem.description}</span>
          )}
          {standardItem.badge && (
            <Badge variant={standardItem.badge.variant} className="ml-2">
              {standardItem.badge.content}
            </Badge>
          )}
          {standardItem.shortcut && (
            <span className="text-xs text-muted-foreground ml-auto">{standardItem.shortcut}</span>
          )}
        </DropdownMenuItem>
      )
    }
  }
}

/**
 * 下拉菜单按钮组件
 */
export function DropdownMenuButton({
  label,
  icon: Icon,
  items,
  variant = "outline",
  size = "default",
  align = "end",
  menuWidth,
  menuClassName,
  triggerClassName,
  onOpenChange,
  onSelect,
  radioValue,
  onRadioValueChange,
  showIcons = true,
}: DropdownMenuButtonProps) {
  return (
    <DropdownMenu onOpenChange={onOpenChange}>
      <DropdownMenuTrigger asChild>
        <Button variant={variant} size={size} className={cn("cursor-pointer", triggerClassName)}>
          {Icon && <Icon className={cn("h-4 w-4", label && "mr-2")} />}
          {label}
          <ChevronDown className="ml-2 h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align={align}
        className={cn("min-w-[180px]", menuClassName)}
        style={menuWidth ? { width: menuWidth } : undefined}
      >
        {items.some(item => item.type === "radio") ? (
          <DropdownMenuRadioGroup value={radioValue} onValueChange={onRadioValueChange}>
            {items.map((item, index) => renderMenuItem(item, onSelect, showIcons, index, items))}
          </DropdownMenuRadioGroup>
        ) : (
          items.map((item, index) => renderMenuItem(item, onSelect, showIcons, index, items))
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

/**
 * 更多菜单按钮组件
 */
export function MoreMenuButton({
  items,
  align = "end",
  menuWidth,
  menuClassName,
  triggerClassName,
  variant = "ghost",
  size = "icon",
  onOpenChange,
  onSelect,
  showIcons = true,
}: MoreMenuButtonProps) {
  return (
    <DropdownMenu onOpenChange={onOpenChange}>
      <DropdownMenuTrigger asChild>
        <Button variant={variant} size={size} className={cn("cursor-pointer", triggerClassName)}>
          <MoreHorizontal className="h-4 w-4" />
          <span className="sr-only">更多选项</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align={align}
        className={cn("min-w-[180px]", menuClassName)}
        style={menuWidth ? { width: menuWidth } : undefined}
      >
        {items.map((item) => renderMenuItem(item, onSelect, showIcons))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

/**
 * 操作菜单按钮组件
 */
export function ActionMenuButton({
  primaryActions,
  secondaryActions = [],
  visibleItemsCount = 2,
  align = "end",
  menuWidth,
  menuClassName,
  triggerClassName,
  variant = "ghost",
  size = "icon",
  onOpenChange,
  onSelect,
  showIcons = true,
}: ActionMenuButtonProps) {
  const visibleActions = primaryActions.slice(0, visibleItemsCount)
  const moreActions = [...primaryActions.slice(visibleItemsCount), ...secondaryActions]

  return (
    <div className="flex items-center gap-1">
      {visibleActions.map((action) => {
        // 确保只处理有label的菜单项
        if ('label' in action) {
          return (
            <Button
              key={action.label}
              variant={variant}
              size={size}
              disabled={action.disabled}
              onClick={() => {
                if ('onClick' in action) {
                  action.onClick?.()
                }
                if (action.value && onSelect) {
                  onSelect(action.value)
                }
              }}
              className={cn("cursor-pointer", triggerClassName)}
              title={action.label}
            >
              {'icon' in action && action.icon && <action.icon className="h-4 w-4" />}
              {size !== "icon" && action.label}
            </Button>
          )
        }
        return null
      })}

      {moreActions.length > 0 && (
        <MoreMenuButton
          items={moreActions}
          align={align}
          menuWidth={menuWidth}
          menuClassName={menuClassName}
          triggerClassName={triggerClassName}
          variant={variant}
          size={size}
          onOpenChange={onOpenChange}
          onSelect={onSelect}
          showIcons={showIcons}
        />
      )}
    </div>
  )
}

/**
 * 上下文菜单组件
 */
export function ContextMenu({
  children,
  items,
  onSelect,
  menuClassName,
  showIcons = true,
}: {
  children: React.ReactNode
  items: MenuItem[]
  onSelect?: (value: string) => void
  menuClassName?: string
  showIcons?: boolean
}) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        {children}
      </DropdownMenuTrigger>
      <DropdownMenuContent className={cn("min-w-[180px]", menuClassName)}>
        {items.map((item) => renderMenuItem(item, onSelect, showIcons))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
} 
---
description: 
globs: 
alwaysApply: true
---
# 开发工作流程规则

## 项目开发流程
1. 需求分析
   - 充分理解用户需求，站在用户角度思考
   - 识别需求中可能存在的缺漏，与用户讨论并完善需求
   - 选择最简单的解决方案来满足用户需求

2. 开发准备
   - 查阅`.doc`目录下的相关设计规范文档，确保理解设计要求
   - 特别关注与任务相关的设计规范，如色彩系统、排版、布局和组件规范
   - 分析任务涉及的界面元素，检查是否符合设计系统规范
   - 明确开发任务的范围和依赖关系

3. 开发过程
   - 回答用户问题前，先对问题分析，列出修改步骤，之后再逐步进行修改
   - 首先浏览项目文档和代码，理解项目目标、架构和实现方式
   - 按照组件划分和代码规范进行开发
   - 确保实现符合设计规范文档中的要求
   - 在README.md中清晰描述功能的用途、使用方法、参数说明和返回值

4. 问题解决流程
   - 全面阅读相关代码文件，理解所有代码的功能和逻辑
   - 分析导致问题的原因，提出解决问题的思路
   - 参考设计规范文档，确保修复方案符合设计标准
   - 与用户进行多次交互，根据反馈调整解决方案
   - 当一个bug经过两次调整仍未解决时，启动系统性思考模式

## 代码提交与记录
1. 完成任务后，更新ChangeLog.md文件
   - 包括新增功能说明
   - 包括修复的问题
   - 包括优化建议

2. 检查事项
   - 确保所有需求都已实现
   - 确保代码遵循项目规范和设计规范
   - 确保没有引入新的问题
   - 确保文档与代码保持一致

## 代码审查标准
1. 功能性
   - 代码是否实现了需求的所有功能
   - 是否处理了边缘情况和异常情况

2. 代码质量
   - 代码是否遵循TypeScript最佳实践
   - 是否使用了Next.js 15的最新特性
   - 组件结构是否合理
   - 是否有冗余代码

3. 设计规范遵循
   - 是否符合项目的设计哲学和核心原则
   - 色彩使用是否符合色彩系统规范
   - 排版是否遵循排版系统规范
   - 布局是否遵循网格系统规范
   - 组件实现是否符合组件规范
   - 交互设计是否符合交互规范
   - 表单设计是否符合表单设计规范
   - 图标使用是否符合图标系统规范

4. 性能考虑
   - 是否使用了适当的缓存策略
   - 大型列表是否进行了优化
   - 图片是否进行了优化
   - 是否避免了不必要的重渲染

5. 用户体验
   - UI是否一致且符合设计
   - 是否提供了适当的加载状态
   - 是否提供了友好的错误提示
   - 是否具有响应式设计

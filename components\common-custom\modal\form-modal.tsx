"use client"

import { ReactNode } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { X } from "lucide-react"
import { cn } from "@/lib/utils"

/**
 * 表单模态框组件
 */
export function FormModal({
  open,
  onOpenChange,
  title,
  description,
  children,
  onSubmit,
  submitText = "保存",
  cancelText = "取消",
  onCancel,
  className,
  maxWidth = "sm:max-w-[425px]",
}: {
  open?: boolean
  onOpenChange?: (open: boolean) => void
  title?: ReactNode
  description?: ReactNode
  children: ReactNode
  onSubmit?: () => void | Promise<void>
  submitText?: string
  cancelText?: string
  onCancel?: () => void
  className?: string
  maxWidth?: string
}) {
  const handleSubmit = async () => {
    if (onSubmit) {
      await onSubmit();
    }
    
    if (onOpenChange) {
      onOpenChange(false);
    }
  }
  
  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
    
    if (onOpenChange) {
      onOpenChange(false);
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className={cn(maxWidth, className)}>
        <button
          onClick={handleCancel}
          className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
        >
          <X className="h-4 w-4" />
          <span className="sr-only">关闭</span>
        </button>
        
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          {description && <DialogDescription>{description}</DialogDescription>}
        </DialogHeader>
        
        {children}
        
        <DialogFooter>
          <Button variant="outline" onClick={handleCancel}>{cancelText}</Button>
          <Button onClick={handleSubmit}>{submitText}</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 
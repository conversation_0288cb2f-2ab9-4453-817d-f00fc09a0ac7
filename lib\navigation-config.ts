import { usePathname } from "next/navigation"
import { navigationConfig } from "@/services/mock/navigationMock"
import {NavigationConfig} from "@/types/navigation";

// 获取导航配置数据
export function getNavigationConfig(): NavigationConfig {
  // 在实际项目中，可以在这里从API获取配置
  // 但现在我们直接使用本地JSON文件
  return navigationConfig as NavigationConfig
}

// 根据当前路径判断是否显示导航的Hook
export function useShowNavigation(): boolean {
  const pathname = usePathname()
  const config = getNavigationConfig()
  
  // 如果配置为不显示导航，直接返回false
  if (!config.showNavigation) {
    return false
  }
  
  // 如果当前路径在排除列表中，不显示导航
  if (config.excludePaths && pathname) {
    return !config.excludePaths.some(path => 
      pathname === path || 
      pathname.startsWith(`${path}/`)
    )
  }
  
  return true
} 

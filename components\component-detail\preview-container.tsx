"use client";

import React, { useState, useEffect } from "react";
import { ComponentPreview } from "./preview";
import { cn } from "@/lib/utils";
import { Separator } from "../ui/separator";
import { ScrollArea } from "../ui/scroll-area";
import { Button } from "../ui/button";
import { Code, ExternalLink, Copy, X } from "lucide-react";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "../ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../ui/tabs";
import Link from "next/link";
import { MainLayout } from "../navigation/main-layout";

interface CodeFile {
  name: string;
  language: string;
  code: string;
}

interface ComponentExample {
  id: string;
  title: string;
  description?: string;
  code: string;
  extraFiles?: CodeFile[];
  scope?: Record<string, any>;
}

// 添加导航项类型定义
interface NavItem {
  id: string;
  title: string;
  isSubItem?: boolean;
}

interface ComponentPreviewContainerProps {
  title: string;
  description?: string;
  whenToUse?: string;
  examples?: ComponentExample[];
  apiDocs?: React.ReactNode;
  className?: string;
  // 单个示例模式的属性
  code?: string;
  scope?: Record<string, any>;
}

export function ComponentPreviewContainer({
  title,
  description,
  whenToUse,
  examples = [],
  apiDocs,
  className,
  code,
  scope,
}: ComponentPreviewContainerProps) {
  // 当前激活的导航项
  const [activeSection, setActiveSection] = useState<string>("intro");
  // 控制代码对话框
  const [codeDialogOpen, setCodeDialogOpen] = useState(false);
  const [currentCode, setCurrentCode] = useState<string>("");
  const [currentTitle, setCurrentTitle] = useState<string>("");
  const [currentFiles, setCurrentFiles] = useState<CodeFile[]>([]);
  const [showCode, setShowCode] = useState<Record<string, boolean>>({});

  // 处理导航点击事件
  const handleNavClick = (id: string) => {
    setActiveSection(id);
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
    }
  };

  const handleViewCode = (example: ComponentExample) => {
    setCurrentCode(example.code);
    setCurrentTitle(example.title);
    setCurrentFiles(example.extraFiles || []);
    setCodeDialogOpen(true);
  };

  const toggleCodeVisibility = (id: string) => {
    setShowCode(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  // 判断是否为单个示例模式
  const isSingleExampleMode = Boolean(code && scope);

  const navItems: NavItem[] = isSingleExampleMode
    ? [
        { id: "intro", title: "介绍" },
        { id: "examples", title: "代码演示" },
      ]
    : [
        { id: "intro", title: "介绍" },
        { id: "when-to-use", title: "何时使用" },
        { id: "examples", title: "代码演示" },
        ...(examples && examples.length > 0
          ? examples.map(example => ({ id: example.id, title: example.title, isSubItem: true }))
          : []
        ),
        { id: "api", title: "API" },
      ];

  const content = (
    <div className={cn("flex relative mt-5", className)}>
      {/* 主要内容区域 */}
      <div className="flex-1 pr-8">
        {/* 标题和描述 */}
        <div className="space-y-2" id="intro">
          <h1 className="text-3xl font-bold tracking-tight">{title}</h1>
          {description && <p className="text-lg text-muted-foreground">{description}</p>}
        </div>
        
        {/* 何时使用 */}
        {whenToUse && (
          <div className="space-y-2 mt-8" id="when-to-use">
            <h2 className="text-xl font-semibold tracking-tight">何时使用</h2>
            <p className="text-muted-foreground">{whenToUse}</p>
          </div>
        )}
        
        <Separator className="my-8" />
        
        {/* 代码示例 */}
        <div className="space-y-8" id="examples">
          <h2 className="text-xl font-semibold tracking-tight">代码演示</h2>
          <div className="space-y-6">
            {isSingleExampleMode ? (
              // 单个示例模式
              <div className="border border-[#EAECF0] rounded-lg overflow-hidden">
                <div className="py-2.5 px-4 border-b border-[#EAECF0] bg-muted/5 flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <h3 className="font-medium text-sm">{title}</h3>
                    {description && (
                      <span className="text-xs text-muted-foreground">{description}</span>
                    )}
                  </div>
                  <div className="flex items-center space-x-1">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-7 w-7"
                      onClick={() => toggleCodeVisibility('single-example')}
                    >
                      <Code className="h-3.5 w-3.5" />
                    </Button>
                  </div>
                </div>
                <div className="p-6">
                  <ComponentPreview code={code!} scope={scope!} />
                </div>
                {showCode['single-example'] && (
                  <div className="border-t border-[#EAECF0] bg-muted/5">
                    <pre className="p-4 text-sm overflow-x-auto">
                      <code>{code}</code>
                    </pre>
                  </div>
                )}
              </div>
            ) : (
              // 多个示例模式
              examples.map((example) => (
              <div 
                key={example.id} 
                id={example.id}
                className="border border-[#EAECF0] rounded-lg overflow-hidden"
              >
                <div className="py-2.5 px-4 border-b border-[#EAECF0] bg-muted/5 flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <h3 className="font-medium text-sm">{example.title}</h3>
                    {example.description && (
                      <span className="text-xs text-muted-foreground">{example.description}</span>
                    )}
                  </div>
                  <div className="flex items-center space-x-1">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-7 w-7 rounded-full"
                      onClick={() => toggleCodeVisibility(example.id)}
                    >
                      <Code className="h-3.5 w-3.5" />
                      <span className="sr-only">查看代码</span>
                    </Button>
                    <Link 
                      href={`/preview/${example.id}`} 
                      target="_blank" 
                      passHref
                    >
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-7 w-7 rounded-full"
                        asChild
                      >
                        <span>
                          <ExternalLink className="h-3.5 w-3.5" />
                          <span className="sr-only">新窗口预览</span>
                        </span>
                      </Button>
                    </Link>
                  </div>
                </div>

                {/* 组件预览 */}
                <div className={cn(
                  "flex items-center justify-center min-h-[200px] bg-background border border-border rounded-md",
                  example.id.includes("breadcrumb") ? "p-4" :
                  example.id.includes("data-table") ? "p-4" :
                  example.id.includes("card") ? "p-6" :
                  example.id.includes("chart") ? "p-4" : "p-8"  // 图表和卡片组件使用适中的内边距
                )}>
                  <ComponentPreview
                    code={example.code}
                    scope={example.scope}
                    className={cn(
                      "w-full",
                      example.id.includes("breadcrumb") || example.id.includes("data-table") ? "max-w-full" :
                      example.id.includes("card") ? "max-w-md" :
                      example.id.includes("chart") ? "max-w-2xl" : ""  // 图表组件使用更大的最大宽度
                    )}
                  />
                </div>

                {/* 代码展示区域 - antd 风格 */}
                {showCode[example.id] && (
                  <div className="border-t border-[#EAECF0]">
                    <Tabs defaultValue="main" className="w-full">
                      <div className="flex items-center justify-between px-4 py-1.5 bg-muted/5">
                        <TabsList className="bg-transparent">
                          <TabsTrigger value="main" className="text-xs h-6">
                            主文件
                          </TabsTrigger>
                          {example.extraFiles?.map((file, idx) => (
                            <TabsTrigger key={idx} value={`file-${idx}`} className="text-xs h-6">
                              {file.name}
                            </TabsTrigger>
                          ))}
                        </TabsList>
                        <div className="flex items-center gap-1">
                          <Button 
                            variant="ghost" 
                            size="sm"
                            className="h-6 w-6"
                            onClick={() => copyToClipboard(example.code)}
                          >
                            <Copy className="h-3 w-3" />
                            <span className="sr-only">复制代码</span>
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="sm"
                            className="h-6 w-6"
                            onClick={() => toggleCodeVisibility(example.id)}
                          >
                            <X className="h-3 w-3" />
                            <span className="sr-only">收起代码</span>
                          </Button>
                        </div>
                      </div>

                      <TabsContent value="main" className="mt-0">
                        <pre className="bg-slate-950 text-slate-50 p-4 overflow-auto text-sm rounded-b-lg max-h-[400px]">
                          <code>{example.code}</code>
                        </pre>
                      </TabsContent>

                      {example.extraFiles?.map((file, idx) => (
                        <TabsContent key={idx} value={`file-${idx}`} className="mt-0">
                          <pre className="bg-slate-950 text-slate-50 p-4 overflow-auto text-sm rounded-b-lg max-h-[400px]">
                            <code>{file.code}</code>
                          </pre>
                        </TabsContent>
                      ))}
                    </Tabs>
                  </div>
                )}
              </div>
            ))
            )}
          </div>
        </div>
        
        <Separator className="my-8" />
        
        {/* API文档 */}
        {apiDocs && (
          <div className="space-y-4" id="api">
            <h2 className="text-xl font-semibold tracking-tight">API</h2>
            <div className="pt-2">
              {apiDocs}
            </div>
          </div>
        )}
      </div>
      
      {/* 右侧导航栏 */}
      <div className="hidden xl:block w-[280px] shrink-0">
        <div className="fixed mt-5">
          <div className="border border-[#EAECF0] rounded-lg p-5 w-[280px]">
            <h3 className="text-sm font-medium mb-3">目录导航</h3>
            <ScrollArea className="max-h-[calc(100vh-140px)]">
              <nav className="space-y-2">
                {navItems.map((item) => (
                  <a
                    key={item.id}
                    href={`#${item.id}`}
                    onClick={(e) => {
                      e.preventDefault();
                      handleNavClick(item.id);
                    }}
                    className={cn(
                      "block py-1.5 px-3 text-sm rounded-md transition-colors",
                      item.isSubItem ? "pl-6" : "font-medium",
                      activeSection === item.id 
                        ? "bg-muted text-foreground" 
                        : "text-muted-foreground hover:text-foreground hover:bg-muted/50"
                    )}
                  >
                    {item.title}
                  </a>
                ))}
              </nav>
            </ScrollArea>
          </div>
        </div>
      </div>
    </div>
  );

  // 多文件代码查看对话框
  const codeDialog = (
    <Dialog open={codeDialogOpen} onOpenChange={setCodeDialogOpen}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle>{currentTitle} - 代码示例</DialogTitle>
        </DialogHeader>
        <div className="mt-4">
          <Tabs defaultValue="main">
            <TabsList>
              <TabsTrigger value="main">主文件</TabsTrigger>
              {currentFiles.map((file, idx) => (
                <TabsTrigger key={idx} value={`file-${idx}`}>{file.name}</TabsTrigger>
              ))}
            </TabsList>
            <TabsContent value="main" className="mt-2">
              <div className="bg-slate-950 text-slate-50 rounded-md p-4 overflow-auto">
                <pre className="text-sm font-mono">{currentCode}</pre>
              </div>
            </TabsContent>
            {currentFiles.map((file, idx) => (
              <TabsContent key={idx} value={`file-${idx}`} className="mt-2">
                <div className="bg-slate-950 text-slate-50 rounded-md p-4 overflow-auto">
                  <pre className="text-sm font-mono">{file.code}</pre>
                </div>
              </TabsContent>
            ))}
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  );

  return (
    <>
      <MainLayout pageTitle={title}>
        {content}
      </MainLayout>
      {codeDialog}
    </>
  );
}

// 剪贴板复制功能
async function copyToClipboard(text: string) {
  try {
    await navigator.clipboard.writeText(text);
  } catch (err) {
    console.error("复制失败:", err);
  }
} 
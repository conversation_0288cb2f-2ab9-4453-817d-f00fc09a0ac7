"use client"

import React from "react"
import { ComponentPreviewContainer } from "@/components/component-detail/preview-container"
import { Tag, TagGroup, ColorTag, TagInput } from "@/components/common-custom/tag"
import { allExamples } from "./examples"

// ============================================================================
// API文档组件
// ============================================================================

function TagApiDocs() {
  return (
    <div className="space-y-6">
      {/* Tag 组件API */}
      <div>
        <h3 className="font-medium text-lg mb-2">Tag</h3>
        <p className="text-muted-foreground mb-4">基础标签组件，用于标记和分类</p>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">label</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">标签文本</td>
              </tr>
              <tr>
                <td className="p-2 border">variant</td>
                <td className="p-2 border">"default" | "secondary" | "outline" | "destructive"</td>
                <td className="p-2 border">"secondary"</td>
                <td className="p-2 border">标签变体</td>
              </tr>
              <tr>
                <td className="p-2 border">size</td>
                <td className="p-2 border">"sm" | "md" | "lg"</td>
                <td className="p-2 border">"md"</td>
                <td className="p-2 border">标签大小</td>
              </tr>
              <tr>
                <td className="p-2 border">closable</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">false</td>
                <td className="p-2 border">是否可关闭</td>
              </tr>
              <tr>
                <td className="p-2 border">selected</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">false</td>
                <td className="p-2 border">是否选中</td>
              </tr>
              <tr>
                <td className="p-2 border">disabled</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">false</td>
                <td className="p-2 border">是否禁用</td>
              </tr>
              <tr>
                <td className="p-2 border">icon</td>
                <td className="p-2 border">LucideIcon | ReactNode</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">标签图标</td>
              </tr>
              <tr>
                <td className="p-2 border">onClick</td>
                <td className="p-2 border">() =&gt; void</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">点击回调</td>
              </tr>
              <tr>
                <td className="p-2 border">onClose</td>
                <td className="p-2 border">() =&gt; void</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">关闭回调</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* TagGroup 组件API */}
      <div>
        <h3 className="font-medium text-lg mb-2">TagGroup</h3>
        <p className="text-muted-foreground mb-4">标签组组件，用于管理多个标签</p>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">tags</td>
                <td className="p-2 border">TagProps[] | string[]</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">标签列表</td>
              </tr>
              <tr>
                <td className="p-2 border">maxVisible</td>
                <td className="p-2 border">number</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">最大显示数量</td>
              </tr>
              <tr>
                <td className="p-2 border">moreTagsDisplay</td>
                <td className="p-2 border">"count" | "popover"</td>
                <td className="p-2 border">"count"</td>
                <td className="p-2 border">更多标签显示方式</td>
              </tr>
              <tr>
                <td className="p-2 border">closable</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">false</td>
                <td className="p-2 border">是否可关闭标签</td>
              </tr>
              <tr>
                <td className="p-2 border">onClose</td>
                <td className="p-2 border">(tag: string | TagProps, index: number) =&gt; void</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">关闭标签回调</td>
              </tr>
              <tr>
                <td className="p-2 border">onClick</td>
                <td className="p-2 border">(tag: string | TagProps, index: number) =&gt; void</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">点击标签回调</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* TagInput 组件API */}
      <div>
        <h3 className="font-medium text-lg mb-2">TagInput</h3>
        <p className="text-muted-foreground mb-4">标签输入框组件，支持动态添加和编辑标签</p>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">value</td>
                <td className="p-2 border">string[]</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">标签值列表</td>
              </tr>
              <tr>
                <td className="p-2 border">onChange</td>
                <td className="p-2 border">(value: string[]) =&gt; void</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">值变化回调</td>
              </tr>
              <tr>
                <td className="p-2 border">placeholder</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">"输入标签..."</td>
                <td className="p-2 border">输入框占位符</td>
              </tr>
              <tr>
                <td className="p-2 border">maxTags</td>
                <td className="p-2 border">number</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">最大标签数量</td>
              </tr>
              <tr>
                <td className="p-2 border">validate</td>
                <td className="p-2 border">(value: string) =&gt; boolean | string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">验证函数</td>
              </tr>
              <tr>
                <td className="p-2 border">allowDuplicates</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">false</td>
                <td className="p-2 border">是否允许重复</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* 使用指南 */}
      <div>
        <h3 className="font-medium text-lg mb-2">使用指南</h3>
        <div className="space-y-4 text-sm">
          <div>
            <h4 className="font-medium mb-2">基本用法</h4>
            <p className="text-muted-foreground mb-2">
              最简单的标签使用方式：
            </p>
            <pre className="bg-muted p-3 rounded-md overflow-x-auto">
              <code>{`<Tag label="标签文本" />
<Tag label="可关闭" closable onClose={() => handleClose()} />
<Tag label="可点击" onClick={() => handleClick()} />`}</code>
            </pre>
          </div>
          
          <div>
            <h4 className="font-medium mb-2">标签组</h4>
            <p className="text-muted-foreground mb-2">
              管理多个标签：
            </p>
            <pre className="bg-muted p-3 rounded-md overflow-x-auto">
              <code>{`<TagGroup
  tags={["标签1", "标签2", "标签3"]}
  closable
  onClose={(tag, index) => handleTagClose(tag, index)}
  maxVisible={5}
/>`}</code>
            </pre>
          </div>
          
          <div>
            <h4 className="font-medium mb-2">标签输入</h4>
            <p className="text-muted-foreground mb-2">
              动态添加和编辑标签：
            </p>
            <pre className="bg-muted p-3 rounded-md overflow-x-auto">
              <code>{`<TagInput
  value={tags}
  onChange={setTags}
  placeholder="输入标签..."
  maxTags={10}
  validate={(value) => value.length >= 2}
/>`}</code>
            </pre>
          </div>
        </div>
      </div>
    </div>
  )
}

// ============================================================================
// 主预览组件
// ============================================================================

export default function TagExamplePage() {
  return (
    <ComponentPreviewContainer
      title="标签 Tag"
      description="用于标记和分类的标签组件集合，支持基础标签、标签组、颜色标签和标签输入框"
      whenToUse="当需要对内容进行分类标记时使用；适用于文章标签、用户标签、状态标识、筛选条件等场景；支持多种样式和交互模式"
      examples={allExamples}
      apiDocs={<TagApiDocs />}
    />
  )
}

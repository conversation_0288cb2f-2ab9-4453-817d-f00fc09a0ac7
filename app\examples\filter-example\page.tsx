"use client"

import React from "react"
import { ComponentPreviewContainer } from "@/components/component-detail/preview-container"
import { Filter, FilterBadge, FilterItemRenderer } from "@/components/common-custom/filter"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { CalendarIcon, X, Filter as FilterIcon, ChevronDown } from "lucide-react"
import { Card } from "@/components/ui/card"

// 基础筛选器示例代码
const basicFilterCode = `
import React, { useState } from "react"
import { Filter } from "@/components/common-custom/filter"
import { FilterItem } from "@/types/filter"

function BasicFilterExample() {
  // 筛选条件配置
  const filterItems: FilterItem[] = [
    {
      name: "status",
      label: "状态",
      type: "select",
      options: [
        { label: "全部", value: "all" },
        { label: "活跃", value: "active" },
        { label: "非活跃", value: "inactive" },
        { label: "已归档", value: "archived" }
      ],
      defaultValue: "all"
    },
    {
      name: "category",
      label: "分类",
      type: "select",
      options: [
        { label: "全部", value: "all" },
        { label: "电子产品", value: "electronics" },
        { label: "服装", value: "clothing" },
        { label: "家居", value: "home" },
        { label: "食品", value: "food" }
      ],
      defaultValue: "all"
    },
    {
      name: "dateRange",
      label: "日期范围",
      type: "date-range",
      defaultValue: { from: undefined, to: undefined }
    }
  ]
  
  // 筛选值状态
  const [filterValues, setFilterValues] = useState({
    status: "all",
    category: "all",
    dateRange: { from: undefined, to: undefined }
  })
  
  const handleFilterChange = (values) => {
    setFilterValues(values)
    console.log("筛选条件变更:", values)
  }
  
  const handleFilterSubmit = (values) => {
    console.log("提交筛选:", values)
    // 执行筛选逻辑
  }
  
  const handleFilterClear = () => {
    console.log("清空筛选")
    // 可以在这里执行额外的清空逻辑
  }
  
  return (
    <div className="space-y-4">
      <Filter
        filters={filterItems}
        values={filterValues}
        onChange={handleFilterChange}
        onSubmit={handleFilterSubmit}
        onClear={handleFilterClear}
        popover={true}
        popoverTitle="筛选条件"
      />
      
      {/* 显示当前筛选值 */}
      <div className="p-4 border rounded-md">
        <h4 className="font-medium mb-2">当前筛选条件</h4>
        <pre className="text-sm bg-muted p-2 rounded">
          {JSON.stringify(filterValues, null, 2)}
        </pre>
      </div>
    </div>
  )
}

render(<BasicFilterExample />)
`

// 内联筛选器示例代码
const inlineFilterCode = `
import React, { useState } from "react"
import { Filter } from "@/components/common-custom/filter"
import { FilterItem } from "@/types/filter"
import { Card } from "@/components/ui/card"

function InlineFilterExample() {
  // 筛选条件配置
  const filterItems: FilterItem[] = [
    {
      name: "status",
      label: "状态",
      type: "select",
      options: [
        { label: "全部", value: "all" },
        { label: "活跃", value: "active" },
        { label: "非活跃", value: "inactive" }
      ],
      defaultValue: "all"
    },
    {
      name: "price",
      label: "价格",
      type: "select",
      options: [
        { label: "全部", value: "all" },
        { label: "低于100", value: "lt100" },
        { label: "100-500", value: "100-500" },
        { label: "高于500", value: "gt500" }
      ],
      defaultValue: "all"
    },
    {
      name: "keyword",
      label: "关键词",
      type: "text",
      placeholder: "搜索关键词",
      defaultValue: ""
    }
  ]
  
  // 筛选值状态
  const [filterValues, setFilterValues] = useState({
    status: "all",
    price: "all",
    keyword: ""
  })
  
  const handleFilterChange = (values) => {
    setFilterValues(values)
    console.log("筛选条件变更:", values)
  }
  
  return (
    <Card className="p-4">
      <Filter
        filters={filterItems}
        values={filterValues}
        onChange={handleFilterChange}
        popover={false}
        className="flex flex-wrap gap-4"
      />
      
      {/* 显示当前筛选值 */}
      <div className="mt-4 p-4 border rounded-md">
        <h4 className="font-medium mb-2">当前筛选条件</h4>
        <pre className="text-sm bg-muted p-2 rounded">
          {JSON.stringify(filterValues, null, 2)}
        </pre>
      </div>
    </Card>
  )
}

render(<InlineFilterExample />)
`

// 自定义筛选器示例代码
const customFilterCode = `
import React, { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { X, Filter as FilterIcon, ChevronDown } from "lucide-react"
import { Card } from "@/components/ui/card"

function CustomFilterExample() {
  // 筛选条件状态
  const [filters, setFilters] = useState({
    status: "",
    category: [],
    price: { min: "", max: "" },
    search: "",
  })

  // 状态选项
  const statusOptions = [
    { label: "全部", value: "" },
    { label: "活跃", value: "active" },
    { label: "非活跃", value: "inactive" },
    { label: "已归档", value: "archived" },
  ]

  // 分类选项
  const categoryOptions = [
    { label: "电子产品", value: "electronics" },
    { label: "服装", value: "clothing" },
    { label: "家居", value: "home" },
    { label: "食品", value: "food" },
    { label: "玩具", value: "toys" },
  ]

  // 处理筛选条件变更
  const handleFilterChange = (filterKey, value) => {
    setFilters((prev) => ({
      ...prev,
      [filterKey]: value,
    }))
    console.log(\`Filter \${filterKey} changed to: \`, value)
  }

  // 处理重置筛选条件
  const handleResetFilters = () => {
    setFilters({
      status: "",
      category: [],
      price: { min: "", max: "" },
      search: "",
    })
    console.log("Filters reset")
  }

  // 渲染活跃筛选器徽章
  const renderActiveBadges = () => {
    const badges = []

    if (filters.status) {
      const statusOption = statusOptions.find(opt => opt.value === filters.status)
      badges.push(
        <Badge key="status" variant="secondary" className="gap-1">
          状态: {statusOption?.label || filters.status}
          <X className="w-3 h-3 cursor-pointer" onClick={() => handleFilterChange("status", "")} />
        </Badge>
      )
    }

    if (filters.price.min || filters.price.max) {
      badges.push(
        <Badge key="price" variant="secondary" className="gap-1">
          价格: {filters.price.min || '0'} - {filters.price.max || '∞'}
          <X className="w-3 h-3 cursor-pointer" onClick={() => handleFilterChange("price", { min: "", max: "" })} />
        </Badge>
      )
    }

    if (filters.search) {
      badges.push(
        <Badge key="search" variant="secondary" className="gap-1">
          搜索: {filters.search}
          <X className="w-3 h-3 cursor-pointer" onClick={() => handleFilterChange("search", "")} />
        </Badge>
      )
    }

    return badges
  }

  return (
    <Card className="p-4">
      <div className="flex flex-col gap-6">
        {/* 搜索栏 */}
        <div className="flex gap-2">
          <div className="flex-1">
            <Input 
              placeholder="搜索..." 
              value={filters.search}
              onChange={(e) => handleFilterChange("search", e.target.value)}
            />
          </div>
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="gap-1">
                <FilterIcon className="h-4 w-4" />
                筛选
                <ChevronDown className="h-4 w-4" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80 p-4" align="end">
              <div className="grid gap-4">
                <div className="space-y-2">
                  <Label htmlFor="status">状态</Label>
                  <Select
                    value={filters.status}
                    onValueChange={(value) => handleFilterChange("status", value)}
                  >
                    <SelectTrigger id="status">
                      <SelectValue placeholder="选择状态" />
                    </SelectTrigger>
                    <SelectContent>
                      {statusOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label>价格范围</Label>
                  <div className="flex items-center gap-2">
                    <Input
                      type="number"
                      placeholder="最小"
                      value={filters.price.min}
                      onChange={(e) => 
                        handleFilterChange("price", { ...filters.price, min: e.target.value })
                      }
                    />
                    <span>-</span>
                    <Input
                      type="number"
                      placeholder="最大"
                      value={filters.price.max}
                      onChange={(e) => 
                        handleFilterChange("price", { ...filters.price, max: e.target.value })
                      }
                    />
                  </div>
                </div>
                
                <Button onClick={handleResetFilters} variant="outline" className="mt-2">
                  重置筛选
                </Button>
              </div>
            </PopoverContent>
          </Popover>
        </div>
        
        {/* 活跃筛选器 */}
        {renderActiveBadges().length > 0 && (
          <div className="flex flex-wrap gap-2">
            {renderActiveBadges()}
            <Badge 
              variant="outline" 
              className="cursor-pointer" 
              onClick={handleResetFilters}
            >
              清除全部
            </Badge>
          </div>
        )}
      </div>
      
      {/* 筛选结果示例 */}
      <div className="mt-4 p-4 border rounded-md">
        <h4 className="font-medium mb-2">当前筛选条件</h4>
        <pre className="text-sm bg-muted p-2 rounded">
          {JSON.stringify(filters, null, 2)}
        </pre>
      </div>
    </Card>
  )
}

render(<CustomFilterExample />)
`

// API文档组件
function FilterApiDocs() {
  return (
    <div className="space-y-8">
      <div>
        <h3 className="font-medium text-lg mb-2">Filter 组件</h3>
        <p className="text-muted-foreground mb-4">通用筛选器组件，支持多种筛选条件类型。</p>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">filters</td>
                <td className="p-2 border">FilterItem[]</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">筛选条件配置</td>
              </tr>
              <tr>
                <td className="p-2 border">values</td>
                <td className="p-2 border">Record&lt;string, any&gt;</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">筛选值</td>
              </tr>
              <tr>
                <td className="p-2 border">onChange</td>
                <td className="p-2 border">(values: Record&lt;string, any&gt;) =&gt; void</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">值变化回调</td>
              </tr>
              <tr>
                <td className="p-2 border">onClear</td>
                <td className="p-2 border">() =&gt; void</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">清空筛选回调</td>
              </tr>
              <tr>
                <td className="p-2 border">onSubmit</td>
                <td className="p-2 border">(values: Record&lt;string, any&gt;) =&gt; void</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">提交筛选回调</td>
              </tr>
              <tr>
                <td className="p-2 border">popover</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">true</td>
                <td className="p-2 border">是否显示弹出式筛选</td>
              </tr>
              <tr>
                <td className="p-2 border">popoverTitle</td>
                <td className="p-2 border">ReactNode</td>
                <td className="p-2 border">"筛选条件"</td>
                <td className="p-2 border">弹出框标题</td>
              </tr>
              <tr>
                <td className="p-2 border">className</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">自定义类名</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div>
        <h3 className="font-medium text-lg mb-2">FilterItem 类型</h3>
        <p className="text-muted-foreground mb-4">筛选条件项配置。</p>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">name</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">筛选字段名</td>
              </tr>
              <tr>
                <td className="p-2 border">label</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">筛选项标签</td>
              </tr>
              <tr>
                <td className="p-2 border">type</td>
                <td className="p-2 border">"select" | "multi-select" | "date-range" | "text" | "number" | "boolean" | "custom"</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">筛选项类型</td>
              </tr>
              <tr>
                <td className="p-2 border">options</td>
                <td className="p-2 border">FilterOption[]</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">选项列表(用于select类型)</td>
              </tr>
              <tr>
                <td className="p-2 border">defaultValue</td>
                <td className="p-2 border">any</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">默认值</td>
              </tr>
              <tr>
                <td className="p-2 border">placeholder</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">提示文本</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

export default function FilterPreview() {
  const examples = [
    {
      id: "basic-filter",
      title: "基础筛选器",
      description: "使用Filter组件创建的基本筛选器，支持多种筛选条件类型",
      code: basicFilterCode,
      scope: { 
        React, 
        useState: React.useState,
        Filter
      },
    },
    {
      id: "inline-filter",
      title: "内联筛选器",
      description: "直接显示在页面上的内联筛选器，无需弹出框",
      code: inlineFilterCode,
      scope: { 
        React, 
        useState: React.useState,
        Filter,
        Card
      },
    },
    {
      id: "custom-filter",
      title: "自定义筛选器",
      description: "完全自定义的筛选器实现，适用于更复杂的场景",
      code: customFilterCode,
      scope: { 
        React, 
        useState: React.useState,
        Button,
        Input,
        Label,
        Select,
        SelectContent,
        SelectItem,
        SelectTrigger,
        SelectValue,
        Badge,
        Popover,
        PopoverContent,
        PopoverTrigger,
        X,
        FilterIcon,
        ChevronDown,
        Card
      },
    },
  ];

  return (
    <ComponentPreviewContainer
      title="筛选器 Filter"
      description="用于数据筛选和过滤的组件，支持多种筛选条件类型，如下拉选择、日期范围、文本输入等。"
      whenToUse="当需要对数据集合进行筛选时使用，适用于列表页、表格、搜索结果等场景。筛选器可以帮助用户快速缩小数据范围，找到所需信息。"
      examples={examples}
      apiDocs={<FilterApiDocs />}
    />
  );
} 
 
 
 
 

 
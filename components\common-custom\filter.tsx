"use client"

import { useState, useCallback } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { CalendarIcon, X, Filter as FilterIcon } from "lucide-react"
import { format } from "date-fns"
import { zhCN } from "date-fns/locale"
import { FilterProps, FilterItem, DateRange } from "@/types/filter"

/**
 * 过滤器徽章组件
 */
export function FilterBadge({ 
  name, 
  value, 
  label, 
  onRemove 
}: { 
  name: string, 
  value: any, 
  label?: string, 
  onRemove: () => void 
}) {
  return (
    <Badge variant="secondary" className="gap-1">
      {label || name}: {typeof value === 'object' ? JSON.stringify(value) : value}
      <X className="w-3 h-3 cursor-pointer" onClick={onRemove} />
    </Badge>
  )
}

/**
 * 过滤器项渲染
 */
export function FilterItemRenderer<T>({ 
  item, 
  value, 
  onChange 
}: { 
  item: FilterItem<T>, 
  value: T, 
  onChange: (value: T) => void 
}) {
  // 如果有自定义渲染函数，则使用它
  if (item.render) {
    return item.render({ value, onChange, item })
  }

  // 根据类型渲染不同的控件
  switch (item.type) {
    case "select":
      return (
        <div className={item.className}>
          <Label htmlFor={item.name}>{item.label}</Label>
          <Select 
            value={value as string} 
            onValueChange={(val) => onChange(val as T)}
          >
            <SelectTrigger>
              <SelectValue placeholder={item.placeholder || `选择${item.label}`} />
            </SelectTrigger>
            <SelectContent>
              {item.options?.map((option) => (
                <SelectItem 
                  key={option.value} 
                  value={option.value || "none"} // 防止空字符串作为值，因为RadixUI的Select组件要求value不能为空字符串
                >
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )
    
    case "multi-select":
      // 多选实现可以根据需求扩展
      return null
      
    case "text":
      return (
        <div className={item.className}>
          <Label>{item.label}</Label>
          <Input
            placeholder={item.placeholder}
            value={value as string}
            onChange={(e) => onChange(e.target.value as unknown as T)}
          />
        </div>
      )
      
    case "number":
      return (
        <div className={item.className}>
          <Label>{item.label}</Label>
          <Input
            type="number"
            placeholder={item.placeholder}
            value={value as number}
            onChange={(e) => onChange(Number(e.target.value) as unknown as T)}
          />
        </div>
      )
      
    case "boolean":
      // 布尔值实现可以根据需求扩展
      return null
      
    case "date-range":
      return (
        <div className={item.className}>
          <Label>{item.label}</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="w-full justify-start text-left font-normal">
                <CalendarIcon className="mr-2 h-4 w-4" />
                {(value as DateRange).from ? (
                  (value as DateRange).to ? (
                    <>
                      {format((value as DateRange).from!, "yyyy-MM-dd", { locale: zhCN })} -{" "}
                      {format((value as DateRange).to!, "yyyy-MM-dd", { locale: zhCN })}
                    </>
                  ) : (
                    format((value as DateRange).from!, "yyyy-MM-dd", { locale: zhCN })
                  )
                ) : (
                  item.placeholder || "选择日期范围"
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="range"
                selected={value as DateRange}
                onSelect={(range) => onChange(range as T || {} as T)}
                numberOfMonths={2}
              />
            </PopoverContent>
          </Popover>
        </div>
      )
      
    default:
      return null
  }
}

/**
 * 过滤器组件
 */
export function Filter<TFilters extends Record<string, any> = Record<string, any>>({
  filters,
  values,
  onChange,
  onClear,
  onSubmit,
  activeFiltersCount,
  popover = true,
  popoverTitle = "筛选条件",
  className,
  renderBadge,
}: FilterProps<TFilters>) {
  const [isOpen, setIsOpen] = useState(false)
  
  // 计算活跃过滤器数量
  const getActiveFiltersCount = useCallback(() => {
    if (activeFiltersCount !== undefined) {
      return activeFiltersCount
    }
    
    let count = 0
    for (const filter of filters) {
      const value = values[filter.name]
      if (filter.type === "select" && value !== "all" && value !== undefined && value !== "") {
        count++
      } else if (filter.type === "text" && value) {
        count++
      } else if (filter.type === "date-range" && (value?.from || value?.to)) {
        count++
      } else if (value !== undefined && value !== "" && value !== filter.defaultValue) {
        count++
      }
    }
    return count
  }, [activeFiltersCount, filters, values])
  
  // 处理单个过滤器变化
  const handleFilterChange = useCallback((name: string, value: any) => {
    onChange({ ...values, [name]: value })
  }, [onChange, values])
  
  // 清空所有过滤器
  const handleClearFilters = useCallback(() => {
    const defaultValues = filters.reduce((acc, filter) => {
      acc[filter.name] = filter.defaultValue !== undefined ? filter.defaultValue : ""
      return acc
    }, {} as Record<string, any>)
    
    onChange(defaultValues as TFilters)
    
    if (onClear) {
      onClear()
    }
  }, [filters, onChange, onClear])
  
  // 处理提交
  const handleSubmit = useCallback(() => {
    if (onSubmit) {
      onSubmit(values)
    }
    
    if (popover) {
      setIsOpen(false)
    }
  }, [onSubmit, values, popover])
  
  // 渲染过滤器徽章
  const renderFilterBadge = useCallback((name: string, value: any) => {
    const filter = filters.find(f => f.name === name)
    
    if (!filter) return null
    
    // 如果值为空或默认值，则不显示徽章
    if (value === undefined || value === "" || value === filter.defaultValue) {
      return null
    }
    
    // 对于日期范围，如果没有选择日期则不显示徽章
    if (filter.type === "date-range" && !value.from && !value.to) {
      return null
    }
    
    // 对于选择类型，如果选择了"全部"则不显示徽章
    if (filter.type === "select" && value === "all") {
      return null
    }
    
    // 获取显示标签
    let displayValue = value
    if (filter.type === "select") {
      const option = filter.options?.find(opt => opt.value === value)
      displayValue = option?.label || value
    } else if (filter.type === "date-range") {
      displayValue = value.from && value.to 
        ? `${format(value.from, "yyyy-MM-dd")} - ${format(value.to, "yyyy-MM-dd")}`
        : value.from 
          ? `从 ${format(value.from, "yyyy-MM-dd")}`
          : `至 ${format(value.to, "yyyy-MM-dd")}`
    }
    
    const onRemove = () => handleFilterChange(name, filter.defaultValue !== undefined ? filter.defaultValue : "")
    
    if (renderBadge) {
      return renderBadge(name, value, onRemove)
    }
    
    return (
      <FilterBadge 
        key={name} 
        name={filter.label} 
        value={displayValue} 
        onRemove={onRemove} 
      />
    )
  }, [filters, handleFilterChange, renderBadge])
  
  // 渲染过滤器表单
  const renderFilterForm = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="font-medium">{popoverTitle}</h4>
        <Button variant="ghost" size="sm" onClick={handleClearFilters}>
          清空
        </Button>
      </div>

      <div className="space-y-3">
        {filters.map((filter) => (
          <FilterItemRenderer
            key={filter.name}
            item={filter}
            value={values[filter.name]}
            onChange={(value) => handleFilterChange(filter.name, value)}
          />
        ))}
      </div>

      <div className="flex gap-2">
        <Button onClick={handleSubmit} className="flex-1">
          应用筛选
        </Button>
        <Button variant="outline" onClick={handleClearFilters}>
          重置
        </Button>
      </div>
    </div>
  )
  
  // 渲染过滤器徽章列表
  const renderActiveBadges = () => {
    const activeBadges = Object.entries(values)
      .map(([name, value]) => renderFilterBadge(name, value))
      .filter(Boolean)
    
    if (activeBadges.length === 0) {
      return null
    }
    
    return (
      <div className="flex flex-wrap gap-2">
        {activeBadges}
      </div>
    )
  }
  
  // 如果使用弹出式过滤器
  if (popover) {
    return (
      <div className={className}>
        <div className="flex items-center gap-2">
          <Popover open={isOpen} onOpenChange={setIsOpen}>
            <PopoverTrigger asChild>
              <Button variant="outline" className="gap-2">
                <FilterIcon className="w-4 h-4" />
                筛选
                {getActiveFiltersCount() > 0 && (
                  <Badge variant="secondary" className="ml-1">
                    {getActiveFiltersCount()}
                  </Badge>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80" align="start">
              {renderFilterForm()}
            </PopoverContent>
          </Popover>
          
          {renderActiveBadges()}
        </div>
        
        <div className="text-sm text-muted-foreground mt-2">
          当前筛选条件: {getActiveFiltersCount()} 个活跃筛选器
        </div>
      </div>
    )
  }
  
  // 内联过滤器
  return (
    <div className={className}>
      {renderFilterForm()}
      {renderActiveBadges()}
      
      <div className="text-sm text-muted-foreground mt-2">
        当前筛选条件: {getActiveFiltersCount()} 个活跃筛选器
      </div>
    </div>
  )
} 
"use client"

import React from "react"
import { ComponentPreviewContainer } from "@/components/component-detail/preview-container"
import { 
  FileUpload, 
  FileUploadArea, 
  FileListItem, 
  FileUploadStats,
  formatFileSize,
  getFileIcon
} from "@/components/common-custom/file-upload"
import { But<PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Upload, File, X, Download, Eye, RefreshCw, CloudUpload, Share2, Trash2 } from "lucide-react"

// 基础文件上传示例代码
const basicFileUploadCode = `
import React, { useState } from "react"
import { FileUpload } from "@/components/common-custom/file-upload"
import { UploadFile } from "@/types/file-upload"

function BasicFileUploadExample() {
  const [files, setFiles] = useState<UploadFile[]>([])

  const handleFilesAdded = (newFiles: File[]) => {
    // 转换为上传文件格式
    const uploadFiles = newFiles.map((file) => ({
      id: Math.random().toString(36).substring(2, 9),
      name: file.name,
      size: file.size,
      type: file.type,
      progress: 0,
      status: "uploading" as const,
      file,
    }))
    
    setFiles((prev) => [...prev, ...uploadFiles])
    
    // 模拟上传进度
    uploadFiles.forEach((file) => {
      const interval = setInterval(() => {
        setFiles((prevFiles) => {
          const fileIndex = prevFiles.findIndex((f) => f.id === file.id)
          if (fileIndex === -1) return prevFiles
          
          const updatedFiles = [...prevFiles]
          const currentFile = updatedFiles[fileIndex]
          
          if (currentFile.progress < 100) {
            updatedFiles[fileIndex] = {
              ...currentFile,
              progress: Math.min(currentFile.progress + 20, 100),
            }
          } else {
            updatedFiles[fileIndex] = {
              ...currentFile,
              status: "success",
              url: URL.createObjectURL(currentFile.file!),
            }
            clearInterval(interval)
          }
          
          return updatedFiles
        })
      }, 500)
    })
  }
  
  const handleRetry = (file: UploadFile) => {
    setFiles((prev) =>
      prev.map((f) =>
        f.id === file.id
          ? { ...f, progress: 0, status: "uploading" }
          : f
      )
    )
    
    // 模拟重新上传
    const interval = setInterval(() => {
      setFiles((prevFiles) => {
        const fileIndex = prevFiles.findIndex((f) => f.id === file.id)
        if (fileIndex === -1) return prevFiles
        
        const updatedFiles = [...prevFiles]
        const currentFile = updatedFiles[fileIndex]
        
        if (currentFile.progress < 100) {
          updatedFiles[fileIndex] = {
            ...currentFile,
            progress: Math.min(currentFile.progress + 20, 100),
          }
        } else {
          updatedFiles[fileIndex] = {
            ...currentFile,
            status: "success",
            url: URL.createObjectURL(currentFile.file!),
          }
          clearInterval(interval)
        }
        
        return updatedFiles
      })
    }, 500)
  }
  
  const handleRemove = (file: UploadFile) => {
    setFiles((prev) => prev.filter((f) => f.id !== file.id))
  }
  
  const handleView = (file: UploadFile) => {
    if (file.url) {
      window.open(file.url, "_blank")
    }
  }
  
  const handleDownload = (file: UploadFile) => {
    if (file.url) {
      const a = document.createElement("a")
      a.href = file.url
      a.download = file.name
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
    }
  }
  
  return (
    <FileUpload
      files={files}
      uploadAreaProps={{
        multiple: true,
        accept: "image/*,application/pdf",
        hint: "支持 JPG、PNG、PDF 等格式，单个文件不超过 10MB",
      }}
      onFilesAdded={handleFilesAdded}
      onRetry={handleRetry}
      onRemove={handleRemove}
      onView={handleView}
      onDownload={handleDownload}
    />
  )
}

render(<BasicFileUploadExample />)
`

// 自定义上传区域示例代码
const customUploadAreaCode = `
import React, { useState } from "react"
import { FileUploadArea } from "@/components/common-custom/file-upload"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { CloudUpload } from "lucide-react"

function CustomUploadAreaExample() {
  const [files, setFiles] = useState<File[]>([])
  
  const handleFilesAdded = (newFiles: File[]) => {
    setFiles((prev) => [...prev, ...newFiles])
    console.log("已选择文件:", newFiles)
  }
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>自定义上传区域</CardTitle>
      </CardHeader>
      <CardContent>
        <FileUploadArea
          multiple={true}
          accept="image/*,application/pdf"
          prompt="点击或拖拽文件到此处"
          dragActivePrompt="松开鼠标上传文件"
          hint="最大支持5MB，允许JPG、PNG、PDF格式"
          icon={CloudUpload}
          buttonText="浏览文件"
          onChange={handleFilesAdded}
          className="bg-slate-50"
        />
        
        {files.length > 0 && (
          <div className="mt-4">
            <h4 className="font-medium mb-2">已选择 {files.length} 个文件:</h4>
            <ul className="list-disc pl-5">
              {files.map((file, index) => (
                <li key={index} className="text-sm">
                  {file.name} ({(file.size / 1024).toFixed(1)} KB)
                </li>
              ))}
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

render(<CustomUploadAreaExample />)
`

// 文件列表项示例代码
const fileListItemCode = `
import React, { useState } from "react"
import { FileListItem } from "@/components/common-custom/file-upload"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Share2, Trash2 } from "lucide-react"

function FileListItemExample() {
  const [files, setFiles] = useState([
    {
      id: "1",
      name: "产品设计方案.pdf",
      size: 2500000,
      type: "application/pdf",
      progress: 100,
      status: "success",
      url: "#"
    },
    {
      id: "2",
      name: "项目截图.png",
      size: 1200000,
      type: "image/png",
      progress: 100,
      status: "success",
      url: "#"
    },
    {
      id: "3",
      name: "错误日志.txt",
      size: 350000,
      type: "text/plain",
      progress: 0,
      status: "error",
      error: "上传失败，请重试"
    },
    {
      id: "4",
      name: "视频演示.mp4",
      size: 15000000,
      type: "video/mp4",
      progress: 45,
      status: "uploading"
    }
  ])
  
  const handleRemove = (file) => {
    setFiles((prev) => prev.filter((f) => f.id !== file.id))
  }
  
  const handleRetry = (file) => {
    setFiles((prev) =>
      prev.map((f) =>
        f.id === file.id
          ? { ...f, status: "uploading", progress: 0 }
          : f
      )
    )
    
    // 模拟上传进度
    const interval = setInterval(() => {
      setFiles((prevFiles) => {
        const fileIndex = prevFiles.findIndex((f) => f.id === file.id)
        if (fileIndex === -1) return prevFiles
        
        const updatedFiles = [...prevFiles]
        const currentFile = updatedFiles[fileIndex]
        
        if (currentFile.progress < 100) {
          updatedFiles[fileIndex] = {
            ...currentFile,
            progress: Math.min(currentFile.progress + 20, 100),
          }
        } else {
          updatedFiles[fileIndex] = {
            ...currentFile,
            status: "success",
            url: "#",
          }
          clearInterval(interval)
        }
        
        return updatedFiles
      })
    }, 500)
  }
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>文件列表</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {files.map((file) => (
          <FileListItem
            key={file.id}
            file={file}
            onRemove={handleRemove}
            onRetry={handleRetry}
            renderActions={(file) => (
              <>
                {file.status === "success" && (
                  <Button variant="ghost" size="sm">
                    <Share2 className="h-4 w-4" />
                  </Button>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleRemove(file)}
                >
                  <Trash2 className="h-4 w-4 text-red-500" />
                </Button>
              </>
            )}
          />
        ))}
      </CardContent>
    </Card>
  )
}

render(<FileListItemExample />)
`

// 上传统计示例代码
const uploadStatsCode = `
import React from "react"
import { FileUploadStats } from "@/components/common-custom/file-upload"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

function FileUploadStatsExample() {
  const files = [
    {
      id: "1",
      name: "产品设计方案.pdf",
      size: 2500000,
      type: "application/pdf",
      progress: 100,
      status: "success",
      url: "#"
    },
    {
      id: "2",
      name: "项目截图.png",
      size: 1200000,
      type: "image/png",
      progress: 100,
      status: "success",
      url: "#"
    },
    {
      id: "3",
      name: "错误日志.txt",
      size: 350000,
      type: "text/plain",
      progress: 0,
      status: "error",
      error: "上传失败，请重试"
    },
    {
      id: "4",
      name: "视频演示.mp4",
      size: 15000000,
      type: "video/mp4",
      progress: 45,
      status: "uploading"
    }
  ]
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>上传统计</CardTitle>
      </CardHeader>
      <CardContent>
        <FileUploadStats files={files} />
      </CardContent>
    </Card>
  )
}

render(<FileUploadStatsExample />)
`

// API文档组件
function FileUploadApiDocs() {
  return (
    <div className="space-y-8">
      <div>
        <h3 className="font-medium text-lg mb-2">FileUpload 组件</h3>
        <p className="text-muted-foreground mb-4">完整的文件上传组件，包含上传区域、文件列表和统计功能。</p>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">files</td>
                <td className="p-2 border">UploadFile[]</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">上传文件列表</td>
              </tr>
              <tr>
                <td className="p-2 border">uploadAreaProps</td>
                <td className="p-2 border">FileUploadAreaProps</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">上传区域属性</td>
              </tr>
              <tr>
                <td className="p-2 border">onFilesAdded</td>
                <td className="p-2 border">(files: File[]) =&gt; void</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">添加文件回调</td>
              </tr>
              <tr>
                <td className="p-2 border">onRetry</td>
                <td className="p-2 border">(file: UploadFile) =&gt; void</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">重试上传回调</td>
              </tr>
              <tr>
                <td className="p-2 border">onRemove</td>
                <td className="p-2 border">(file: UploadFile) =&gt; void</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">删除文件回调</td>
              </tr>
              <tr>
                <td className="p-2 border">onView</td>
                <td className="p-2 border">(file: UploadFile) =&gt; void</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">查看文件回调</td>
              </tr>
              <tr>
                <td className="p-2 border">onDownload</td>
                <td className="p-2 border">(file: UploadFile) =&gt; void</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">下载文件回调</td>
              </tr>
              <tr>
                <td className="p-2 border">autoUpload</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">true</td>
                <td className="p-2 border">是否自动上传</td>
              </tr>
              <tr>
                <td className="p-2 border">showFileList</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">true</td>
                <td className="p-2 border">是否显示文件列表</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div>
        <h3 className="font-medium text-lg mb-2">FileUploadArea 组件</h3>
        <p className="text-muted-foreground mb-4">文件上传区域组件，支持拖拽上传和点击选择文件。</p>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">multiple</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">false</td>
                <td className="p-2 border">是否支持多文件上传</td>
              </tr>
              <tr>
                <td className="p-2 border">maxSize</td>
                <td className="p-2 border">number</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">最大文件大小(字节)</td>
              </tr>
              <tr>
                <td className="p-2 border">accept</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">接受的文件类型</td>
              </tr>
              <tr>
                <td className="p-2 border">prompt</td>
                <td className="p-2 border">ReactNode</td>
                <td className="p-2 border">"拖拽文件到此处上传"</td>
                <td className="p-2 border">上传区提示文本</td>
              </tr>
              <tr>
                <td className="p-2 border">hint</td>
                <td className="p-2 border">ReactNode</td>
                <td className="p-2 border">支持格式提示</td>
                <td className="p-2 border">支持提示信息</td>
              </tr>
              <tr>
                <td className="p-2 border">disabled</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">false</td>
                <td className="p-2 border">是否禁用</td>
              </tr>
              <tr>
                <td className="p-2 border">buttonText</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">"选择文件"</td>
                <td className="p-2 border">按钮文本</td>
              </tr>
              <tr>
                <td className="p-2 border">onChange</td>
                <td className="p-2 border">(files: File[]) =&gt; void</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">文件变更回调</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

export default function FileUploadPreview() {
  const examples = [
    {
      id: "basic-file-upload",
      title: "基础文件上传",
      description: "完整的文件上传组件，包含拖拽上传、文件列表和进度显示",
      code: basicFileUploadCode,
      scope: { 
        React, 
        useState: React.useState,
        FileUpload,
      },
    },
    {
      id: "custom-upload-area",
      title: "自定义上传区域",
      description: "可自定义的文件上传区域组件",
      code: customUploadAreaCode,
      scope: {
        React,
        useState: React.useState,
        FileUploadArea,
        Card,
        CardContent,
        CardHeader,
        CardTitle,
        CloudUpload,
      },
    },
    {
      id: "file-list-item",
      title: "文件列表项",
      description: "显示文件上传状态和操作的列表项组件",
      code: fileListItemCode,
      scope: {
        React,
        useState: React.useState,
        FileListItem,
        Card,
        CardContent,
        CardHeader,
        CardTitle,
        Button,
        Share2,
        Trash2,
      },
    },
    {
      id: "upload-stats",
      title: "上传统计",
      description: "显示文件上传统计信息的组件",
      code: uploadStatsCode,
      scope: { 
        React,
        FileUploadStats,
        Card,
        CardContent,
        CardHeader,
        CardTitle,
      },
    },
  ];

  return (
    <ComponentPreviewContainer
      title="文件上传 FileUpload"
      description="用于文件上传的组件，支持拖拽上传、多文件上传、上传进度显示等功能。"
      whenToUse="当需要上传一个或多个文件时使用，适用于表单提交、资源管理、图片上传等场景。"
      examples={examples}
      apiDocs={<FileUploadApiDocs />}
    />
  );
} 
 
 
 
 

 
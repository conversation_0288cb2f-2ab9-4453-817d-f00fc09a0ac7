"use client"

import React, { useState, useEffect } from "react"
import { 
  Users,
  Loader2, 
  PlusCircle,
  X,
  Shield
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { getUserRoleDetailRequest } from "@/services/api/userRoleRequestApi"
import { toast } from "sonner"

interface UserRole {
  roleCode: string;
  roleName: string;
  createTime: string;
}

interface UserRoleDetail {
  userId: number;
  userName: string;
  teamCode: string;
  teamName: string;
  roles: UserRole[];
}

interface UserRolesProps {
  userId: number;
  isEditing?: boolean;
  onAddRole?: () => void;
  onRemoveRole?: (roleCode: string) => void;
}

export function UserRoles({ userId, isEditing = false, onAddRole, onRemoveRole }: UserRolesProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [userRoleDetail, setUserRoleDetail] = useState<UserRoleDetail | null>(null)
  const [error, setError] = useState<string | null>(null)

  // 获取用户角色详情
  useEffect(() => {
    const loadRoleData = async () => {
      if (!userId) return

      setIsLoading(true)
      setError(null)
      try {
        // 这里假设默认团队代码为 "default"
        // 在实际应用中可能需要从上下文或者其他地方获取当前团队代码
        const teamCode = "default"
        const roleDetail = await getUserRoleDetailRequest(userId, teamCode)
        if (roleDetail) {
          setUserRoleDetail(roleDetail)
        } else {
          setError("未能获取用户角色信息")
        }
      } catch (err) {
        console.error("获取用户角色失败", err)
        setError("获取用户角色失败")
      } finally {
        setIsLoading(false)
      }
    }

    loadRoleData()
  }, [userId])

  // 格式化日期
  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString("zh-CN")
    } catch (error) {
      return dateString
    }
  }

  // 处理删除角色
  const handleRemoveRole = (roleCode: string) => {
    if (onRemoveRole) {
      onRemoveRole(roleCode)
    }
  }

  return (
    <Card className="border-border/40">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg flex items-center gap-2">
              <Shield className="h-5 w-5 text-primary" />
              用户角色
            </CardTitle>
            <CardDescription>用户所拥有的系统角色</CardDescription>
          </div>
          {isEditing && onAddRole && (
            <Button 
              variant="outline" 
              size="sm" 
              onClick={onAddRole}
              className="gap-1"
            >
              <PlusCircle className="h-4 w-4" />
              添加角色
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        ) : error ? (
          <div className="text-center py-6 text-muted-foreground">
            <p>{error}</p>
          </div>
        ) : !userRoleDetail || !userRoleDetail.roles || userRoleDetail.roles.length === 0 ? (
          <div className="text-center py-6">
            <Users className="h-12 w-12 mx-auto text-muted-foreground mb-3" />
            <h3 className="font-medium mb-1">无角色分配</h3>
            <p className="text-sm text-muted-foreground">
              该用户目前未分配任何角色
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {userRoleDetail.teamName && (
              <div className="mb-4">
                <p className="text-sm font-medium mb-1">所属团队</p>
                <Badge variant="outline" className="bg-muted/50">
                  {userRoleDetail.teamName}
                </Badge>
              </div>
            )}
            
            <Separator />
            
            <div className="pt-2">
              <p className="text-sm font-medium mb-3">角色列表</p>
              <div className="space-y-3">
                {userRoleDetail.roles.map((role) => (
                  <div 
                    key={role.roleCode} 
                    className="flex items-center justify-between p-3 rounded-md border border-border/50 bg-card"
                  >
                    <div>
                      <p className="font-medium">{role.roleName}</p>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge variant="outline" className="bg-primary/10 hover:bg-primary/20">
                          {role.roleCode}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          分配时间: {formatDate(role.createTime)}
                        </span>
                      </div>
                    </div>
                    {isEditing && onRemoveRole && (
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        onClick={() => handleRemoveRole(role.roleCode)}
                        className="text-muted-foreground hover:text-destructive"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default UserRoles; 
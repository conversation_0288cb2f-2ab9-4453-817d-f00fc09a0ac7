/**
 * 提示框组件示例代码集合
 * 
 * 按照新规范管理示例代码，避免在页面文件中硬编码
 */

import React from "react"
import { Tooltip, IconTooltip, Popover, HoverCard } from "@/components/common-custom/tooltip"
import { Button } from "@/components/ui/button"
import { HelpCircle, Info, Settings, User, Heart, Calendar, MapPin } from "lucide-react"

// ============================================================================
// 基础提示框示例
// ============================================================================

export const basicTooltipExample = {
  id: "basic-tooltip",
  title: "基础提示框",
  description: "展示不同位置和触发方式的基础提示框",
  code: `
import React from "react";
import { Tooltip } from "@/components/common-custom/tooltip";
import { Button } from "@/components/ui/button";

function BasicTooltipExample() {
  return (
    <div className="space-y-6">
      <div>
        <h4 className="text-sm font-medium mb-2">不同位置</h4>
        <div className="flex flex-wrap items-center gap-4">
          <Tooltip content="顶部提示" side="top">
            <Button variant="outline">顶部</Button>
          </Tooltip>
          
          <Tooltip content="右侧提示" side="right">
            <Button variant="outline">右侧</Button>
          </Tooltip>
          
          <Tooltip content="底部提示" side="bottom">
            <Button variant="outline">底部</Button>
          </Tooltip>
          
          <Tooltip content="左侧提示" side="left">
            <Button variant="outline">左侧</Button>
          </Tooltip>
        </div>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">不同对齐方式</h4>
        <div className="flex flex-wrap items-center gap-4">
          <Tooltip content="开始对齐" side="bottom" align="start">
            <Button variant="outline">开始对齐</Button>
          </Tooltip>
          
          <Tooltip content="居中对齐" side="bottom" align="center">
            <Button variant="outline">居中对齐</Button>
          </Tooltip>
          
          <Tooltip content="结束对齐" side="bottom" align="end">
            <Button variant="outline">结束对齐</Button>
          </Tooltip>
        </div>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">不同触发方式</h4>
        <div className="flex flex-wrap items-center gap-4">
          <Tooltip content="悬停触发" trigger="hover">
            <Button variant="outline">悬停触发</Button>
          </Tooltip>
          
          <Tooltip content="点击触发" trigger="click">
            <Button variant="outline">点击触发</Button>
          </Tooltip>
          
          <Tooltip content="聚焦触发" trigger="focus">
            <Button variant="outline">聚焦触发</Button>
          </Tooltip>
        </div>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">延迟设置</h4>
        <div className="flex flex-wrap items-center gap-4">
          <Tooltip content="无延迟" delayDuration={0}>
            <Button variant="outline">无延迟</Button>
          </Tooltip>
          
          <Tooltip content="短延迟" delayDuration={100}>
            <Button variant="outline">短延迟</Button>
          </Tooltip>
          
          <Tooltip content="长延迟" delayDuration={1000}>
            <Button variant="outline">长延迟</Button>
          </Tooltip>
        </div>
      </div>
    </div>
  );
}

render(<BasicTooltipExample />);
  `,
  scope: { Tooltip, Button, React },
}

// ============================================================================
// 图标提示框示例
// ============================================================================

export const iconTooltipExample = {
  id: "icon-tooltip",
  title: "图标提示框",
  description: "展示不同样式的图标提示框",
  code: `
import React from "react";
import { IconTooltip } from "@/components/common-custom/tooltip";
import { HelpCircle, Info, Settings } from "lucide-react";

function IconTooltipExample() {
  return (
    <div className="space-y-6">
      <div>
        <h4 className="text-sm font-medium mb-2">不同图标</h4>
        <div className="flex flex-wrap items-center gap-4">
          <div className="flex items-center gap-2">
            <span>帮助信息</span>
            <IconTooltip 
              content="这是一个帮助提示，提供额外的说明信息"
              icon={HelpCircle}
            />
          </div>
          
          <div className="flex items-center gap-2">
            <span>详细信息</span>
            <IconTooltip 
              content="点击查看更多详细信息"
              icon={Info}
              iconColor="primary"
            />
          </div>
          
          <div className="flex items-center gap-2">
            <span>设置选项</span>
            <IconTooltip 
              content="配置相关设置"
              icon={Settings}
              iconColor="secondary"
            />
          </div>
        </div>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">不同尺寸</h4>
        <div className="flex flex-wrap items-center gap-4">
          <div className="flex items-center gap-2">
            <span>超小</span>
            <IconTooltip 
              content="超小尺寸图标"
              iconSize="xs"
            />
          </div>
          
          <div className="flex items-center gap-2">
            <span>小</span>
            <IconTooltip 
              content="小尺寸图标"
              iconSize="sm"
            />
          </div>
          
          <div className="flex items-center gap-2">
            <span>中</span>
            <IconTooltip 
              content="中等尺寸图标"
              iconSize="md"
            />
          </div>
          
          <div className="flex items-center gap-2">
            <span>大</span>
            <IconTooltip 
              content="大尺寸图标"
              iconSize="lg"
            />
          </div>
        </div>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">不同颜色</h4>
        <div className="flex flex-wrap items-center gap-4">
          <IconTooltip 
            content="默认颜色"
            iconColor="default"
          />
          <IconTooltip 
            content="静音颜色"
            iconColor="muted"
          />
          <IconTooltip 
            content="主要颜色"
            iconColor="primary"
          />
          <IconTooltip 
            content="次要颜色"
            iconColor="secondary"
          />
          <IconTooltip 
            content="危险颜色"
            iconColor="destructive"
          />
        </div>
      </div>
    </div>
  );
}

render(<IconTooltipExample />);
  `,
  scope: { IconTooltip, HelpCircle, Info, Settings, React },
}

// ============================================================================
// 悬浮卡片示例
// ============================================================================

export const hoverCardExample = {
  id: "hover-card",
  title: "悬浮卡片",
  description: "展示悬浮卡片的功能和配置",
  code: `
import React from "react";
import { HoverCard } from "@/components/common-custom/tooltip";
import { Button } from "@/components/ui/button";
import { User, Calendar, MapPin } from "lucide-react";

function HoverCardExample() {
  return (
    <div className="space-y-6">
      <div>
        <h4 className="text-sm font-medium mb-2">用户信息卡片</h4>
        <div className="flex flex-wrap items-center gap-4">
          <HoverCard
            content={
              <div className="p-4 space-y-3">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                    <User className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h4 className="font-medium">张三</h4>
                    <p className="text-sm text-muted-foreground">前端开发工程师</p>
                  </div>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-4 h-4 text-muted-foreground" />
                    <span>2020年1月入职</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <MapPin className="w-4 h-4 text-muted-foreground" />
                    <span>北京市海淀区</span>
                  </div>
                </div>
              </div>
            }
          >
            <Button variant="link">@zhangsan</Button>
          </HoverCard>

          <HoverCard
            content={
              <div className="p-4">
                <h4 className="font-medium mb-2">项目信息</h4>
                <p className="text-sm text-muted-foreground mb-2">
                  这是一个基于React的前端项目，使用了最新的技术栈。
                </p>
                <div className="flex gap-2">
                  <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">React</span>
                  <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded">TypeScript</span>
                  <span className="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded">Tailwind</span>
                </div>
              </div>
            }
            openDelay={500}
            closeDelay={200}
          >
            <Button variant="outline">项目详情</Button>
          </HoverCard>
        </div>
      </div>
    </div>
  );
}

render(<HoverCardExample />);
  `,
  scope: { HoverCard, Button, User, Calendar, MapPin, React },
}

// ============================================================================
// 统一导出所有示例
// ============================================================================

export const allExamples = [
  basicTooltipExample,
  iconTooltipExample,
  hoverCardExample,
]

// ============================================================================
// 按类别导出示例
// ============================================================================

export const basicExamples = [basicTooltipExample, iconTooltipExample]
export const advancedExamples = [hoverCardExample]

// ============================================================================
// 示例元数据
// ============================================================================

export const exampleMetadata = {
  total: allExamples.length,
  categories: {
    basic: basicExamples.length,
    advanced: advancedExamples.length,
  },
  tags: ["tooltip", "popover", "hint", "help", "overlay"],
  lastUpdated: "2024-01-01",
}

"use client"

import { useState, useCallback } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import {
  Upload,
  File,
  ImageIcon,
  X,
  Check,
  AlertCircle,
  Download,
  Eye,
  Trash2,
  FileText,
  FileImage,
  FileVideo,
  Music,
  RefreshCw,
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  FileUploadProps, 
  FileUploadAreaProps, 
  FileListItemProps, 
  FileUploadStatsProps,
  UploadFile
} from "@/types/file-upload"

/**
 * 文件大小格式化
 */
export function formatFileSize(bytes: number) {
  if (bytes === 0) return "0 Bytes"
  const k = 1024
  const sizes = ["Bytes", "KB", "MB", "GB"]
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
}

/**
 * 获取文件图标
 */
export function getFileIcon(type: string) {
  if (type.startsWith("image/")) return <FileImage className="h-4 w-4" />
  if (type.startsWith("video/")) return <FileVideo className="h-4 w-4" />
  if (type.startsWith("audio/")) return <Music className="h-4 w-4" />
  if (type.includes("pdf")) return <FileText className="h-4 w-4" />
  return <File className="h-4 w-4" />
}

/**
 * 文件上传区域组件
 */
export function FileUploadArea({
  multiple = false,
  maxSize,
  accept,
  prompt = "拖拽文件到此处上传",
  dragActivePrompt = "释放鼠标上传文件",
  hint = "支持 JPG、PNG、PDF、DOC 等格式，单个文件不超过 10MB",
  disabled = false,
  icon: Icon = Upload,
  customIcon,
  buttonText = "选择文件",
  onChange,
  className,
}: FileUploadAreaProps) {
  const [dragActive, setDragActive] = useState(false)

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (disabled) return
    
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }, [disabled])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (disabled) return
    
    setDragActive(false)
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      if (onChange) {
        onChange(Array.from(e.dataTransfer.files))
      }
    }
  }, [disabled, onChange])

  const handleFileChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (disabled || !e.target.files || e.target.files.length === 0) return
    
    if (onChange) {
      onChange(Array.from(e.target.files))
    }
    // 重置input值，确保相同文件可以再次选择
    e.target.value = ""
  }, [disabled, onChange])

  const fileInputId = "file-upload-" + Math.random().toString(36).substr(2, 9)

  return (
    <div
      className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
        disabled ? "opacity-60 cursor-not-allowed" : "cursor-pointer"
      } ${
        dragActive ? "border-primary bg-primary/5" : "border-muted-foreground/25 hover:border-primary/50"
      } ${className || ""}`}
      onDragEnter={handleDrag}
      onDragLeave={handleDrag}
      onDragOver={handleDrag}
      onDrop={handleDrop}
      onClick={() => {
        if (!disabled) {
          document.getElementById(fileInputId)?.click()
        }
      }}
    >
      {customIcon || <Icon className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />}
      <h4 className="text-lg font-medium mb-2">{dragActive ? dragActivePrompt : prompt}</h4>
      {hint && <p className="text-muted-foreground mb-4">{hint}</p>}
      <Button variant="outline" className="cursor-pointer" disabled={disabled}>
        {buttonText}
      </Button>
      <input
        id={fileInputId}
        type="file"
        multiple={multiple}
        accept={accept}
        className="hidden"
        onChange={handleFileChange}
        disabled={disabled}
      />
    </div>
  )
}

/**
 * 文件列表项组件
 */
export function FileListItem({
  file,
  onView,
  onDownload,
  onRetry,
  onRemove,
  className,
  renderActions,
}: FileListItemProps) {
  return (
    <div className={`flex items-center gap-4 p-4 border rounded-lg ${className || ""}`}>
      <div className="flex items-center gap-3 flex-1">
        {getFileIcon(file.type)}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <span className="font-medium truncate">{file.name}</span>
            <Badge
              variant={
                file.status === "success"
                  ? "default"
                  : file.status === "error"
                    ? "destructive"
                    : "secondary"
              }
              className={
                file.status === "success"
                  ? "bg-green-100 text-green-800 hover:bg-green-100"
                  : file.status === "error"
                    ? ""
                    : "bg-blue-100 text-blue-800 hover:bg-blue-100"
              }
            >
              {file.status === "success" ? "已完成" : file.status === "error" ? "失败" : "上传中"}
            </Badge>
          </div>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <span>{formatFileSize(file.size)}</span>
            {file.status === "uploading" && <span>• {Math.round(file.progress)}%</span>}
          </div>
          {file.status === "uploading" && <Progress value={file.progress} className="h-2 mt-2" />}
        </div>
      </div>

      <div className="flex items-center gap-2">
        {renderActions ? (
          renderActions(file)
        ) : (
          <>
            {file.status === "success" && onView && (
              <Button variant="ghost" size="sm" onClick={() => onView(file)}>
                <Eye className="h-4 w-4" />
              </Button>
            )}
            {file.status === "success" && onDownload && (
              <Button variant="ghost" size="sm" onClick={() => onDownload(file)}>
                <Download className="h-4 w-4" />
              </Button>
            )}
            {file.status === "error" && onRetry && (
              <Button variant="ghost" size="sm" onClick={() => onRetry(file)}>
                <RefreshCw className="h-4 w-4" />
              </Button>
            )}
            {onRemove && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onRemove(file)}
                className="text-destructive hover:text-destructive"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </>
        )}
      </div>
    </div>
  )
}

/**
 * 文件上传统计组件
 */
export function FileUploadStats({ files, className }: FileUploadStatsProps) {
  const successCount = files.filter((f) => f.status === "success").length
  const errorCount = files.filter((f) => f.status === "error").length
  const totalSize = files.reduce((acc, file) => acc + file.size, 0)

  return (
    <div className={`grid grid-cols-1 md:grid-cols-4 gap-4 ${className || ""}`}>
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-2">
            <Upload className="h-4 w-4 text-blue-500" />
            <div>
              <div className="text-2xl font-bold">{files.length}</div>
              <div className="text-xs text-muted-foreground">总文件数</div>
            </div>
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-2">
            <Check className="h-4 w-4 text-green-500" />
            <div>
              <div className="text-2xl font-bold">{successCount}</div>
              <div className="text-xs text-muted-foreground">上传成功</div>
            </div>
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-2">
            <AlertCircle className="h-4 w-4 text-red-500" />
            <div>
              <div className="text-2xl font-bold">{errorCount}</div>
              <div className="text-xs text-muted-foreground">上传失败</div>
            </div>
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-2">
            <File className="h-4 w-4 text-purple-500" />
            <div>
              <div className="text-2xl font-bold">{formatFileSize(totalSize)}</div>
              <div className="text-xs text-muted-foreground">总大小</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

/**
 * 完整文件上传组件
 */
export function FileUpload({
  files,
  uploadAreaProps,
  onFilesAdded,
  onUpload,
  onRetry,
  onRemove,
  onView,
  onDownload,
  onClear,
  className,
  autoUpload = true,
  showFileList = true,
}: FileUploadProps) {
  const handleFilesAdded = (newFiles: File[]) => {
    if (onFilesAdded) {
      onFilesAdded(newFiles)
    }
    
    if (autoUpload && onUpload) {
      onUpload(newFiles)
    }
  }
  
  return (
    <div className={`space-y-6 ${className || ""}`}>
      <FileUploadArea 
        {...uploadAreaProps}
        onChange={handleFilesAdded}
      />
      
      {showFileList && files.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>文件上传进度</CardTitle>
              {onClear && (
                <Button variant="outline" size="sm" onClick={onClear}>
                  清空列表
                </Button>
              )}
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {files.map((file) => (
                <FileListItem
                  key={file.id}
                  file={file}
                  onView={onView}
                  onDownload={onDownload}
                  onRetry={onRetry}
                  onRemove={onRemove}
                />
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
} 
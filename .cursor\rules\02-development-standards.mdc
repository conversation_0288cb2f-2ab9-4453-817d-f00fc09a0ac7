---
description: 
globs: 
alwaysApply: true
---
# 开发规范

## 技术栈规范
- 使用 Next.js 15 App Router 架构，不使用 Pages Router
- 优先使用 Server Components，只在必要时使用 Client Components
- 使用 TypeScript 进行类型检查，确保代码质量
- 使用 pnpm 管理依赖包
- 使用 shadcn/ui 组件库

## 设计规范遵循
- 在实现新功能或修复bug前，必须先查阅并遵循`.doc`目录下的设计规范文档
- 严格遵守色彩系统规范，使用预定义的色彩变量，不使用硬编码颜色值
- 遵循排版系统规范，确保文本大小、间距和行高符合规范
- 布局必须按照布局网格规范实现，确保响应式设计的一致性
- 组件开发必须符合组件系统规范，确保视觉和交互的一致性
- 动画和过渡效果必须遵循交互设计规范，避免过度使用动画
- 表单实现必须遵循表单设计规范，确保用户体验的一致性
- 当设计规范与功能需求冲突时，应寻求团队讨论解决
- 在代码审查过程中，遵循设计规范是重要的评估标准之一

## 组件开发规范
1. 组件分组清晰：
   - 基础组件：`components/ui` 目录
   - 自定义组件：`components/custom` 目录
   - 布局组件：`components/layout` 目录
   - 导航组件：`components/navigation` 目录
   - 页面特定组件：`components/pages` 目录
   - 公共组件：`components/common` 目录

2. 新增 shadcn/ui 组件时使用命令: `pnpm dlx shadcn@latest add <component-name>`
3. 组件应具备响应式设计，确保在不同设备上有良好体验
4. 在实现新逻辑前，先检查是否已有组件可重用

## 数据处理规范
1. 所有 API 请求方法放在 `services/api` 目录下，命名规则为 `xxxRequestApi.ts`
2. 模拟数据放在 `services/mock` 目录下，命名规则为 `xxxMock.ts`
3. 所有 API 调用必须包含错误处理逻辑
4. 类型定义统一放在 `types` 目录下
5. 不允许在组件中硬编码数据，动态数据必须通过服务层获取

## 编码规范
1. 使用 TypeScript 编写所有代码
2. 编写详细的代码注释，提高可读性
3. 确保所有交互有适当的视觉反馈
4. 使用设计系统中定义的颜色变量，不使用硬编码颜色值
5. 导入 React 类型时使用显式导入，如: `import { ReactNode } from "react";`

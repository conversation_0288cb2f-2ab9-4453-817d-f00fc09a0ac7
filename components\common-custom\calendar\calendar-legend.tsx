"use client"

import React from "react"
import { cn } from "@/lib/utils"
import { CalendarLegendProps } from "./types"

/**
 * 日历图例组件
 * 
 * 用于显示日历事件的图例说明
 */
export function CalendarLegend({
  legends,
  position = "bottom",
  className,
  renderItem,
}: CalendarLegendProps) {
  
  if (!legends.length) return null
  
  // 获取图例颜色样式
  const getLegendColorClass = (legend: any) => {
    if (legend.color) {
      return {
        backgroundColor: legend.color,
      }
    }
    
    switch (legend.type) {
      case "primary":
        return "bg-primary"
      case "secondary":
        return "bg-secondary"
      case "success":
        return "bg-green-500"
      case "warning":
        return "bg-yellow-500"
      case "danger":
        return "bg-red-500"
      default:
        return "bg-blue-500"
    }
  }
  
  // 获取容器布局样式
  const getContainerClass = () => {
    switch (position) {
      case "top":
      case "bottom":
        return "flex flex-wrap items-center gap-4"
      case "left":
      case "right":
        return "flex flex-col space-y-2"
      default:
        return "flex flex-wrap items-center gap-4"
    }
  }
  
  return (
    <div className={cn(
      "calendar-legend",
      "p-3 border-t",
      {
        "border-t-0 border-b": position === "top",
        "border-l border-t-0": position === "right",
        "border-r border-t-0": position === "left",
      },
      getContainerClass(),
      className
    )}>
      {legends.map((legend) => {
        // 自定义渲染
        if (renderItem) {
          return (
            <div key={legend.id}>
              {renderItem(legend)}
            </div>
          )
        }
        
        // 默认渲染
        return (
          <div
            key={legend.id}
            className="flex items-center gap-2"
            title={legend.description}
          >
            <div
              className={cn(
                "w-3 h-3 rounded-sm",
                typeof getLegendColorClass(legend) === "string" 
                  ? getLegendColorClass(legend) 
                  : ""
              )}
              style={
                typeof getLegendColorClass(legend) === "object" 
                  ? getLegendColorClass(legend) 
                  : undefined
              }
            />
            <span className="text-sm text-muted-foreground">
              {legend.label}
            </span>
          </div>
        )
      })}
    </div>
  )
}

CalendarLegend.displayName = "CalendarLegend"

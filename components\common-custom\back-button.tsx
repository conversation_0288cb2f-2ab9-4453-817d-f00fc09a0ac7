"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

// ============================================================================
// 类型定义 - 单文件组件类型定义直接在组件文件内
// ============================================================================

/**
 * 返回按钮组件属性
 */
export interface BackButtonProps {
  /**
   * 跳转链接
   */
  href?: string

  /**
   * 自定义类名
   */
  className?: string

  /**
   * 按钮内容
   */
  children?: React.ReactNode

  /**
   * 是否显示"返回"文字
   * @default false
   */
  showText?: boolean

  /**
   * 按钮尺寸
   * @default "md"
   */
  size?: "sm" | "md" | "lg"

  /**
   * 按钮圆角样式
   * @default "default"
   */
  rounded?: "default" | "md" | "full"

  /**
   * 按钮变体样式
   * @default "default"
   */
  variant?: "default" | "ghost" | "primary" | "destructive" | "outline"

  /**
   * 点击事件处理函数
   */
  onClick?: React.MouseEventHandler<HTMLButtonElement>

  /**
   * 导航回调函数（用于自定义导航逻辑）
   */
  onNavigate?: (href: string, e: React.MouseEvent) => void

  /**
   * 是否禁用
   * @default false
   */
  disabled?: boolean

  /**
   * 按钮类型
   * @default "button"
   */
  type?: "button" | "submit" | "reset"
}

/**
 * 尺寸样式映射类型
 */
type SizeClassMap = {
  [K in NonNullable<BackButtonProps['size']>]: string
}

/**
 * 圆角样式映射类型
 */
type RoundedClassMap = {
  [K in NonNullable<BackButtonProps['rounded']>]: string
}

/**
 * 变体样式映射类型
 */
type VariantClassMap = {
  [K in NonNullable<BackButtonProps['variant']>]: string
}

// ============================================================================
// 组件实现
// ============================================================================

/**
 * 返回按钮组件（通用版本）
 *
 * 提供导航返回功能的按钮组件，支持多种显示模式、尺寸、圆角和变体样式
 * 这是通用版本，不依赖特定的路由库，通过回调函数处理导航
 *
 * @example
 * ```tsx
 * // 基础用法
 * <BackButton onNavigate={(href) => router.push(href)} href="/" />
 *
 * // 显示文字
 * <BackButton href="/" showText onNavigate={handleNavigate}>返回首页</BackButton>
 *
 * // 自定义样式
 * <BackButton
 *   href="/"
 *   size="lg"
 *   variant="primary"
 *   rounded="full"
 *   showText
 *   onNavigate={handleNavigate}
 * >
 *   返回主页
 * </BackButton>
 * ```
 */
export function BackButton({
  href,
  className,
  children,
  showText = false,
  size = "md",
  rounded = "default",
  variant = "default",
  onClick,
  onNavigate,
  disabled = false,
  type = "button",
  ...props
}: BackButtonProps) {
  // 样式映射配置
  const sizeClasses: SizeClassMap = {
    sm: "h-8 px-2 py-1 text-xs",
    md: "h-10 px-3 py-2 text-sm",
    lg: "h-12 px-4 py-2.5 text-base"
  }

  const iconSizes: SizeClassMap = {
    sm: "h-4 w-4",
    md: "h-5 w-5",
    lg: "h-6 w-6"
  }

  const roundedClasses: RoundedClassMap = {
    default: "rounded-md",
    md: "rounded-lg",
    full: "rounded-full"
  }

  const variantClasses: VariantClassMap = {
    default: "hover:bg-muted/70 hover:text-foreground",
    ghost: "hover:bg-transparent hover:text-foreground/80",
    primary: "bg-primary text-primary-foreground hover:bg-primary/90",
    destructive: "text-destructive hover:text-destructive/80",
    outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground"
  }

  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (onClick) {
      onClick(e);
    }
    
    if (href && onNavigate && !e.defaultPrevented) {
      onNavigate(href, e);
    }
  };

  return (
    <button
      type={type}
      className={cn(
        // 基础样式
        "inline-flex items-center gap-1 font-medium transition-colors",
        "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
        "disabled:pointer-events-none disabled:opacity-50",

        // 动态样式
        sizeClasses[size],
        roundedClasses[rounded],
        variantClasses[variant],

        // 自定义样式
        className
      )}
      onClick={handleClick}
      disabled={disabled}
      {...props}
    >
      {/* 返回图标 */}
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="16"
        height="16"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className={cn("shrink-0", iconSizes[size])}
        aria-hidden="true"
      >
        <path d="m15 18-6-6 6-6"/>
      </svg>

      {/* 文字内容 */}
      {showText && (
        <span className="truncate">
          {children || "返回"}
        </span>
      )}
    </button>
  )
}

// ============================================================================
// 类型导出
// ============================================================================

export type { BackButtonProps }

// ============================================================================
// 常量导出
// ============================================================================

/**
 * 支持的尺寸选项
 */
export const BACK_BUTTON_SIZES = ['sm', 'md', 'lg'] as const

/**
 * 支持的变体选项
 */
export const BACK_BUTTON_VARIANTS = ['default', 'ghost', 'primary', 'destructive', 'outline'] as const

/**
 * 支持的圆角选项
 */
export const BACK_BUTTON_ROUNDED = ['default', 'md', 'full'] as const

/**
 * 默认配置
 */
export const DEFAULT_BACK_BUTTON_CONFIG = {
  showText: false,
  size: 'md' as const,
  rounded: 'default' as const,
  variant: 'default' as const,
} satisfies Partial<BackButtonProps>

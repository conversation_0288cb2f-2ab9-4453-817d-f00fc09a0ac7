"use client"

import { <PERSON>, <PERSON><PERSON>ontent, CardFooter } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Quote as Quote<PERSON><PERSON>, <PERSON> } from "lucide-react"

/**
 * 推荐卡片组件
 * 用于展示客户推荐信息
 */
export function TestimonialCard({
  data
}: {
  data: {
    quote: string
    author: {
      name: string
      position?: string
      company?: string
      avatar?: string
    }
    rating?: number
  }
}) {
  return (
    <Card className="transition-all hover:shadow-md h-full flex flex-col">
      <CardContent className="pt-6 flex-grow">
        <div className="mb-4">
          <QuoteIcon className="h-8 w-8 text-muted-foreground/40" />
        </div>
        
        {data.rating !== undefined && (
          <div className="flex items-center mb-4">
            {[...Array(5)].map((_, i) => (
              <Star 
                key={i} 
                className={`h-4 w-4 ${
                  i < data.rating! 
                    ? "text-yellow-500 fill-yellow-500" 
                    : "text-muted-foreground/30"
                }`} 
              />
            ))}
          </div>
        )}
        
        <p className="text-muted-foreground leading-relaxed">{data.quote}</p>
      </CardContent>
      
      <CardFooter className="pt-0 pb-6 border-t mt-auto">
        <div className="flex items-center pt-4">
          <Avatar className="h-10 w-10 mr-3">
            {data.author.avatar && <AvatarImage src={data.author.avatar} alt={data.author.name} />}
            <AvatarFallback>
              {data.author.name
                .split(" ")
                .map(n => n[0])
                .join("")
                .toUpperCase()
                .substring(0, 2)}
            </AvatarFallback>
          </Avatar>
          
          <div>
            <p className="font-medium">{data.author.name}</p>
            {(data.author.position || data.author.company) && (
              <p className="text-sm text-muted-foreground">
                {data.author.position}
                {data.author.position && data.author.company && " · "}
                {data.author.company}
              </p>
            )}
          </div>
        </div>
      </CardFooter>
    </Card>
  )
} 
"use client"

import { ReactNode } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { cn } from "@/lib/utils"
import { InfoCardItem } from "@/types/card-templates"

/**
 * 信息卡片组件
 * 用于展示系统状态、活动等信息
 */
export function InfoCard({
  title,
  description,
  icon: Icon,
  statusItems,
  activities,
  className,
  renderContent,
  renderFooter
}: InfoCardItem) {
  return (
    <Card className={cn("transition-all hover:shadow-md", className)}>
      <CardHeader>
        <div className="flex items-center gap-2">
          {Icon && <Icon className="h-5 w-5 text-muted-foreground" />}
          <div>
            <CardTitle>{title}</CardTitle>
            {description && <CardDescription>{description}</CardDescription>}
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {renderContent ? (
          renderContent()
        ) : (
          <>
            {statusItems && statusItems.length > 0 && (
              <div className="space-y-3">
                {statusItems.map((item, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="text-sm text-muted-foreground">
                      {item.label}
                    </div>
                    <div className="font-medium text-sm">
                      {typeof item.value === 'number' ? `${item.value}%` : item.value}
                    </div>
                  </div>
                ))}
              </div>
            )}
            
            {activities && activities.length > 0 && (
              <div className="space-y-3">
                {activities.map((activity, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <Avatar className="h-7 w-7">
                      {activity.avatar && <AvatarImage src={activity.avatar} />}
                      {activity.avatarFallback && <AvatarFallback>{activity.avatarFallback}</AvatarFallback>}
                    </Avatar>
                    <div className="flex-1 space-y-1">
                      <p className="text-sm leading-none">
                        <span className="font-medium">{activity.user}</span>{" "}
                        {activity.action}{" "}
                        {activity.target && <span className="font-medium">{activity.target}</span>}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {activity.time}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </>
        )}
        
        {renderFooter && (
          <div className="mt-4">
            {renderFooter()}
          </div>
        )}
      </CardContent>
    </Card>
  )
} 
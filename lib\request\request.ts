import axios from 'axios';
import type { AxiosInstance, AxiosRequestConfig } from 'axios';
import { message, notification, loading, formatErrorMessage, generateRequestId } from './utils';
import { CancelToken, Cancel, isCancel } from './cancel';
import { ErrorShowType } from './types';
import type { 
  RequestConfig, 
  RequestOptionsInit, 
  RequestMethod, 
  RequestResponse, 
  ResponseError, 
  ResponseStructure,
  RequestInterceptor,
  ResponseInterceptor,
  Interceptors,
  RequestInstance,
  HttpMethod,
  AxiosResponse
} from './types';

// 全局请求和响应拦截器
const globalRequestInterceptors: RequestInterceptor[] = [];
const globalResponseInterceptors: ResponseInterceptor[] = [];

// 默认配置
const defaultConfig = {
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  },
  requestStatusConfig: {
    defaultShowLoading: false,
    defaultShowSuccessMessage: false,
    defaultShowErrorMessage: true
  }
} as const;

/**
 * 创建 axios 实例并附加基础拦截器
 */
function createAxiosInstance(config: RequestConfig): AxiosInstance {
  const axiosInstance = axios.create(config);

  // 添加请求拦截器
  axiosInstance.interceptors.request.use(
    (axiosConfig) => {
      return axiosConfig;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  // 添加响应拦截器
  axiosInstance.interceptors.response.use(
    (response) => {
      return response;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  return axiosInstance;
}

/**
 * 构建请求方法
 */
function createRequest(axiosInstance: AxiosInstance, config: RequestConfig): RequestInstance {
  /**
   * 基础请求函数，统一处理各种请求方式
   * @param url 请求URL
   * @param dataOrOptions 数据或选项
   * @param maybeOptions
   */
  const requestFunc = async <T = any>(
    url: string, 
    dataOrOptions?: any, 
    maybeOptions?: Omit<RequestOptionsInit, 'data'>
  ): Promise<AxiosResponse<any>> => {
    // 处理参数重载
    let options: RequestOptionsInit = {} as RequestOptionsInit;
    let data: any = undefined;

    // 如果第二个参数是对象且不是数组，且第三个参数未提供，则可能是options或data
    if (dataOrOptions && typeof dataOrOptions === 'object' && !Array.isArray(dataOrOptions) && !maybeOptions) {
      // 检查是否包含params、headers等配置字段（如果有，则认为是options）
      if ('params' in dataOrOptions || 'headers' in dataOrOptions || 'method' in dataOrOptions || 
          'timeout' in dataOrOptions || 'responseType' in dataOrOptions || 'withCredentials' in dataOrOptions ||
          'showLoading' in dataOrOptions || 'showSuccessMessage' in dataOrOptions || 'showErrorMessage' in dataOrOptions) {
        options = { ...dataOrOptions };
      } else {
        // 否则视为data，并使用空对象作为options
        data = dataOrOptions;
      }
    } else {
      // 第二个参数是data，第三个参数是options
      data = dataOrOptions;
      options = { ...(maybeOptions || {}) };
    }

    // 为GET、HEAD、DELETE、OPTIONS请求将data转为params
    if (options.method && ['get', 'head', 'delete', 'options'].includes(options.method.toLowerCase()) && data) {
      options.params = { ...options.params, ...data };
      data = undefined;
    } else if (data !== undefined) {
      // 对于其他请求，将data保存在options.data中
      options.data = data;
    }

    // 为POST、PUT、PATCH请求设置JSON内容类型
    if (options.method && ['post', 'put', 'patch'].includes(options.method.toLowerCase()) && 
        options.data && !options.headers?.['Content-Type']) {
      options.headers = {
        'Content-Type': 'application/json',
        ...options.headers
      };
    }

    // 生成请求ID，用于跟踪请求
    const requestId = generateRequestId();

    // 合并配置
    const mergedOptions = {
      ...config,
      ...options,
      url,
      headers: {
        ...config.headers || {},
        ...options.headers || {},
      },
    } as RequestOptionsInit;

    // 处理loading状态
    const shouldShowLoading = options.showLoading !== undefined 
      ? options.showLoading 
      : config.requestStatusConfig?.defaultShowLoading || false;

    if (shouldShowLoading) {
      loading.show(options.loadingText);
    }

    // 处理取消令牌
    if (options.cancelToken && options.cancelToken instanceof CancelToken) {
      options.cancelToken.throwIfRequested();
    }

    // 执行请求前拦截器
    const requestInterceptors = [
      ...(globalRequestInterceptors || []),
      ...(config.requestInterceptors || []),
      ...(options.requestInterceptors || []),
    ];
    let newOptions = { ...mergedOptions };

    // 执行请求拦截器链
    const processRequestInterceptors = async () => {
      for (const interceptor of requestInterceptors) {
        let handler;
        let errorHandler;
        
        if (typeof interceptor === 'function') {
          handler = interceptor;
        } else if (Array.isArray(interceptor) && interceptor.length > 0) {
          handler = interceptor[0];
          if (interceptor.length > 1) {
            errorHandler = interceptor[1];
          }
        }

        if (handler) {
          try {
            newOptions = await handler(newOptions);
          } catch (e) {
            if (errorHandler) {
              errorHandler(e);
            }
            throw e;
          }
        }
      }
      return newOptions;
    };

    try {
      // 处理请求拦截器
      newOptions = await processRequestInterceptors();
      
      // 如果有取消令牌，监听取消事件
      if (newOptions.cancelToken && newOptions.cancelToken instanceof CancelToken) {
        const cancelPromise = newOptions.cancelToken.promise;
        if (cancelPromise) {
          cancelPromise.then((reason) => {
            throw new Cancel(reason);
          });
        }
      }
      
      // 执行请求
      const response: AxiosResponse = await axiosInstance.request(newOptions);
      
      // 处理响应
      const { getResponse } = newOptions;
      const errorConfig = config.errorConfig || {};
      const { errorThrower } = errorConfig;

      // 错误抛出
      if (errorThrower && response.data) {
        try {
          errorThrower(response.data);
        } catch (e) {
          if (newOptions.skipErrorHandler) {
            throw e;
          }
          const errorHandler = errorConfig?.errorHandler;
          if (errorHandler) {
            errorHandler(e as ResponseError);
          }
          throw e;
        }
      }

      // 执行响应拦截器
      const responseInterceptors = [
        ...(globalResponseInterceptors || []),
        ...(config.responseInterceptors || []),
        ...(newOptions.responseInterceptors || []),
      ];
      let finalResponse = response;

      // 执行响应拦截器链
      for (const interceptor of responseInterceptors) {
        let handler;
        let errorHandler;
        
        if (typeof interceptor === 'function') {
          handler = interceptor;
        } else if (Array.isArray(interceptor) && interceptor.length > 0) {
          handler = interceptor[0];
          if (interceptor.length > 1) {
            errorHandler = interceptor[1];
          }
        }

        if (handler) {
          try {
            finalResponse = await handler(finalResponse);
          } catch (e) {
            if (errorHandler) {
              errorHandler(e);
            }
            throw e;
          }
        }
      }

      // 隐藏loading
      if (shouldShowLoading) {
        loading.hide();
      }

      // 显示成功消息
      const shouldShowSuccessMessage = options.showSuccessMessage !== undefined 
        ? options.showSuccessMessage 
        : config.requestStatusConfig?.defaultShowSuccessMessage || false;

      if (shouldShowSuccessMessage && options.successMessage) {
        message.success(options.successMessage);
      }

      // 返回响应
      if (getResponse) {
        return {
          data: finalResponse.data.data || finalResponse.data,
          response: finalResponse,
        } as any;
      }

      // 处理标准响应格式
      // 1. 如果符合标准格式且有data字段，返回data字段的内容
      // 2. 否则返回整个响应数据
      const responseData = finalResponse;
      if (responseData && typeof responseData === 'object') {
        // 判断是否为标准响应格式(包含code、msg和data)
        if ('code' in responseData && 'msg' in responseData && 'data' in responseData) {
          return responseData;
        }
        
        // 不是标准格式但包含data字段
        if ('data' in responseData) {
          return responseData.data;
        }
      }

      return responseData;
    } catch (error) {
      // 隐藏loading
      if (shouldShowLoading) {
        loading.hide();
      }

      // 判断是否是取消错误
      if (isCancel(error)) {
        // 取消请求的错误直接抛出，一般在上层业务中处理
        throw error;
      }
      
      // 错误处理
      const { skipErrorHandler } = newOptions;
      const errorConfig = config.errorConfig || {};
      
      // 显示错误消息
      const shouldShowErrorMessage = options.showErrorMessage !== undefined 
        ? options.showErrorMessage 
        : config.requestStatusConfig?.defaultShowErrorMessage || true;

      if (shouldShowErrorMessage) {
        const errorMsg = options.errorMessage 
          ? options.errorMessage 
          : formatErrorMessage(error, '请求失败');
        message.error(errorMsg);
      }

      if (skipErrorHandler) {
        throw error;
      }

      const errorHandler = errorConfig?.errorHandler;
      if (errorHandler) {
        errorHandler(error as ResponseError);
      }

      throw error;
    }
  };

  // 使用函数对象模式创建requestInstance
  const requestInstance = Object.assign(
    requestFunc,
    {
      get: null as any,
      post: null as any,
      put: null as any,
      delete: null as any,
      patch: null as any,
      head: null as any,
      options: null as any,
      interceptors: null as any,
      CancelToken: null as any,
      isCancel: null as any,
      extendOptions: null as any,
      request: null as any  // 添加request方法
    }
  ) as RequestInstance;
  
  // 为各方法绑定默认的method值
  const methods: HttpMethod[] = ['get', 'post', 'delete', 'put', 'patch', 'head', 'options'];
  
  methods.forEach(method => {
    const methodFunction = <T = any>(url: string, dataOrOptions?: any, maybeOptions?: Omit<RequestOptionsInit, 'data'>) => {
      // 处理参数
      let data = undefined;
      let options = {} as RequestOptionsInit;
      
      // 如果第二个参数是对象且不是数组，且第三个参数未提供，可能是data或options
      if (dataOrOptions && typeof dataOrOptions === 'object' && !Array.isArray(dataOrOptions) && !maybeOptions) {
        // 检查是否是options
        if ('params' in dataOrOptions || 'headers' in dataOrOptions || 'timeout' in dataOrOptions || 
            'responseType' in dataOrOptions || 'withCredentials' in dataOrOptions) {
          options = { ...dataOrOptions };
        } else {
          // 否则视为data
          data = dataOrOptions;
        }
      } else {
        // 第二个参数是data，第三个参数是options
        data = dataOrOptions;
        options = { ...(maybeOptions || {}) };
      }
      
      // 为GET、HEAD、DELETE、OPTIONS请求将data转为params
      if (['get', 'head', 'delete', 'options'].includes(method) && data) {
        options.params = { ...options.params, ...data };
        data = undefined;
      }
      
      // 设置请求方法
      options.method = method;
      
      // 发起请求
      return requestFunc<T>(url, data, options);
    };
    
    requestInstance[method] = methodFunction;
  });

  // 添加拦截器接口
  requestInstance.interceptors = {
    request: {
      use: (onFulfilled, onRejected, options = { global: true }) => {
        if (options.global) {
          const interceptor = onRejected ? [onFulfilled, onRejected] : onFulfilled;
          if (interceptor) {
            globalRequestInterceptors.push(interceptor as RequestInterceptor);
          }
          return globalRequestInterceptors.length - 1;
        }
        
        if (config.requestInterceptors === undefined) {
          config.requestInterceptors = [];
        }
        const interceptor = onRejected ? [onFulfilled, onRejected] : onFulfilled;
        if (interceptor) {
          config.requestInterceptors.push(interceptor as RequestInterceptor);
        }
        return config.requestInterceptors.length - 1;
      },
      eject: (id: number) => {
        globalRequestInterceptors.splice(id, 1);
      }
    },
    response: {
      use: (onFulfilled, onRejected, options = { global: true }) => {
        if (options.global) {
          const interceptor = onRejected ? [onFulfilled, onRejected] : onFulfilled;
          if (interceptor) {
            globalResponseInterceptors.push(interceptor as ResponseInterceptor);
          }
          return globalResponseInterceptors.length - 1;
        }

        if (config.responseInterceptors === undefined) {
          config.responseInterceptors = [];
        }
        const interceptor = onRejected ? [onFulfilled, onRejected] : onFulfilled;
        if (interceptor) {
          config.responseInterceptors.push(interceptor as ResponseInterceptor);
        }
        return config.responseInterceptors.length - 1;
      },
      eject: (id: number) => {
        globalResponseInterceptors.splice(id, 1);
      }
    }
  };

  // 将取消令牌相关方法挂载到请求实例
  requestInstance.CancelToken = CancelToken as any;
  requestInstance.isCancel = isCancel;

  // 添加 extendOptions 方法，用于更新配置
  requestInstance.extendOptions = (newOptions: RequestConfig) => {
    config = {
      ...config,
      ...newOptions,
      headers: {
        ...config.headers || {},
        ...newOptions.headers || {},
      },
      // 合并 params
      params: {
        ...config.params || {},
        ...newOptions.params || {},
      },
      // 合并 requestStatusConfig
      requestStatusConfig: {
        ...config.requestStatusConfig || {},
        ...newOptions.requestStatusConfig || {},
      }
    };
  };

  // 将requestFunc方法也赋值给request，保持与axios一致的API
  requestInstance.request = requestFunc;

  return requestInstance;
}

/**
 * 创建默认错误处理函数
 */
function createDefaultErrorHandler() {
  return function (error: ResponseError) {
    if (error.name === 'BizError') {
      const errorInfo = error.info;
      if (errorInfo) {
        const { errorMessage, errorCode } = errorInfo;
        switch (errorInfo.showType) {
          case ErrorShowType.SILENT:
            // do nothing
            break;
          case ErrorShowType.WARN_MESSAGE:
            message.warn(errorMessage || '未知警告');
            break;
          case ErrorShowType.ERROR_MESSAGE:
            message.error(errorMessage || '未知错误');
            break;
          case ErrorShowType.NOTIFICATION:
            notification.open({
              message: errorCode || '未知错误代码',
              description: errorMessage || '发生未知错误',
            });
            break;
          case ErrorShowType.REDIRECT:
            // redirect to error page
            break;
          default:
            message.error(errorMessage || '未知错误');
        }
      }
    } else if (error.response) {
      // HTTP 错误
      message.error(`请求错误: ${error.response.status}`);
    } else if (error.request) {
      // 请求超时等
      message.error('请求超时，请重试');
    } else {
      // 其他错误
      message.error('请求错误，请重试');
    }
  };
}

/**
 * 创建默认错误抛出函数
 */
function createDefaultErrorThrower() {
  return function (res: any) {
    const { success, code, data, msg, message, errorCode, errorMessage, showType } = res as ResponseStructure;
    // 判断请求是否成功，一般通过code或success字段判断
    if (code !== '200' && code !== '0' && !success) {
      // 先创建基础 Error
      const errorInstance = new Error(errorMessage || message || msg || '未知错误');
      
      // 创建 ResponseError 对象并复制属性
      const error: ResponseError = {
        name: 'BizError',
        message: errorInstance.message,
        data,
        info: { 
          errorCode: errorCode || code, 
          errorMessage: errorMessage || message || msg, 
          showType, 
          data 
        },
        isAxiosError: false,
        toJSON: () => ({
          message: errorMessage || message || msg || '未知错误',
          name: 'BizError',
        })
      };
      
      throw error;
    }
  };
}

/**
 * 创建高级请求实例
 */
function extend(config: RequestConfig = {}): RequestInstance {
  // 合并默认配置
  const mergedConfig = {
    ...defaultConfig,
    ...config,
    headers: {
      ...defaultConfig.headers,
      ...config.headers
    },
    requestStatusConfig: {
      ...defaultConfig.requestStatusConfig,
      ...config.requestStatusConfig
    }
  } as RequestConfig;

  // 创建默认错误处理
  if (!mergedConfig.errorConfig) {
    mergedConfig.errorConfig = {};
  }
  
  if (!mergedConfig.errorConfig.errorThrower) {
    mergedConfig.errorConfig.errorThrower = createDefaultErrorThrower();
  }
  
  if (!mergedConfig.errorConfig.errorHandler) {
    mergedConfig.errorConfig.errorHandler = createDefaultErrorHandler();
  }

  // 创建 axios 实例
  const axiosInstance = createAxiosInstance(mergedConfig);
  
  // 构建请求实例
  return createRequest(axiosInstance, mergedConfig);
}

export { extend, CancelToken, isCancel }; 
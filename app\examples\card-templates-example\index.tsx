"use client"

import * as React from "react"
import { Eye, ThumbsUp, MessageSquare, Calendar, Clock } from "lucide-react"
import { 
  BlogCard,
  ProductCardTemplate,
  EventCard,
  DashboardCard,
  TestimonialCard
} from "@/components/common-custom/card-templates"

export function CardTemplatesExample() {
  const blogPostData = {
    title: "构建现代化React应用：最佳实践与技巧",
    description: "学习如何使用最新的React特性和模式构建高性能、可维护的前端应用程序。",
    coverImage: "/placeholder.jpg",
    author: {
      name: "张伟",
      avatar: "/placeholder-user.jpg",
    },
    date: "2023-11-20",
    viewCount: 1420,
    likeCount: 238,
    commentCount: 32,
    category: "前端开发",
  }

  const productData = {
    name: "专业设计师办公椅",
    price: 1299,
    originalPrice: 1599,
    rating: 4.8,
    reviewCount: 124,
    image: "/placeholder.jpg",
    inStock: true,
    features: ["人体工学设计", "高质量网面靠背", "多角度调节", "静音滚轮"],
  }

  const eventData = {
    title: "2023技术创新峰会",
    description: "加入我们的年度峰会，探讨最新的技术趋势和创新解决方案。",
    date: "2023-12-15",
    time: "09:00 - 18:00",
    location: "上海国际会议中心",
    image: "/placeholder.jpg",
    attendees: 356,
    categories: ["技术", "创新", "峰会"],
  }

  const dashboardCardData = {
    title: "月度销售额",
    value: "￥128,560",
    change: 12.5,
    trend: "increase" as "increase" | "decrease" | "neutral",
    description: "较上月增长12.5%",
    icon: <ThumbsUp className="h-4 w-4" />,
  }

  const testimonialData = {
    quote: "这个产品彻底改变了我们的工作流程，提高了团队效率，节省了大量时间。",
    author: {
      name: "李明",
      position: "技术总监",
      company: "未来科技有限公司",
      avatar: "/placeholder-user.jpg",
    },
    rating: 5,
  }

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 max-w-6xl mx-auto">
      <BlogCard
        title={blogPostData.title}
        excerpt={blogPostData.description}
        author={blogPostData.author}
        publishDate={blogPostData.date}
        readTime="5分钟"
        category={blogPostData.category}
        image={blogPostData.coverImage}
        stats={{
          views: blogPostData.viewCount,
          likes: blogPostData.likeCount,
          comments: blogPostData.commentCount
        }}
        className="h-full"
      />
      <ProductCardTemplate data={productData} />
      <EventCard data={eventData} />
      <DashboardCard data={dashboardCardData} />
      <TestimonialCard data={testimonialData} />
    </div>
  )
} 
"use client"

import { ReactNode, useEffect, useState } from 'react'
import { usePathname, useRouter } from 'next/navigation'
import { setupAuthInterceptors } from '@/services/api/authInterceptor'
import { isLoggedIn } from '@/services/api/authService'
import { Loader2 } from 'lucide-react'
import { request } from '@/lib/request'

interface AuthProviderProps {
  children: ReactNode
}

// 不需要认证的路径
const PUBLIC_PATHS = ['/login']

export function AuthProvider({ children }: AuthProviderProps) {
  const pathname = usePathname()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // 设置全局拦截器
    setupAuthInterceptors(request)

    // 检查当前页面是否需要认证
    const isPublicPath = PUBLIC_PATHS.some(path => pathname.startsWith(path))
    
    // 如果是公开页面，无需验证
    if (isPublicPath) {
      setIsLoading(false)
      return
    }
    
    // 检查是否登录
    if (!isLoggedIn()) {
      // 未登录，重定向到登录页
      router.push('/login')
    } else {
      // 已登录，继续渲染
      setIsLoading(false)
    }
  }, [pathname, router])

  // 如果正在加载且不是公开页面，显示加载指示器
  if (isLoading && !PUBLIC_PATHS.some(path => pathname.startsWith(path))) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    )
  }

  return children
} 
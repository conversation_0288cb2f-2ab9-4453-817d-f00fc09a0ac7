// 密码库相关类型定义

// 密码强度枚举
export enum PasswordStrength {
  WEAK = 0,
  MEDIUM = 1,
  STRONG = 2
}

// 查询类型枚举
export enum PasswordQueryType {
  ALL = 0,
  FAVORITES = 1,
  RECENT = 2,
  STRONG = 3,
  WEAK = 4,
  MEDIUM = 5
}

// 密码库项目接口
export interface PasswordVaultItem {
  id: number;
  userId: number;
  checkPassword: string;
  passwordIcon: string;
  keyName: string;
  encryptedPassword: string;
  passwordStrength: number;
  remarks: string;
  platform: string;
  createUser: string | null;
  createTime: string;
  updateUser: string | null;
  updateTime: string;
  isDeleted: number;
  deleteTime: string | null;
  isCollected: boolean;
  tagNames: string[];
}

// 创建密码请求参数
export interface CreatePasswordParams {
  userId: number;
  checkPassword: string;
  passwordIcon: string;
  keyName: string;
  platform: string;
  encryptedPassword: string;
  passwordStrength: number;
  remarks: string;
  tagNames: string[];
}

// 密码列表查询参数
export interface PasswordListParams {
  userId: number;
  queryType: PasswordQueryType;
}

// 收藏请求参数
export interface CollectParams {
  moduleCode: string;
  sourceId: number;
  targetId: number;
}

// 将API响应的数据转换为UI组件使用的格式
export interface UIPasswordItem {
  id: string;
  name: string;
  account: string;
  password: string;
  platform: string;
  tags: string[];
  isFavorite: boolean;
  strength: "weak" | "medium" | "strong";
  password_strength: number; // 1-弱 2-中 3-强
  lastUpdated: string;
  notes: string;
  icon: string;
  createdAt: string;
  usageCount?: number;
  breachStatus?: "safe" | "breached";
  searchKeyword?: string; // 用于模糊搜索的关键字
}

// 转换工具函数类型
export type PasswordVaultConverter = {
  toUIFormat: (item: PasswordVaultItem) => UIPasswordItem;
  toAPIFormat: (item: UIPasswordItem) => CreatePasswordParams;
} 
import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// 生成随机字符串
export function generateRandomString(length = 6): string {
  const characters = "abcdefghijklmnopqrstuvwxyz"
  let result = ""
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length))
  }
  return result
}

/**
 * 格式化日期
 * @param dateString 日期字符串或Date对象
 * @param format 格式化模式，默认为"YYYY-MM-DD HH:mm:ss"
 * @returns 格式化后的日期字符串
 */
export function formatDate(dateString: string | Date, format: string = "YYYY-MM-DD HH:mm:ss"): string {
  if (!dateString) return "--";
  
  const date = typeof dateString === "string" ? new Date(dateString) : dateString;
  
  // 检查日期是否有效
  if (isNaN(date.getTime())) return "--";
  
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  
  return format
    .replace("YYYY", String(year))
    .replace("MM", month)
    .replace("DD", day)
    .replace("HH", hours)
    .replace("mm", minutes)
    .replace("ss", seconds);
}

// 生成权限标识
export function generatePermissionId(node: any, permissions: any[]): string {
  const path: string[] = []

  // 递归查找节点路径
  const findPath = (nodes: any[], id: string, currentPath: string[] = []): boolean => {
    for (const node of nodes) {
      const newPath = [...currentPath, generateRandomString(4)]

      if (node.id === id) {
        path.push(...newPath)
        return true
      }

      if (node.children && node.children.length > 0) {
        if (findPath(node.children, id, newPath)) {
          return true
        }
      }
    }
    return false
  }

  // 如果有父节点，查找路径
  if (node.parentId) {
    findPath(permissions, node.parentId)
  }

  // 添加当前节点的随机标识
  path.push(generateRandomString(4))

  return path.join(":")
}

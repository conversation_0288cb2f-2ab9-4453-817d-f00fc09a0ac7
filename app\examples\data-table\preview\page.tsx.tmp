"use client";

import React from "react";
import { ComponentPreviewContainer } from "@/components/component-detail/preview-container";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { 
  flexRender, 
  getCoreRowModel, 
  useReactTable,
  getPaginationRowModel,
  getSortedRowModel,
  getFilteredRowModel
} from "@tanstack/react-table";
import { 
  ArrowUpDown, 
  ChevronDown, 
  MoreHorizontal, 
  Search, 
  Pencil, 
  Save, 
  <PERSON>, 
  <PERSON>,
  <PERSON>, 
  Trash
} from "lucide-react";

// 基础表格代码示例
const basicTableCode = `
import React from "react";
import { 
  flexRender, 
  getCoreRowModel, 
  useReactTable,
  getPaginationRowModel,
} from "@tanstack/react-table";

import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

// 模拟数据
const data = [
  {
    id: "u-001",
    name: "张三",
    email: "<EMAIL>",
    role: "管理员",
    status: "活跃",
  },
  {
    id: "u-002",
    name: "李四",
    email: "<EMAIL>",
    role: "用户",
    status: "活跃",
  },
  {
    id: "u-003",
    name: "王五",
    email: "<EMAIL>",
    role: "编辑",
    status: "未活跃",
  },
  {
    id: "u-004",
    name: "赵六",
    email: "<EMAIL>",
    role: "用户",
    status: "待处理",
  },
  {
    id: "u-005",
    name: "钱七",
    email: "<EMAIL>",
    role: "编辑",
    status: "活跃",
  },
];

function BasicDataTable() {
  const columns = [
    {
      accessorKey: "name",
      header: "姓名",
    },
    {
      accessorKey: "email",
      header: "邮箱",
    },
    {
      accessorKey: "role",
      header: "角色",
    },
    {
      accessorKey: "status",
      header: "状态",
      cell: ({ row }) => {
        const status = row.getValue("status");
        return (
          <div className="flex items-center">
            <span
              className={\`mr-2 h-2 w-2 rounded-full \${
                status === "活跃" ? "bg-green-500" : 
                status === "未活跃" ? "bg-gray-400" : "bg-yellow-500"
              }\`}
            />
            {status}
          </div>
        );
      },
    },
  ];

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    initialState: {
      pagination: {
        pageSize: 3,
      },
    },
  });

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  无数据
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => table.previousPage()}
          disabled={!table.getCanPreviousPage()}
        >
          上一页
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => table.nextPage()}
          disabled={!table.getCanNextPage()}
        >
          下一页
        </Button>
      </div>
    </div>
  );
}
`;

// 高级表格代码示例
const advancedTableCode = `
import React, { useState } from "react";
import { 
  flexRender, 
  getCoreRowModel, 
  useReactTable,
  getPaginationRowModel,
  getSortedRowModel,
  getFilteredRowModel,
} from "@tanstack/react-table";
import { ArrowUpDown, ChevronDown, MoreHorizontal } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

// 模拟数据
const data = [
  {
    id: "u-001",
    name: "张三",
    email: "<EMAIL>",
    role: "管理员",
    status: "活跃",
    lastLogin: "2023-12-01 10:30",
  },
  {
    id: "u-002",
    name: "李四",
    email: "<EMAIL>",
    role: "用户",
    status: "活跃",
    lastLogin: "2023-12-03 14:20",
  },
  {
    id: "u-003",
    name: "王五",
    email: "<EMAIL>",
    role: "编辑",
    status: "未活跃",
    lastLogin: "2023-11-28 09:45",
  },
  {
    id: "u-004",
    name: "赵六",
    email: "<EMAIL>",
    role: "用户",
    status: "待处理",
    lastLogin: "2023-12-05 16:10",
  },
  {
    id: "u-005",
    name: "钱七",
    email: "<EMAIL>",
    role: "编辑",
    status: "活跃",
    lastLogin: "2023-12-04 11:25",
  },
];

function AdvancedDataTable() {
  const [sorting, setSorting] = useState([]);
  const [columnFilters, setColumnFilters] = useState([]);
  const [columnVisibility, setColumnVisibility] = useState({});
  const [rowSelection, setRowSelection] = useState({});

  const columns = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="全选"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="选择行"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "name",
      header: "姓名",
      cell: ({ row }) => <div>{row.getValue("name")}</div>,
    },
    {
      accessorKey: "email",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            邮箱
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        )
      },
      cell: ({ row }) => <div>{row.getValue("email")}</div>,
    },
    {
      accessorKey: "role",
      header: "角色",
      cell: ({ row }) => <div>{row.getValue("role")}</div>,
    },
    {
      accessorKey: "status",
      header: "状态",
      cell: ({ row }) => {
        const status = row.getValue("status");
        return (
          <div className="flex items-center">
            <span
              className={\`mr-2 h-2 w-2 rounded-full \${
                status === "活跃" ? "bg-green-500" : 
                status === "未活跃" ? "bg-gray-400" : "bg-yellow-500"
              }\`}
            />
            {status}
          </div>
        )
      },
    },
    {
      accessorKey: "lastLogin",
      header: "最后登录",
      cell: ({ row }) => <div>{row.getValue("lastLogin")}</div>,
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const user = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">打开菜单</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>操作</DropdownMenuLabel>
              <DropdownMenuItem
                onClick={() => navigator.clipboard.writeText(user.id)}
              >
                复制用户ID
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>查看详情</DropdownMenuItem>
              <DropdownMenuItem>编辑用户</DropdownMenuItem>
              <DropdownMenuItem>删除用户</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )
      },
    },
  ];

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
    initialState: {
      pagination: {
        pageSize: 5,
      },
    },
  });

  return (
    <div className="space-y-4">
      <div className="flex items-center py-4">
        <Input
          placeholder="按姓名筛选..."
          value={(table.getColumn("name")?.getFilterValue() ?? "")}
          onChange={(event) =>
            table.getColumn("name")?.setFilterValue(event.target.value)
          }
          className="max-w-sm"
        />
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="ml-auto">
              列设置 <ChevronDown className="ml-2 h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {table
              .getAllColumns()
              .filter((column) => column.getCanHide())
              .map((column) => {
                // 获取列的中文标题
                let columnTitle = column.id;
                if (column.id === "name") columnTitle = "姓名";
                if (column.id === "email") columnTitle = "邮箱";
                if (column.id === "role") columnTitle = "角色";
                if (column.id === "status") columnTitle = "状态";
                if (column.id === "lastLogin") columnTitle = "最后登录";
                
                return (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    checked={column.getIsVisible()}
                    onCheckedChange={(value) =>
                      column.toggleVisibility(!!value)
                    }
                  >
                    {columnTitle}
                  </DropdownMenuCheckboxItem>
                )
              })}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  无数据
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-between">
        <div className="flex-1 text-sm text-muted-foreground">
          {table.getFilteredSelectedRowModel().rows.length} 项已选择，共 {table.getFilteredRowModel().rows.length} 项
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            上一页
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            下一页
          </Button>
        </div>
      </div>
    </div>
  );
}
`;

// 可编辑表格代码示例
const editableTableCode = `
import React, { useState } from "react";
import { 
  flexRender, 
  getCoreRowModel, 
  useReactTable,
  getPaginationRowModel,
} from "@tanstack/react-table";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Pencil, Save, X, Check } from "lucide-react";

// 模拟初始数据
const initialData = [
  { id: "1", name: "张三", email: "<EMAIL>", role: "管理员" },
  { id: "2", name: "李四", email: "<EMAIL>", role: "用户" },
  { id: "3", name: "王五", email: "<EMAIL>", role: "编辑" },
  { id: "4", name: "赵六", email: "<EMAIL>", role: "用户" },
];

function EditableDataTable() {
  const [data, setData] = useState([...initialData]);
  const [editingRow, setEditingRow] = useState(null);
  const [editedValues, setEditedValues] = useState({});

  // 开始编辑行
  const startEditing = (row) => {
    setEditingRow(row.id);
    setEditedValues({
      name: row.original.name,
      email: row.original.email,
      role: row.original.role,
    });
  };

  // 取消编辑
  const cancelEditing = () => {
    setEditingRow(null);
    setEditedValues({});
  };

  // 保存编辑
  const saveEditing = (id) => {
    setData(
      data.map((row) =>
        row.id === id
          ? {
              ...row,
              name: editedValues.name,
              email: editedValues.email,
              role: editedValues.role,
            }
          : row
      )
    );
    setEditingRow(null);
    setEditedValues({});
  };

  // 处理编辑字段的值变化
  const handleFieldChange = (field, value) => {
    setEditedValues({
      ...editedValues,
      [field]: value,
    });
  };

  const columns = [
    {
      accessorKey: "name",
      header: "姓名",
      cell: ({ row }) => {
        return editingRow === row.id ? (
          <Input
            value={editedValues.name}
            onChange={(e) => handleFieldChange("name", e.target.value)}
            className="h-8"
          />
        ) : (
          <div>{row.getValue("name")}</div>
        );
      },
    },
    {
      accessorKey: "email",
      header: "邮箱",
      cell: ({ row }) => {
        return editingRow === row.id ? (
          <Input
            value={editedValues.email}
            onChange={(e) => handleFieldChange("email", e.target.value)}
            className="h-8"
          />
        ) : (
          <div>{row.getValue("email")}</div>
        );
      },
    },
    {
      accessorKey: "role",
      header: "角色",
      cell: ({ row }) => {
        return editingRow === row.id ? (
          <Input
            value={editedValues.role}
            onChange={(e) => handleFieldChange("role", e.target.value)}
            className="h-8"
          />
        ) : (
          <div>{row.getValue("role")}</div>
        );
      },
    },
    {
      id: "actions",
      header: "操作",
      cell: ({ row }) => {
        return editingRow === row.id ? (
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => saveEditing(row.id)}
              className="h-8 w-8"
            >
              <Save className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={cancelEditing}
              className="h-8 w-8"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        ) : (
          <Button
            variant="ghost"
            size="icon"
            onClick={() => startEditing(row)}
            className="h-8 w-8"
          >
            <Pencil className="h-4 w-4" />
          </Button>
        );
      },
    },
  ];

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    initialState: {
      pagination: {
        pageSize: 4,
      },
    },
  });

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  无数据
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => table.previousPage()}
          disabled={!table.getCanPreviousPage()}
        >
          上一页
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => table.nextPage()}
          disabled={!table.getCanNextPage()}
        >
          下一页
        </Button>
      </div>
    </div>
  );
}
`;

// API文档组件
function DataTableApiDocs() {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="font-medium text-lg mb-2">数据表格组件API</h3>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">data</td>
                <td className="p-2 border">any[]</td>
                <td className="p-2 border">[]</td>
                <td className="p-2 border">表格数据源</td>
              </tr>
              <tr>
                <td className="p-2 border">columns</td>
                <td className="p-2 border">ColumnDef[]</td>
                <td className="p-2 border">[]</td>
                <td className="p-2 border">列定义配置</td>
              </tr>
              <tr>
                <td className="p-2 border">pagination</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">true</td>
                <td className="p-2 border">是否启用分页</td>
              </tr>
              <tr>
                <td className="p-2 border">sorting</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">true</td>
                <td className="p-2 border">是否启用排序</td>
              </tr>
              <tr>
                <td className="p-2 border">filtering</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">false</td>
                <td className="p-2 border">是否启用筛选</td>
              </tr>
              <tr>
                <td className="p-2 border">selection</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">false</td>
                <td className="p-2 border">是否启用选择</td>
              </tr>
              <tr>
                <td className="p-2 border">onRowClick</td>
                <td className="p-2 border">(row: any) => void</td>
                <td className="p-2 border">undefined</td>
                <td className="p-2 border">行点击回调</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

export default function DataTablePreview() {
  const examples = [
    {
      id: "basic-table",
      title: "基础数据表格",
      description: "基础数据表格，支持分页",
      code: basicTableCode,
      scope: { 
        React, 
        useState: React.useState,
        Table, 
        TableBody, 
        TableCell, 
        TableHead, 
        TableHeader, 
        TableRow,
        Button,
        flexRender,
        getCoreRowModel,
        useReactTable,
        getPaginationRowModel
      }, 
    },
    {
      id: "advanced-table",
      title: "高级数据表格",
      description: "高级数据表格，支持排序、筛选、列控制和行选择",
      code: advancedTableCode,
      scope: { 
        React, 
        useState: React.useState,
        Table, 
        TableBody, 
        TableCell, 
        TableHead, 
        TableHeader, 
        TableRow,
        Button,
        Input,
        Checkbox,
        DropdownMenu,
        DropdownMenuCheckboxItem,
        DropdownMenuContent,
        DropdownMenuItem,
        DropdownMenuLabel,
        DropdownMenuSeparator,
        DropdownMenuTrigger,
        flexRender,
        getCoreRowModel,
        useReactTable,
        getPaginationRowModel,
        getSortedRowModel,
        getFilteredRowModel,
        ArrowUpDown,
        ChevronDown,
        MoreHorizontal
      },
    },
    {
      id: "editable-table",
      title: "可编辑数据表格",
      description: "支持行内编辑的数据表格",
      code: editableTableCode,
      scope: { 
        React, 
        useState: React.useState,
        Table, 
        TableBody, 
        TableCell, 
        TableHead, 
        TableHeader, 
        TableRow,
        Button,
        Input,
        flexRender,
        getCoreRowModel,
        useReactTable,
        getPaginationRowModel,
        Pencil,
        Save,
        X,
        Check
      },
    }
  ];

  return (
    <ComponentPreviewContainer
      title="数据表格 DataTable"
      description="用于展示和操作结构化数据的表格组件。"
      whenToUse="需要展示大量结构化数据时，需要对数据进行排序、筛选、分页等操作时，需要展示具有主从关系的数据或分层数据时。"
      examples={examples}
      apiDocs={<DataTableApiDocs />}
    />
  );
}

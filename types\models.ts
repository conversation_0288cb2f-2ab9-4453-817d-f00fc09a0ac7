/**
 * API模型定义
 * 集中定义所有API使用的模型接口
 */
import { PermissionGroup, PermissionNode } from './permission';

// 团队模型接口定义
export interface Team {
  id: number;
  teamCode: string;
  teamName: string;
  teamLogo: string;
  teamThemeColor: string;
  teamType: string;
  parentId?: number;
  leaderId?: string | null;
  status: number;
  description: string;
  privateTag: number;
  createUser?: string | null;
  createTime?: string;
  updateUser?: string | null;
  updateTime?: string | null;
  isDeleted?: number;
  deleteTime?: string | null;
  memberCount?: number;
}

// 用户模型接口定义
export interface User {
  _id: string;
  name: string;
  email: string;
  avatar: string;
  teamId?: string;
  roleIds: string[];
  status: string;
  lastLogin: string;
  createdAt?: string;
  updatedAt?: string;
}

// 角色模型接口定义
export interface Role {
  id: number;
  teamCode: string;
  teamName: string;
  roleName: string;
  roleCode: string;
  description?: string;
  status: number;
  statusLabel?: string;
  permissionCount?: number;
  userCount?: number;
  createTime?: string;
  updateTime?: string;
  // 扩展字段 - 权限信息
  permissionGroups?: PermissionGroup[];
  permissionTrees?: Record<string, PermissionNode[]>;
  selectedPermissions?: string[];
}

// 群组接口定义
export interface Group {
  id: string;
  name: string;
  description?: string;
  members?: string[];
  createdAt?: string;
  updatedAt?: string;
  [key: string]: any;
}

// 群组查询参数接口
export interface GroupQueryParams {
  page?: number;
  size?: number;
  keyword?: string;
  sort?: string;
  order?: 'asc' | 'desc';
  [key: string]: any;
}

// 创建群组参数接口
export interface CreateGroupParams {
  name: string;
  description?: string;
  members?: string[];
  [key: string]: any;
} 
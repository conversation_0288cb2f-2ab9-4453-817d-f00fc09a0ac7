/**
 * 复杂组件类型定义文件
 * 
 * 这是复杂组件的标准类型定义模板，展示了如何组织复杂的类型结构
 */

import { ReactNode, ComponentType } from "react"
import { LucideIcon } from "lucide-react"

// ============================================================================
// 基础类型定义
// ============================================================================

/**
 * 组件尺寸类型
 */
export type ComponentSize = "sm" | "md" | "lg"

/**
 * 组件变体类型
 */
export type ComponentVariant = "default" | "primary" | "secondary" | "destructive"

/**
 * 组件状态类型
 */
export type ComponentStatus = "idle" | "loading" | "success" | "error"

/**
 * 排序方向类型
 */
export type SortDirection = "asc" | "desc"

// ============================================================================
// 数据项类型定义
// ============================================================================

/**
 * 复杂组件数据项
 */
export interface ComplexComponentItem {
  /**
   * 唯一标识符
   */
  id: string | number
  
  /**
   * 项目标题
   */
  title: string
  
  /**
   * 项目描述
   */
  description?: string
  
  /**
   * 项目值
   */
  value?: any
  
  /**
   * 项目状态
   */
  status?: ComponentStatus
  
  /**
   * 项目图标
   */
  icon?: LucideIcon | ComponentType<{ className?: string }>
  
  /**
   * 是否禁用
   */
  disabled?: boolean
  
  /**
   * 是否选中
   */
  selected?: boolean
  
  /**
   * 自定义数据
   */
  meta?: Record<string, any>
  
  /**
   * 子项目
   */
  children?: ComplexComponentItem[]
}

// ============================================================================
// 配置类型定义
// ============================================================================

/**
 * 复杂组件配置
 */
export interface ComplexComponentConfig {
  /**
   * 是否显示头部
   * @default true
   */
  showHeader?: boolean
  
  /**
   * 是否显示工具栏
   * @default true
   */
  showToolbar?: boolean
  
  /**
   * 是否显示分页
   * @default true
   */
  showPagination?: boolean
  
  /**
   * 是否支持多选
   * @default false
   */
  multiSelect?: boolean
  
  /**
   * 是否支持排序
   * @default true
   */
  sortable?: boolean
  
  /**
   * 是否支持筛选
   * @default true
   */
  filterable?: boolean
  
  /**
   * 每页显示数量
   * @default 10
   */
  pageSize?: number
  
  /**
   * 默认排序字段
   */
  defaultSortField?: string
  
  /**
   * 默认排序方向
   * @default "asc"
   */
  defaultSortDirection?: SortDirection
}

// ============================================================================
// 事件处理类型定义
// ============================================================================

/**
 * 项目点击事件处理器
 */
export type ComplexComponentItemClickHandler = (
  item: ComplexComponentItem,
  event: React.MouseEvent
) => void

/**
 * 项目选择事件处理器
 */
export type ComplexComponentItemSelectHandler = (
  selectedItems: ComplexComponentItem[]
) => void

/**
 * 排序变更事件处理器
 */
export type ComplexComponentSortChangeHandler = (
  field: string,
  direction: SortDirection
) => void

/**
 * 筛选变更事件处理器
 */
export type ComplexComponentFilterChangeHandler = (
  filters: Record<string, any>
) => void

/**
 * 分页变更事件处理器
 */
export type ComplexComponentPageChangeHandler = (
  page: number,
  pageSize: number
) => void

// ============================================================================
// 主组件属性类型定义
// ============================================================================

/**
 * 复杂组件属性
 */
export interface ComplexComponentProps {
  /**
   * 数据项列表
   */
  items: ComplexComponentItem[]
  
  /**
   * 组件配置
   */
  config?: ComplexComponentConfig
  
  /**
   * 组件尺寸
   * @default "md"
   */
  size?: ComponentSize
  
  /**
   * 组件变体
   * @default "default"
   */
  variant?: ComponentVariant
  
  /**
   * 是否加载中
   * @default false
   */
  loading?: boolean
  
  /**
   * 错误信息
   */
  error?: string | null
  
  /**
   * 自定义类名
   */
  className?: string
  
  /**
   * 自定义头部内容
   */
  headerContent?: ReactNode
  
  /**
   * 自定义工具栏内容
   */
  toolbarContent?: ReactNode
  
  /**
   * 自定义底部内容
   */
  footerContent?: ReactNode
  
  // 事件处理器
  onItemClick?: ComplexComponentItemClickHandler
  onItemSelect?: ComplexComponentItemSelectHandler
  onSortChange?: ComplexComponentSortChangeHandler
  onFilterChange?: ComplexComponentFilterChangeHandler
  onPageChange?: ComplexComponentPageChangeHandler
}

// ============================================================================
// 子组件属性类型定义
// ============================================================================

/**
 * 复杂组件头部属性
 */
export interface ComplexComponentHeaderProps {
  title?: string
  description?: string
  actions?: ReactNode
  className?: string
}

/**
 * 复杂组件内容属性
 */
export interface ComplexComponentContentProps {
  items: ComplexComponentItem[]
  selectedItems?: ComplexComponentItem[]
  onItemClick?: ComplexComponentItemClickHandler
  onItemSelect?: ComplexComponentItemSelectHandler
  className?: string
}

/**
 * 复杂组件工具栏属性
 */
export interface ComplexComponentToolbarProps {
  searchValue?: string
  onSearchChange?: (value: string) => void
  filters?: Record<string, any>
  onFilterChange?: ComplexComponentFilterChangeHandler
  actions?: ReactNode
  className?: string
}

/**
 * 复杂组件分页属性
 */
export interface ComplexComponentPaginationProps {
  currentPage: number
  totalPages: number
  pageSize: number
  totalItems: number
  onPageChange: ComplexComponentPageChangeHandler
  className?: string
}

// ============================================================================
// Hook类型定义
// ============================================================================

/**
 * 复杂组件状态
 */
export interface ComplexComponentState {
  items: ComplexComponentItem[]
  selectedItems: ComplexComponentItem[]
  currentPage: number
  pageSize: number
  sortField?: string
  sortDirection?: SortDirection
  filters: Record<string, any>
  searchValue: string
  loading: boolean
  error: string | null
}

/**
 * 复杂组件Hook返回值
 */
export interface UseComplexComponentReturn {
  state: ComplexComponentState
  actions: {
    setItems: (items: ComplexComponentItem[]) => void
    selectItem: (item: ComplexComponentItem) => void
    selectItems: (items: ComplexComponentItem[]) => void
    clearSelection: () => void
    setPage: (page: number) => void
    setPageSize: (pageSize: number) => void
    setSorting: (field: string, direction: SortDirection) => void
    setFilters: (filters: Record<string, any>) => void
    setSearchValue: (value: string) => void
    setLoading: (loading: boolean) => void
    setError: (error: string | null) => void
  }
}

# Custom Components

自定义组件目录，包含项目特定的业务组件。经过重构优化，现在主要包含项目特定的核心组件。

## 🧹 重构说明

本目录已完成组件结构重构：
- ✅ 大部分通用组件已迁移至 `common-custom` 目录
- ✅ 保留项目特定的核心组件
- ✅ 通过 `index.ts` 重新导出标准化组件，保持向后兼容

## 📁 当前组件

### 🔐 认证相关组件
- `auth-provider.tsx` - 认证提供者组件，处理用户登录状态和权限验证

### 🛠️ 开发工具
- `stagewise-toolbar.tsx` - Stagewise工具栏包装组件（仅开发环境）

### 📤 重新导出的标准化组件
通过 `index.ts` 重新导出以下组件，保持向后兼容：
- `DataTable` - 数据表格组件（来自 common-custom）
- `FormDialog` - 表单对话框组件（来自 common-custom）
- `GlobalLoading` - 全局加载组件（来自 common-custom）
- `PageLoading` - 页面加载组件（来自 project-custom）
- `BackButton` - 返回按钮组件（来自 common-custom）
- `HeaderWithBreadcrumb` - 面包屑导航组件（来自 project-custom）
- 导航和侧边栏相关组件（来自 navigation 目录）

## 🎯 使用方式

```tsx
// 推荐：从index.ts统一导入（保持向后兼容）
import { 
  AuthProvider,
  DataTable,
  FormDialog,
  GlobalLoading,
  PageLoading,
  HeaderWithBreadcrumb,
  BackButton
} from "@/components/custom"

// 或者直接使用标准化组件
import { DataTable } from "@/components/common-custom/data-table"
import { BackButton } from "@/components/common-custom/back-button"
import { HeaderWithBreadcrumb } from "@/components/project-custom/breadcrumb"
```

## 📋 设计原则

1. **项目特定**：保留真正项目特定的组件
2. **标准化优先**：优先使用 common-custom 和 project-custom 组件
3. **向后兼容**：通过重新导出保持现有代码的兼容性
4. **清晰分层**：明确区分项目特定组件和通用组件
5. **易于维护**：减少重复代码，提高可维护性

## 🔄 迁移指南

如果你需要直接使用标准化组件：

### 数据表格
```tsx
// 旧方式（仍然有效）
import { DataTable } from "@/components/custom"

// 新方式（推荐）
import { AdvancedDataTable } from "@/components/common-custom/data-table"
```

### 表单组件
```tsx
// 旧方式（仍然有效）
import { FormDialog } from "@/components/custom"

// 新方式（推荐）
import { FormDialog } from "@/components/common-custom/form"
```

### 导航组件
```tsx
// 旧方式（仍然有效）
import { BackButton, HeaderWithBreadcrumb } from "@/components/custom"

// 新方式（推荐）
import { BackButton } from "@/components/common-custom/back-button"
import { HeaderWithBreadcrumb } from "@/components/project-custom/breadcrumb"
```

## 🔄 反向贡献评估

### 推荐通用化的组件

#### AuthProvider 认证提供者组件 ⭐⭐⭐⭐⭐
- **通用价值**：认证是几乎所有业务应用的核心需求
- **改造建议**：抽象认证检查、路由导航等逻辑，支持多框架使用
- **未来计划**：考虑贡献到business-components项目

### 保持项目特定的组件

#### StagewiseToolbarWrapper 开发工具组件
- **特定用途**：仅用于开发环境的工具集成
- **维护建议**：保持为项目特定组件，不适合通用化

## 📚 相关文档

- [通用组件文档](../common-custom/README.md)
- [项目特定组件文档](../project-custom/README.md)
- [组件清单](../../docs/组件清单.md)
- [实施指南](../../docs/implementation-guide.md)
- [MCP工具使用规范](../../docs/MCP工具使用规范.md)

"use client";

import { useEffect, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';

/**
 * Stagewise工具栏包装组件
 * 
 * 该组件仅在开发环境中动态加载Stagewise工具栏，
 * 在生产环境中不会加载任何资源，避免影响生产构建
 */
export function StagewiseToolbarWrapper() {
  const [StagewiseToolbar, setStagewiseToolbar] = useState<any>(null);
  const [reactPluginModule, setReactPluginModule] = useState<any>(null);
  const [isDevEnvironment, setIsDevEnvironment] = useState(false);
  const [loadError, setLoadError] = useState<Error | null>(null);

  useEffect(() => {
    // 检查当前环境
    const isDev = process.env.NODE_ENV === 'development';
    setIsDevEnvironment(isDev);
    
    if (typeof window !== 'undefined' && isDev) {
      // 为crypto.randomUUID添加polyfill
      if (window.crypto && !window.crypto.randomUUID) {
        // 使用uuid库的v4函数提供randomUUID功能
        Object.defineProperty(window.crypto, 'randomUUID', {
          configurable: true,
          writable: true,
          value: () => uuidv4()
        });
      }

      // 并行加载Stagewise工具栏和React插件
      Promise.all([
        import('@stagewise/toolbar-next').then(mod => mod.StagewiseToolbar),
        import('@stagewise-plugins/react')
      ])
        .then(([toolbarComponent, pluginModule]) => {
          setStagewiseToolbar(() => toolbarComponent);
          setReactPluginModule(() => pluginModule);
        })
        .catch(err => {
          console.error('无法加载Stagewise工具栏或插件:', err);
          setLoadError(err instanceof Error ? err : new Error(String(err)));
        });
    }
  }, []);

  // 如果加载出错，在开发环境中显示错误信息
  if (loadError && isDevEnvironment) {
    return (
      <div style={{
        position: 'fixed',
        bottom: '10px',
        right: '10px',
        background: '#ff5555',
        color: 'white',
        padding: '8px 12px',
        borderRadius: '4px',
        fontSize: '14px',
        zIndex: 9999
      }}>
        Stagewise工具栏加载失败: {loadError.message}
      </div>
    );
  }

  // 只有当组件和插件都加载完成且在开发环境中才渲染
  if (!StagewiseToolbar || !reactPluginModule || !isDevEnvironment) {
    return null;
  }

  // 获取正确的ReactPlugin
  const getReactPlugin = () => {
    // 检查模块导出
    const ReactPlugin = reactPluginModule.default || reactPluginModule.ReactPlugin;
    
    if (!ReactPlugin) {
      console.error('无法找到ReactPlugin导出');
      return null;
    }
    
    try {
      // 尝试确定ReactPlugin的类型并正确使用
      if (typeof ReactPlugin === 'function') {
        // 检查是否是构造函数
        if (ReactPlugin.prototype && ReactPlugin.prototype.constructor === ReactPlugin) {
          return new ReactPlugin();
        } else {
          // 是普通函数
          return ReactPlugin();
        }
      } else {
        // 是对象或其他类型
        return ReactPlugin;
      }
    } catch (error) {
      console.error('初始化ReactPlugin时出错:', error);
      return null;
    }
  };

  const reactPlugin = getReactPlugin();
  
  // 如果无法获取插件，显示错误
  if (!reactPlugin) {
    return (
      <div style={{
        position: 'fixed',
        bottom: '10px',
        right: '10px',
        background: '#ff5555',
        color: 'white',
        padding: '8px 12px',
        borderRadius: '4px',
        fontSize: '14px',
        zIndex: 9999
      }}>
        Stagewise工具栏初始化失败: 无法初始化ReactPlugin
      </div>
    );
  }

  // Stagewise配置对象
  const stagewiseConfig = {
    plugins: [reactPlugin],
    // 这里可以添加更多配置选项
    options: {
      position: 'bottom-right', // 工具栏位置
      theme: 'auto', // 主题设置，可选 'light', 'dark', 'auto'
    }
  };

  return <StagewiseToolbar config={stagewiseConfig} />;
} 
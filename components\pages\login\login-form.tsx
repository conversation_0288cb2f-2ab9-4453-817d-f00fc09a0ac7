﻿"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Loader2, AlertCircle } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { login, LoginParams } from "@/services/api/authService"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"

export function LoginForm({
  className,
  ...props
}: React.ComponentProps<"form">) {
  const router = useRouter()
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [errorMessage, setErrorMessage] = useState<string | null>(null)
  const [formData, setFormData] = useState<LoginParams>({
    account: "",
    password: "",
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
    // 清除错误信息
    setErrorMessage(null)
  }

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    if (!formData.account || !formData.password) {
      const message = !formData.account && !formData.password 
        ? "请输入账号和密码" 
        : !formData.account 
          ? "请输入账号" 
          : "请输入密码";
      
      setErrorMessage(message);
      return;
    }
    
    try {
      setIsLoading(true);
      
      const response = await login(formData);
      
      if (response.code === "200" && response.data) {
        // 保存token到localStorage
        localStorage.setItem('auth_token', response.data.token);
        localStorage.setItem('auth_token_type', response.data.tokenType || 'Bearer');
        
        toast({
          title: "登录成功",
          description: "欢迎回来！"
        });
        
        // 登录成功，跳转到首页
        router.push("/");
      } else {
        // 显示具体的错误信息
        const message = response.msg || "登录失败，请检查账号和密码";
        setErrorMessage(message);
        
        // 显示toast提示
        toast({
          title: "登录失败",
          description: message,
          variant: "destructive",
        });
        
        // 不再清空密码字段
      }
    } catch (error) {
      console.error("登录异常:", error);
      let errorMsg = "网络异常，请稍后重试";
      
      if (error instanceof Error) {
        errorMsg = error.message || errorMsg;
      }
      
      setErrorMessage(errorMsg);
      
      toast({
        title: "登录异常",
        description: errorMsg,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <form className={cn("flex flex-col gap-6", className)} {...props} onSubmit={handleSubmit}>
      <div className="flex flex-col items-center gap-2 text-center">
        <h1 className="text-2xl font-bold lg:text-gray-900">登录您的账号</h1>
        <p className="text-muted-foreground text-sm text-balance">
          请输入您的账号信息，安全登录Keel Cloud平台
        </p>
      </div>
      
      {/* 错误提示 */}
      {errorMessage && (
        <Alert variant="destructive" className="border-red-100 bg-red-50 text-red-800">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>登录失败</AlertTitle>
          <AlertDescription>{errorMessage}</AlertDescription>
        </Alert>
      )}
      
      <div className="grid gap-5">
        <div className="grid gap-2">
          <Label htmlFor="account" className="font-medium">账号</Label>
          <Input 
            id="account" 
            name="account"
            type="text" 
            placeholder="请输入您的手机号/邮箱/用户名" 
            value={formData.account}
            onChange={handleChange}
            required 
            autoComplete="username"
            disabled={isLoading}
            onKeyDown={(e) => e.key === 'Enter' && document.getElementById('password')?.focus()}
            className="rounded-md focus-visible:ring-primary focus-visible:border-primary"
          />
        </div>
        <div className="grid gap-2">
          <div className="flex items-center justify-between">
            <Label htmlFor="password" className="font-medium">密码</Label>
            <a
              href="#"
              className="text-sm text-primary hover:text-primary/90 hover:underline"
            >
              忘记密码?
            </a>
          </div>
          <Input 
            id="password" 
            name="password"
            type="password" 
            placeholder="请输入您的密码"
            value={formData.password}
            onChange={handleChange}
            required 
            autoComplete="current-password"
            disabled={isLoading}
            className="rounded-md focus-visible:ring-primary focus-visible:border-primary"
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                e.preventDefault();
                const submitButton = document.querySelector('button[type="submit"]') as HTMLButtonElement;
                submitButton?.click();
              }
            }}
          />
        </div>
        <Button 
          type="submit" 
          className="w-full mt-2 bg-gradient-to-r from-gray-900 to-black hover:from-black hover:to-gray-900 shadow-lg" 
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              登录中...
            </>
          ) : (
            "登录"
          )}
        </Button>
      </div>
      <div className="text-center text-sm text-muted-foreground">
        还没有账号?{" "}
        <a href="#" className="text-primary hover:text-primary/90 hover:underline">
          注册
        </a>
      </div>
    </form>
  )
}

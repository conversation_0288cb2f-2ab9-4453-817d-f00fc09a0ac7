"use client"

import React, { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Search, X } from "lucide-react"
import { cn } from "@/lib/utils"
import { IconButtonWithBadge } from "@/components/common-custom/icon-button-with-badge"

export interface CollapsibleSearchProps {
  /**
   * 搜索值
   */
  value?: string
  
  /**
   * 值变化回调
   */
  onChange?: (value: string) => void
  
  /**
   * 搜索提交回调
   */
  onSearch?: (value: string) => void
  
  /**
   * 占位符
   */
  placeholder?: string
  
  /**
   * 是否默认展开
   */
  defaultExpanded?: boolean
  
  /**
   * 是否禁用
   */
  disabled?: boolean
  
  /**
   * 尺寸
   */
  size?: "sm" | "md" | "lg"
  
  /**
   * 自定义类名
   */
  className?: string
  
  /**
   * 展开状态变化回调
   */
  onExpandedChange?: (expanded: boolean) => void

  /**
   * 展开方向
   */
  expandDirection?: "left" | "right"
}

/**
 * 可折叠搜索框组件
 * 
 * 支持展开和收起状态，默认显示搜索图标，点击后展开输入框
 */
export function CollapsibleSearch({
  value = "",
  onChange,
  onSearch,
  placeholder = "搜索...",
  defaultExpanded = false,
  disabled = false,
  size = "md",
  className,
  onExpandedChange,
  expandDirection = "right",
}: CollapsibleSearchProps) {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded)
  const [internalValue, setInternalValue] = useState(value)
  const inputRef = useRef<HTMLInputElement>(null)

  // 同步外部value
  useEffect(() => {
    setInternalValue(value)
  }, [value])

  // 尺寸映射
  const sizeClasses = {
    sm: "h-8",
    md: "h-9",
    lg: "h-10"
  }

  const iconSizeClasses = {
    sm: "h-3.5 w-3.5",
    md: "h-4 w-4", 
    lg: "h-5 w-5"
  }

  // 处理展开
  const handleExpand = () => {
    if (disabled) return
    
    setIsExpanded(true)
    onExpandedChange?.(true)
    
    // 延迟聚焦，确保动画完成
    setTimeout(() => {
      inputRef.current?.focus()
    }, 150)
  }

  // 处理收起
  const handleCollapse = () => {
    setIsExpanded(false)
    onExpandedChange?.(false)
    
    // 如果有值，触发搜索
    if (internalValue.trim()) {
      onSearch?.(internalValue)
    }
  }

  // 处理值变化
  const handleValueChange = (newValue: string) => {
    setInternalValue(newValue)
    onChange?.(newValue)
  }

  // 处理清空
  const handleClear = () => {
    setInternalValue("")
    onChange?.("")
    inputRef.current?.focus()
  }

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      onSearch?.(internalValue)
      handleCollapse()
    } else if (e.key === "Escape") {
      handleCollapse()
    }
  }

  // 处理点击外部
  const handleBlur = (e: React.FocusEvent) => {
    // 如果失焦不是因为点击清空按钮，则收起
    if (!e.relatedTarget?.closest('[data-search-clear]')) {
      handleCollapse()
    }
  }

  return (
    <div className={cn("relative", className)}>
      {!isExpanded ? (
        // 收起状态：只显示搜索图标
        <IconButtonWithBadge
          icon={Search}
          label="展开搜索"
          variant="ghost"
          size="sm"
          onClick={handleExpand}
          disabled={disabled}
          className={cn(sizeClasses[size], sizeClasses[size])}
        />
      ) : (
        // 展开状态：显示完整搜索框
        <div className={cn(
          "flex items-center bg-background border border-input rounded-md transition-all duration-200 ease-in-out",
          expandDirection === "left" ? "origin-right flex-row-reverse" : "origin-left",
          sizeClasses[size],
          "w-64" // 展开后的宽度
        )}>
          <Search className={cn(
            expandDirection === "left" ? "mr-3" : "ml-3",
            "text-muted-foreground flex-shrink-0",
            iconSizeClasses[size]
          )} />

          <Input
            ref={inputRef}
            value={internalValue}
            onChange={(e) => handleValueChange(e.target.value)}
            onKeyDown={handleKeyDown}
            onBlur={handleBlur}
            placeholder={placeholder}
            disabled={disabled}
            className="border-0 bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 h-full"
          />

          {internalValue && (
            <Button
              variant="ghost"
              size="sm"
              className="h-full w-8 p-0 flex-shrink-0"
              onClick={handleClear}
              data-search-clear
            >
              <X className={iconSizeClasses[size]} />
            </Button>
          )}
        </div>
      )}
    </div>
  )
}

/**
 * 默认配置
 */
export const DEFAULT_COLLAPSIBLE_SEARCH_CONFIG = {
  placeholder: "搜索...",
  defaultExpanded: false,
  size: "md" as const,
  expandDirection: "right" as const,
} as const

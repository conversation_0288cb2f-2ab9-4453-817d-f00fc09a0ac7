"use client"

import React from "react"
import { ComponentPreviewContainer } from "@/components/component-detail/preview-container"
import {
  BlogCard,
  ProductCardTemplate,
  EventCard,
  DashboardCard,
  TestimonialCard
} from "@/components/common-custom/card-templates"
import { Button } from "@/components/ui/button"
import { allExamples } from "./examples"

// ============================================================================
// API文档组件
// ============================================================================

function ApiDocs() {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="font-medium text-lg mb-2">卡片模板组件</h3>
        <p className="text-muted-foreground">
          提供多种预设卡片样式，适用于不同的数据展示场景。每个卡片组件都有独立的属性接口，支持自定义样式和交互功能。
        </p>
      </div>

      {/* BlogCard API */}
      <div>
        <h4 className="text-md font-medium mb-3">BlogCard 属性</h4>
        <div className="overflow-x-auto">
          <table className="w-full text-sm border-collapse border border-border">
            <thead>
              <tr className="border-b bg-muted/50">
                <th className="py-2 px-3 text-left font-medium">属性名</th>
                <th className="py-2 px-3 text-left font-medium">类型</th>
                <th className="py-2 px-3 text-left font-medium">默认值</th>
                <th className="py-2 px-3 text-left font-medium">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">title</td>
                <td className="py-2 px-3">string</td>
                <td className="py-2 px-3">-</td>
                <td className="py-2 px-3">文章标题</td>
              </tr>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">excerpt</td>
                <td className="py-2 px-3">string</td>
                <td className="py-2 px-3">-</td>
                <td className="py-2 px-3">文章摘要</td>
              </tr>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">author</td>
                <td className="py-2 px-3">object</td>
                <td className="py-2 px-3">-</td>
                <td className="py-2 px-3">作者信息 {`{name, avatar?}`}</td>
              </tr>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">publishDate</td>
                <td className="py-2 px-3">string</td>
                <td className="py-2 px-3">-</td>
                <td className="py-2 px-3">发布日期</td>
              </tr>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">readTime</td>
                <td className="py-2 px-3">string</td>
                <td className="py-2 px-3">-</td>
                <td className="py-2 px-3">阅读时间</td>
              </tr>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">category</td>
                <td className="py-2 px-3">string</td>
                <td className="py-2 px-3">-</td>
                <td className="py-2 px-3">文章分类</td>
              </tr>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">image</td>
                <td className="py-2 px-3">string</td>
                <td className="py-2 px-3">-</td>
                <td className="py-2 px-3">封面图片</td>
              </tr>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">stats</td>
                <td className="py-2 px-3">object</td>
                <td className="py-2 px-3">-</td>
                <td className="py-2 px-3">统计信息 {`{views?, likes?, comments?}`}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* ProductCardTemplate API */}
      <div>
        <h4 className="text-md font-medium mb-3">ProductCardTemplate 属性</h4>
        <div className="overflow-x-auto">
          <table className="w-full text-sm border-collapse border border-border">
            <thead>
              <tr className="border-b bg-muted/50">
                <th className="py-2 px-3 text-left font-medium">属性名</th>
                <th className="py-2 px-3 text-left font-medium">类型</th>
                <th className="py-2 px-3 text-left font-medium">默认值</th>
                <th className="py-2 px-3 text-left font-medium">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">data.name</td>
                <td className="py-2 px-3">string</td>
                <td className="py-2 px-3">-</td>
                <td className="py-2 px-3">产品名称</td>
              </tr>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">data.price</td>
                <td className="py-2 px-3">number</td>
                <td className="py-2 px-3">-</td>
                <td className="py-2 px-3">产品价格</td>
              </tr>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">data.originalPrice</td>
                <td className="py-2 px-3">number</td>
                <td className="py-2 px-3">-</td>
                <td className="py-2 px-3">原价</td>
              </tr>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">data.rating</td>
                <td className="py-2 px-3">number</td>
                <td className="py-2 px-3">-</td>
                <td className="py-2 px-3">评分</td>
              </tr>
              <tr className="border-b">
                <td className="py-2 px-3 font-mono">data.image</td>
                <td className="py-2 px-3">string</td>
                <td className="py-2 px-3">-</td>
                <td className="py-2 px-3">产品图片</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div>
        <h4 className="text-md font-medium mb-2">何时使用</h4>
        <ul className="list-disc list-inside text-muted-foreground space-y-1">
          <li>需要展示博客文章、新闻或内容摘要时</li>
          <li>电商产品展示和商品列表页面</li>
          <li>活动信息展示和事件列表</li>
          <li>仪表盘数据统计展示</li>
          <li>用户评价和推荐展示</li>
        </ul>
      </div>

      {/* 使用指南 */}
      <div>
        <h3 className="font-medium text-lg mb-2">使用指南</h3>
        <div className="space-y-4 text-sm">
          <div>
            <h4 className="font-medium mb-2">基本用法</h4>
            <p className="text-muted-foreground mb-2">
              最简单的使用方式，只需要提供必要的数据：
            </p>
            <pre className="bg-muted p-3 rounded-md overflow-x-auto">
              <code>{`<BlogCard
  title="文章标题"
  excerpt="文章摘要"
  author={{ name: "作者名" }}
  publishDate="2024-01-01"
  readTime="5分钟"
  category="技术"
/>`}</code>
            </pre>
          </div>

          <div>
            <h4 className="font-medium mb-2">自定义样式</h4>
            <p className="text-muted-foreground mb-2">
              通过 className 属性自定义卡片样式：
            </p>
            <pre className="bg-muted p-3 rounded-md overflow-x-auto">
              <code>{`<ProductCardTemplate
  data={productData}
  className="shadow-lg hover:shadow-xl transition-shadow"
/>`}</code>
            </pre>
          </div>

          <div>
            <h4 className="font-medium mb-2">事件处理</h4>
            <p className="text-muted-foreground mb-2">
              通过回调函数处理用户交互：
            </p>
            <pre className="bg-muted p-3 rounded-md overflow-x-auto">
              <code>{`<BlogCard
  title="文章标题"
  excerpt="文章摘要"
  author={{ name: "作者名" }}
  publishDate="2024-01-01"
  readTime="5分钟"
  category="技术"
  onReadMore={() => console.log('阅读更多')}
  onLike={() => console.log('点赞')}
  onComment={() => console.log('评论')}
/>`}</code>
            </pre>
          </div>

          <div>
            <h4 className="font-medium mb-2">最佳实践</h4>
            <div className="space-y-2 text-muted-foreground">
              <p>• 确保提供完整的数据结构，避免缺少必要字段</p>
              <p>• 使用合适的图片尺寸和格式以获得最佳显示效果</p>
              <p>• 为交互元素提供适当的回调函数</p>
              <p>• 在响应式布局中合理控制卡片的最大宽度</p>
              <p>• 保持数据格式的一致性，便于批量渲染</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function CardTemplatesExamplePage() {
  return (
    <ComponentPreviewContainer
      title="卡片模板 CardTemplates"
      description="预设的卡片模板组件集合，适用于各种内容展示场景。包含博客卡片、产品卡片、活动卡片、仪表盘卡片和推荐卡片等多种样式，支持自定义样式和交互功能。"
      whenToUse="当需要展示结构化内容时使用；当需要统一的卡片样式和布局时使用；当需要快速构建内容展示界面时使用；适用于博客文章、产品展示、活动信息、数据统计、用户评价等场景。"
      examples={allExamples}
      apiDocs={<ApiDocs />}
    />
  )
}
 
 
 
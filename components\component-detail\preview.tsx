"use client";

import React, { useState, useEffect, Component, ErrorInfo, ReactNode } from "react";
import { LiveProvider, LiveEditor, LiveError, LivePreview } from "react-live";
import { But<PERSON> } from "../ui/button";
import { Copy } from "lucide-react";
import { cn } from "@/lib/utils";

// 自定义错误边界组件
class PreviewErrorBoundary extends Component<
  { children: ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Preview error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="p-8 flex items-center justify-center min-h-[200px] text-center">
          <div className="text-red-500">
            <div className="text-sm font-medium mb-2">预览出错</div>
            <div className="text-xs text-muted-foreground">
              {this.state.error?.message || '组件渲染失败'}
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

interface ComponentPreviewProps {
  code: string;
  scope?: Record<string, any>;
  className?: string;
}

export function ComponentPreview({
  code,
  scope,
  className,
}: ComponentPreviewProps) {
  const [copied, setCopied] = useState(false);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    // 确保在客户端渲染
    setIsClient(true);
  }, []);

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(code);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error("Failed to copy code", err);
    }
  };

  // 处理代码示例，处理noInline模式下的代码
  const processedCode = code.includes('export default') 
    ? `${code.replace(/export\s+default\s+function\s+(\w+)/, 'function $1')}\n\nrender(<${code.match(/export\s+default\s+function\s+(\w+)/)?.[1] || 'Component'} />);`
    : code;

  return (
    <div className={cn("relative w-full", className)}>
      {/* 代码预览区域 */}
      {isClient && (
        <PreviewErrorBoundary>
          <LiveProvider
            code={processedCode}
            scope={{ ...scope, render: (value: React.ReactNode) => value }}
            noInline
            transformCode={(code) => {
              // 移除可能使用require的导入方式，而不是尝试替换
              return code
                .replace(/require\(['"](.*)['"]\)/g, '// import from $1')
                .replace(/import\s+.*\s+from\s+['"](.*)['"]/g, '// import from $1');
            }}
          >
            <div className="p-8 flex items-start justify-center min-h-[200px]">
              <div className="w-full max-w-none overflow-x-auto">
                <LivePreview />
              </div>
            </div>
            <LiveError className="text-xs text-red-500 mt-2 p-2 bg-red-50 rounded mx-6 mb-2" />
          </LiveProvider>
        </PreviewErrorBoundary>
      )}
    </div>
  );
} 
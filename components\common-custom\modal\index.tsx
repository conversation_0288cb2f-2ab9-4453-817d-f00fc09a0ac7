"use client"

/**
 * 模态框组件集合
 * 重新导出所有模态框相关组件
 */

// ============================================================================
// 统一导出 - 复杂组件按分类导出
// ============================================================================

// 主组件导出
export { Modal } from "./modal"
export { AlertDialogModal } from "./alert-dialog-modal"
export { FormModal } from "./form-modal"
export { InfoModal } from "./info-modal"

// 触发器组件导出
export { ModalTrigger, AlertDialogModalTrigger } from "./triggers"

// 类型导出
export * from "./types"

// 默认配置导出
export {
  DEFAULT_MODAL_CONFIG,
  DEFAULT_BUTTON_CONFIG,
  DEFAULT_ALERT_DIALOG_CONFIG,
  DEFAULT_FORM_MODAL_CONFIG,
  DEFAULT_INFO_MODAL_CONFIG,
  DEFAULT_TRIGGER_CONFIG
} from "./types"
"use client"

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { TrendingUp, TrendingDown } from "lucide-react"
import { cn } from "@/lib/utils"
import { StatCardItem } from "@/types/card-templates"

/**
 * 统计卡片组件
 * 用于展示各类统计数据，支持趋势显示
 */
export function StatCard({
  title,
  icon: Icon,
  value,
  change,
  trend = "neutral",
  description,
  className,
  onClick
}: StatCardItem) {
  return (
    <Card 
      className={cn(
        "transition-all hover:shadow-md",
        onClick ? "cursor-pointer" : "",
        className
      )} 
      onClick={onClick}
    >
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {Icon && <Icon className="h-4 w-4 text-muted-foreground" />}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {change && (
          <div className={`flex items-center text-xs ${
            trend === "up" ? "text-green-600" : trend === "down" ? "text-red-600" : "text-muted-foreground"
          }`}>
            {trend === "up" ? (
              <TrendingUp className="h-3 w-3 mr-1" />
            ) : trend === "down" ? (
              <TrendingDown className="h-3 w-3 mr-1" />
            ) : null}
            {change} {description && <span>{description}</span>}
          </div>
        )}
      </CardContent>
    </Card>
  )
} 
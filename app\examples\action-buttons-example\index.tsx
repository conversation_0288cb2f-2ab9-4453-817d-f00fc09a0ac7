import * as React from "react"
import { ActionButtons, ActionItem } from "@/components/common-custom/action-buttons"
import { Edit, Trash2, Eye, Copy } from "lucide-react"

export function ActionButtonsExample() {
  // 基础操作按钮配置
  const basicActions: ActionItem[] = [
    { id: "edit", label: "编辑", icon: <Edit className="w-4 h-4" />, onClick: () => console.log("编辑") },
    { id: "view", label: "查看", icon: <Eye className="w-4 h-4" />, variant: "outline", onClick: () => console.log("查看") },
    { id: "delete", label: "删除", icon: <Trash2 className="w-4 h-4" />, destructive: true, onClick: () => console.log("删除") },
  ]

  // 删除确认配置
  const deleteConfirmProps = {
    title: "确认删除",
    description: "此操作无法撤销。这将永久删除该项目及其所有相关数据。",
    onConfirm: () => console.log("确认删除"),
  }

  return (
    <ActionButtons 
      actions={basicActions} 
      variant="default" 
      deleteConfirmProps={deleteConfirmProps}
    />
  )
} 
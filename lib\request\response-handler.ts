/**
 * 响应处理工具函数
 * 提供处理API响应数据和异常的通用方法
 */
import { toast } from "sonner";
import { ApiResponse } from "@/types/api";

/**
 * 响应处理选项接口
 */
export interface ResponseProcessOptions {
  successMessage?: string; // 成功提示消息
  errorMessage?: string;   // 错误提示消息
  showSuccessToast?: boolean; // 是否显示成功提示
  showErrorToast?: boolean;   // 是否显示错误提示
  customCodeCheck?: (code: string | number) => boolean; // 自定义状态码检查
}

/**
 * 处理API响应数据
 * @param response API响应对象
 * @param options 处理选项
 * @returns 处理后的数据或null
 */
export const processResponse = <T>(
  response: ApiResponse<T> | undefined | null,
  options: ResponseProcessOptions = {}
): T | null => {
  const {
    successMessage = "操作成功",
    errorMessage = "请求异常",
    showSuccessToast = false,
    showErrorToast = true,
    customCodeCheck
  } = options;

  // 响应不存在
  if (!response) {
    if (showErrorToast) {
      toast.error(errorMessage);
    }
    return null;
  }

  // 自定义状态码检查函数
  const isSuccess = customCodeCheck 
    ? customCodeCheck(response.code)
    : (response.code === "200" || response.code === 200);

  // 检查响应状态码
  if (isSuccess) {
    // 请求成功
    if (showSuccessToast && successMessage) {
      toast.success(successMessage);
    }
    return response.data;
  } else {
    // 请求失败
    if (showErrorToast) {
      // 优先使用响应中的错误消息
      const msg = response.msg || errorMessage;
      toast.error(msg);
    }
    return null;
  }
};

/**
 * 处理API响应数据并确保返回数组
 * @param response API响应对象
 * @param options 处理选项
 * @returns 处理后的数组数据或空数组
 */
export const processArrayResponse = <T>(
  response: ApiResponse<T[]> | undefined | null,
  options: ResponseProcessOptions = {}
): T[] => {
  const result = processResponse<T[]>(response, options);
  
  // 确保返回数组
  if (Array.isArray(result)) {
    return result;
  }
  
  return [];
};

/**
 * 处理API异常
 * @param error 捕获的异常
 * @param fallbackMessage 默认错误消息
 * @param showToast 是否显示Toast提示
 * @returns 统一处理后的错误对象
 */
export const handleApiError = (
  error: unknown, 
  fallbackMessage: string = "操作失败", 
  showToast: boolean = true
): { message: string, error: unknown } => {
  let errorMessage = fallbackMessage;

  // 提取错误消息
  if (error instanceof Error) {
    errorMessage = error.message || fallbackMessage;
  } else if (typeof error === 'object' && error !== null) {
    // 尝试从API错误响应中提取消息
    const errorObj = error as any;
    if (errorObj.message) {
      errorMessage = errorObj.message;
    } else if (errorObj.msg) {
      errorMessage = errorObj.msg;
    } else if (errorObj.data?.message) {
      errorMessage = errorObj.data.message;
    } else if (errorObj.data?.msg) {
      errorMessage = errorObj.data.msg;
    } else if (errorObj.response?.data?.message) {
      errorMessage = errorObj.response.data.message;
    } else if (errorObj.response?.data?.msg) {
      errorMessage = errorObj.response.data.msg;
    } else if (errorObj.info?.errorMessage) {
      errorMessage = errorObj.info.errorMessage;
    }
  }

  // 显示错误提示
  if (showToast) {
    toast.error(errorMessage);
  }

  // 打印错误到控制台（开发环境可用）
  console.error("[API Error]", error);

  return {
    message: errorMessage,
    error
  };
};

/**
 * 安全执行异步操作的包装函数
 * @param asyncFn 异步函数
 * @param errorMessage 错误消息
 * @param options 选项
 */
export const safeExecute = async <T>(
  asyncFn: () => Promise<T>,
  errorMessage: string = "操作执行失败",
  options: {
    showSuccessToast?: boolean;
    successMessage?: string;
    showErrorToast?: boolean;
    onSuccess?: (result: T) => void;
    onError?: (error: any) => void;
    retryCount?: number; // 重试次数
  } = {}
): Promise<T | null> => {
  const {
    showSuccessToast = false,
    successMessage = "操作成功",
    showErrorToast = true,
    onSuccess,
    onError,
    retryCount = 0
  } = options;

  let lastError: any = null;
  let attempts = 0;

  while (attempts <= retryCount) {
    try {
      const result = await asyncFn();
      
      if (showSuccessToast) {
        toast.success(successMessage);
      }
      
      if (onSuccess) {
        onSuccess(result);
      }
      
      return result;
    } catch (error) {
      lastError = error;
      attempts++;
      
      // 最后一次尝试失败才显示错误
      if (attempts > retryCount) {
        handleApiError(error, errorMessage, showErrorToast);
        
        if (onError) {
          onError(error);
        }
      } else {
        // 非最后一次尝试失败，可以在控制台记录重试信息
        console.warn(`请求失败，正在进行第${attempts}次重试`, error);
      }
    }
  }
  
  return null;
}; 
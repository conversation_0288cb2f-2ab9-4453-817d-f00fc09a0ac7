/**
 * 分页组件示例代码集合
 * 
 * 按照新规范管理示例代码，避免在页面文件中硬编码
 */

import React from "react"
import { Pagination } from "@/components/common-custom/pagination"

// ============================================================================
// 基础示例
// ============================================================================

export const basicExample = {
  id: "basic-pagination",
  title: "基础分页",
  description: "基本的分页组件，包含页码导航和总数显示",
  code: `
import React, { useState } from "react";
import { Pagination } from "@/components/common-custom/pagination";

function BasicPaginationExample() {
  const [pageIndex, setPageIndex] = useState(0);
  const [pageSize, setPageSize] = useState(20);
  
  const totalItems = 150;
  const pageCount = Math.ceil(totalItems / pageSize);
  
  return (
    <div className="space-y-4">
      <div className="p-4 border rounded-md bg-muted/50">
        <p className="text-sm">
          <strong>当前状态:</strong> 第 {pageIndex + 1} 页，每页 {pageSize} 条，共 {totalItems} 条记录
        </p>
      </div>
      
      <Pagination
        pageIndex={pageIndex}
        pageCount={pageCount}
        pageSize={pageSize}
        totalItems={totalItems}
        onPageChange={setPageIndex}
        onPageSizeChange={(size) => {
          setPageSize(size);
          setPageIndex(0); // 重置到第一页
        }}
      />
    </div>
  );
}

render(<BasicPaginationExample />);
  `,
  scope: { Pagination, React, useState: React.useState },
}

// ============================================================================
// 自定义配置示例
// ============================================================================

export const customConfigExample = {
  id: "custom-config-pagination",
  title: "自定义配置",
  description: "展示不同配置选项的分页组件",
  code: `
import React, { useState } from "react";
import { Pagination } from "@/components/common-custom/pagination";

function CustomConfigPaginationExample() {
  const [pageIndex, setPageIndex] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  
  const totalItems = 87;
  const pageCount = Math.ceil(totalItems / pageSize);
  
  return (
    <div className="space-y-6">
      <div>
        <h4 className="text-sm font-medium mb-2">自定义每页数量选项</h4>
        <Pagination
          pageIndex={pageIndex}
          pageCount={pageCount}
          pageSize={pageSize}
          totalItems={totalItems}
          pageSizeOptions={[5, 10, 15, 25]}
          onPageChange={setPageIndex}
          onPageSizeChange={(size) => {
            setPageSize(size);
            setPageIndex(0);
          }}
        />
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">隐藏部分功能</h4>
        <Pagination
          pageIndex={pageIndex}
          pageCount={pageCount}
          pageSize={pageSize}
          totalItems={totalItems}
          showPageSizeChanger={false}
          showQuickJumper={false}
          onPageChange={setPageIndex}
          onPageSizeChange={setPageSize}
        />
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">禁用状态</h4>
        <Pagination
          pageIndex={pageIndex}
          pageCount={pageCount}
          pageSize={pageSize}
          totalItems={totalItems}
          disabled={true}
          onPageChange={setPageIndex}
          onPageSizeChange={setPageSize}
        />
      </div>
    </div>
  );
}

render(<CustomConfigPaginationExample />);
  `,
  scope: { Pagination, React, useState: React.useState },
}

// ============================================================================
// 大数据量示例
// ============================================================================

export const largeDataExample = {
  id: "large-data-pagination",
  title: "大数据量分页",
  description: "处理大量数据的分页场景",
  code: `
import React, { useState } from "react";
import { Pagination } from "@/components/common-custom/pagination";

function LargeDataPaginationExample() {
  const [pageIndex, setPageIndex] = useState(0);
  const [pageSize, setPageSize] = useState(50);
  
  const totalItems = 10000;
  const pageCount = Math.ceil(totalItems / pageSize);
  
  return (
    <div className="space-y-4">
      <div className="p-4 border rounded-md bg-blue-50">
        <h4 className="font-medium mb-2">大数据量场景</h4>
        <p className="text-sm text-muted-foreground">
          模拟处理 10,000 条记录的分页场景
        </p>
      </div>
      
      <Pagination
        pageIndex={pageIndex}
        pageCount={pageCount}
        pageSize={pageSize}
        totalItems={totalItems}
        pageSizeOptions={[20, 50, 100, 200]}
        onPageChange={(page) => {
          setPageIndex(page);
          console.log(\`跳转到第 \${page + 1} 页\`);
        }}
        onPageSizeChange={(size) => {
          setPageSize(size);
          setPageIndex(0);
          console.log(\`每页显示 \${size} 条\`);
        }}
      />
      
      <div className="text-xs text-muted-foreground">
        当前显示第 {pageIndex * pageSize + 1} - {Math.min((pageIndex + 1) * pageSize, totalItems)} 条记录
      </div>
    </div>
  );
}

render(<LargeDataPaginationExample />);
  `,
  scope: { Pagination, React, useState: React.useState },
}

// ============================================================================
// 响应式示例
// ============================================================================

export const responsiveExample = {
  id: "responsive-pagination",
  title: "响应式分页",
  description: "展示分页组件在不同屏幕尺寸下的表现",
  code: `
import React, { useState } from "react";
import { Pagination } from "@/components/common-custom/pagination";

function ResponsivePaginationExample() {
  const [pageIndex, setPageIndex] = useState(2);
  const [pageSize, setPageSize] = useState(15);
  
  const totalItems = 120;
  const pageCount = Math.ceil(totalItems / pageSize);
  
  return (
    <div className="space-y-4">
      <div className="p-4 border rounded-md bg-green-50">
        <h4 className="font-medium mb-2">响应式设计</h4>
        <p className="text-sm text-muted-foreground">
          在小屏幕上，每页数量选择器和快速跳转会自动隐藏，页码信息会重新排列
        </p>
      </div>
      
      <div className="border rounded-md p-4">
        <Pagination
          pageIndex={pageIndex}
          pageCount={pageCount}
          pageSize={pageSize}
          totalItems={totalItems}
          onPageChange={setPageIndex}
          onPageSizeChange={(size) => {
            setPageSize(size);
            setPageIndex(0);
          }}
          className="border-t pt-4"
        />
      </div>
      
      <div className="text-xs text-muted-foreground">
        💡 提示：调整浏览器窗口大小查看响应式效果
      </div>
    </div>
  );
}

render(<ResponsivePaginationExample />);
  `,
  scope: { Pagination, React, useState: React.useState },
}

// ============================================================================
// 实际应用示例
// ============================================================================

export const practicalExample = {
  id: "practical-pagination",
  title: "实际应用场景",
  description: "模拟真实的数据列表分页场景",
  code: `
import React, { useState, useMemo } from "react";
import { Pagination } from "@/components/common-custom/pagination";

function PracticalPaginationExample() {
  const [pageIndex, setPageIndex] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  
  // 模拟用户数据
  const allUsers = useMemo(() => 
    Array.from({ length: 45 }, (_, i) => ({
      id: i + 1,
      name: \`用户\${i + 1}\`,
      email: \`user\${i + 1}@example.com\`,
      role: ['管理员', '编辑', '查看者'][i % 3],
    })), []
  );
  
  const totalItems = allUsers.length;
  const pageCount = Math.ceil(totalItems / pageSize);
  
  // 当前页数据
  const currentPageData = useMemo(() => {
    const start = pageIndex * pageSize;
    const end = start + pageSize;
    return allUsers.slice(start, end);
  }, [allUsers, pageIndex, pageSize]);
  
  return (
    <div className="space-y-4">
      <div className="border rounded-md">
        <div className="p-4 border-b bg-muted/50">
          <h4 className="font-medium">用户列表</h4>
        </div>
        
        <div className="divide-y">
          {currentPageData.map((user) => (
            <div key={user.id} className="p-4 flex items-center justify-between">
              <div>
                <div className="font-medium">{user.name}</div>
                <div className="text-sm text-muted-foreground">{user.email}</div>
              </div>
              <div className="text-sm px-2 py-1 bg-blue-100 text-blue-800 rounded">
                {user.role}
              </div>
            </div>
          ))}
        </div>
        
        <div className="border-t">
          <Pagination
            pageIndex={pageIndex}
            pageCount={pageCount}
            pageSize={pageSize}
            totalItems={totalItems}
            onPageChange={setPageIndex}
            onPageSizeChange={(size) => {
              setPageSize(size);
              setPageIndex(0);
            }}
          />
        </div>
      </div>
    </div>
  );
}

render(<PracticalPaginationExample />);
  `,
  scope: { Pagination, React, useState: React.useState, useMemo: React.useMemo },
}

// ============================================================================
// 统一导出所有示例
// ============================================================================

export const allExamples = [
  basicExample,
  customConfigExample,
  largeDataExample,
  responsiveExample,
  practicalExample,
]

// ============================================================================
// 按类别导出示例
// ============================================================================

export const basicExamples = [basicExample, customConfigExample]
export const advancedExamples = [largeDataExample, responsiveExample]
export const practicalExamples = [practicalExample]

// ============================================================================
// 示例元数据
// ============================================================================

export const exampleMetadata = {
  total: allExamples.length,
  categories: {
    basic: basicExamples.length,
    advanced: advancedExamples.length,
    practical: practicalExamples.length,
  },
  tags: ["pagination", "navigation", "data", "table", "responsive"],
  lastUpdated: "2024-01-01",
}

"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { use } from "react"
import { 
  Calendar, 
  Clock, 
  Edit, 
  FileText, 
  MoreHorizontal, 
  Users, 
  CheckCircle2,
  CircleDashed,
  ArrowLeft,
  ChevronRight,
  BarChart3,
  MessageSquare,
  ListTodo,
  Folder,
  Share2,
  Plus
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuSeparator,
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu"
import { HeaderWithBreadcrumb, type BreadcrumbItem } from "@/components/project-custom/breadcrumb"
import { BackButton } from "@/components/common-custom/back-button"

// 模拟项目详细数据
const mockProject = {
  id: "1",
  name: "Web应用重构",
  description: "重构现有Web应用的前端架构，提高性能和用户体验，解决现有架构中的技术债务问题。通过引入新的前端框架和组件库，实现更好的用户交互体验和更高的开发效率。",
  status: "进行中",
  progress: 65,
  dueDate: "2024-09-15",
  createdAt: "2024-03-01",
  updatedAt: "2024-06-05",
  startDate: "2024-03-15",
  teamId: "1",
  teamName: "前端团队",
  priority: "高",
  tags: ["前端", "React", "重构"],
  members: [
    { id: "1", name: "张三", role: "项目经理", avatar: "/avatars/user1.png" },
    { id: "2", name: "李四", role: "前端开发", avatar: "/avatars/user2.png" },
    { id: "3", name: "王五", role: "UI设计师", avatar: "/avatars/user3.png" },
    { id: "4", name: "赵六", role: "测试工程师", avatar: "/avatars/user4.png" },
  ],
  tasks: {
    total: 24,
    completed: 15,
    inProgress: 6,
    todo: 3
  },
  milestones: [
    { id: "1", name: "需求分析", dueDate: "2024-03-30", status: "已完成" },
    { id: "2", name: "架构设计", dueDate: "2024-04-15", status: "已完成" },
    { id: "3", name: "核心功能实现", dueDate: "2024-07-30", status: "进行中" },
    { id: "4", name: "测试与优化", dueDate: "2024-08-30", status: "未开始" },
    { id: "5", name: "部署上线", dueDate: "2024-09-15", status: "未开始" }
  ],
  recentActivities: [
    { id: "1", user: "张三", action: "更新了项目进度", date: "2024-06-05 14:30" },
    { id: "2", user: "李四", action: "完成了任务: 优化首页加载性能", date: "2024-06-04 10:15" },
    { id: "3", user: "王五", action: "添加了新设计文件", date: "2024-06-03 16:45" }
  ]
};

interface ProjectDetailPageProps {
  params: Promise<{ id: string }> | { id: string };
}

export default function ProjectDetailPage({ params }: ProjectDetailPageProps) {
  const router = useRouter()
  
  // 使用React.use解包params
  const resolvedParams = params instanceof Promise ? use(params) : params;
  const projectId = resolvedParams.id;
  
  const [activeTab, setActiveTab] = useState("overview");
  
  // 面包屑项目
  const breadcrumbItems: BreadcrumbItem[] = [
    { label: "首页", href: "/" },
    { label: "项目管理", href: "/projects" },
    { label: mockProject.name, isCurrent: true }
  ];
  
  // 获取状态徽章颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case "进行中": return "bg-blue-500";
      case "已完成": return "bg-green-500";
      case "规划中": return "bg-amber-500";
      case "已暂停": return "bg-slate-500";
      case "未开始": return "bg-slate-500";
      default: return "bg-gray-500";
    }
  };
  
  // 获取优先级标签颜色
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "高": return "text-red-600 bg-red-100";
      case "中": return "text-amber-600 bg-amber-100";
      case "低": return "text-blue-600 bg-blue-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };
  
  // 格式化日期
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  };
  
  return (
    <div className="flex min-h-screen w-full flex-col bg-gradient-to-b from-background to-background/80">
      <HeaderWithBreadcrumb items={breadcrumbItems} />
      
      <div className="p-4 md:p-6 max-w-7xl mx-auto w-full">
        <main className="space-y-6">
          {/* 返回按钮和标题 */}
          <div className="flex items-center justify-between gap-4">
            <div className="flex items-center gap-3">
              <BackButton
                href="/projects"
                onNavigate={(href) => router.push(href)}
              />
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <h1 className="text-2xl font-bold tracking-tight">{mockProject.name}</h1>
                  <Badge className={getStatusColor(mockProject.status)}>
                    {mockProject.status}
                  </Badge>
                </div>
                <div className="flex items-center gap-3 text-sm text-muted-foreground">
                  <div className="flex items-center gap-1.5">
                    <Calendar className="h-3.5 w-3.5" />
                    <span>开始: {formatDate(mockProject.startDate)}</span>
                  </div>
                  <div className="flex items-center gap-1.5">
                    <Clock className="h-3.5 w-3.5" />
                    <span>截止: {formatDate(mockProject.dueDate)}</span>
                  </div>
                  <div className="flex items-center gap-1.5">
                    <Users className="h-3.5 w-3.5" />
                    <span>{mockProject.members.length}位成员</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="flex gap-2">
              <Button variant="outline" className="cursor-pointer">
                <Share2 className="h-4 w-4 mr-1.5" />
                分享
              </Button>
              <Button className="cursor-pointer">
                <Edit className="h-4 w-4 mr-1.5" />
                编辑项目
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="icon" className="h-9 w-9 cursor-pointer">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem className="cursor-pointer">导出项目报告</DropdownMenuItem>
                  <DropdownMenuItem className="cursor-pointer">复制项目链接</DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem className="cursor-pointer text-red-600">归档项目</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
          
          {/* 标签导航 */}
          <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab} className="space-y-4">
            <TabsList className="bg-muted/40 p-0 h-10">
              <TabsTrigger 
                value="overview" 
                className={`rounded-none border-b-2 ${
                  activeTab === "overview" 
                    ? "border-primary" 
                    : "border-transparent"
                } px-4 h-10 data-[state=active]:bg-background`}
              >
                概览
              </TabsTrigger>
              <TabsTrigger 
                value="tasks" 
                className={`rounded-none border-b-2 ${
                  activeTab === "tasks" 
                    ? "border-primary" 
                    : "border-transparent"
                } px-4 h-10 data-[state=active]:bg-background`}
              >
                任务
              </TabsTrigger>
              <TabsTrigger 
                value="members" 
                className={`rounded-none border-b-2 ${
                  activeTab === "members" 
                    ? "border-primary" 
                    : "border-transparent"
                } px-4 h-10 data-[state=active]:bg-background`}
              >
                成员
              </TabsTrigger>
              <TabsTrigger 
                value="files" 
                className={`rounded-none border-b-2 ${
                  activeTab === "files" 
                    ? "border-primary" 
                    : "border-transparent"
                } px-4 h-10 data-[state=active]:bg-background`}
              >
                文件
              </TabsTrigger>
              <TabsTrigger 
                value="activity" 
                className={`rounded-none border-b-2 ${
                  activeTab === "activity" 
                    ? "border-primary" 
                    : "border-transparent"
                } px-4 h-10 data-[state=active]:bg-background`}
              >
                动态
              </TabsTrigger>
            </TabsList>
            
            {/* 概览内容 */}
            <TabsContent value="overview" className="space-y-6">
              {/* 项目描述和进度 */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div className="lg:col-span-2 space-y-6">
                  {/* 项目描述卡片 */}
                  <Card className="border border-border/30">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">项目描述</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-muted-foreground">{mockProject.description}</p>
                      
                      <div className="flex flex-wrap gap-1 mt-4">
                        {mockProject.tags.map((tag, index) => (
                          <Badge key={index} variant="outline" className="bg-muted/40">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                  
                  {/* 里程碑卡片 */}
                  <Card className="border border-border/30">
                    <CardHeader className="pb-2">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-lg">里程碑</CardTitle>
                        <Button variant="outline" size="sm" className="h-8 cursor-pointer">
                          <Plus className="h-3.5 w-3.5 mr-1" />
                          添加里程碑
                        </Button>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {mockProject.milestones.map((milestone, index) => (
                          <div key={milestone.id} className="flex items-center gap-4">
                            <div className="relative flex items-center justify-center">
                              <div className={`h-8 w-8 rounded-full flex items-center justify-center ${
                                milestone.status === "已完成" 
                                  ? "bg-green-100" 
                                  : milestone.status === "进行中"
                                    ? "bg-blue-100"
                                    : "bg-slate-100"
                              }`}>
                                {milestone.status === "已完成" ? (
                                  <CheckCircle2 className="h-4 w-4 text-green-600" />
                                ) : milestone.status === "进行中" ? (
                                  <CircleDashed className="h-4 w-4 text-blue-600" />
                                ) : (
                                  <div className="h-2 w-2 rounded-full bg-slate-400" />
                                )}
                              </div>
                              {index < mockProject.milestones.length - 1 && (
                                <div className={`absolute top-8 w-px h-8 ${
                                  milestone.status === "已完成" 
                                    ? "bg-green-200" 
                                    : "bg-slate-200"
                                }`} />
                              )}
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center justify-between">
                                <div className="font-medium">{milestone.name}</div>
                                <Badge className={getStatusColor(milestone.status)}>
                                  {milestone.status}
                                </Badge>
                              </div>
                              <div className="text-sm text-muted-foreground mt-1">
                                截止日期: {formatDate(milestone.dueDate)}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>
                
                {/* 右侧信息卡片 */}
                <div className="space-y-6">
                  {/* 进度卡片 */}
                  <Card className="border border-border/30">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">项目进度</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-lg font-bold">{mockProject.progress}%</span>
                        <Badge variant="outline" className={getPriorityColor(mockProject.priority)}>
                          {mockProject.priority}优先级
                        </Badge>
                      </div>
                      <Progress value={mockProject.progress} className="h-2" />
                      
                      <div className="grid grid-cols-2 gap-4 mt-6">
                        <div className="space-y-1">
                          <div className="text-sm text-muted-foreground">开始日期</div>
                          <div className="font-medium">{formatDate(mockProject.startDate)}</div>
                        </div>
                        <div className="space-y-1">
                          <div className="text-sm text-muted-foreground">截止日期</div>
                          <div className="font-medium">{formatDate(mockProject.dueDate)}</div>
                        </div>
                        <div className="space-y-1">
                          <div className="text-sm text-muted-foreground">创建时间</div>
                          <div className="font-medium">{formatDate(mockProject.createdAt)}</div>
                        </div>
                        <div className="space-y-1">
                          <div className="text-sm text-muted-foreground">最后更新</div>
                          <div className="font-medium">{formatDate(mockProject.updatedAt)}</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                  
                  {/* 任务状态卡片 */}
                  <Card className="border border-border/30">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">任务概览</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <div className="h-3 w-3 rounded-full bg-green-500"></div>
                            <span>已完成</span>
                          </div>
                          <span className="font-medium">{mockProject.tasks.completed}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <div className="h-3 w-3 rounded-full bg-blue-500"></div>
                            <span>进行中</span>
                          </div>
                          <span className="font-medium">{mockProject.tasks.inProgress}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <div className="h-3 w-3 rounded-full bg-slate-500"></div>
                            <span>待办</span>
                          </div>
                          <span className="font-medium">{mockProject.tasks.todo}</span>
                        </div>
                        <Separator />
                        <div className="flex items-center justify-between font-medium">
                          <span>总计</span>
                          <span>{mockProject.tasks.total}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                  
                  {/* 团队成员卡片 */}
                  <Card className="border border-border/30">
                    <CardHeader className="pb-2">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-lg">团队成员</CardTitle>
                        <Button variant="outline" size="sm" className="h-8 cursor-pointer">
                          <Plus className="h-3.5 w-3.5 mr-1" />
                          添加成员
                        </Button>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {mockProject.members.map(member => (
                          <div key={member.id} className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <Avatar className="h-8 w-8">
                                <AvatarFallback>{member.name[0]}</AvatarFallback>
                              </Avatar>
                              <div>
                                <div className="font-medium">{member.name}</div>
                                <div className="text-xs text-muted-foreground">{member.role}</div>
                              </div>
                            </div>
                            <Button variant="ghost" size="icon" className="h-7 w-7 cursor-pointer">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
              
              {/* 最近活动 */}
              <Card className="border border-border/30">
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">最近活动</CardTitle>
                    <Button variant="link" size="sm" className="h-8 cursor-pointer text-primary">
                      查看全部
                      <ChevronRight className="h-3.5 w-3.5 ml-1" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {mockProject.recentActivities.map(activity => (
                      <div key={activity.id} className="flex items-start gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarFallback>{activity.user[0]}</AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <div>
                            <span className="font-medium">{activity.user}</span>
                            <span className="text-muted-foreground"> {activity.action}</span>
                          </div>
                          <div className="text-xs text-muted-foreground mt-1">{activity.date}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            {/* 其他标签页内容 - 为简洁起见，仅展示占位信息 */}
            <TabsContent value="tasks">
              <Card className="border border-border/30">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>项目任务</CardTitle>
                    <Button className="cursor-pointer">
                      <Plus className="h-4 w-4 mr-1.5" />
                      添加任务
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-center py-8 text-muted-foreground">
                    <div className="text-center">
                      <ListTodo className="h-10 w-10 mx-auto mb-3" />
                      <p>任务管理功能即将上线</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="members">
              <Card className="border border-border/30">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>团队成员</CardTitle>
                    <Button className="cursor-pointer">
                      <Plus className="h-4 w-4 mr-1.5" />
                      添加成员
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-center py-8 text-muted-foreground">
                    <div className="text-center">
                      <Users className="h-10 w-10 mx-auto mb-3" />
                      <p>成员管理功能即将上线</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="files">
              <Card className="border border-border/30">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>项目文件</CardTitle>
                    <Button className="cursor-pointer">
                      <Plus className="h-4 w-4 mr-1.5" />
                      上传文件
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-center py-8 text-muted-foreground">
                    <div className="text-center">
                      <FileText className="h-10 w-10 mx-auto mb-3" />
                      <p>文件管理功能即将上线</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="activity">
              <Card className="border border-border/30">
                <CardHeader>
                  <CardTitle>项目动态</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-center py-8 text-muted-foreground">
                    <div className="text-center">
                      <BarChart3 className="h-10 w-10 mx-auto mb-3" />
                      <p>项目动态功能即将上线</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </div>
  )
} 
/**
 * 用户管理接口
 * 提供用户相关的API调用功能
 */
import apiClient from './requestApi';
import { ApiResponse, PageResult } from '@/types/api';
import { User } from '@/types/user';
import { toast } from "sonner";
import { processArrayResponse, processResponse } from '@/lib/request';

// API基础路径
const BASE_URL = '/api/user';

// 用户查询参数类型
export interface UserQueryParams {
  username?: string;
  realName?: string;
  phone?: string;
  email?: string;
  status?: number;
  pageNum: number;
  pageSize: number;
}

// 用户创建参数类型
export interface UserCreateParams {
  username: string;
  password: string;
  realName?: string;
  email?: string;
  phone?: string;
  avatar?: string;
  gender?: number;
  status?: number;
  departmentId?: number;
  roleIds?: number[];
}

// 用户更新参数类型
export interface UserUpdateParams {
  id: number;
  realName?: string;
  email?: string;
  phone?: string;
  avatar?: string;
  gender?: number;
  status?: number;
  departmentId?: number;
}

/**
 * 分页查询用户
 * @param params 查询参数
 * @returns 用户分页数据
 */
export const getUserPageRequest = async (params: UserQueryParams): Promise<PageResult<User>> => {
  try {
    const response = await apiClient.post<ApiResponse<PageResult<User>>>(`${BASE_URL}/page`, params);
    
    return processResponse(response, {
      errorMessage: "获取用户列表失败",
      showErrorToast: true
    }) || {
      total: 0,
      list: [],
      pageNum: params.pageNum,
      pageSize: params.pageSize,
      pages: 0
    };
  } catch (error) {
    console.error("获取用户列表失败", error);
    toast.error("获取用户列表失败");
    return {
      total: 0,
      list: [],
      pageNum: params.pageNum,
      pageSize: params.pageSize,
      pages: 0
    };
  }
};

/**
 * 创建用户
 * @param params 用户数据
 * @returns 创建结果
 */
export const createUserRequest = async (params: UserCreateParams): Promise<number | null> => {
  try {
    const response = await apiClient.post<ApiResponse<number>>(`${BASE_URL}/create`, params);
    
    return processResponse(response, {
      successMessage: "用户创建成功",
      errorMessage: "用户创建失败",
      showSuccessToast: true,
      showErrorToast: true
    });
  } catch (error) {
    console.error("创建用户失败", error);
    toast.error("创建用户失败");
    return null;
  }
};

/**
 * 更新用户
 * @param params 用户数据
 * @returns 更新结果
 */
export const updateUserRequest = async (params: UserUpdateParams): Promise<boolean> => {
  try {
    const response = await apiClient.put<ApiResponse<boolean>>(`${BASE_URL}/update`, params);
    
    return processResponse(response, {
      successMessage: "用户更新成功",
      errorMessage: "用户更新失败",
      showSuccessToast: true,
      showErrorToast: true
    }) || false;
  } catch (error) {
    console.error("更新用户失败", error);
    toast.error("更新用户失败");
    return false;
  }
};

/**
 * 删除用户
 * @param id 用户ID
 * @returns 删除结果
 */
export const deleteUserRequest = async (id: number): Promise<boolean> => {
  try {
    const response = await apiClient.delete<ApiResponse<boolean>>(`${BASE_URL}/delete?id=${id}`);
    
    return processResponse(response, {
      successMessage: "用户删除成功",
      errorMessage: "用户删除失败",
      showSuccessToast: true,
      showErrorToast: true
    }) || false;
  } catch (error) {
    console.error("删除用户失败", error);
    toast.error("删除用户失败");
    return false;
  }
};

/**
 * 获取用户详情
 * @param id 用户ID
 * @returns 用户详情
 */
export const getUserByIdRequest = async (id: number): Promise<User | null> => {
  try {
    const response = await apiClient.get<ApiResponse<User>>(`${BASE_URL}/getById?id=${id}`);
    
    return processResponse(response, {
      errorMessage: "获取用户详情失败",
      showErrorToast: true
    });
  } catch (error) {
    console.error("获取用户详情失败", error);
    toast.error("获取用户详情失败");
    return null;
  }
};

/**
 * 修改用户密码
 * @param userId 用户ID
 * @param oldPassword 旧密码
 * @param newPassword 新密码
 * @returns 修改结果
 */
export const changePasswordRequest = async (userId: number, oldPassword: string, newPassword: string): Promise<boolean> => {
  try {
    const response = await apiClient.post<ApiResponse<boolean>>(`${BASE_URL}/changePassword`, {
      userId,
      oldPassword,
      newPassword
    });
    
    return processResponse(response, {
      successMessage: "密码修改成功",
      errorMessage: "密码修改失败",
      showSuccessToast: true,
      showErrorToast: true
    }) || false;
  } catch (error) {
    console.error("修改密码失败", error);
    toast.error("修改密码失败");
    return false;
  }
};

/**
 * 重置用户密码
 * @param userId 用户ID
 * @param newPassword 新密码
 * @returns 重置结果
 */
export const resetPasswordRequest = async (userId: number, newPassword: string): Promise<boolean> => {
  try {
    const response = await apiClient.post<ApiResponse<boolean>>(`${BASE_URL}/resetPassword`, {
      userId,
      newPassword
    });
    
    return processResponse(response, {
      successMessage: "密码重置成功",
      errorMessage: "密码重置失败",
      showSuccessToast: true,
      showErrorToast: true
    }) || false;
  } catch (error) {
    console.error("重置密码失败", error);
    toast.error("重置密码失败");
    return false;
  }
};

/**
 * 更新用户状态
 * @param userId 用户ID
 * @param status 状态
 * @returns 更新结果
 */
export const updateUserStatusRequest = async (userId: number, status: number): Promise<boolean> => {
  try {
    const response = await apiClient.post<ApiResponse<boolean>>(`${BASE_URL}/updateStatus`, {
      userId,
      status
    });
    
    return processResponse(response, {
      successMessage: "用户状态更新成功",
      errorMessage: "用户状态更新失败",
      showSuccessToast: true,
      showErrorToast: true
    }) || false;
  } catch (error) {
    console.error("更新用户状态失败", error);
    toast.error("更新用户状态失败");
    return false;
  }
}; 
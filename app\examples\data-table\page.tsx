"use client"

import React from "react"
import { ComponentPreviewContainer } from "@/components/component-detail/preview-container"
import { DataTable } from "@/components/common-custom/data-table"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  MoreHorizontal,
  Eye,
  Pencil,
  Trash2,
  Plus
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

// 导入所有示例组件
import { BasicDataTableExample } from "./components/basic-table-example"
import { AdvancedDataTableExample } from "./components/advanced-table-example"
import { EditableDataTableExample } from "./components/editable-table-example"
import { ExpandableDataTableExample } from "./components/expandable-table-example"
import { GroupedDataTableExample } from "./components/grouped-table-example"
import { ResponsiveDataTableExample } from "./components/responsive-table-example"
import { ServerDataTableExample } from "./components/server-table-example"
import { TreeTableExample } from "./components/tree-table-example"
import { JsonDataTableExample } from "./components/json-table-example"
import { mockTableData } from "./data"

// 基础数据表格示例代码
const basicDataTableCode = `
import { BasicDataTableExample } from "./components/basic-table-example"
import { mockTableData } from "./data"

function Example() {
  return <BasicDataTableExample data={mockTableData} />
}

render(<Example />)
`

// 高级数据表格示例代码
const advancedDataTableCode = `
import { AdvancedDataTableExample } from "./components/advanced-table-example"
import { mockTableData } from "./data"

function Example() {
  return <AdvancedDataTableExample data={mockTableData} />
}

render(<Example />)
`

// 可编辑数据表格示例代码
const editableDataTableCode = `
import { EditableDataTableExample } from "./components/editable-table-example"
import { mockTableData } from "./data"

function Example() {
  return <EditableDataTableExample data={mockTableData} />
}

render(<Example />)
`

// 可展开数据表格示例代码
const expandableDataTableCode = `
import { ExpandableDataTableExample } from "./components/expandable-table-example"
import { mockTableData } from "./data"

function Example() {
  return <ExpandableDataTableExample data={mockTableData} />
}

render(<Example />)
`

// 分组数据表格示例代码
const groupedDataTableCode = `
import { GroupedDataTableExample } from "./components/grouped-table-example"
import { mockTableData } from "./data"

function Example() {
  return <GroupedDataTableExample data={mockTableData} />
}

render(<Example />)
`

// 响应式数据表格示例代码
const responsiveDataTableCode = `
import { ResponsiveDataTableExample } from "./components/responsive-table-example"
import { mockTableData } from "./data"

function Example() {
  return <ResponsiveDataTableExample data={mockTableData} />
}

render(<Example />)
`

// 服务端数据表格示例代码
const serverDataTableCode = `
import { ServerDataTableExample } from "./components/server-table-example"

function Example() {
  return <ServerDataTableExample />
}

render(<Example />)
`

// 树形数据表格示例代码
const treeDataTableCode = `
import { TreeTableExample } from "./components/tree-table-example"

function Example() {
  return <TreeTableExample />
}

render(<Example />)
`

// JSON配置数据表格示例代码
const jsonDataTableCode = `
import { JsonDataTableExample } from "./components/json-table-example"
import { mockTableData } from "./data"

function Example() {
  return <JsonDataTableExample data={mockTableData} />
}

render(<Example />)
`



// 所有示例
const allExamples = [
  {
    id: "basic-data-table",
    title: "基础数据表格",
    description: "基本的数据表格，包含分页功能",
    code: basicDataTableCode,
    scope: {
      React,
      BasicDataTableExample,
      mockTableData,
    },
  },
  {
    id: "advanced-data-table",
    title: "高级数据表格",
    description: "包含排序、筛选、列显示控制、行选择等高级功能",
    code: advancedDataTableCode,
    scope: {
      React,
      AdvancedDataTableExample,
      mockTableData,
    },
  },
  {
    id: "editable-data-table",
    title: "可编辑数据表格",
    description: "支持内联编辑的数据表格，可直接在表格中修改数据",
    code: editableDataTableCode,
    scope: {
      React,
      EditableDataTableExample,
      mockTableData,
    },
  },
  {
    id: "expandable-data-table",
    title: "可展开数据表格",
    description: "支持行展开功能，可显示详细信息",
    code: expandableDataTableCode,
    scope: {
      React,
      ExpandableDataTableExample,
      mockTableData,
    },
  },
  {
    id: "grouped-data-table",
    title: "分组数据表格",
    description: "按指定字段对数据进行分组显示",
    code: groupedDataTableCode,
    scope: {
      React,
      GroupedDataTableExample,
      mockTableData,
    },
  },
  {
    id: "responsive-data-table",
    title: "响应式数据表格",
    description: "适配移动端的响应式数据表格",
    code: responsiveDataTableCode,
    scope: {
      React,
      ResponsiveDataTableExample,
      mockTableData,
    },
  },
  {
    id: "server-data-table",
    title: "服务端数据表格",
    description: "支持服务端分页、排序、筛选的数据表格",
    code: serverDataTableCode,
    scope: {
      React,
      ServerDataTableExample,
    },
  },
  {
    id: "tree-data-table",
    title: "树形数据表格",
    description: "支持层级结构的树形数据表格",
    code: treeDataTableCode,
    scope: {
      React,
      TreeTableExample,
    },
  },
  {
    id: "json-data-table",
    title: "JSON配置数据表格",
    description: "通过JSON配置快速生成数据表格",
    code: jsonDataTableCode,
    scope: {
      React,
      JsonDataTableExample,
      mockTableData,
    },
  },
]

// API文档组件
function DataTableApiDocs() {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="font-medium text-lg mb-2">基础数据表格 API</h3>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">data</td>
                <td className="p-2 border">T[]</td>
                <td className="p-2 border">[]</td>
                <td className="p-2 border">表格数据</td>
              </tr>
              <tr>
                <td className="p-2 border">columns</td>
                <td className="p-2 border">ColumnDef&lt;T&gt;[]</td>
                <td className="p-2 border">[]</td>
                <td className="p-2 border">列配置，基于 @tanstack/react-table</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div>
        <h3 className="font-medium text-lg mb-2">高级功能</h3>
        <div className="space-y-4">
          <div>
            <h4 className="font-medium mb-2">排序与筛选</h4>
            <p className="text-sm text-muted-foreground">
              支持列排序、全局搜索、列筛选等功能，基于 @tanstack/react-table 实现
            </p>
          </div>
          <div>
            <h4 className="font-medium mb-2">行选择</h4>
            <p className="text-sm text-muted-foreground">
              支持单选、多选、全选功能，可获取选中行数据
            </p>
          </div>
          <div>
            <h4 className="font-medium mb-2">可编辑表格</h4>
            <p className="text-sm text-muted-foreground">
              支持内联编辑，可直接在表格中修改数据，支持验证和保存
            </p>
          </div>
          <div>
            <h4 className="font-medium mb-2">可展开行</h4>
            <p className="text-sm text-muted-foreground">
              支持行展开功能，可显示详细信息或子表格
            </p>
          </div>
          <div>
            <h4 className="font-medium mb-2">分组显示</h4>
            <p className="text-sm text-muted-foreground">
              支持按指定字段对数据进行分组显示
            </p>
          </div>
          <div>
            <h4 className="font-medium mb-2">响应式设计</h4>
            <p className="text-sm text-muted-foreground">
              在移动端自动切换为卡片布局，提供更好的用户体验
            </p>
          </div>
          <div>
            <h4 className="font-medium mb-2">服务端分页</h4>
            <p className="text-sm text-muted-foreground">
              支持服务端分页、排序、筛选，适用于大数据量场景
            </p>
          </div>
          <div>
            <h4 className="font-medium mb-2">树形结构</h4>
            <p className="text-sm text-muted-foreground">
              支持层级结构数据的展示，可展开/收起子节点
            </p>
          </div>
          <div>
            <h4 className="font-medium mb-2">JSON配置</h4>
            <p className="text-sm text-muted-foreground">
              通过JSON配置快速生成数据表格，支持动态配置
            </p>
          </div>
        </div>
      </div>

      <div>
        <h3 className="font-medium text-lg mb-2">使用建议</h3>
        <div className="space-y-2 text-sm text-muted-foreground">
          <p>• 对于简单的数据展示，使用基础数据表格</p>
          <p>• 需要复杂交互时，使用高级数据表格</p>
          <p>• 移动端优先考虑响应式数据表格</p>
          <p>• 大数据量场景使用服务端数据表格</p>
          <p>• 层级数据使用树形数据表格</p>
          <p>• 需要快速配置时使用JSON配置表格</p>
        </div>
      </div>
    </div>
  )
}

export default function DataTableExamplePage() {
  return (
    <ComponentPreviewContainer
      title="数据表格 DataTable"
      description="功能强大的数据表格组件集合，提供从基础展示到高级交互的完整解决方案。支持排序、筛选、分页、行选择、内联编辑、可展开行、分组显示、响应式设计、服务端分页、树形结构和JSON配置等丰富功能"
      whenToUse="适用于各种数据展示和管理场景：用户管理、订单列表、产品目录、数据分析、内容管理等。根据具体需求选择合适的表格类型：基础表格用于简单展示，高级表格用于复杂交互，响应式表格用于移动端，服务端表格用于大数据量，树形表格用于层级数据，JSON配置表格用于动态生成"
      examples={allExamples}
      apiDocs={<DataTableApiDocs />}
    />
  )
}

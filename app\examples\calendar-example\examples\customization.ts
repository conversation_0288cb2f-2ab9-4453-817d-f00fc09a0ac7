import React from "react"
import { Calendar } from "@/components/common-custom/calendar"

export const customStyleExample = {
  id: "custom-style-calendar",
  title: "自定义样式",
  description: "自定义样式和主题的日历",
  code: `
import React, { useState } from "react";
import { Calendar } from "@/components/common-custom/calendar";

function CustomStyleCalendar() {
  const [date, setDate] = useState<Date | undefined>(new Date());

  const events = [
    {
      id: "1",
      date: new Date(2024, 0, 15),
      title: "重要会议",
      color: "#8B5CF6",
    },
    {
      id: "2",
      date: new Date(2024, 0, 18),
      title: "项目评审",
      color: "#10B981",
    },
  ];

  return (
    <Calendar
      mode="single"
      value={date}
      events={events}
      handlers={{
        onSelect: setDate
      }}
      features={{
        showEvents: true,
        maxEventsPerDay: 3
      }}
      style={{
        showBorder: true,
        showShadow: true,
        className: "w-fit bg-gradient-to-br from-blue-50 to-indigo-50",
        calendarClassName: "p-6",
        eventClassName: "font-medium"
      }}
    />
  );
}

render(<CustomStyleCalendar />);
  `,
  scope: { Calendar, React, useState: React.useState },
}

export const customRenderExample = {
  id: "custom-render-calendar",
  title: "自定义渲染",
  description: "自定义事件和图例渲染的日历",
  code: `
import React, { useState } from "react";
import { Calendar } from "@/components/common-custom/calendar";

function CustomRenderCalendar() {
  const [date, setDate] = useState<Date | undefined>(new Date());

  const events = [
    {
      id: "1",
      date: new Date(2024, 0, 15),
      title: "🎯 目标设定",
      description: "季度目标制定",
      type: "primary" as const,
    },
    {
      id: "2",
      date: new Date(2024, 0, 18),
      title: "📊 数据分析",
      description: "月度数据回顾",
      type: "success" as const,
    },
  ];

  const legends = [
    {
      id: "goal",
      label: "目标管理",
      color: "#3B82F6",
      description: "目标设定和跟踪"
    },
    {
      id: "analysis",
      label: "数据分析",
      color: "#10B981",
      description: "数据收集和分析"
    },
  ];

  const renderEvent = (event: any) => (
    <div className="bg-card border border-border rounded px-1 py-0.5 shadow-sm">
      <div className="text-xs font-medium text-card-foreground truncate">
        {event.title}
      </div>
    </div>
  );

  const renderLegend = (legend: any) => (
    <div className="flex items-center gap-2 px-2 py-1 bg-gray-50 rounded">
      <div 
        className="w-4 h-4 rounded-full"
        style={{ backgroundColor: legend.color }}
      />
      <span className="text-sm font-medium">{legend.label}</span>
    </div>
  );

  return (
    <Calendar
      mode="single"
      value={date}
      events={events}
      legends={legends}
      handlers={{
        onSelect: setDate
      }}
      features={{
        showEvents: true,
        showLegend: true,
        legendPosition: "right"
      }}
      style={{
        showBorder: true,
        className: "w-fit"
      }}
      renderEvent={renderEvent}
      renderLegend={renderLegend}
    />
  );
}

render(<CustomRenderCalendar />);
  `,
  scope: { Calendar, React, useState: React.useState },
}

/**
 * 文本截断组件示例代码集合
 * 
 * 按照新规范管理示例代码，避免在页面文件中硬编码
 */

import React from "react"
import { TruncateText } from "@/components/common-custom/truncate-text"

// ============================================================================
// 基础示例
// ============================================================================

export const basicExample = {
  id: "basic-truncate-text",
  title: "基础文本截断",
  description: "基本的单行文本截断功能",
  code: `
import React from "react";
import { TruncateText } from "@/components/common-custom/truncate-text";

function BasicTruncateExample() {
  const longText = "这是一段很长的文本内容，用来演示文本截断功能的效果。当文本超出容器宽度时，会自动截断并显示省略号。";
  
  return (
    <div className="space-y-4">
      <div>
        <h4 className="text-sm font-medium mb-2">单行截断 (200px)</h4>
        <div className="border p-3 rounded">
          <TruncateText text={longText} maxWidth="200px" />
        </div>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">单行截断 (300px)</h4>
        <div className="border p-3 rounded">
          <TruncateText text={longText} maxWidth="300px" />
        </div>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">无提示框</h4>
        <div className="border p-3 rounded">
          <TruncateText text={longText} maxWidth="200px" showTooltip={false} />
        </div>
      </div>
    </div>
  );
}

render(<BasicTruncateExample />);
  `,
  scope: { TruncateText, React },
}

// ============================================================================
// 多行截断示例
// ============================================================================

export const multiLineExample = {
  id: "multi-line-truncate",
  title: "多行文本截断",
  description: "支持多行文本的截断显示",
  code: `
import React from "react";
import { TruncateText } from "@/components/common-custom/truncate-text";

function MultiLineTruncateExample() {
  const longText = "这是一段很长的文本内容，用来演示多行文本截断功能。当文本内容超过指定的行数时，会自动截断并显示省略号。这个功能特别适用于卡片组件、列表项或者其他需要限制文本显示高度的场景。通过设置不同的行数，可以灵活控制文本的显示效果。";
  
  return (
    <div className="space-y-4">
      <div>
        <h4 className="text-sm font-medium mb-2">2行截断</h4>
        <div className="border p-3 rounded" style={{ width: "300px" }}>
          <TruncateText text={longText} lines={2} />
        </div>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">3行截断</h4>
        <div className="border p-3 rounded" style={{ width: "300px" }}>
          <TruncateText text={longText} lines={3} />
        </div>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">4行截断</h4>
        <div className="border p-3 rounded" style={{ width: "300px" }}>
          <TruncateText text={longText} lines={4} />
        </div>
      </div>
    </div>
  );
}

render(<MultiLineTruncateExample />);
  `,
  scope: { TruncateText, React },
}

// ============================================================================
// 可展开示例
// ============================================================================

export const expandableExample = {
  id: "expandable-truncate",
  title: "可展开文本",
  description: "支持展开和收起的文本截断",
  code: `
import React, { useState } from "react";
import { TruncateText } from "@/components/common-custom/truncate-text";

function ExpandableTruncateExample() {
  const [expandState, setExpandState] = useState({});
  
  const longText = "这是一段很长的文本内容，用来演示可展开的文本截断功能。用户可以点击展开按钮查看完整内容，也可以点击收起按钮隐藏多余内容。这个功能特别适用于文章摘要、产品描述、用户评论等需要节省空间但又要提供完整信息的场景。通过这种交互方式，既保持了页面的整洁，又满足了用户查看详细信息的需求。";
  
  const handleExpandChange = (id, expanded) => {
    setExpandState(prev => ({ ...prev, [id]: expanded }));
    console.log(\`文本 \${id} \${expanded ? '展开' : '收起'}\`);
  };
  
  return (
    <div className="space-y-6">
      <div>
        <h4 className="text-sm font-medium mb-2">单行可展开</h4>
        <div className="border p-3 rounded" style={{ width: "400px" }}>
          <TruncateText 
            text={longText} 
            maxWidth="350px"
            expandable={true}
            onExpandChange={(expanded) => handleExpandChange('single', expanded)}
          />
        </div>
        <p className="text-xs text-muted-foreground mt-1">
          状态: {expandState.single ? '已展开' : '已收起'}
        </p>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">多行可展开</h4>
        <div className="border p-3 rounded" style={{ width: "400px" }}>
          <TruncateText 
            text={longText} 
            lines={2}
            expandable={true}
            onExpandChange={(expanded) => handleExpandChange('multi', expanded)}
          />
        </div>
        <p className="text-xs text-muted-foreground mt-1">
          状态: {expandState.multi ? '已展开' : '已收起'}
        </p>
      </div>
    </div>
  );
}

render(<ExpandableTruncateExample />);
  `,
  scope: { TruncateText, React, useState: React.useState },
}

// ============================================================================
// 截断位置示例
// ============================================================================

export const positionExample = {
  id: "position-truncate",
  title: "不同截断位置",
  description: "展示开头、中间、结尾不同位置的截断效果",
  code: `
import React from "react";
import { TruncateText } from "@/components/common-custom/truncate-text";

function PositionTruncateExample() {
  const longText = "这是一段很长的文本内容用来演示不同位置截断的效果";
  const filePath = "/very/long/path/to/some/important/file/document.pdf";
  
  return (
    <div className="space-y-4">
      <div>
        <h4 className="text-sm font-medium mb-2">结尾截断 (默认)</h4>
        <div className="border p-3 rounded">
          <TruncateText text={longText} maxWidth="200px" position="end" />
        </div>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">开头截断</h4>
        <div className="border p-3 rounded">
          <TruncateText text={filePath} maxWidth="200px" position="start" />
        </div>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">中间截断</h4>
        <div className="border p-3 rounded">
          <TruncateText text={longText} maxWidth="200px" position="middle" />
        </div>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">自定义省略符号</h4>
        <div className="border p-3 rounded">
          <TruncateText 
            text={longText} 
            maxWidth="200px" 
            ellipsis="***" 
          />
        </div>
      </div>
    </div>
  );
}

render(<PositionTruncateExample />);
  `,
  scope: { TruncateText, React },
}

// ============================================================================
// 提示框配置示例
// ============================================================================

export const tooltipExample = {
  id: "tooltip-truncate",
  title: "提示框配置",
  description: "不同的提示框位置和样式配置",
  code: `
import React from "react";
import { TruncateText } from "@/components/common-custom/truncate-text";

function TooltipTruncateExample() {
  const longText = "这是一段很长的文本内容，用来演示提示框的不同配置选项";
  
  return (
    <div className="space-y-6 p-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <h4 className="text-sm font-medium mb-2">顶部提示</h4>
          <div className="border p-3 rounded">
            <TruncateText 
              text={longText} 
              maxWidth="150px" 
              tooltipSide="top"
            />
          </div>
        </div>
        
        <div>
          <h4 className="text-sm font-medium mb-2">底部提示</h4>
          <div className="border p-3 rounded">
            <TruncateText 
              text={longText} 
              maxWidth="150px" 
              tooltipSide="bottom"
            />
          </div>
        </div>
        
        <div>
          <h4 className="text-sm font-medium mb-2">左侧提示</h4>
          <div className="border p-3 rounded">
            <TruncateText 
              text={longText} 
              maxWidth="150px" 
              tooltipSide="left"
            />
          </div>
        </div>
        
        <div>
          <h4 className="text-sm font-medium mb-2">右侧提示</h4>
          <div className="border p-3 rounded">
            <TruncateText 
              text={longText} 
              maxWidth="150px" 
              tooltipSide="right"
            />
          </div>
        </div>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">自定义提示框宽度</h4>
        <div className="border p-3 rounded">
          <TruncateText 
            text={longText} 
            maxWidth="150px" 
            tooltipMaxWidth="500px"
          />
        </div>
      </div>
    </div>
  );
}

render(<TooltipTruncateExample />);
  `,
  scope: { TruncateText, React },
}

// ============================================================================
// 统一导出所有示例
// ============================================================================

export const allExamples = [
  basicExample,
  multiLineExample,
  expandableExample,
  positionExample,
  tooltipExample,
]

// ============================================================================
// 按类别导出示例
// ============================================================================

export const basicExamples = [basicExample, multiLineExample]
export const advancedExamples = [expandableExample, positionExample]
export const configExamples = [tooltipExample]

// ============================================================================
// 示例元数据
// ============================================================================

export const exampleMetadata = {
  total: allExamples.length,
  categories: {
    basic: basicExamples.length,
    advanced: advancedExamples.length,
    config: configExamples.length,
  },
  tags: ["text", "truncate", "ellipsis", "tooltip", "expandable"],
  lastUpdated: "2024-01-01",
}

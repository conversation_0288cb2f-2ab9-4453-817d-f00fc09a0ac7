"use client"

import { ReactNode } from "react"
import { But<PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { X } from "lucide-react"
import { cn } from "@/lib/utils"

/**
 * 信息模态框组件
 */
export function InfoModal({
  open,
  onOpenChange,
  title,
  description,
  children,
  closeText = "关闭",
  onClose,
  className,
  maxWidth = "sm:max-w-[425px]",
}: {
  open?: boolean
  onOpenChange?: (open: boolean) => void
  title?: ReactNode
  description?: ReactNode
  children: ReactNode
  closeText?: string
  onClose?: () => void
  className?: string
  maxWidth?: string
}) {
  const handleClose = () => {
    if (onClose) {
      onClose();
    }
    
    if (onOpenChange) {
      onOpenChange(false);
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className={cn(maxWidth, className)}>
        <button
          onClick={handleClose}
          className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
        >
          <X className="h-4 w-4" />
          <span className="sr-only">关闭</span>
        </button>
        
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          {description && <DialogDescription>{description}</DialogDescription>}
        </DialogHeader>
        
        {children}
        
        <DialogFooter>
          <Button onClick={handleClose}>{closeText}</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 
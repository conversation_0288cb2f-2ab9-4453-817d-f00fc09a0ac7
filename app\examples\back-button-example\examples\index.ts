/**
 * 返回按钮组件示例代码集合
 * 
 * 按照新规范管理示例代码，避免在页面文件中硬编码
 */

import React from "react"
import { BackButton } from "@/components/common-custom/back-button"

// ============================================================================
// 基础示例
// ============================================================================

export const basicExample = {
  id: "basic-back-button",
  title: "基础用法",
  description: "基础的返回按钮，支持只显示图标或同时显示图标和文字",
  code: `
import React from "react";
import { BackButton } from "@/components/common-custom/back-button";

function BasicBackButton() {
  return (
    <div className="flex flex-wrap items-center gap-4">
      <BackButton href="/" />
      <BackButton href="/" showText>返回首页</BackButton>
      <BackButton href="/" showText>
        自定义文字
      </BackButton>
    </div>
  );
}

render(<BasicBackButton />);
  `,
  scope: { BackButton, React },
}

// ============================================================================
// 尺寸示例
// ============================================================================

export const sizeExample = {
  id: "size-back-button",
  title: "不同尺寸",
  description: "展示不同尺寸的返回按钮：小、中、大",
  code: `
import React from "react";
import { BackButton } from "@/components/common-custom/back-button";

function SizeBackButton() {
  return (
    <div className="space-y-4">
      <div className="flex items-center gap-4">
        <BackButton href="/" size="sm" showText>小尺寸</BackButton>
        <BackButton href="/" size="md" showText>中尺寸</BackButton>
        <BackButton href="/" size="lg" showText>大尺寸</BackButton>
      </div>
      
      <div className="flex items-center gap-4">
        <BackButton href="/" size="sm" />
        <BackButton href="/" size="md" />
        <BackButton href="/" size="lg" />
      </div>
    </div>
  );
}

render(<SizeBackButton />);
  `,
  scope: { BackButton, React },
}

// ============================================================================
// 变体样式示例
// ============================================================================

export const variantExample = {
  id: "variant-back-button",
  title: "变体样式",
  description: "展示不同变体样式的返回按钮",
  code: `
import React from "react";
import { BackButton } from "@/components/common-custom/back-button";

function VariantBackButton() {
  return (
    <div className="space-y-4">
      <div className="flex flex-wrap items-center gap-4">
        <BackButton href="/" variant="default" showText>默认样式</BackButton>
        <BackButton href="/" variant="ghost" showText>透明样式</BackButton>
        <BackButton href="/" variant="primary" showText>主题色</BackButton>
        <BackButton href="/" variant="destructive" showText>警告样式</BackButton>
        <BackButton href="/" variant="outline" showText>轮廓样式</BackButton>
      </div>
      
      <div className="flex flex-wrap items-center gap-4">
        <BackButton href="/" variant="default" />
        <BackButton href="/" variant="ghost" />
        <BackButton href="/" variant="primary" />
        <BackButton href="/" variant="destructive" />
        <BackButton href="/" variant="outline" />
      </div>
    </div>
  );
}

render(<VariantBackButton />);
  `,
  scope: { BackButton, React },
}

// ============================================================================
// 圆角样式示例
// ============================================================================

export const roundedExample = {
  id: "rounded-back-button",
  title: "圆角样式",
  description: "展示不同圆角样式的返回按钮",
  code: `
import React from "react";
import { BackButton } from "@/components/common-custom/back-button";

function RoundedBackButton() {
  return (
    <div className="flex flex-wrap items-center gap-4">
      <BackButton href="/" rounded="default" variant="primary" showText>
        默认圆角
      </BackButton>
      <BackButton href="/" rounded="md" variant="primary" showText>
        中等圆角
      </BackButton>
      <BackButton href="/" rounded="full" variant="primary" showText>
        完全圆形
      </BackButton>
    </div>
  );
}

render(<RoundedBackButton />);
  `,
  scope: { BackButton, React },
}

// ============================================================================
// 交互示例
// ============================================================================

export const interactiveExample = {
  id: "interactive-back-button",
  title: "交互功能",
  description: "展示带有事件处理的返回按钮",
  code: `
import React, { useState } from "react";
import { BackButton } from "@/components/common-custom/back-button";

function InteractiveBackButton() {
  const [clickCount, setClickCount] = useState(0);
  const [lastAction, setLastAction] = useState("");

  const handleClick = (action) => (event) => {
    event.preventDefault(); // 阻止默认跳转
    setClickCount(prev => prev + 1);
    setLastAction(action);
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-wrap items-center gap-4">
        <BackButton 
          href="/" 
          variant="primary"
          showText
          onClick={handleClick("返回首页")}
        >
          返回首页
        </BackButton>
        
        <BackButton 
          href="/" 
          variant="destructive"
          showText
          onClick={handleClick("取消操作")}
        >
          取消操作
        </BackButton>
        
        <BackButton 
          href="/" 
          variant="outline"
          onClick={handleClick("仅图标")}
        />
      </div>
      
      <div className="p-4 bg-muted rounded-md">
        <p className="text-sm">
          <strong>点击次数:</strong> {clickCount}
        </p>
        <p className="text-sm">
          <strong>最后操作:</strong> {lastAction || "无"}
        </p>
      </div>
    </div>
  );
}

render(<InteractiveBackButton />);
  `,
  scope: { BackButton, React, useState: React.useState },
}

// ============================================================================
// 组合示例
// ============================================================================

export const combinedExample = {
  id: "combined-back-button",
  title: "组合用法",
  description: "展示组合不同属性的返回按钮示例",
  code: `
import React from "react";
import { BackButton } from "@/components/common-custom/back-button";

function CombinedBackButton() {
  return (
    <div className="space-y-6">
      <div>
        <h4 className="text-sm font-medium mb-2">小尺寸组合</h4>
        <div className="flex flex-wrap items-center gap-2">
          <BackButton 
            href="/" 
            size="sm" 
            variant="primary"
            rounded="full"
            showText
          >
            返回主页
          </BackButton>
          
          <BackButton 
            href="/" 
            size="sm" 
            variant="outline"
            rounded="md"
            showText
          >
            取消
          </BackButton>
        </div>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">大尺寸组合</h4>
        <div className="flex flex-wrap items-center gap-4">
          <BackButton 
            href="/" 
            size="lg" 
            variant="destructive"
            rounded="md"
            showText
          >
            取消并返回
          </BackButton>
          
          <BackButton 
            href="/" 
            size="lg" 
            variant="primary"
            showText
            className="shadow-lg"
          >
            确认返回
          </BackButton>
        </div>
      </div>
    </div>
  );
}

render(<CombinedBackButton />);
  `,
  scope: { BackButton, React },
}

// ============================================================================
// 统一导出所有示例
// ============================================================================

export const allExamples = [
  basicExample,
  sizeExample,
  variantExample,
  roundedExample,
  interactiveExample,
  combinedExample,
]

// ============================================================================
// 按类别导出示例
// ============================================================================

export const basicExamples = [basicExample, sizeExample]
export const styleExamples = [variantExample, roundedExample]
export const advancedExamples = [interactiveExample, combinedExample]

// ============================================================================
// 示例元数据
// ============================================================================

export const exampleMetadata = {
  total: allExamples.length,
  categories: {
    basic: basicExamples.length,
    style: styleExamples.length,
    advanced: advancedExamples.length,
  },
  tags: ["navigation", "button", "back", "interactive"],
  lastUpdated: "2024-01-01",
}

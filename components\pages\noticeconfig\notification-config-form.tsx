"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { AlertCircle, TestTube, Save, X, Plus, Trash2, CheckCircle, XCircle, Loader2 } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import type {
  NotificationConfig,
  NotificationType,
  SMSConfig,
  EmailConfig,
  DingTalkConfig,
  TestResult,
} from "@/types/notification"
import { VariableManagerInline } from "@/components/pages/noticeconfig/variable-manager-inline"
import { ContentPreview } from "@/components/pages/noticeconfig/content-preview"

interface NotificationConfigFormProps {
  config?: NotificationConfig | null
  onClose: () => void
}

export function NotificationConfigForm({ config, onClose }: NotificationConfigFormProps) {
  const [formData, setFormData] = useState({
    name: config?.name || "",
    description: config?.description || "",
    type: config?.type || ("sms" as NotificationType),
    enabled: config?.enabled ?? true,
    environment: config?.environment || ("development" as const),
  })

  const [smsConfig, setSmsConfig] = useState<Partial<SMSConfig>>(
    config?.type === "sms"
      ? (config.config as SMSConfig)
      : {
          provider: "aliyun",
          templateId: "",
          recipients: [],
          smsType: "notification",
          content: "",
          variables: {},
        },
  )

  const [emailConfig, setEmailConfig] = useState<Partial<EmailConfig>>(
    config?.type === "email"
      ? (config.config as EmailConfig)
      : {
          smtp: {
            host: "",
            port: 587,
            encryption: "tls",
            username: "",
            password: "",
            useOAuth: false,
          },
          subject: "",
          contentFormat: "html",
          content: "",
          attachments: [],
          variables: {},
        },
  )

  const [dingtalkConfig, setDingtalkConfig] = useState<Partial<DingTalkConfig>>(
    config?.type === "dingtalk"
      ? (config.config as DingTalkConfig)
      : {
          webhookUrl: "",
          secret: "",
          messageType: "text",
          content: "",
          atMobiles: [],
          variables: {},
        },
  )

  const [showTestDialog, setShowTestDialog] = useState(false)
  const [testResult, setTestResult] = useState<TestResult | null>(null)
  const [isTestingConfig, setIsTestingConfig] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  const getCurrentContent = () => {
    switch (formData.type) {
      case "sms":
        return smsConfig.content || ""
      case "email":
        return emailConfig.content || ""
      case "dingtalk":
        return dingtalkConfig.content || ""
      default:
        return ""
    }
  }

  const getCurrentSubject = () => {
    return formData.type === "email" ? emailConfig.subject || "" : ""
  }

  const getCurrentVariables = () => {
    switch (formData.type) {
      case "sms":
        return smsConfig.variables || {}
      case "email":
        return emailConfig.variables || {}
      case "dingtalk":
        return dingtalkConfig.variables || {}
      default:
        return {}
    }
  }

  const handleVariablesChange = (variables: Record<string, string>) => {
    switch (formData.type) {
      case "sms":
        setSmsConfig({ ...smsConfig, variables })
        break
      case "email":
        setEmailConfig({ ...emailConfig, variables })
        break
      case "dingtalk":
        setDingtalkConfig({ ...dingtalkConfig, variables })
        break
    }
  }

  const handleVariableInsert = (variable: string) => {
    // This would typically insert the variable at the cursor position
    console.log("Variable inserted:", variable)
  }

  const validateConfig = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = "配置名称不能为空"
    }

    if (!formData.description.trim()) {
      newErrors.description = "配置描述不能为空"
    }

    switch (formData.type) {
      case "sms":
        if (!smsConfig.templateId) {
          newErrors.templateId = "模板ID不能为空"
        }
        if (!smsConfig.content) {
          newErrors.content = "短信内容不能为空"
        }
        break
      case "email":
        if (!emailConfig.smtp?.host) {
          newErrors.smtpHost = "SMTP服务器不能为空"
        }
        if (!emailConfig.smtp?.username) {
          newErrors.smtpUsername = "用户名不能为空"
        }
        if (!emailConfig.subject) {
          newErrors.subject = "邮件主题不能为空"
        }
        if (!emailConfig.content) {
          newErrors.content = "邮件内容不能为空"
        }
        break
      case "dingtalk":
        if (!dingtalkConfig.webhookUrl) {
          newErrors.webhookUrl = "Webhook URL不能为空"
        }
        if (!dingtalkConfig.content) {
          newErrors.content = "消息内容不能为空"
        }
        break
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSave = () => {
    if (!validateConfig()) {
      return
    }

    // TODO: Implement save logic
    console.log("Saving config:", { formData, smsConfig, emailConfig, dingtalkConfig })
    onClose()
  }

  const handleTest = async () => {
    if (!validateConfig()) {
      return
    }

    setIsTestingConfig(true)
    setShowTestDialog(true)
    setTestResult(null)

    try {
      // Simulate test API call
      await new Promise((resolve) => setTimeout(resolve, 2000))

      // Mock test result based on config type
      const mockResult: TestResult = {
        success: Math.random() > 0.3, // 70% success rate for demo
        message: "",
        timestamp: new Date().toISOString(),
      }

      if (mockResult.success) {
        switch (formData.type) {
          case "sms":
            mockResult.message = "短信发送成功"
            mockResult.details = `已发送到 ${smsConfig.recipients?.length || 0} 个号码`
            break
          case "email":
            mockResult.message = "邮件发送成功"
            mockResult.details = `SMTP连接正常，邮件格式验证通过`
            break
          case "dingtalk":
            mockResult.message = "钉钉消息发送成功"
            mockResult.details = `Webhook连接正常，消息已推送到群聊`
            break
        }
      } else {
        switch (formData.type) {
          case "sms":
            mockResult.message = "短信发送失败"
            mockResult.details = "模板ID不存在或参数错误"
            break
          case "email":
            mockResult.message = "邮件发送失败"
            mockResult.details = "SMTP认证失败，请检查用户名和密码"
            break
          case "dingtalk":
            mockResult.message = "钉钉消息发送失败"
            mockResult.details = "Webhook URL无效或签名验证失败"
            break
        }
      }

      setTestResult(mockResult)
    } catch (error) {
      setTestResult({
        success: false,
        message: "测试过程中发生错误",
        details: "网络连接异常，请稍后重试",
        timestamp: new Date().toISOString(),
      })
    } finally {
      setIsTestingConfig(false)
    }
  }

  const addRecipient = (type: "sms" | "dingtalk") => {
    if (type === "sms") {
      setSmsConfig({
        ...smsConfig,
        recipients: [...(smsConfig.recipients || []), ""],
      })
    } else {
      setDingtalkConfig({
        ...dingtalkConfig,
        atMobiles: [...(dingtalkConfig.atMobiles || []), ""],
      })
    }
  }

  const updateRecipient = (index: number, value: string, type: "sms" | "dingtalk") => {
    if (type === "sms") {
      const recipients = [...(smsConfig.recipients || [])]
      recipients[index] = value
      setSmsConfig({ ...smsConfig, recipients })
    } else {
      const atMobiles = [...(dingtalkConfig.atMobiles || [])]
      atMobiles[index] = value
      setDingtalkConfig({ ...dingtalkConfig, atMobiles })
    }
  }

  const removeRecipient = (index: number, type: "sms" | "dingtalk") => {
    if (type === "sms") {
      const recipients = [...(smsConfig.recipients || [])]
      recipients.splice(index, 1)
      setSmsConfig({ ...smsConfig, recipients })
    } else {
      const atMobiles = [...(dingtalkConfig.atMobiles || [])]
      atMobiles.splice(index, 1)
      setDingtalkConfig({ ...dingtalkConfig, atMobiles })
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">{config ? "编辑通知配置" : "新建通知配置"}</h2>
          <p className="text-muted-foreground">配置通知参数、内容模板和变量设置</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleTest}>
            <TestTube className="h-4 w-4 mr-2" />
            测试
          </Button>
          <Button onClick={handleSave}>
            <Save className="h-4 w-4 mr-2" />
            保存
          </Button>
          <Button variant="ghost" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="grid gap-6 lg:grid-cols-5">
        <div className="lg:col-span-3 space-y-6">
          {/* Basic Info */}
          <Card>
            <CardHeader>
              <CardTitle>基本信息</CardTitle>
              <CardDescription>配置基本信息和环境设置</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="name">配置名称</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    placeholder="输入配置名称"
                    className={errors.name ? "border-red-500" : ""}
                  />
                  {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="type">通知类型</Label>
                  <Select
                    value={formData.type}
                    onValueChange={(value) => setFormData({ ...formData, type: value as NotificationType })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="sms">📱 短信</SelectItem>
                      <SelectItem value="email">📧 邮件</SelectItem>
                      <SelectItem value="dingtalk">💬 钉钉</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">描述</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  placeholder="输入配置描述"
                  rows={3}
                  className={errors.description ? "border-red-500" : ""}
                />
                {errors.description && <p className="text-sm text-red-500">{errors.description}</p>}
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="environment">环境</Label>
                  <Select
                    value={formData.environment}
                    onValueChange={(value) => setFormData({ ...formData, environment: value as any })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="development">开发环境</SelectItem>
                      <SelectItem value="testing">测试环境</SelectItem>
                      <SelectItem value="production">生产环境</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center space-x-2 pt-8">
                  <Switch
                    id="enabled"
                    checked={formData.enabled}
                    onCheckedChange={(checked) => setFormData({ ...formData, enabled: checked })}
                  />
                  <Label htmlFor="enabled">启用配置</Label>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Type-specific Configuration */}
          {formData.type === "sms" && (
            <Card>
              <CardHeader>
                <CardTitle>短信配置</CardTitle>
                <CardDescription>配置短信服务商和发送参数</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label>服务商</Label>
                    <Select
                      value={smsConfig.provider}
                      onValueChange={(value) => setSmsConfig({ ...smsConfig, provider: value as any })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="aliyun">阿里云</SelectItem>
                        <SelectItem value="tencent">腾讯云</SelectItem>
                        <SelectItem value="huawei">华为云</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>模板ID</Label>
                    <Input
                      value={smsConfig.templateId}
                      onChange={(e) => setSmsConfig({ ...smsConfig, templateId: e.target.value })}
                      placeholder="SMS_123456"
                      className={errors.templateId ? "border-red-500" : ""}
                    />
                    {errors.templateId && <p className="text-sm text-red-500">{errors.templateId}</p>}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>短信类型</Label>
                  <Select
                    value={smsConfig.smsType}
                    onValueChange={(value) => setSmsConfig({ ...smsConfig, smsType: value as any })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="verification">验证码</SelectItem>
                      <SelectItem value="notification">通知</SelectItem>
                      <SelectItem value="marketing">营销</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>接收号码</Label>
                  <div className="space-y-2">
                    {(smsConfig.recipients || []).map((recipient, index) => (
                      <div key={index} className="flex gap-2">
                        <Input
                          value={recipient}
                          onChange={(e) => updateRecipient(index, e.target.value, "sms")}
                          placeholder="手机号码"
                        />
                        <Button variant="outline" size="sm" onClick={() => removeRecipient(index, "sms")}>
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                    <Button variant="outline" onClick={() => addRecipient("sms")} className="w-full">
                      <Plus className="h-4 w-4 mr-2" />
                      添加号码
                    </Button>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>短信内容</Label>
                  <Textarea
                    value={smsConfig.content}
                    onChange={(e) => setSmsConfig({ ...smsConfig, content: e.target.value })}
                    placeholder="您的验证码是${code}，5分钟内有效。"
                    rows={3}
                    className={errors.content ? "border-red-500" : ""}
                  />
                  {errors.content && <p className="text-sm text-red-500">{errors.content}</p>}
                  <p className="text-sm text-muted-foreground">
                    支持变量替换，如 ${"{"}code{"}"}, ${"{"}userName{"}"}
                  </p>
                </div>
              </CardContent>
            </Card>
          )}

          {formData.type === "email" && (
            <Card>
              <CardHeader>
                <CardTitle>邮件配置</CardTitle>
                <CardDescription>配置SMTP服务器和邮件模板</CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="smtp" className="space-y-4">
                  <TabsList>
                    <TabsTrigger value="smtp">SMTP配置</TabsTrigger>
                    <TabsTrigger value="content">内容模板</TabsTrigger>
                  </TabsList>

                  <TabsContent value="smtp" className="space-y-4">
                    <div className="grid gap-4 md:grid-cols-2">
                      <div className="space-y-2">
                        <Label>SMTP服务器</Label>
                        <Input
                          value={emailConfig.smtp?.host}
                          onChange={(e) =>
                            setEmailConfig({
                              ...emailConfig,
                              smtp: { ...emailConfig.smtp!, host: e.target.value },
                            })
                          }
                          placeholder="smtp.gmail.com"
                          className={errors.smtpHost ? "border-red-500" : ""}
                        />
                        {errors.smtpHost && <p className="text-sm text-red-500">{errors.smtpHost}</p>}
                      </div>
                      <div className="space-y-2">
                        <Label>端口</Label>
                        <Input
                          type="number"
                          value={emailConfig.smtp?.port}
                          onChange={(e) =>
                            setEmailConfig({
                              ...emailConfig,
                              smtp: { ...emailConfig.smtp!, port: Number.parseInt(e.target.value) },
                            })
                          }
                          placeholder="587"
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>加密协议</Label>
                      <Select
                        value={emailConfig.smtp?.encryption}
                        onValueChange={(value) =>
                          setEmailConfig({
                            ...emailConfig,
                            smtp: { ...emailConfig.smtp!, encryption: value as any },
                          })
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="ssl">SSL</SelectItem>
                          <SelectItem value="tls">TLS</SelectItem>
                          <SelectItem value="starttls">STARTTLS</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="grid gap-4 md:grid-cols-2">
                      <div className="space-y-2">
                        <Label>用户名</Label>
                        <Input
                          value={emailConfig.smtp?.username}
                          onChange={(e) =>
                            setEmailConfig({
                              ...emailConfig,
                              smtp: { ...emailConfig.smtp!, username: e.target.value },
                            })
                          }
                          placeholder="<EMAIL>"
                          className={errors.smtpUsername ? "border-red-500" : ""}
                        />
                        {errors.smtpUsername && <p className="text-sm text-red-500">{errors.smtpUsername}</p>}
                      </div>
                      <div className="space-y-2">
                        <Label>密码</Label>
                        <Input
                          type="password"
                          value={emailConfig.smtp?.password}
                          onChange={(e) =>
                            setEmailConfig({
                              ...emailConfig,
                              smtp: { ...emailConfig.smtp!, password: e.target.value },
                            })
                          }
                          placeholder="应用密码"
                        />
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={emailConfig.smtp?.useOAuth}
                        onCheckedChange={(checked) =>
                          setEmailConfig({
                            ...emailConfig,
                            smtp: { ...emailConfig.smtp!, useOAuth: checked },
                          })
                        }
                      />
                      <Label>使用OAuth 2.0认证</Label>
                    </div>
                  </TabsContent>

                  <TabsContent value="content" className="space-y-4">
                    <div className="space-y-2">
                      <Label>邮件主题</Label>
                      <Input
                        value={emailConfig.subject}
                        onChange={(e) => setEmailConfig({ ...emailConfig, subject: e.target.value })}
                        placeholder="订单确认 - ${orderNumber}"
                        className={errors.subject ? "border-red-500" : ""}
                      />
                      {errors.subject && <p className="text-sm text-red-500">{errors.subject}</p>}
                    </div>

                    <div className="space-y-2">
                      <Label>内容格式</Label>
                      <Select
                        value={emailConfig.contentFormat}
                        onValueChange={(value) => setEmailConfig({ ...emailConfig, contentFormat: value as any })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="text">纯文本</SelectItem>
                          <SelectItem value="html">HTML</SelectItem>
                          <SelectItem value="markdown">Markdown</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>邮件内容</Label>
                      <Textarea
                        value={emailConfig.content}
                        onChange={(e) => setEmailConfig({ ...emailConfig, content: e.target.value })}
                        placeholder={
                          emailConfig.contentFormat === "html"
                            ? "<h1>感谢您的订单</h1><p>订单号：${orderNumber}</p>"
                            : emailConfig.contentFormat === "markdown"
                              ? "# 感谢您的订单\n\n订单号：${orderNumber}"
                              : "感谢您的订单\n订单号：${orderNumber}"
                        }
                        rows={8}
                        className={errors.content ? "border-red-500" : ""}
                      />
                      {errors.content && <p className="text-sm text-red-500">{errors.content}</p>}
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          )}

          {formData.type === "dingtalk" && (
            <Card>
              <CardHeader>
                <CardTitle>钉钉配置</CardTitle>
                <CardDescription>配置钉钉机器人和消息格式</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>Webhook URL</Label>
                  <Input
                    value={dingtalkConfig.webhookUrl}
                    onChange={(e) => setDingtalkConfig({ ...dingtalkConfig, webhookUrl: e.target.value })}
                    placeholder="https://oapi.dingtalk.com/robot/send?access_token=..."
                    className={errors.webhookUrl ? "border-red-500" : ""}
                  />
                  {errors.webhookUrl && <p className="text-sm text-red-500">{errors.webhookUrl}</p>}
                </div>

                <div className="space-y-2">
                  <Label>加签密钥</Label>
                  <Input
                    type="password"
                    value={dingtalkConfig.secret}
                    onChange={(e) => setDingtalkConfig({ ...dingtalkConfig, secret: e.target.value })}
                    placeholder="SEC..."
                  />
                </div>

                <div className="space-y-2">
                  <Label>消息类型</Label>
                  <Select
                    value={dingtalkConfig.messageType}
                    onValueChange={(value) => setDingtalkConfig({ ...dingtalkConfig, messageType: value as any })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="text">文本消息</SelectItem>
                      <SelectItem value="link">链接消息</SelectItem>
                      <SelectItem value="actionCard">卡片消息</SelectItem>
                      <SelectItem value="feedCard">图文消息</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>@手机号</Label>
                  <div className="space-y-2">
                    {(dingtalkConfig.atMobiles || []).map((mobile, index) => (
                      <div key={index} className="flex gap-2">
                        <Input
                          value={mobile}
                          onChange={(e) => updateRecipient(index, e.target.value, "dingtalk")}
                          placeholder="手机号码"
                        />
                        <Button variant="outline" size="sm" onClick={() => removeRecipient(index, "dingtalk")}>
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                    <Button variant="outline" onClick={() => addRecipient("dingtalk")} className="w-full">
                      <Plus className="h-4 w-4 mr-2" />
                      添加手机号
                    </Button>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>消息内容</Label>
                  <Textarea
                    value={dingtalkConfig.content}
                    onChange={(e) => setDingtalkConfig({ ...dingtalkConfig, content: e.target.value })}
                    placeholder="🚨 系统告警\n\n服务：${serviceName}\n错误：${errorMessage}\n时间：${timestamp}"
                    rows={6}
                    className={errors.content ? "border-red-500" : ""}
                  />
                  {errors.content && <p className="text-sm text-red-500">{errors.content}</p>}
                  <p className="text-sm text-muted-foreground">支持Markdown格式和变量替换</p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Error Summary */}
          {Object.keys(errors).length > 0 && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                请检查并修正以下错误：
                <ul className="mt-2 list-disc list-inside">
                  {Object.entries(errors).map(([field, error]) => (
                    <li key={field} className="text-sm">
                      {error}
                    </li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}
        </div>

        {/* Right Sidebar - Always visible */}
        <div className="lg:col-span-2 space-y-6">
          {/* Real-time Preview */}
          <ContentPreview
            type={formData.type}
            content={getCurrentContent()}
            subject={getCurrentSubject()}
            format={formData.type === "email" ? emailConfig.contentFormat : "text"}
            variables={getCurrentVariables()}
          />

          {/* Variable Manager */}
          <VariableManagerInline
            content={getCurrentContent()}
            subject={getCurrentSubject()}
            customVariables={getCurrentVariables()}
            onCustomVariablesChange={handleVariablesChange}
            onVariableInsert={handleVariableInsert}
          />
        </div>
      </div>

      {/* Test Dialog */}
      <Dialog open={showTestDialog} onOpenChange={setShowTestDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>测试通知配置</DialogTitle>
            <DialogDescription>正在测试通知配置的连接和参数设置</DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            {isTestingConfig ? (
              <div className="flex items-center justify-center py-8">
                <div className="flex items-center gap-3">
                  <Loader2 className="h-6 w-6 animate-spin" />
                  <span>正在测试配置...</span>
                </div>
              </div>
            ) : testResult ? (
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  {testResult.success ? (
                    <CheckCircle className="h-6 w-6 text-green-500" />
                  ) : (
                    <XCircle className="h-6 w-6 text-red-500" />
                  )}
                  <div>
                    <h4 className="font-medium">{testResult.message}</h4>
                    {testResult.details && <p className="text-sm text-muted-foreground">{testResult.details}</p>}
                  </div>
                </div>
                <div className="text-sm text-muted-foreground">
                  测试时间: {new Date(testResult.timestamp).toLocaleString()}
                </div>
              </div>
            ) : null}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}

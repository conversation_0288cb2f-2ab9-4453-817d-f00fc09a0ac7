/**
 * 仪表板组件示例代码集合
 * 
 * 按照新规范管理示例代码，避免在页面文件中硬编码
 */

import React from "react"
import {
  StatCard,
  QuickActions,
  ActivityList,
  ProductRanking,
  TaskList,
  SystemStatus,
  ChartCard,
  AnalyticsDashboard
} from "@/components/common-custom/dashboard"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  TrendingUp,
  TrendingDown,
  Users,
  ShoppingCart,
  DollarSign,
  Activity,
  Plus,
  FileText,
  Settings,
  Download,
  Upload,
  CheckCircle,
  Clock,
  AlertCircle,
  Star,
  Package,
  Zap,
  Shield,
  Wifi,
  Database,
  Server
} from "lucide-react"

// ============================================================================
// 统计卡片示例
// ============================================================================

export const statCardExample = {
  id: "stat-card",
  title: "统计卡片",
  description: "展示关键指标的统计卡片组件",
  code: `
import React from "react";
import { StatCard } from "@/components/common-custom/dashboard";

function StatCardExample() {
  const stats = [
    {
      title: "总用户数",
      value: "12,345",
      change: "+12%",
      trend: "up" as const,
      description: "较上月增长",
    },
    {
      title: "活跃用户",
      value: "8,901",
      change: "+5%",
      trend: "up" as const,
      description: "本月活跃",
    },
    {
      title: "收入",
      value: "¥234,567",
      change: "-3%",
      trend: "down" as const,
      description: "较上月下降",
    },
    {
      title: "转化率",
      value: "23.4%",
      change: "+1.2%",
      trend: "up" as const,
      description: "持续优化",
    },
  ];
  
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {stats.map((stat, index) => (
        <StatCard
          key={index}
          title={stat.title}
          value={stat.value}
          change={stat.change}
          trend={stat.trend}
          description={stat.description}
        />
      ))}
    </div>
  );
}

render(<StatCardExample />);
  `,
  scope: { StatCard, React },
}

// ============================================================================
// 快速操作示例
// ============================================================================

export const quickActionsExample = {
  id: "quick-actions",
  title: "快速操作",
  description: "常用操作的快捷入口组件",
  code: `
import React from "react";
import { QuickActions } from "@/components/common-custom/dashboard";
import { Plus, Users, FileText, Settings, Download, Upload } from "lucide-react";

function QuickActionsExample() {
  const actions = [
    {
      title: "新建用户",
      description: "添加新的系统用户",
      icon: Plus,
      onClick: () => console.log("新建用户"),
    },
    {
      title: "用户管理",
      description: "管理系统用户",
      icon: Users,
      onClick: () => console.log("用户管理"),
    },
    {
      title: "生成报告",
      description: "生成数据分析报告",
      icon: FileText,
      onClick: () => console.log("生成报告"),
    },
    {
      title: "系统设置",
      description: "配置系统参数",
      icon: Settings,
      onClick: () => console.log("系统设置"),
    },
    {
      title: "导出数据",
      description: "导出业务数据",
      icon: Download,
      onClick: () => console.log("导出数据"),
    },
    {
      title: "导入数据",
      description: "批量导入数据",
      icon: Upload,
      onClick: () => console.log("导入数据"),
    },
  ];
  
  return (
    <QuickActions
      title="快速操作"
      actions={actions}
      maxVisible={4}
    />
  );
}

render(<QuickActionsExample />);
  `,
  scope: { QuickActions, Plus, Users, FileText, Settings, Download, Upload, React },
}

// ============================================================================
// 活动列表示例
// ============================================================================

export const activityListExample = {
  id: "activity-list",
  title: "活动列表",
  description: "显示最近活动和操作记录",
  code: `
import React from "react";
import { ActivityList } from "@/components/common-custom/dashboard";

function ActivityListExample() {
  const activities = [
    {
      id: "1",
      user: "张三",
      action: "创建了新项目",
      target: "电商平台重构",
      time: "2分钟前",
      type: "create" as const,
    },
    {
      id: "2",
      user: "李四",
      action: "更新了任务状态",
      target: "用户界面设计",
      time: "5分钟前",
      type: "update" as const,
    },
    {
      id: "3",
      user: "王五",
      action: "完成了代码审查",
      target: "API接口开发",
      time: "10分钟前",
      type: "complete" as const,
    },
    {
      id: "4",
      user: "赵六",
      action: "删除了过期文件",
      target: "临时数据清理",
      time: "15分钟前",
      type: "delete" as const,
    },
    {
      id: "5",
      user: "钱七",
      action: "发布了新版本",
      target: "v2.1.0",
      time: "30分钟前",
      type: "publish" as const,
    },
  ];
  
  return (
    <ActivityList
      title="最近活动"
      activities={activities}
      maxItems={5}
      showViewAll={true}
      onViewAll={() => console.log("查看全部活动")}
    />
  );
}

render(<ActivityListExample />);
  `,
  scope: { ActivityList, React },
}

// ============================================================================
// 系统状态示例
// ============================================================================

export const systemStatusExample = {
  id: "system-status",
  title: "系统状态",
  description: "显示系统各模块的运行状态",
  code: `
import React from "react";
import { SystemStatus } from "@/components/common-custom/dashboard";

function SystemStatusExample() {
  const services = [
    {
      name: "API服务",
      status: "online" as const,
      uptime: "99.9%",
      responseTime: "120ms",
      lastCheck: "刚刚",
    },
    {
      name: "数据库",
      status: "online" as const,
      uptime: "99.8%",
      responseTime: "45ms",
      lastCheck: "1分钟前",
    },
    {
      name: "缓存服务",
      status: "warning" as const,
      uptime: "98.5%",
      responseTime: "200ms",
      lastCheck: "2分钟前",
    },
    {
      name: "文件存储",
      status: "offline" as const,
      uptime: "95.2%",
      responseTime: "超时",
      lastCheck: "5分钟前",
    },
  ];
  
  return (
    <SystemStatus
      title="系统状态"
      services={services}
      showRefresh={true}
      onRefresh={() => console.log("刷新系统状态")}
    />
  );
}

render(<SystemStatusExample />);
  `,
  scope: { SystemStatus, React },
}

// ============================================================================
// 综合仪表板示例
// ============================================================================

export const analyticsDashboardExample = {
  id: "analytics-dashboard",
  title: "综合仪表板",
  description: "完整的数据分析仪表板布局",
  code: `
import React from "react";
import { AnalyticsDashboard } from "@/components/common-custom/dashboard";

function AnalyticsDashboardExample() {
  const dashboardData = {
    stats: [
      { title: "总访问量", value: "125,430", change: "+12%", trend: "up" as const },
      { title: "新用户", value: "3,245", change: "+8%", trend: "up" as const },
      { title: "转化率", value: "3.2%", change: "-0.5%", trend: "down" as const },
      { title: "收入", value: "¥45,230", change: "+15%", trend: "up" as const },
    ],
    chartData: [
      { name: "1月", value: 400 },
      { name: "2月", value: 300 },
      { name: "3月", value: 600 },
      { name: "4月", value: 800 },
      { name: "5月", value: 500 },
      { name: "6月", value: 700 },
    ],
    activities: [
      { user: "系统", action: "生成月度报告", time: "刚刚", type: "system" as const },
      { user: "管理员", action: "更新配置", time: "5分钟前", type: "update" as const },
    ],
  };
  
  return (
    <div className="min-h-[600px]">
      <AnalyticsDashboard
        title="数据分析仪表板"
        data={dashboardData}
        refreshInterval={30000}
        onRefresh={() => console.log("刷新仪表板数据")}
      />
    </div>
  );
}

render(<AnalyticsDashboardExample />);
  `,
  scope: { AnalyticsDashboard, React },
}

// ============================================================================
// 统一导出所有示例
// ============================================================================

export const allExamples = [
  statCardExample,
  quickActionsExample,
  activityListExample,
  systemStatusExample,
  analyticsDashboardExample,
]

// ============================================================================
// 按类别导出示例
// ============================================================================

export const basicExamples = [statCardExample, quickActionsExample]
export const advancedExamples = [activityListExample, systemStatusExample]
export const comprehensiveExamples = [analyticsDashboardExample]

// ============================================================================
// 示例元数据
// ============================================================================

export const exampleMetadata = {
  total: allExamples.length,
  categories: {
    basic: basicExamples.length,
    advanced: advancedExamples.length,
    comprehensive: comprehensiveExamples.length,
  },
  tags: ["dashboard", "analytics", "stats", "charts", "activities"],
  lastUpdated: "2024-01-01",
}

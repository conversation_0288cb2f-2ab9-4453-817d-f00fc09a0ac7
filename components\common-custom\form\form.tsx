"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { Check, Loader2 } from "lucide-react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog"
import { FormProps, FormFieldProps } from "@/types/form"
import { FormField } from "./form-field"
import { FormSection } from "./form-section"

/**
 * 表单组件
 */
export function Form<T extends Record<string, any>>({
  title,
  description,
  fields,
  values,
  errors,
  onChange,
  onSubmit,
  submitLabel = "提交",
  cancelLabel = "取消",
  onCancel,
  isLoading,
  isSuccess,
  successMessage = "操作成功",
  children,
  className,
  cardProps,
  layout = "vertical",
  sections,
  renderField,
  // 对话框相关属性
  isDialog = false,
  dialogO<PERSON>,
  onDialogOpenChange,
  dialogMaxWidth = "sm",
}: FormProps<T>) {
  const [showSuccess, setShowSuccess] = useState(isSuccess)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit?.(values as T)
    if (isSuccess) setShowSuccess(true)
  }

  const handleFieldChange = (name: string, value: any) => {
    onChange?.(name, value)
  }

  const renderFormField = (field: FormFieldProps, index: number) => {
    if (renderField) {
      return renderField(field, index)
    }
    
    return (
      <FormField
        key={field.name || index}
        {...field}
        value={values?.[field.name]}
        error={errors?.[field.name]}
        onChange={handleFieldChange}
      />
    )
  }

  const renderFormContent = () => {
    return (
      <div className={cn("space-y-6", className)}>
        {sections ? (
          sections.map((section, index) => (
            <FormSection
              key={index}
              title={section.title}
              description={section.description}
            >
              <div className={cn(layout === "horizontal" && "grid grid-cols-1 md:grid-cols-2 gap-4")}>
                {section.fields.map(renderFormField)}
              </div>
            </FormSection>
          ))
        ) : (
          <div className={cn(layout === "horizontal" && "grid grid-cols-1 md:grid-cols-2 gap-4")}>
            {fields?.map(renderFormField)}
          </div>
        )}
        
        {children}
      </div>
    )
  }

  const renderFormButtons = () => (
    <div className="flex justify-end space-x-4">
      {onCancel && (
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isLoading}
        >
          {cancelLabel}
        </Button>
      )}
      
      <Button type="submit" disabled={isLoading}>
        {isLoading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            处理中
          </>
        ) : showSuccess ? (
          <>
            <Check className="mr-2 h-4 w-4" />
            {successMessage}
          </>
        ) : (
          submitLabel
        )}
      </Button>
    </div>
  )
  
  const getDialogWidthClass = () => {
    switch (dialogMaxWidth) {
      case "xs": return "max-w-xs"
      case "sm": return "max-w-sm"
      case "md": return "max-w-md"
      case "lg": return "max-w-lg"
      case "xl": return "max-w-xl"
      case "2xl": return "max-w-2xl"
      case "3xl": return "max-w-3xl"
      case "4xl": return "max-w-4xl"
      case "5xl": return "max-w-5xl"
      case "6xl": return "max-w-6xl"
      case "7xl": return "max-w-7xl"
      case "full": return "max-w-full"
      default: return "max-w-sm"
    }
  }

  // 对话框形式显示表单
  if (isDialog) {
    return (
      <Dialog open={dialogOpen} onOpenChange={onDialogOpenChange}>
        <DialogContent className={cn("sm:max-w-md", getDialogWidthClass())}>
          {title && (
            <DialogHeader>
              <DialogTitle>{title}</DialogTitle>
              {description && <DialogDescription>{description}</DialogDescription>}
            </DialogHeader>
          )}
          
          <form onSubmit={handleSubmit}>
            {renderFormContent()}
            
            <DialogFooter className="mt-6">
              {renderFormButtons()}
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    )
  }

  // 卡片形式显示表单
  return (
    <Card {...cardProps}>
      {title && (
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>
      )}
      
      <form onSubmit={handleSubmit}>
        <CardContent>
          {renderFormContent()}
        </CardContent>
        
        <CardFooter>
          {renderFormButtons()}
        </CardFooter>
      </form>
    </Card>
  )
} 
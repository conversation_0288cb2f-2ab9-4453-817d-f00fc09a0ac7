"use client"

import { ReactNode } from "react"
import { Badge } from "@/components/ui/badge"
import { Loader2 } from "lucide-react"
import { cn } from "@/lib/utils"
import type { SearchResultsProps, SearchItem } from "@/types/search"

/**
 * 搜索结果列表组件
 */
export function SearchResults<T>({
  results,
  onResultClick,
  grouped = false,
  loading = false,
  loadingText = "搜索中...",
  noResultsText = "无搜索结果",
  className,
  renderItem,
}: SearchResultsProps<T>) {
  // 处理图标
  const renderIcon = (icon: any): ReactNode => {
    if (!icon) return null
    
    // 如果是函数组件（包括 Lucide 图标）
    if (typeof icon === "function") {
      // 使用 JSX 语法渲染组件
      const IconComponent = icon
      return <IconComponent className="w-4 h-4 text-muted-foreground mr-2 flex-shrink-0" />
    }
    
    // 如果已经是 ReactNode（如 JSX 元素）
    return icon
  }

  // 默认渲染结果项
  const defaultRenderItem = (item: SearchItem<T>) => (
    <div className="flex items-start gap-2 w-full">
      {renderIcon(item.icon)}
      <div className="flex-1 min-w-0">
        <div className="font-medium truncate">{item.title}</div>
        {item.description && (
          <div className="text-sm text-muted-foreground truncate">{item.description}</div>
        )}
        {item.tags && item.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mt-1">
            {item.tags.map((tag, i) => (
              <Badge key={i} variant="secondary" className="text-xs px-1 py-0">
                {tag}
              </Badge>
            ))}
          </div>
        )}
      </div>
    </div>
  )

  if (loading) {
    return (
      <div className={cn("p-4 text-center", className)}>
        <Loader2 className="w-5 h-5 animate-spin mx-auto mb-2" />
        <p className="text-sm text-muted-foreground">{loadingText}</p>
      </div>
    )
  }

  if (results.length === 0) {
    return (
      <div className={cn("p-4 text-center", className)}>
        <p className="text-sm text-muted-foreground">{noResultsText}</p>
      </div>
    )
  }

  // 对于分组结果
  if (grouped) {
    return (
      <div className={className}>
        {results.map((group, groupIndex) => (
          <div key={groupIndex} className="mb-4 last:mb-0">
            {group.title && (
              <div className="px-2 py-1.5 text-sm font-medium text-muted-foreground">
                {group.title}
              </div>
            )}
            <div className="space-y-1">
              {group.items.map((item, index) => (
                <div
                  key={index}
                  className={cn(
                    "px-2 py-1.5 rounded-md cursor-pointer flex items-center",
                    "hover:bg-accent hover:text-accent-foreground"
                  )}
                  onClick={() => onResultClick && onResultClick(item.data, group.id)}
                >
                  {renderItem ? renderItem(item) : defaultRenderItem(item)}
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    )
  }

  // 对于非分组结果
  return (
    <div className={className}>
      <div className="space-y-1">
        {results.map((item, index) => (
          <div
            key={index}
            className={cn(
              "px-2 py-1.5 rounded-md cursor-pointer flex items-center",
              "hover:bg-accent hover:text-accent-foreground"
            )}
            onClick={() => onResultClick && onResultClick(item.data)}
          >
            {renderItem ? renderItem(item) : defaultRenderItem(item)}
          </div>
        ))}
      </div>
    </div>
  )
} 
import { notFound } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { HeaderWithBreadcrumb } from "@/components/custom/breadcrumb";
import { 
  ShieldCheck, 
  Info, 
  Tag 
} from "lucide-react";
import { getRoleByIdRequest } from "@/services/api/roleRequestApi";
import RoleEditForm from "@/components/pages/roles/role-edit-form";

// 获取角色信息
async function getRoleData(id: string) {
  const role = await getRoleByIdRequest(id);
  if (!role) {
    return null;
  }
  
  return role;
}

export default async function RoleEditPage(props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  const role = await getRoleData(params.id);

  // 角色不存在则显示404
  if (!role) {
    notFound();
  }

  // 面包屑数据
  const breadcrumbItems = [
    { label: "设置", href: "/settings" },
    { label: "角色管理", href: "/settings/roles" },
    { label: role.roleName, href: `/settings/roles/${role.id}` },
    { label: "编辑", active: true }
  ];

  return (
    <div className="flex flex-col min-h-screen">
      <HeaderWithBreadcrumb items={breadcrumbItems} />
      <main className="flex-1">
        <div className="container mx-auto px-6 py-6 max-w-7xl">
          <div className="space-y-6">
            {/* 角色编辑卡片 */}
            <Card className="shadow-sm">
              <CardHeader>
                <div>
                  <CardTitle className="text-2xl flex items-center gap-2">
                    <ShieldCheck className="h-6 w-6 text-primary" />
                    编辑角色
                  </CardTitle>
                  <CardDescription className="flex items-center gap-1 mt-1">
                    <Info className="h-3 w-3" />
                    修改角色信息
                  </CardDescription>
                </div>
              </CardHeader>
              <CardContent>
                <RoleEditForm role={role} />
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
} 
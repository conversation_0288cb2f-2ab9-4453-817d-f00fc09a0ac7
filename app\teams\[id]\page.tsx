"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useSearchParams } from "next/navigation"
import { use } from "react"
import Link from "next/link"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import {
  AlertTriangle,
  ArrowLeft,
  Building,
  Calendar,
  Edit,
  Eye,
  FileDown,
  FileUp,
  MoreHorizontal,
  Plus,
  Shield,
  Trash2,
  User,
  UserPlus,
  Users,
  CheckCircle2,
  Bell,
  Clock,
  Activity,
  BarChart3,
  Search,
  Mail,
  Phone,
  ChevronRight,
  Pencil,
  Save,
  X,
  Loader2,
  AlertCircle,
  Settings,
  Globe2,
  Lock,
  FileText,
  Folder,
  ExternalLink
} from "lucide-react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Switch } from "@/components/ui/switch"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { HeaderWithBreadcrumb, type BreadcrumbItem } from "@/components/project-custom/breadcrumb"
import { TruncateText } from "@/components/common-custom/truncate-text"
import { getTeamDetailRequest, updateTeamRequest, deleteTeamRequest, getTeamOptionsRequest } from "@/services/api/teamRequestApi"
import { BackButton } from "@/components/common-custom/back-button"
import { IconSelector } from "@/components/common-custom/icon-selector"
import { TeamOverviewPanel } from "@/components/pages/team/TeamOverviewPanel"
import { NavigationLink } from "@/components/custom/navigation-link"

// 保留成员数据用于成员管理页签
const members = [
  {
    id: "user1",
    name: "张三",
    avatar: "/placeholder.svg?height=40&width=40",
    role: "部门总监",
    email: "<EMAIL>",
    joinDate: "2023-01-15",
  },
  {
    id: "user2",
    name: "李四",
    avatar: "/placeholder.svg?height=40&width=40",
    role: "技术经理",
    email: "<EMAIL>",
    joinDate: "2023-02-20",
  },
  {
    id: "user3",
    name: "王五",
    avatar: "/placeholder.svg?height=40&width=40",
    role: "高级开发",
    email: "<EMAIL>",
    joinDate: "2023-03-10",
  },
  {
    id: "user4",
    name: "赵六",
    avatar: "/placeholder.svg?height=40&width=40",
    role: "开发工程师",
    email: "<EMAIL>",
    joinDate: "2023-04-05",
  },
  {
    id: "user5",
    name: "钱七",
    avatar: "/placeholder.svg?height=40&width=40",
    role: "测试工程师",
    email: "<EMAIL>",
    joinDate: "2023-05-15",
  },
]

// 保留角色数据用于角色管理页签
const roles = [
  {
    id: "role1",
    name: "管理员",
    description: "拥有所有权限",
    memberCount: 3,
    permissions: ["all"],
  },
  {
    id: "role2",
    name: "部门总监",
    description: "管理部门所有事务",
    memberCount: 1,
    permissions: ["team.manage", "member.manage", "role.view"],
  },
  {
    id: "role3",
    name: "技术经理",
    description: "管理技术团队",
    memberCount: 4,
    permissions: ["team.view", "member.manage", "role.view"],
  },
  {
    id: "role4",
    name: "开发工程师",
    description: "开发人员",
    memberCount: 25,
    permissions: ["team.view", "member.view"],
  },
  {
    id: "role5",
    name: "测试工程师",
    description: "测试人员",
    memberCount: 12,
    permissions: ["team.view", "member.view"],
  },
]

// 保留用于选择成员的选项
const userOptions = [
  { value: "user1", label: "张三" },
  { value: "user2", label: "李四" },
  { value: "user3", label: "王五" },
  { value: "user4", label: "赵六" },
  { value: "user5", label: "钱七" },
]

// 定义项目数据用于项目列表页签
const projects = [
  { id: 1, name: "数据分析平台", status: "进行中", progress: 65, dueDate: "2024-06-30" },
  { id: 2, name: "移动端重构", status: "规划中", progress: 10, dueDate: "2024-08-15" },
  { id: 3, name: "内容管理系统", status: "已完成", progress: 100, dueDate: "2024-03-10" },
  { id: 4, name: "用户体验改进", status: "进行中", progress: 35, dueDate: "2024-07-20" }
];

interface TeamDetailPageProps {
  params: Promise<{ id: string }> | { id: string };
}

export default function TeamDetailPage({ params }: TeamDetailPageProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const isEditMode = searchParams.get("mode") === "edit"
  
  // 使用React.use解包params
  const resolvedParams = params instanceof Promise ? use(params) : params;
  const teamId = resolvedParams.id;
  const [activeTab, setActiveTab] = useState("overview")
  const [showAddMemberDialog, setShowAddMemberDialog] = useState(false)
  const [showAddRoleDialog, setShowAddRoleDialog] = useState(false)
  const [showDeleteTeamDialog, setShowDeleteTeamDialog] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [updateError, setUpdateError] = useState<string | null>(null)
  const [deleteConfirmName, setDeleteConfirmName] = useState("")
  const [isDeleting, setIsDeleting] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [teamOptions, setTeamOptions] = useState<Array<{id: number; label: string; value: string}>>([])
  
  // 团队数据状态 - 保留并补充活跃成员数量
  const [teamData, setTeamData] = useState({
    id: "",
    name: "加载中...",
    description: "",
    createdAt: "",
    memberCount: 0,
    activeMembers: 0, // 添加活跃成员数量字段
    subTeamCount: 0,
    status: "启用",
    isPrivate: false,
    themeColor: "blue",
    teamType: "tech",
    parentCode: null as string | null,
    parentName: null as string | null,
    teamCode: "",
    teamLogo: "/placeholder.svg",
  })
  
  // 可编辑团队数据
  const [editableTeamData, setEditableTeamData] = useState({
    name: "",
    description: "",
    status: "启用",
    themeColor: "blue", 
    teamType: "tech",
    isPrivate: false,
    parentCode: null as string | null,
    parentName: null as string | null,
    teamCode: "",
    teamLogo: "/placeholder.svg",
    icon: "Building"
  })
  
  // 是否处于编辑状态
  const [isEditing, setIsEditing] = useState(false)

  // 获取团队选项数据
  useEffect(() => {
    const fetchTeamOptions = async () => {
      try {
        const options = await getTeamOptionsRequest();
        // 添加默认选项
        const allOptions = [
          { id: 0, label: "无上级团队（顶级团队）", value: "0" },
          ...options.filter(team => team.value !== teamId) // 排除当前团队
        ];
        setTeamOptions(allOptions);

        // 如果已有父团队ID，尝试设置父团队名称
        if (teamData.parentCode) {
          const parentTeam = options.find(option => option.value === teamData.parentCode);
          if (parentTeam) {
            setTeamData(prev => ({
              ...prev,
              parentName: parentTeam.label
            }));
            setEditableTeamData(prev => ({
              ...prev,
              parentName: parentTeam.label
            }));
          }
        }
      } catch (error) {
        console.error("获取团队选项失败:", error);
      }
    };
    
    fetchTeamOptions();
  }, [teamId, teamData.parentCode]);

  // 获取团队详情数据
  useEffect(() => {
    const fetchTeamDetail = async () => {
      setIsLoading(true);
      try {
        const teamDetail = await getTeamDetailRequest(Number(teamId));
        
        if (teamDetail) {
          // 成员数量安全处理
          const memberCount = teamDetail.memberCount || 0;
          
          // 将API返回的数据映射到前端使用的字段
          const mappedData = {
            id: teamDetail.id.toString(),
            name: teamDetail.teamName,
            description: teamDetail.description || "",
            createdAt: teamDetail.createTime ? teamDetail.createTime.substring(0, 10) : "",
            memberCount: memberCount,
            activeMembers: Math.round(memberCount * 0.8), // 假设80%的成员活跃
            subTeamCount: 3, // 暂时固定值
            status: teamDetail.status === 1 ? "启用" : "停用",
            isPrivate: teamDetail.privateTag === 1,
            themeColor: mapThemeColor(teamDetail.teamThemeColor),
            teamType: teamDetail.teamType || "tech",
            parentId: teamDetail.parentCode ? teamDetail.parentCode.toString() : null,
            parentName: null, // 会通过teamOptions接口更新
            teamCode: teamDetail.teamCode,
            teamLogo: teamDetail.teamLogo || "/placeholder.svg",
          };

          setTeamData(mappedData);
          
          // 同步更新可编辑数据
          setEditableTeamData({
            name: mappedData.name,
            description: mappedData.description,
            status: mappedData.status,
            themeColor: mappedData.themeColor,
            teamType: mappedData.teamType,
            isPrivate: mappedData.isPrivate,
            parentCode: mappedData.parentCode,
            parentName: mappedData.parentName,
            teamCode: mappedData.teamCode,
            teamLogo: mappedData.teamLogo,
            icon: "Building" // 添加默认图标字段
          });
        }
      } catch (error) {
        console.error("获取团队详情失败:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTeamDetail();
  }, [teamId]);

  // 主题色映射函数
  const mapThemeColor = (colorCode: string) => {
    // 根据色值返回对应的颜色名称
    const colorMap: Record<string, string> = {
      "#000000": "black",
      "#3B82F6": "blue",
      "#10B981": "green",
      "#EF4444": "red",
      "#8B5CF6": "purple",
      "#F97316": "orange",
      "#6366F1": "indigo",
      "#EC4899": "pink",
      "#F59E0B": "amber",
      "#6B7280": "gray",
    };
    
    // 查找最接近的颜色
    return colorMap[colorCode] || "blue";
  };

  // 初始化时，如果是编辑模式，自动进入编辑状态
  useEffect(() => {
    if (isEditMode) {
      setIsEditing(true)
    }
  }, [isEditMode])

  const handleDeleteTeam = async () => {
    if (deleteConfirmName !== teamData.name) return
    
    setIsDeleting(true)
    
    try {
      // 调用删除API
      const success = await deleteTeamRequest(teamId)
      
      if (success) {
        setShowDeleteTeamDialog(false)
        router.push('/teams')
      } else {
        // 如果API返回失败
        alert("删除团队失败，请稍后重试")
      }
    } catch (error) {
      console.error("删除团队失败:", error)
      alert("删除团队失败，请稍后重试")
    } finally {
      setDeleteConfirmName("")
      setIsDeleting(false)
    }
  }

  const handleCopyTeamId = () => {
    navigator.clipboard.writeText(teamId)
    setTimeout(() => {
      // 这里不需要设置 showCopiedNotification，因为它是未使用的变量
    }, 2000)
  }
  
  // 处理编辑保存
  const handleSaveEdit = async () => {
    // 验证表单数据
    if (!editableTeamData.name.trim()) {
      setUpdateError("团队名称不能为空")
      return
    }
    
    setIsSaving(true)
    setUpdateError(null)
    
    try {
      // 构建请求参数，仅包含API需要的字段
      const teamDataToUpdate = {
        id: teamId,
        teamName: editableTeamData.name.trim(),
        description: editableTeamData.description.trim(),
        status: editableTeamData.status === "启用" ? 1 : 0,
        teamThemeColor: getColorCode(editableTeamData.themeColor),
        teamType: editableTeamData.teamType,
        teamCode: editableTeamData.teamCode,
        teamLogo: editableTeamData.teamLogo,
        privateTag: editableTeamData.isPrivate ? 1 : 0,
        parentId: editableTeamData.parentId ? Number(editableTeamData.parentId) : null
      }
      
      // 调用API更新团队
      const success = await updateTeamRequest(teamDataToUpdate)
      
      if (success) {
        // 更新本地数据
        Object.assign(teamData, {
          name: editableTeamData.name,
          description: editableTeamData.description,
          status: editableTeamData.status,
          themeColor: editableTeamData.themeColor,
          teamType: editableTeamData.teamType,
          parentId: editableTeamData.parentId,
          parentName: editableTeamData.parentName,
          isPrivate: editableTeamData.isPrivate,
          teamCode: editableTeamData.teamCode,
          teamLogo: editableTeamData.teamLogo
        })
        
        setIsEditing(false)
        
        // 如果是从编辑模式进入的，保存后返回查看模式
        if (isEditMode) {
          router.push(`/teams/${teamId}`)
        }
      } else {
        setUpdateError("更新团队信息失败")
      }
    } catch (error) {
      console.error("保存团队信息失败:", error)
      setUpdateError("保存团队信息失败，请稍后重试")
    } finally {
      setIsSaving(false)
    }
  }
  
  // 根据颜色名称获取颜色代码
  const getColorCode = (colorName: string): string => {
    const colorMap: Record<string, string> = {
      "black": "#000000",
      "blue": "#3B82F6",
      "green": "#10B981",
      "red": "#EF4444",
      "purple": "#8B5CF6",
      "orange": "#F97316",
      "indigo": "#6366F1",
      "pink": "#EC4899",
      "amber": "#F59E0B",
      "gray": "#6B7280",
    };
    
    return colorMap[colorName] || "#3B82F6";
  };
  
  // 处理取消编辑
  const handleCancelEdit = () => {
    // 重置为原始数据
    setEditableTeamData({
      name: teamData.name,
      description: teamData.description,
      status: teamData.status || "启用",
      themeColor: teamData.themeColor || "blue",
      teamType: teamData.teamType || "tech",
      isPrivate: teamData.isPrivate || false,
      parentId: teamData.parentId,
      parentName: teamData.parentName,
      teamCode: teamData.teamCode,
      teamLogo: teamData.teamLogo,
      icon: "Building" // 添加默认图标字段
    })
    setUpdateError(null)
    setIsEditing(false)
    // 如果是从编辑模式进入的，取消后返回查看模式
    if (isEditMode) {
      router.push(`/teams/${teamId}`)
    }
  }

  // 面包屑项目
  const breadcrumbItems: BreadcrumbItem[] = [
    { label: "首页", href: "/" },
    { label: "团队管理", href: "/teams" },
    { label: teamData.name, isCurrent: true }
  ]

  // 模拟使用率数据
  const usageStats = {
    members: { current: teamData.memberCount, max: 50, percentage: (teamData.memberCount / 50) * 100 },
    subTeams: { current: teamData.subTeamCount, max: 10, percentage: (teamData.subTeamCount / 10) * 100 },
    storage: { current: 245, max: 500, percentage: (245 / 500) * 100 }
  };

  // 近期活动数据
  const recentActivities = [
    { id: 1, type: "member_added", user: "李四", target: "王五", date: "2024-01-15 14:30" },
    { id: 2, type: "role_changed", user: "张三", target: "王五", role: "技术经理", date: "2024-01-14 09:15" },
    { id: 3, type: "team_updated", user: "张三", date: "2024-01-10 16:45" }
  ];

  // 获取活动图标
  const getActivityIcon = (type: string) => {
    switch (type) {
      case "create":
        return <Plus className="h-4 w-4" />
      case "update":
        return <Edit className="h-4 w-4" />
      case "delete":
        return <Trash2 className="h-4 w-4" />
      case "member":
        return <User className="h-4 w-4" />
      default:
        return <Activity className="h-4 w-4" />
    }
  }

  return (
    <div className="flex min-h-screen w-full flex-col bg-gradient-to-b from-background to-background/80">
      <HeaderWithBreadcrumb items={breadcrumbItems} />
      <div className="p-4 md:p-6 max-w-7xl mx-auto w-full">
        <main className="space-y-6">
          {/* 修改顶部区域：左侧回退按钮和标题，右侧操作按钮 */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <BackButton
                href="/teams"
                onNavigate={(href) => router.push(href)}
              />
              {!isLoading && (
                <div 
                  className="h-10 w-10 rounded-full flex items-center justify-center flex-shrink-0"
                  style={{ 
                    backgroundColor: `${getColorCode(teamData.themeColor)}20`,
                  }}
                >
                  <Building className="h-6 w-6" style={{ color: getColorCode(teamData.themeColor) }} />
                </div>
              )}
              <div className="flex flex-col">
                <div className="flex items-center gap-2">
                  <h1 className="text-2xl font-bold tracking-tight">
                    {isLoading ? "加载中..." : teamData.name}
                  </h1>
                  {!isLoading && (
                    <Badge 
                      variant={teamData.status === "启用" ? "default" : "destructive"} 
                      className={`${teamData.status === "启用" ? "bg-green-500/80" : ""}`}
                    >
                      {teamData.status}
                    </Badge>
                  )}
                </div>
                {!isLoading && (
                  <div className="flex items-center text-sm text-muted-foreground mt-1">
                    <span>{teamData.teamCode && `代码: ${teamData.teamCode}`}</span>
                    {teamData.description && (
                      <div className="flex items-center ml-3">
                        <span className="mx-1">·</span>
                        <AlertCircle className="h-3.5 w-3.5 mr-1" />
                        <TruncateText text={teamData.description} maxWidth="280px" />
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
            
            {!isLoading && (
              <div className="flex gap-3">
                {!isEditing ? (
                  <>
                    <Button 
                      size="sm"
                      variant="outline"
                      className="flex items-center gap-2 border border-border text-foreground hover:bg-muted/10"
                      onClick={() => router.push(`/teams/${teamId}?mode=edit`)}
                    >
                      <Pencil className="h-3.5 w-3.5" />
                      <span>编辑信息</span>
                    </Button>
                    <Button 
                      size="sm"
                      variant="outline"
                      className="flex items-center gap-2 border border-border text-foreground hover:bg-muted/10"
                      onClick={() => setShowAddMemberDialog(true)}
                    >
                      <UserPlus className="h-3.5 w-3.5" />
                      <span>添加成员</span>
                    </Button>
                  </>
                ) : (
                  <div className="flex gap-2">
                    <Button 
                      variant="outline" 
                      size="sm"
                      className="border border-border text-foreground hover:bg-muted/10"
                      onClick={handleCancelEdit}
                    >
                      取消
                    </Button>
                    <Button 
                      size="sm"
                      variant="default"
                      onClick={handleSaveEdit}
                      disabled={isSaving}
                    >
                      {isSaving ? (
                        <>
                          <div className="mr-2 h-3.5 w-3.5 animate-spin rounded-full border-2 border-background border-t-transparent"></div>
                          保存中
                        </>
                      ) : "保存更改"}
                    </Button>
                  </div>
                )}
              </div>
            )}
          </div>
          
          {/* 修改tab页卡片选中时的边框样式 */}
          <div className="mb-8">
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
              <div 
                className={`rounded-lg p-4 cursor-pointer transition-all ${
                  activeTab === "overview" 
                    ? "bg-primary/10 shadow-sm" 
                    : "hover:bg-muted/50"
                }`}
                onClick={() => setActiveTab("overview")}
              >
                <div className="flex items-center gap-3">
                  <div className={`${activeTab === "overview" ? "bg-primary/15" : "bg-muted/50"} text-primary h-10 w-10 rounded-md flex items-center justify-center transition-all`}>
                    <Activity className="h-5 w-5" />
                  </div>
                  <div>
                    <div className="font-medium">概览</div>
                    <div className="text-xs text-muted-foreground">查看团队基本信息</div>
                  </div>
                </div>
              </div>
              
              <div 
                className={`rounded-lg p-4 cursor-pointer transition-all ${
                  activeTab === "projects" 
                    ? "bg-amber-500/10 shadow-sm" 
                    : "hover:bg-muted/50"
                }`}
                onClick={() => setActiveTab("projects")}
              >
                <div className="flex items-center gap-3">
                  <div className={`${activeTab === "projects" ? "bg-amber-500/15" : "bg-muted/50"} text-amber-500 h-10 w-10 rounded-md flex items-center justify-center transition-all`}>
                    <FileText className="h-5 w-5" />
                  </div>
                  <div>
                    <div className="font-medium">项目列表</div>
                    <div className="text-xs text-muted-foreground">查看团队项目</div>
                  </div>
                </div>
              </div>
              
              <div 
                className={`rounded-lg p-4 cursor-pointer transition-all ${
                  activeTab === "members" 
                    ? "bg-blue-500/10 shadow-sm" 
                    : "hover:bg-muted/50"
                }`}
                onClick={() => setActiveTab("members")}
              >
                <div className="flex items-center gap-3">
                  <div className={`${activeTab === "members" ? "bg-blue-500/15" : "bg-muted/50"} text-blue-500 h-10 w-10 rounded-md flex items-center justify-center transition-all`}>
                    <Users className="h-5 w-5" />
                  </div>
                  <div>
                    <div className="font-medium">成员管理</div>
                    <div className="text-xs text-muted-foreground">管理团队成员</div>
                  </div>
                </div>
              </div>
              
              <div 
                className={`rounded-lg p-4 cursor-pointer transition-all ${
                  activeTab === "settings" 
                    ? "bg-purple-500/10 shadow-sm" 
                    : "hover:bg-muted/50"
                }`}
                onClick={() => setActiveTab("settings")}
              >
                <div className="flex items-center gap-3">
                  <div className={`${activeTab === "settings" ? "bg-purple-500/15" : "bg-muted/50"} text-purple-500 h-10 w-10 rounded-md flex items-center justify-center transition-all`}>
                    <Settings className="h-5 w-5" />
                  </div>
                  <div>
                    <div className="font-medium">高级设置</div>
                    <div className="text-xs text-muted-foreground">管理团队配置</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 内容区域 - 简化卡片设计 */}
          <div className="space-y-8">
            {/* 修改概览页内容区域，移除左上角标题和操作按钮 */}
            {activeTab === "overview" && (
              <div className="bg-card rounded-xl p-6">
                {isLoading ? (
                  <div className="flex justify-center items-center py-12">
                    <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                    <span className="ml-2 text-muted-foreground">加载团队信息中...</span>
                  </div>
                ) : isEditing ? (
                  // 编辑模式 - 保留原来的编辑表单
                  <div className="space-y-6">
                    {/* 基本信息表单组 - 修改为与新建页一致的结构和顺序 */}
                    <div className="space-y-6">
                      <div className="pb-4 border-b">
                        <h3 className="font-medium">基本信息</h3>
                      </div>
                      
                      <div className="space-y-4 pl-1">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                          <div className="space-y-1.5">
                            <Label htmlFor="team-name" className="text-sm font-medium">
                              团队名称
                              <span className="text-destructive ml-1">*</span>
                            </Label>
                            <Input
                              id="team-name"
                              placeholder="输入团队名称"
                              value={editableTeamData.name || ""}
                              onChange={(e) => setEditableTeamData({...editableTeamData, name: e.target.value})}
                              className={`h-9 ${updateError ? 'border-destructive focus-visible:ring-destructive/20' : ''}`}
                              required
                            />
                            {updateError && (
                              <p className="text-xs text-destructive mt-1 flex items-center">
                                <AlertCircle className="h-3 w-3 mr-1" />
                                {updateError}
                              </p>
                            )}
                          </div>

                          <div className="space-y-1.5">
                            <Label htmlFor="parent-team" className="text-sm font-medium">上级团队</Label>
                            <Select
                              value={editableTeamData.parentId || "0"}
                              onValueChange={(value) => {
                                const selectedParent = teamOptions.find(t => t.value === value);
                                setEditableTeamData({
                                  ...editableTeamData,
                                  parentId: value === "0" ? null : value,
                                  parentName: value === "0" ? null : selectedParent?.label || null
                                });
                              }}
                            >
                              <SelectTrigger className="h-9">
                                <SelectValue placeholder="选择上级团队" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="0">无上级团队（顶级团队）</SelectItem>
                                {teamOptions
                                  .filter(team => team.value !== teamId && team.value !== "0")
                                  .map(team => (
                                    <SelectItem key={team.value} value={team.value}>
                                      {team.label}
                                    </SelectItem>
                                  ))
                                }
                              </SelectContent>
                            </Select>
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                          <div className="space-y-1.5">
                            <Label htmlFor="team-code" className="text-sm font-medium">
                              团队代码
                              <span className="text-destructive ml-1">*</span>
                            </Label>
                            <Input
                              id="team-code"
                              placeholder="输入团队代码"
                              value={editableTeamData.teamCode || ""}
                              disabled
                              className="h-9"
                            />
                            <p className="text-xs text-muted-foreground mt-1">团队代码创建后不可更改</p>
                          </div>

                          <div className="space-y-1.5">
                            <Label htmlFor="team-status" className="text-sm font-medium">
                              团队状态
                              <span className="text-destructive ml-1">*</span>
                            </Label>
                            <div className="flex items-center gap-2 mt-1">
                              <Button
                                type="button"
                                variant={editableTeamData.status === "启用" ? "default" : "outline"}
                                onClick={() => setEditableTeamData({...editableTeamData, status: "启用"})}
                                className="h-9 px-3"
                              >
                                <CheckCircle2 className={`h-4 w-4 mr-1.5 ${editableTeamData.status === "启用" ? "" : "text-muted-foreground"}`} />
                                启用
                              </Button>
                              <Button
                                type="button"
                                variant={editableTeamData.status === "停用" ? "default" : "outline"}
                                onClick={() => setEditableTeamData({...editableTeamData, status: "停用"})}
                                className="h-9 px-3"
                              >
                                <AlertCircle className={`h-4 w-4 mr-1.5 ${editableTeamData.status === "停用" ? "" : "text-muted-foreground"}`} />
                                停用
                              </Button>
                            </div>
                          </div>
                        </div>

                        <div className="space-y-1.5">
                          <Label htmlFor="team-description" className="text-sm font-medium">团队描述</Label>
                          <Textarea
                            id="team-description"
                            placeholder="输入团队描述（团队的主要职责、业务范围等）"
                            value={editableTeamData.description || ""}
                            onChange={(e) => setEditableTeamData({...editableTeamData, description: e.target.value})}
                            rows={2}
                            className="resize-none"
                          />
                          <div className="flex justify-end">
                            <p className="text-xs text-muted-foreground">
                              {editableTeamData.description?.length || 0}/200
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    {/* 外观与权限表单组 - 与新建页保持一致 */}
                    <div className="space-y-6 mt-8">
                      <div className="pb-4 border-b">
                        <h3 className="font-medium">外观与权限</h3>
                      </div>
                      
                      <div className="space-y-6 pl-1">
                        <div className="space-y-2">
                          <Label htmlFor="team-color" className="text-sm font-medium">
                            团队主题色
                            <span className="text-destructive ml-1">*</span>
                          </Label>
                          <p className="text-xs text-muted-foreground mt-0.5">选择一个代表团队的颜色</p>
                          <div className="flex flex-wrap gap-2 mt-2">
                            {["black", "blue", "green", "red", "purple", "orange", "indigo", "pink", "amber", "gray"].map(color => (
                              <TooltipProvider key={color}>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <button
                                      type="button"
                                      className={[
                                        'w-7 h-7 rounded-full transition-all focus:outline-none flex-shrink-0',
                                        editableTeamData.themeColor === color ? 'scale-110 ring-2 ring-offset-1 ring-primary' : 'opacity-50 hover:opacity-80',
                                        color === 'black' ? `bg-black ${editableTeamData.themeColor !== color ? 'bg-opacity-70' : ''}` :
                                          color === 'blue' ? `bg-blue-500 ${editableTeamData.themeColor !== color ? 'bg-opacity-70' : ''}` :
                                            color === 'green' ? `bg-green-500 ${editableTeamData.themeColor !== color ? 'bg-opacity-70' : ''}` :
                                              color === 'red' ? `bg-red-500 ${editableTeamData.themeColor !== color ? 'bg-opacity-70' : ''}` :
                                                color === 'purple' ? `bg-purple-500 ${editableTeamData.themeColor !== color ? 'bg-opacity-70' : ''}` :
                                                  color === 'orange' ? `bg-orange-500 ${editableTeamData.themeColor !== color ? 'bg-opacity-70' : ''}` :
                                                    color === 'indigo' ? `bg-indigo-500 ${editableTeamData.themeColor !== color ? 'bg-opacity-70' : ''}` :
                                                      color === 'pink' ? `bg-pink-500 ${editableTeamData.themeColor !== color ? 'bg-opacity-70' : ''}` :
                                                        color === 'amber' ? `bg-amber-500 ${editableTeamData.themeColor !== color ? 'bg-opacity-70' : ''}` :
                                                          `bg-gray-500 ${editableTeamData.themeColor !== color ? 'bg-opacity-70' : ''}`
                                      ].join(' ')}
                                      onClick={() => setEditableTeamData({...editableTeamData, themeColor: color})}
                                      aria-label={`选择${color}颜色`}
                                    />
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p className="capitalize">{
                                      color === "black" ? "黑色" :
                                        color === "blue" ? "蓝色" :
                                          color === "green" ? "绿色" :
                                            color === "red" ? "红色" :
                                              color === "purple" ? "紫色" :
                                                color === "orange" ? "橙色" :
                                                  color === "indigo" ? "靛蓝色" :
                                                    color === "pink" ? "粉色" :
                                                      color === "amber" ? "琥珀色" :
                                                        "灰色"
                                    }</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            ))}
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                          <div className="space-y-2">
                            <Label htmlFor="team-icon" className="text-sm font-medium">
                              团队图标
                              <span className="text-destructive ml-1">*</span>
                            </Label>
                            <div className="flex items-start gap-3">
                              <div className="w-12 h-12 flex items-center justify-center overflow-hidden rounded-md bg-muted/30">
                                <div className="flex items-center justify-center w-full h-full">
                                  <IconSelector
                                    value={editableTeamData.icon}
                                    onChange={(value) => setEditableTeamData({...editableTeamData, icon: value})}
                                    className="flex items-center justify-center w-full h-full"
                                    iconColor={getColorCode(editableTeamData.themeColor)}
                                  />
                                </div>
                              </div>

                              <div className="space-y-1.5">
                                <label
                                  htmlFor="avatar-upload"
                                  className="cursor-pointer inline-flex items-center gap-1.5 px-3 py-1.5 bg-muted rounded-md hover:bg-muted/80 transition-colors text-sm"
                                >
                                  <FileUp className="h-3.5 w-3.5" />
                                  <span>上传图标</span>
                                  <input
                                    id="avatar-upload"
                                    type="file"
                                    accept="image/*"
                                    className="hidden"
                                  />
                                </label>
                                <p className="text-xs text-muted-foreground">推荐尺寸：256x256像素</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  // 查看模式 - 使用新的组件
                  <>
                    <TeamOverviewPanel 
                      teamName={teamData.name}
                      stats={{
                        memberCount: teamData.memberCount,
                        activeMembers: teamData.activeMembers,
                        subTeamCount: teamData.subTeamCount,
                        createdAt: teamData.createdAt
                      }}
                      isLoading={isLoading}
                    />
                  </>
                )}
              </div>
            )}
            
            {activeTab === "members" && (
              <div className="bg-card rounded-xl p-6">
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-6">
                  <div>
                    <h2 className="text-xl font-semibold">成员管理</h2>
                    <p className="text-sm text-muted-foreground">管理团队成员和权限</p>
                  </div>
                  <div className="flex gap-2">
                    <div className="relative w-[180px]">
                      <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        type="search"
                        placeholder="搜索成员..."
                        className="pl-8 h-9 border border-input/30 bg-background/50 ring-1 ring-border/5 hover:border-input/50 transition-colors"
                      />
                    </div>
                    <Button className="h-9" onClick={() => setShowAddMemberDialog(true)}>
                      <UserPlus className="h-4 w-4 mr-2" />
                      添加成员
                    </Button>
                  </div>
                </div>
                
                <div className="overflow-hidden bg-card border border-border/30 rounded-lg">
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader className="bg-muted/30">
                        <TableRow className="hover:bg-transparent border-b border-border/30">
                          <TableHead className="font-medium py-3 px-4 w-[240px]">成员</TableHead>
                          <TableHead className="font-medium py-3 px-4">角色</TableHead>
                          <TableHead className="font-medium py-3 px-4 hidden md:table-cell">邮箱</TableHead>
                          <TableHead className="font-medium py-3 px-4 hidden md:table-cell">加入时间</TableHead>
                          <TableHead className="font-medium py-3 px-4 text-right w-[100px]">操作</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {members.map((member) => (
                          <TableRow key={member.id} className="border-b border-border/10 hover:bg-muted/20 transition-colors">
                            <TableCell className="py-3 px-4">
                              <div className="flex items-center gap-3">
                                <Avatar className="h-9 w-9 border-none ring-2 ring-background">
                                  <AvatarImage src={member.avatar} alt={member.name} />
                                  <AvatarFallback>{member.name.slice(0, 1)}</AvatarFallback>
                                </Avatar>
                                <div>
                                  <div className="font-medium">{member.name}</div>
                                  <div className="text-xs text-muted-foreground hidden sm:block mt-0.5">
                                    {member.email.split('@')[0]}
                                  </div>
                                </div>
                              </div>
                            </TableCell>
                            <TableCell className="py-3 px-4">
                              <Badge variant="secondary" className="border-none bg-muted/50">{member.role}</Badge>
                            </TableCell>
                            <TableCell className="py-3 px-4 hidden md:table-cell text-muted-foreground">{member.email}</TableCell>
                            <TableCell className="py-3 px-4 hidden md:table-cell text-muted-foreground">{member.joinDate}</TableCell>
                            <TableCell className="py-3 px-4 text-right">
                              <div className="flex items-center justify-end gap-1">
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Button variant="ghost" size="icon" className="h-8 w-8 hover:bg-muted/50 transition-colors">
                                        <Edit className="h-4 w-4" />
                                      </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>编辑</TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Button variant="ghost" size="icon" className="h-8 w-8 hover:bg-destructive/10 hover:text-destructive transition-colors">
                                        <Trash2 className="h-4 w-4" />
                                      </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>删除</TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              </div>
            )}
            
            {activeTab === "projects" && (
              <div className="bg-card rounded-xl p-6">
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-6">
                  <div>
                    <h2 className="text-xl font-semibold">项目列表</h2>
                    <p className="text-sm text-muted-foreground">管理团队项目</p>
                  </div>
                  <div className="flex gap-2">
                    <div className="relative w-[180px]">
                      <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        type="search"
                        placeholder="搜索项目..."
                        className="pl-8 h-9 border border-input/30 bg-background/50 ring-1 ring-border/5 hover:border-input/50 transition-colors"
                      />
                    </div>
                    <Button className="h-9 cursor-pointer">
                      <Plus className="h-4 w-4 mr-2" />
                      添加项目
                    </Button>
                  </div>
                </div>
                
                <div className="space-y-4">
                  {projects.map((project) => (
                    <div 
                      key={project.id} 
                      className="p-4 bg-muted/20 rounded-lg border border-border/30 hover:border-border/50 transition-all cursor-pointer"
                    >
                      <div className="flex justify-between items-start">
                        <div className="flex items-start gap-3">
                          <div className="bg-amber-100 text-amber-800 h-10 w-10 rounded-md flex items-center justify-center flex-shrink-0">
                            <Folder className="h-5 w-5" />
                          </div>
                          <div>
                            <h3 className="font-medium text-base">{project.name}</h3>
                            <div className="flex items-center mt-1 text-xs text-muted-foreground">
                              <Calendar className="h-3.5 w-3.5 mr-1.5" />
                              <span>截止日期: {project.dueDate}</span>
                            </div>
                          </div>
                        </div>
                        <Badge 
                          className={
                            project.status === "已完成" ? "bg-green-500" :
                            project.status === "进行中" ? "bg-blue-500" :
                            "bg-amber-500"
                          }
                        >
                          {project.status}
                        </Badge>
                      </div>
                      
                      <div className="mt-4">
                        <div className="flex items-center justify-between text-sm mb-1.5">
                          <span className="text-muted-foreground">项目进度</span>
                          <span className="font-medium">{project.progress}%</span>
                        </div>
                        <div className="h-2 bg-muted/40 rounded-full overflow-hidden">
                          <div
                            className={
                              project.status === "已完成" ? "bg-green-500" :
                              project.status === "进行中" ? "bg-blue-500" :
                              "bg-amber-500"
                            }
                            style={{ width: `${project.progress}%` }}
                          />
                        </div>
                      </div>
                      
                      <div className="mt-4 flex justify-end">
                        <Button variant="outline" size="sm" className="cursor-pointer" asChild>
                          <NavigationLink href={`/projects/${project.id}`}>
                            <ExternalLink className="h-3.5 w-3.5 mr-1.5" />
                            项目详情
                          </NavigationLink>
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            {activeTab === "settings" && (
              <div className="bg-card rounded-xl p-6">
                <div className="mb-6">
                  <h2 className="text-xl font-semibold">高级设置</h2>
                  <p className="text-sm text-muted-foreground">管理团队的高级配置</p>
                </div>
                
                <div className="space-y-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4">
                      <div className="space-y-0.5">
                        <Label>团队可见性</Label>
                        <p className="text-sm text-muted-foreground">
                          控制团队是否对其他用户可见
                        </p>
                      </div>
                      <Switch 
                        checked={teamData.isPrivate}
                        onCheckedChange={(checked) => {
                          // 修复更新逻辑：先更新编辑数据，再保存
                          const updatedData = {
                            ...editableTeamData,
                            isPrivate: checked
                          };
                          setEditableTeamData(updatedData);
                          
                          // 构建请求参数，仅包含API需要的字段
                          const teamDataToUpdate = {
                            id: teamId,
                            teamName: updatedData.name.trim(),
                            description: updatedData.description.trim(),
                            status: updatedData.status === "启用" ? 1 : 0,
                            teamThemeColor: getColorCode(updatedData.themeColor),
                            teamType: updatedData.teamType,
                            teamCode: updatedData.teamCode,
                            teamLogo: updatedData.teamLogo,
                            privateTag: checked ? 1 : 0,
                            parentId: updatedData.parentId ? Number(updatedData.parentId) : null
                          };
                          
                          // 直接调用API更新
                          updateTeamRequest(teamDataToUpdate)
                            .then(success => {
                              if (success) {
                                // 更新本地数据
                                setTeamData(prev => ({
                                  ...prev,
                                  isPrivate: checked
                                }));
                              }
                            })
                            .catch(error => {
                              console.error("更新可见性失败:", error);
                            });
                        }}
                      />
                    </div>
                    
                    <div className="h-px bg-border/40 my-6"></div>
                    
                    <div className="flex items-center justify-between p-4 rounded-xl bg-destructive/5 border border-destructive/20 hover:border-destructive/40 transition-all">
                      <div className="space-y-0.5">
                        <Label>删除团队</Label>
                        <p className="text-sm text-muted-foreground">
                          删除团队及其所有数据，此操作不可恢复
                        </p>
                      </div>
                      <Button 
                        variant="destructive" 
                        onClick={() => setShowDeleteTeamDialog(true)}
                      >
                        删除团队
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </main>
      </div>

      {/* 团队删除确认对话框 */}
      <AlertDialog
        open={showDeleteTeamDialog}
        onOpenChange={setShowDeleteTeamDialog}
      >
        <AlertDialogContent className="max-w-[450px] border border-destructive/20">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-destructive">确认删除团队</AlertDialogTitle>
            <AlertDialogDescription>
              此操作将永久删除该团队及其所有数据，且<span className="font-semibold">不可恢复</span>。请输入团队名称 <span className="font-medium">{teamData.name}</span> 以确认删除。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="py-6">
            <div className="space-y-4">
              <div className="bg-destructive/5 p-3 rounded-md border border-destructive/20 text-sm">
                <div className="flex gap-2 items-start">
                  <AlertTriangle className="h-5 w-5 text-destructive mt-0.5" />
                  <div>
                    <p className="font-medium text-foreground mb-1">删除后，以下内容将被永久删除：</p>
                    <ul className="list-disc pl-5 space-y-1 text-muted-foreground">
                      <li>团队所有成员关联关系</li>
                      <li>团队角色和权限设置</li>
                      <li>团队基本信息和配置</li>
                    </ul>
                  </div>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="confirm-delete" className="text-sm font-medium">
                  输入团队名称确认删除
                </Label>
                <Input
                  id="confirm-delete"
                  placeholder={"请输入：" + teamData.name}
                  value={deleteConfirmName}
                  onChange={(e) => setDeleteConfirmName(e.target.value)}
                  className="border-destructive/50 focus-visible:ring-destructive bg-destructive/5"
                />
              </div>
            </div>
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>取消</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDeleteTeam}
              className="bg-destructive hover:bg-destructive/90 shadow-sm hover:shadow-md transition-all"
              disabled={deleteConfirmName !== teamData.name || isDeleting}
            >
              {isDeleting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  删除中...
                </>
              ) : (
                "删除团队"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* 添加成员对话框 */}
      <Dialog open={showAddMemberDialog} onOpenChange={setShowAddMemberDialog}>
        <DialogContent className="sm:max-w-[425px] border border-border/30 ring-1 ring-border/5">
          <DialogHeader>
            <DialogTitle>添加成员</DialogTitle>
            <DialogDescription>
              添加新成员到"{teamData.name}"团队
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="member-select">选择成员</Label>
              <Select>
                <SelectTrigger className="border border-input/30 focus-visible:ring-offset-0 bg-background/50 shadow-sm">
                  <SelectValue placeholder="选择一位成员" />
                </SelectTrigger>
                <SelectContent>
                  {userOptions.map(user => (
                    <SelectItem key={user.value} value={user.value}>
                      {user.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="role-select">角色</Label>
              <Select>
                <SelectTrigger className="border border-input/30 focus-visible:ring-offset-0 bg-background/50 shadow-sm">
                  <SelectValue placeholder="选择一个角色" />
                </SelectTrigger>
                <SelectContent>
                  {roles.map(role => (
                    <SelectItem key={role.id} value={role.id}>
                      {role.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddMemberDialog(false)} className="border border-input/30 hover:bg-muted/30">取消</Button>
            <Button type="submit" className="shadow-sm hover:shadow-md transition-all">添加成员</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 添加角色对话框 */}
      <Dialog open={showAddRoleDialog} onOpenChange={setShowAddRoleDialog}>
        <DialogContent className="sm:max-w-[425px] border border-border/30 ring-1 ring-border/5">
          <DialogHeader>
            <DialogTitle>创建角色</DialogTitle>
            <DialogDescription>
              为"{teamData.name}"团队创建新角色
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="role-name">角色名称</Label>
              <Input id="role-name" placeholder="输入角色名称" className="border border-input/30 focus-visible:ring-offset-0 bg-background/50 shadow-sm" />
            </div>
            <div className="space-y-2">
              <Label htmlFor="role-description">角色描述</Label>
              <Textarea id="role-description" placeholder="描述角色职责和权限" className="border border-input/30 focus-visible:ring-offset-0 bg-background/50 shadow-sm resize-none" />
            </div>
            <div className="space-y-2">
              <Label>权限</Label>
              <div className="grid grid-cols-2 gap-2">
                <div className="flex items-center space-x-2">
                  <Switch id="perm-view-team" className="data-[state=checked]:bg-primary/90" />
                  <Label htmlFor="perm-view-team">查看团队</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch id="perm-manage-team" className="data-[state=checked]:bg-primary/90" />
                  <Label htmlFor="perm-manage-team">管理团队</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch id="perm-view-member" className="data-[state=checked]:bg-primary/90" />
                  <Label htmlFor="perm-view-member">查看成员</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch id="perm-manage-member" className="data-[state=checked]:bg-primary/90" />
                  <Label htmlFor="perm-manage-member">管理成员</Label>
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddRoleDialog(false)} className="border border-input/30 hover:bg-muted/30">取消</Button>
            <Button type="submit" className="shadow-sm hover:shadow-md transition-all">创建角色</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}


"use client"

import React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Calendar, Clock, Eye, Heart, MessageCircle } from "lucide-react"
import { cn } from "@/lib/utils"

interface BlogCardProps {
  title: string
  excerpt: string
  author: {
    name: string
    avatar?: string
  }
  publishDate: string
  readTime: string
  category: string
  tags?: string[]
  image?: string
  stats?: {
    views: number
    likes: number
    comments: number
  }
  className?: string
  onReadMore?: () => void
  onLike?: () => void
  onComment?: () => void
}

export function BlogCard({
  title,
  excerpt,
  author,
  publishDate,
  readTime,
  category,
  tags = [],
  image,
  stats,
  className,
  onReadMore,
  onLike,
  onComment,
}: BlogCardProps) {
  // 确保 author 对象存在
  if (!author) {
    return null
  }
  return (
    <Card className={cn("overflow-hidden hover:shadow-lg transition-shadow", className)}>
      {image && (
        <div className="aspect-video overflow-hidden">
          <img
            src={image}
            alt={title}
            className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
          />
        </div>
      )}
      
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between mb-2">
          <Badge variant="secondary" className="text-xs">
            {category}
          </Badge>
          <div className="flex items-center text-xs text-muted-foreground">
            <Calendar className="w-3 h-3 mr-1" />
            {publishDate}
          </div>
        </div>
        
        <h3 className="font-semibold text-lg leading-tight line-clamp-2 hover:text-primary cursor-pointer">
          {title}
        </h3>
      </CardHeader>

      <CardContent className="pb-3">
        <p className="text-sm text-muted-foreground line-clamp-3 mb-3">
          {excerpt}
        </p>
        
        {tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-3">
            {tags.slice(0, 3).map((tag, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                #{tag}
              </Badge>
            ))}
            {tags.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{tags.length - 3}
              </Badge>
            )}
          </div>
        )}

        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Avatar className="w-6 h-6">
              {author?.avatar && <AvatarImage src={author.avatar} alt={author.name} />}
              <AvatarFallback className="text-xs">
                {author?.name?.charAt(0)?.toUpperCase() || 'U'}
              </AvatarFallback>
            </Avatar>
            <span className="text-xs text-muted-foreground">{author?.name || '未知作者'}</span>
          </div>
          
          <div className="flex items-center text-xs text-muted-foreground">
            <Clock className="w-3 h-3 mr-1" />
            {readTime}
          </div>
        </div>
      </CardContent>

      <CardFooter className="pt-3 border-t">
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center space-x-4">
            {stats && (
              <>
                <button
                  onClick={onLike}
                  className="flex items-center space-x-1 text-xs text-muted-foreground hover:text-red-500 transition-colors"
                >
                  <Heart className="w-3 h-3" />
                  <span>{stats.likes}</span>
                </button>
                
                <button
                  onClick={onComment}
                  className="flex items-center space-x-1 text-xs text-muted-foreground hover:text-blue-500 transition-colors"
                >
                  <MessageCircle className="w-3 h-3" />
                  <span>{stats.comments}</span>
                </button>
                
                <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                  <Eye className="w-3 h-3" />
                  <span>{stats.views}</span>
                </div>
              </>
            )}
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={onReadMore}
            className="text-xs"
          >
            阅读更多
          </Button>
        </div>
      </CardFooter>
    </Card>
  )
}

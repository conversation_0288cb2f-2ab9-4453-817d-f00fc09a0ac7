import { extend } from './request';
import { CancelToken, Cancel, isCancel } from './cancel';
import { loading } from './utils';
import { 
  processResponse, 
  processArrayResponse, 
  handleApiError, 
  safeExecute 
} from './response-handler';
import { ErrorShowType } from './types';
import type { 
  RequestConfig, 
  RequestOptionsInit, 
  RequestMethod, 
  RequestResponse, 
  ResponseError, 
  ResponseStructure,
  RequestInstance
} from './types';

/**
 * 默认导出的请求实例
 */
const request = extend({});

export {
  extend,
  request,
  CancelToken,
  Cancel,
  isCancel,
  loading,
  ErrorShowType,
  // 导出响应处理工具函数
  processResponse,
  processArrayResponse,
  handleApiError,
  safeExecute
};

export type { 
  RequestConfig, 
  RequestOptionsInit,
  RequestMethod,
  RequestResponse,
  ResponseError,
  ResponseStructure,
  RequestInstance
};

// 导出类型定义
export * from './types'; 
"use client"

import * as React from "react"
import { 
  ColumnDef,
  ExpandedState, 
  flexRender, 
  getCoreRowModel, 
  getExpandedRowModel,
  useReactTable,
  getPaginationRowModel,
} from "@tanstack/react-table"

import { Button } from "@/components/ui/button"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { ChevronDown, ChevronRight } from "lucide-react"
import { User } from "../data"

export interface ExpandableDataTableExampleProps {
  data: User[]
}

export function ExpandableDataTableExample({ data }: ExpandableDataTableExampleProps) {
  const [expanded, setExpanded] = React.useState<ExpandedState>({})

  const columns: ColumnDef<User>[] = [
    {
      id: "expander",
      header: () => null,
      cell: ({ row }) => {
        return row.getCanExpand() ? (
          <Button
            variant="ghost"
            size="icon"
            onClick={() => {
              row.toggleExpanded()
            }}
          >
            {row.getIsExpanded() ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronRight className="h-4 w-4" />
            )}
          </Button>
        ) : null
      },
      enableSorting: false,
    },
    {
      accessorKey: "name",
      header: "姓名",
    },
    {
      accessorKey: "email",
      header: "邮箱",
    },
    {
      accessorKey: "role",
      header: "角色",
    },
    {
      accessorKey: "status",
      header: "状态",
      cell: ({ row }) => {
        const status = row.getValue("status") as string
        return (
          <div className="flex items-center">
            <span
              className={`mr-2 h-2 w-2 rounded-full ${
                status === "活跃" ? "bg-green-500" : 
                status === "未活跃" ? "bg-gray-400" : "bg-yellow-500"
              }`}
            />
            {status}
          </div>
        )
      },
    },
  ]

  const table = useReactTable({
    data,
    columns,
    state: {
      expanded,
    },
    onExpandedChange: setExpanded,
    getRowCanExpand: () => true,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
    initialState: {
      pagination: {
        pageSize: 5,
      },
    },
  })

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <React.Fragment key={row.id}>
                  <TableRow
                    data-state={row.getIsSelected() && "selected"}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                  {row.getIsExpanded() && (
                    <TableRow>
                      <TableCell colSpan={columns.length} className="p-0">
                        <div className="p-4 bg-muted/50">
                          <div className="text-sm">
                            <div className="font-medium">用户详细信息</div>
                            <div className="grid grid-cols-2 gap-4 mt-2">
                              <div>
                                <div className="text-muted-foreground">ID</div>
                                <div>{row.original.id}</div>
                              </div>
                              <div>
                                <div className="text-muted-foreground">最后登录时间</div>
                                <div>{row.original.lastLogin}</div>
                              </div>
                              <div>
                                <div className="text-muted-foreground">部门</div>
                                <div>{Math.random() > 0.5 ? "技术部" : "市场部"}</div>
                              </div>
                              <div>
                                <div className="text-muted-foreground">注册日期</div>
                                <div>2023-{(Math.floor(Math.random() * 12) + 1).toString().padStart(2, '0')}-{(Math.floor(Math.random() * 28) + 1).toString().padStart(2, '0')}</div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </React.Fragment>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  无数据
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => table.previousPage()}
          disabled={!table.getCanPreviousPage()}
        >
          上一页
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => table.nextPage()}
          disabled={!table.getCanNextPage()}
        >
          下一页
        </Button>
      </div>
    </div>
  )
}
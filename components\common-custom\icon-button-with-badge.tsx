"use client"

import React from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import type { LucideIcon } from "lucide-react"

export interface IconButtonWithBadgeProps {
  /**
   * 图标组件
   */
  icon: LucideIcon
  
  /**
   * 按钮标签（用于可访问性）
   */
  label?: string
  
  /**
   * 徽标数量
   */
  badge?: number
  
  /**
   * 徽标文本（优先级高于badge数字）
   */
  badgeText?: string
  
  /**
   * 徽标变体
   */
  badgeVariant?: "default" | "secondary" | "destructive" | "outline"
  
  /**
   * 是否显示徽标点（无数字的小红点）
   */
  showDot?: boolean
  
  /**
   * 按钮变体
   */
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"
  
  /**
   * 按钮尺寸
   */
  size?: "default" | "sm" | "lg" | "icon"
  
  /**
   * 是否禁用
   */
  disabled?: boolean
  
  /**
   * 点击回调
   */
  onClick?: () => void
  
  /**
   * 自定义类名
   */
  className?: string
  
  /**
   * 徽标位置
   */
  badgePosition?: "top-right" | "top-left" | "bottom-right" | "bottom-left"
}

/**
 * 带徽标的图标按钮组件
 * 
 * 支持在按钮右上角显示数字徽标或小红点
 */
export function IconButtonWithBadge({
  icon: Icon,
  label,
  badge,
  badgeText,
  badgeVariant = "destructive",
  showDot = false,
  variant = "ghost",
  size = "sm",
  disabled = false,
  onClick,
  className,
  badgePosition = "top-right",
}: IconButtonWithBadgeProps) {
  // 尺寸映射
  const sizeClasses = {
    default: "h-10 w-10",
    sm: "h-8 w-8",
    lg: "h-11 w-11",
    icon: "h-10 w-10"
  }

  const iconSizeClasses = {
    default: "h-4 w-4",
    sm: "h-4 w-4",
    lg: "h-5 w-5",
    icon: "h-4 w-4"
  }

  // 徽标位置映射
  const badgePositionClasses = {
    "top-right": "-top-1 -right-1",
    "top-left": "-top-1 -left-1",
    "bottom-right": "-bottom-1 -right-1",
    "bottom-left": "-bottom-1 -left-1"
  }

  // 判断是否显示徽标
  const shouldShowBadge = badgeText || (badge && badge > 0) || showDot

  // 获取徽标内容
  const getBadgeContent = () => {
    if (badgeText) return badgeText
    if (badge && badge > 0) {
      return badge > 99 ? "99+" : badge.toString()
    }
    return ""
  }

  return (
    <Button
      variant={variant}
      size={size}
      className={cn(
        "relative p-0",
        sizeClasses[size],
        className
      )}
      onClick={onClick}
      disabled={disabled}
      aria-label={label}
    >
      <Icon className={iconSizeClasses[size]} />
      
      {shouldShowBadge && (
        <Badge
          variant={badgeVariant}
          className={cn(
            "absolute flex items-center justify-center text-xs font-medium",
            badgePositionClasses[badgePosition],
            // 根据内容调整尺寸
            showDot && !badgeText && (!badge || badge === 0)
              ? "h-2 w-2 p-0" // 小红点
              : "h-5 min-w-[20px] px-1" // 数字徽标
          )}
        >
          {!showDot || badgeText || (badge && badge > 0) ? getBadgeContent() : ""}
        </Badge>
      )}
    </Button>
  )
}

/**
 * 预设的图标按钮配置
 */
export const ICON_BUTTON_PRESETS = {
  notification: {
    variant: "ghost" as const,
    size: "sm" as const,
    badgeVariant: "destructive" as const,
    badgePosition: "top-right" as const,
  },
  message: {
    variant: "ghost" as const,
    size: "sm" as const,
    badgeVariant: "default" as const,
    badgePosition: "top-right" as const,
  },
  cart: {
    variant: "ghost" as const,
    size: "sm" as const,
    badgeVariant: "secondary" as const,
    badgePosition: "top-right" as const,
  },
} as const

/**
 * 创建图标按钮的辅助函数
 */
export function createIconButton(config: {
  icon: LucideIcon
  label: string
  preset?: keyof typeof ICON_BUTTON_PRESETS
  badge?: number
  onClick?: () => void
}) {
  const preset = config.preset ? ICON_BUTTON_PRESETS[config.preset] : {}
  
  return {
    icon: config.icon,
    label: config.label,
    badge: config.badge,
    onClick: config.onClick,
    ...preset,
  }
}

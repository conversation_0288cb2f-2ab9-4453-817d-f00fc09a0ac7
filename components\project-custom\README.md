# 项目特定组件

本目录包含项目特定的组件实现，这些组件基于 `common-custom` 目录中的通用组件，但集成了项目特定的依赖和功能。

## 设计原则

1. **基于通用组件**：优先使用 `common-custom` 目录中的通用组件作为基础
2. **集成项目依赖**：集成项目特定的 hooks、providers、路由等依赖
3. **保持接口兼容**：尽量保持与现有组件的接口兼容性
4. **功能增强**：在通用组件基础上提供项目特定的功能增强

## 组件列表

### 面包屑导航组件

- `breadcrumb.tsx` - 集成了 Next.js 路由和侧边栏的面包屑导航组件

### HeaderWithBreadcrumb
带完整头部布局的面包屑组件，集成了：
- `SidebarTrigger`: 侧边栏触发器
- `useRouter`: Next.js 客户端路由导航
- 特定的头部布局和样式
- 操作按钮区域支持

```tsx
import { HeaderWithBreadcrumb } from "@/components/project-custom/breadcrumb";

<HeaderWithBreadcrumb
  items={[
    { label: "首页", href: "/" },
    { label: "用户管理", href: "/users" },
    { label: "用户详情", isCurrent: true }
  ]}
  actions={<Button>操作按钮</Button>}
  breadcrumbProps={{
    variant: "collapsed",
    maxItems: 4
  }}
/>
```

### ProjectBreadcrumb
简化的面包屑组件，仅包含面包屑导航部分：

```tsx
import { ProjectBreadcrumb } from "@/components/project-custom/breadcrumb";

<ProjectBreadcrumb
  items={breadcrumbItems}
  variant="standard"
  className="my-custom-class"
/>
```

## 使用指南

### 优先级原则
1. **优先使用项目特定组件**：在项目中应该优先使用 `project-custom` 目录下的组件
2. **通用组件作为基础**：`common-custom` 目录的组件主要作为构建项目特定组件的基础
3. **避免直接使用通用组件**：除非确实不需要项目特定功能，否则避免直接使用 `common-custom` 组件

### 正确的使用方式
```tsx
// ✅ 推荐：使用项目特定组件（面包屑导航）
import { HeaderWithBreadcrumb } from "@/components/project-custom/breadcrumb";
import { ProjectBreadcrumb } from "@/components/project-custom/breadcrumb";

// ✅ 推荐：直接使用通用组件（其他组件）
import { BackButton } from "@/components/common-custom/back-button";
import { Pagination } from "@/components/common-custom/pagination";
import { TruncateText } from "@/components/common-custom/truncate-text";

// ⚠️ 谨慎使用：直接使用通用面包屑组件（仅在确实不需要项目特定功能时）
import { Breadcrumb } from "@/components/common-custom/breadcrumb";
```

## 开发规范

### 新增项目特定组件
1. **基于通用组件**：优先基于 `common-custom` 中的通用组件进行扩展
2. **集成项目依赖**：集成必要的项目特定依赖（hooks、providers等）
3. **保持接口兼容**：尽量保持与现有组件的接口兼容性
4. **完善文档**：提供清晰的使用示例和API文档

### 组件命名规范
- 保持与通用组件相似的命名，但添加项目特定的前缀或后缀
- 例如：`Breadcrumb` → `ProjectBreadcrumb`、`HeaderWithBreadcrumb`

### 类型定义
- 重用通用组件的类型定义
- 扩展项目特定的属性和配置
- 保持类型的向后兼容性

## 迁移指南

如果你正在从其他组件迁移到项目特定组件：

1. **更新导入路径**：
   ```tsx
   // 旧的导入
   import { HeaderWithBreadcrumb } from "@/components/custom/breadcrumb";
   
   // 新的导入
   import { HeaderWithBreadcrumb } from "@/components/project-custom/breadcrumb";
   ```

2. **检查接口变化**：虽然我们尽量保持兼容性，但请检查是否有新的可选属性可以使用

3. **利用新功能**：项目特定组件通常提供更多的配置选项和功能，可以根据需要使用

## 维护说明

- 当通用组件更新时，需要同步更新项目特定组件
- 定期检查项目特定组件是否还需要，避免过度定制
- 保持文档和示例的更新

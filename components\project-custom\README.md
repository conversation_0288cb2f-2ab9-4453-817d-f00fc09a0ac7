# 项目特定组件

本目录包含项目特定的组件实现，这些组件基于common-custom目录中的通用组件，但集成了项目特定的依赖和功能。

## 组件列表

### 页面加载相关组件

- `page-loading/index.tsx` - 集成了navigation-provider的页面加载指示器
- `page-loading/page-loader.tsx` - 集成了next/navigation的页面加载组件

### 通知相关组件

- `notification/index.tsx` - 集成了hooks/use-toast的通知组件

### 导航相关组件

- `breadcrumb/index.tsx` - 集成了next/navigation的面包屑导航组件
- `page-header/` - 功能完整的页头组件集，集成了路由导航、主题切换等项目特定功能

## 使用方式

在项目中，应该优先使用project-custom目录下的组件，而不是直接使用common-custom目录下的组件。这样可以确保项目特定的依赖和功能得到正确处理。

```tsx
// 正确的使用方式
import { PageLoading } from "@/components/project-custom/page-loading";
import { showToast } from "@/components/project-custom/notification";
import { HeaderWithBreadcrumb } from "@/components/project-custom/breadcrumb";
import { PageHeader } from "@/components/project-custom/page-header";

// 错误的使用方式（不推荐）
import { PageLoading } from "@/components/common-custom/page-loading";
import { showToast } from "@/components/common-custom/notification";
import { Breadcrumb } from "@/components/common-custom/breadcrumb";
```

## 组件通用化原则

当common-custom目录下的组件需要使用项目特定的依赖时，应该遵循以下原则：

1. 在common-custom目录下的组件中移除项目特定的依赖，使其更加通用
2. 在project-custom目录下创建相应的组件，集成项目特定的依赖
3. 确保project-custom目录下的组件API与common-custom目录下的组件保持一致

这样可以确保common-custom目录下的组件可以在不同的项目中重用，而project-custom目录下的组件则可以满足特定项目的需求。 
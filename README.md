# Keel Cloud修复文档

本文档记录了对Keel Cloud的修复过程，解决了数据加载后树级结构丢失和权限信息保存无效的问题。

## 问题描述

1. **数据结构问题**：从数据库中加载数据后丢失树级结构，接口返回的是扁平结构
2. **权限保存问题**：权限信息保存后无法正常显示出来，更新不生效
3. **父子关系维护问题**：移动或保存节点时，树结构关系没有正确维护

## 解决方案

### 1. 数据加载问题修复

添加了自动判断和转换扁平数据结构的功能：

- 在`getPermissions`方法中添加了扁平数据结构检测
- 新增`convertToTree`方法将扁平的权限数据转换为树形结构
- 确保排序正确应用到所有层级的节点

### 2. 权限保存逻辑优化

优化了权限保存逻辑，确保节点关系正确维护：

- 重构`updatePermission`方法，改善处理父节点关系变更的逻辑
- 添加`updateSingleNode`方法专门处理单节点更新
- 优化`updateNodeInTree`方法，正确处理父子关系变更的情况

### 3. 树结构维护

增强了树结构的维护逻辑：

- 修改`savePermissionTree`方法，添加扁平化处理逻辑，确保服务端能正确处理数据
- 优化`createPermission`方法，确保新创建的节点能正确添加到父节点
- 增强`handleMoveNode`方法，确保节点移动后树结构的一致性

### 4. 状态同步机制

新增状态同步机制，确保前端状态与服务器状态的一致性：

- 添加`syncPermissionsState`函数，提供全局状态同步机制
- 在关键操作后（添加、更新、删除、移动）调用同步函数
- 确保展开/折叠状态在操作后也能保持正确

## 技术要点

1. **树结构转换**：使用Map优化扁平结构到树形结构的转换效率
2. **类型安全**：针对临时属性（如`__remove`标记）增加类型扩展
3. **父子关系维护**：使用递归算法确保树结构的完整性
4. **状态同步**：实现前端与服务器状态的双向同步机制

## 注意事项

1. 服务端API需要支持扁平结构的数据处理
2. 权限树的深度和类型限制规则仍然保持原有设计
3. 性能考虑：对大型树结构，可能需要进一步优化转换算法

## API调用说明

本项目现在使用React Query进行数据获取，不再使用Next.js API路由。所有API请求直接从客户端发送到后端服务器。

### API服务架构

- `services/api/config.ts` - API配置，包含基础URL和其他配置
- `services/api/httpClient.ts` - HTTP客户端类，处理所有API请求
- `services/api/groupApi.ts` - 群组API服务，提供群组相关的API函数
- `services/api/reactQuery.ts` - React Query钩子，用于在组件中获取和修改数据
- `services/api/apiFactory.ts` - API工厂，用于创建API服务实例

### 使用示例

```tsx
// 使用React Query钩子获取群组列表
import { useGroupsQuery } from "@/services/api/reactQuery";

function GroupList() {
  const { data, isLoading, isError } = useGroupsQuery({ page: 1, size: 10 });
  
  if (isLoading) return <div>加载中...</div>;
  if (isError) return <div>加载失败</div>;
  
  return (
    <div>
      {data?.data?.map(group => (
        <div key={group.id}>{group.name}</div>
      ))}
    </div>
  );
}
```

### 直接使用API函数

```tsx
import { getGroup, createGroup } from "@/services/api/groupApi";

// 获取群组
const response = await getGroup("group-id");

// 创建群组
const newGroup = await createGroup({ 
  name: "新群组", 
  description: "这是一个新群组" 
});
```

## 团队管理

团队管理模块提供了对公司团队的管理功能，包括团队的创建、编辑、删除，以及成员管理和角色分配等功能。

主要功能包括：

- 团队列表查看（支持列表视图和卡片视图）
- 团队创建与编辑
- 团队成员管理
- 团队角色与权限设置
- 团队活动日志查看

#### 界面视图

1. **列表视图**：以表格形式展示团队基本信息，包括名称、状态、成员数量、负责人等
2. **卡片视图**：以卡片形式展示团队信息，直观易读

#### 数据管理

团队数据通过API接口与后端交互，支持以下操作：

- 分页加载团队列表
- 按名称搜索团队
- 按团队类型筛选
- 按不同字段排序

## 密码管理模块

密码管理模块提供了安全存储和管理密码的功能，包括密码的创建、读取、更新和删除，以及密码健康检查等功能。

### 功能列表

- 安全存储密码信息
- 密码列表展示（支持分类：全部、收藏、最近、高安全性）
- 密码详情查看
- 密码生成器
- 密码强度检测
- 收藏密码
- 标签管理

### API接口

密码管理模块的API接口位于`services/api/passwordVaultRequestApi.ts`，提供以下功能：

#### 获取密码列表

```typescript
// 参数定义
interface PasswordListParams {
  userId: number;
  queryType: PasswordQueryType; // 0:全部, 1:收藏, 2:最近, 3:高安全性
}

// 调用方式
import { passwordListRequest } from "@/services/api/passwordVaultRequestApi";

const passwordList = await passwordListRequest({
  userId: 1, 
  queryType: 0
});
```

#### 创建密码

```typescript
// 参数定义
interface CreatePasswordParams {
  userId: number;
  checkPassword: string;
  passwordIcon: string;
  projectPath: string;
  encryptedPassword: string;
  passwordStrength: number; // 0:弱, 1:中, 2:强
  remarks: string;
  tagNames: string[];
}

// 调用方式
import { createPasswordRequest } from "@/services/api/passwordVaultRequestApi";

await createPasswordRequest({
  userId: 1,
  checkPassword: "测试账号",
  passwordIcon: "🔑",
  projectPath: "测试项目",
  encryptedPassword: "加密后的密码",
  passwordStrength: 2,
  remarks: "备注信息",
  tagNames: ["标签1", "标签2"]
});
```

#### 删除密码

```typescript
// 调用方式
import { deletePasswordRequest } from "@/services/api/passwordVaultRequestApi";

await deletePasswordRequest(passwordId);
```

#### 获取密码详情

```typescript
// 调用方式
import { getPasswordDetailRequest } from "@/services/api/passwordVaultRequestApi";

const passwordDetail = await getPasswordDetailRequest(passwordId);
```

### 数据转换

模块提供了API数据格式和UI组件使用的数据格式之间的转换工具：

```typescript
import { passwordConverter } from "@/services/api/passwordVaultRequestApi";

// API数据转换为UI数据
const uiPassword = passwordConverter.toUIFormat(apiPassword);

// UI数据转换为API数据
const apiPassword = passwordConverter.toAPIFormat(uiPassword);
```

# Web Template Demo

这是一个基于 Next.js 15、React、TypeScript 和 shadcn/ui 的前端项目模板。

## 项目结构

- `app/` - Next.js 应用源码
- `components/` - React 组件 
- `lib/` - 工具函数和服务
  - `request/` - 请求工具库
- `services/` - 服务层
  - `api/` - API 请求接口
  - `mock/` - 模拟数据
- `types/` - TypeScript 类型定义
- `public/` - 静态资源

## 技术栈

- Next.js 15 (App Router)
- React
- TypeScript
- Tailwind CSS
- shadcn/ui

## 请求工具

项目使用了基于 axios 的请求工具库，位于 `lib/request` 目录下。

### 基本用法

```typescript
import { request } from '@/lib/request';

// GET 请求
const getData = async () => {
  const result = await request.get('/api/data');
  return result;
};

// POST 请求 
const createData = async (data: any) => {
  const result = await request.post('/api/data', {
    data
  });
  return result;
};
```

## API 服务

项目的 API 服务位于 `services/api` 目录下，提供了标准化的 API 调用接口。

### 使用方式

```typescript
import { apiClient } from '@/services/api';
import { Group, CreateGroupParams } from '@/services/api/types';

// 获取群组列表
const getGroups = async () => {
  const response = await apiClient.get('/api/groups');
  return response.data;
};

// 创建群组
const createGroup = async (group: CreateGroupParams) => {
  const response = await apiClient.post('/api/groups', {
    data: group
  });
  return response.data;
};
```

## 模拟数据

开发阶段使用的模拟数据位于 `services/mock` 目录下，生产环境应从真实 API 获取数据。

### 使用方式

```typescript
import { navigationConfig } from '@/services/mock/navigation';
import { teamsData, usersData } from '@/services/mock/team';

// 使用导航配置
console.log(navigationConfig);

// 使用团队数据
console.log(teamsData);
```

## 开发

```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 构建生产版本
pnpm build

# 启动生产版本
pnpm start
```

## 导航优化

为提升用户体验，我们对导航系统进行了以下优化：

### 导航UI优化

1. **无子项菜单优化**：菜单项如果没有子项，不再显示展开箭头，避免用户困惑
2. **动态展开状态**：根据当前路径自动展开相应的菜单组，方便用户定位当前位置
3. **移动设备适配**：在移动设备上自动收起侧边栏，提升小屏幕用户体验

### 导航性能优化

1. **页面预加载**：通过`PrefetchLink`组件实现导航项的预加载，当用户悬停在导航项上时预加载对应页面
2. **加载状态提示**：切换页面时显示加载状态，提高用户体验
3. **路由过渡优化**：实现平滑的页面过渡效果

### 技术实现

1. **自定义钩子**：创建`useNavigationEvents`和`useNavigation`钩子管理导航状态
2. **上下文提供者**：通过`NavigationProvider`统一管理导航相关状态
3. **骨架屏**：实现内容加载占位效果，减少页面切换时的视觉跳跃

### 组件说明

- `PrefetchLink`: 扩展Next.js的Link组件，支持hover预加载和延迟预加载
- `PageLoader`: 全页面加载状态组件，用于显示页面切换时的加载效果
- `ContentSkeleton`: 内容骨架屏组件，用于显示内容加载时的占位效果
- `NavigationProvider`: 全局导航状态提供者

### 使用方法

用户无需特别操作，导航优化在系统层面自动完成：

1. 菜单项悬停时自动预加载相关页面
2. 页面切换时自动显示加载状态
3. 根据当前路径自动展开相关菜单项

## 页面加载动效

系统实现了页面跳转和菜单切换时的加载动效，提升用户体验：

1. **全局加载状态**：
   - 页面跳转时显示全屏加载动效，避免用户感觉到页面无响应
   - 加载动效仅在内容区域显示，不会覆盖导航栏
   - 自动适配导航栏展开/收起状态，始终保持在内容区域居中

2. **组件级加载状态**：
   - `NavigationButton`：带加载状态的按钮组件，支持普通按钮和链接按钮
   - `NavigationLink`：带加载状态的链接组件，支持普通链接和按钮样式链接
   - `PrefetchLink`：支持预加载和加载状态的链接组件

3. **特殊页面处理**：
   - 登录页和退出登录页不显示全局加载动效
   - 登录页有专门的加载组件

4. **性能优化**：
   - 已访问过的页面不再显示加载动效，提升用户体验
   - 使用页面缓存机制，记录已访问过的路径
   - 延迟加载策略：如果页面在300ms内加载完成，不显示加载状态，避免闪烁
   - 所有加载计时器都有适当的清理机制，防止内存泄漏
   - 统一的加载状态钩子`useLoadingState`，减少代码重复

### 使用方法

```tsx
// 使用带加载状态的按钮进行页面跳转
<NavigationButton href="/dashboard" variant="default">跳转到仪表盘</NavigationButton>

// 使用带加载状态的链接
<NavigationLink href="/settings">设置</NavigationLink>

// 使用按钮样式的链接
<NavigationLink href="/users" asButton variant="outline">用户列表</NavigationLink>

// 使用支持预加载的链接（鼠标悬停时预加载）
<PrefetchLink href="/users" prefetchTimeout={200}>用户列表</PrefetchLink>
```

### 统一导入方式

所有自定义组件都可以从一个文件导入：

```tsx
import { 
  NavigationButton, 
  NavigationLink,
  PrefetchLink,
  PageLoading
} from "@/components/custom";

// 加载状态钩子可以从hooks目录导入
import { useLoadingState } from "@/hooks";
```

## 设计规范文档

本项目的设计规范文档位于`.doc`目录下，包含了完整的设计系统定义和开发指南。在开发新功能或修复bug前，请务必先阅读相关的设计规范文档，确保代码实现符合项目设计标准。

### 设计规范目录

1. **01-设计哲学.md** - 设计理念、核心原则和价值观
2. **02-色彩系统.md** - 色彩变量、语义化颜色和暗色模式规范
3. **03-排版系统.md** - 字体、文字层级和间距规则
4. **04-布局网格.md** - 网格系统、响应式布局和间距规范
5. **05-组件系统.md** - 基础组件、复合组件和自定义组件规范
6. **06-交互设计.md** - 交互原则、动画系统和状态变化规范
7. **07-主题与暗色模式.md** - 主题配置和暗色模式实现
8. **08-表单设计.md** - 表单组件和交互的设计规范
9. **09-图标系统.md** - 图标使用规范和实现方法
10. **10-通用组件规范.md** - 通用组件的设计和实现规范
11. **11-请求规范.md** - API请求的实现和错误处理
12. **12-数据可视化规范.md** - 图表和数据可视化规范
13. **13-动效与过渡系统.md** - 动画效果和过渡规范
14. **14-图片与媒体规范.md** - 图片和媒体内容的处理规范

### 规范使用指南

1. **实现功能前**：在开始编码前，先查阅相关规范文档
2. **修复bug时**：确保修复方案符合设计规范要求
3. **组件开发**：遵循组件系统中定义的结构和命名规则
4. **视觉一致性**：使用规范中定义的色彩、排版和间距系统
5. **代码审查**：将设计规范作为代码审查的重要标准之一

### 规范维护与更新

如果发现规范文档需要更新或有疑问，请联系设计团队或提出问题，我们会及时更新文档。设计规范是一个不断完善的系统，我们欢迎所有建设性的反馈和建议。

# 业务组件库

基于Next.js 15和shadcn/ui构建的业务组件库，提供一系列高质量、可复用的业务组件，用于快速构建企业级应用界面。

## 特性

- 基于Next.js 15的App Router架构
- 使用TypeScript进行类型检查
- 基于shadcn/ui的组件设计系统
- 响应式设计，适配不同屏幕尺寸
- 遵循最佳实践和设计模式
- 提供详细的文档和示例

## 主要组件

### 数据展示
- 数据表格：功能完整的数据表格，支持排序、筛选、分页等特性
- 高级数据表格：增强版数据表格，支持更多定制化功能
- 统计卡片：展示统计数据的卡片组件
- 时间轴：展示时间相关数据的组件
- 列表视图：展示列表数据的组件

### 表单与输入
- 表单：用于数据输入的表单组件，支持验证和各种输入类型
- 表单对话框：弹出式表单组件
- 搜索：搜索组件
- 文件上传：文件上传组件
- 筛选器：数据筛选组件

### 反馈与状态
- 加载状态：各种加载状态组件
- 骨架屏：内容加载占位组件
- 页面加载器：页面加载组件
- 全局加载：全局加载状态组件
- 空状态：无数据状态组件
- 错误状态：错误状态组件
- 通知：通知提醒组件

### 导航与交互
- 面包屑导航：页面导航组件
- 头部与面包屑：页面头部组件
- 返回按钮：返回上一页组件
- 分页：分页组件
- 操作按钮：操作按钮组件
- 模态框：模态对话框组件
- 标签页：标签页组件
- 工具提示：提示信息组件
- 图标选择器：图标选择组件

### 布局组件
- 卡片模板：各种卡片布局模板
- 日历：日历组件
- 仪表板：仪表板布局组件
- 评论：评论组件
- 标签：标签组件
- 状态徽章：状态标识组件
- 评分：评分组件
- 文本截断：长文本截断组件

## 最近更新

### 2024-05-16
- 完成组件目录结构优化，将复杂组件拆分为多个子组件
- 完成search组件迁移，拆分为SearchInput、SearchResults、GlobalSearch等子组件
- 完成modal组件迁移，拆分为Modal、AlertDialogModal、FormModal等子组件
- 完成dashboard组件迁移，拆分为StatCard、QuickActions、ActivityList等子组件
- 完成card-templates组件迁移，拆分为StatCard、ProductCard、UserCard等子组件
- 删除废弃的组件文件和空目录，清理重复文件，保持代码库整洁

### 2024-05-15
- 新增高级数据表格组件，支持列排序、筛选、自定义操作按钮、密度调整和列配置
- 重构了组件库界面，支持组件分组和单独预览
- 添加了组件变更记录页面，可跳转到对应组件预览

### 2024-05-14
- 优化了数据表格的响应式布局
- 修复了分页组件的样式问题

### 2024-05-10
- 优化了多选筛选器的UI
- 新增日期范围筛选器

### 2024-05-08
- 修复了表单验证问题
- 支持表单分步提交

## 使用方法

### 安装依赖

```bash
pnpm install
```

### 启动开发服务器

```bash
pnpm dev
```

### 构建生产版本

```bash
pnpm build
```

## 目录结构

- `app/`: Next.js App Router页面
  - `examples/`: 组件示例页面
  - `changelog/`: 更新记录页面
- `components/`: 组件库
  - `custom/`: 自定义业务组件
    - `data-table/`: 数据表格组件相关文件
    - `form/`: 表单相关组件
    - `card-templates/`: 卡片模板组件
    - `dashboard/`: 仪表板组件
    - `modal/`: 模态框组件
    - `search/`: 搜索组件
  - `ui/`: 基础UI组件（shadcn/ui）
  - `providers/`: 应用提供程序组件
- `hooks/`: 自定义Hooks
- `lib/`: 工具函数
- `public/`: 静态资源
- `styles/`: 全局样式
- `types/`: 类型定义

## 组件结构

我们的组件按照以下方式组织：

1. **简单组件**: 位于`components/custom`目录下的单文件组件
2. **复杂组件**: 位于`components/custom/{component-name}`目录下的多文件组件
   - 每个复杂组件目录包含一个`index.tsx`作为主要导出
   - 复杂组件的子组件在同一目录下
   - 类型定义可能位于同一目录下的`types.ts`或全局`types`目录

## 组件导航分组

为了更好地组织和查找组件，我们按以下类别进行分组：

1. **数据展示与可视化**: 数据表格、统计卡片、列表视图、时间轴等
2. **表单与数据输入**: 表单、文件上传、搜索、筛选器等
3. **导航与布局**: 面包屑、标签页、卡片模板、仪表板等
4. **反馈与交互**: 通知、模态框、加载状态、操作按钮等
5. **状态与内容**: 空状态、错误状态、评论、标签等
6. **特殊组件**: 日历、组合组件等 
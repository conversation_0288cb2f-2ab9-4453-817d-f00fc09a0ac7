"use client"

import React, { useState, useEffect } from "react"
import { 
  Activity,
  Loader2, 
  Clock,
  BarChart3
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"

interface ActivityItem {
  id: string;
  action: string;
  module: string;
  timestamp: string;
  details?: string;
}

interface ActivityStats {
  loginCount: number;
  lastActive: string;
  activePercent: number;
  activityScore: number;
}

interface UserActivityProps {
  userId: number;
}

export function UserActivity({ userId }: UserActivityProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [activities, setActivities] = useState<ActivityItem[]>([])
  const [stats, setStats] = useState<ActivityStats | null>(null)
  const [error, setError] = useState<string | null>(null)

  // 获取用户活动
  useEffect(() => {
    const loadActivityData = async () => {
      if (!userId) return

      setIsLoading(true)
      setError(null)
      try {
        // 这里应该是一个真实的API调用
        // 为了示例，这里返回一些模拟数据
        await new Promise(resolve => setTimeout(resolve, 600))
        
        // 模拟活动数据
        const mockActivities = [
          {
            id: '1',
            action: '登录系统',
            module: '认证',
            timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
            details: 'IP: ***********'
          },
          {
            id: '2',
            action: '查看项目',
            module: '项目管理',
            timestamp: new Date(Date.now() - 26 * 60 * 60 * 1000).toISOString()
          },
          {
            id: '3',
            action: '更新个人资料',
            module: '用户管理',
            timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString()
          }
        ]
        
        const mockStats = {
          loginCount: 27,
          lastActive: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          activePercent: 65,
          activityScore: 72
        }
        
        setActivities(mockActivities)
        setStats(mockStats)
      } catch (err) {
        console.error("获取用户活动失败", err)
        setError("获取用户活动失败")
      } finally {
        setIsLoading(false)
      }
    }

    loadActivityData()
  }, [userId])

  // 格式化日期
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString)
      return new Intl.DateTimeFormat('zh-CN', { 
        year: 'numeric',
        month: 'numeric',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      }).format(date)
    } catch (error) {
      return dateString
    }
  }

  // 计算活动等级
  const getActivityLevel = (score: number) => {
    if (score >= 80) return { level: '非常活跃', color: 'bg-green-500' }
    if (score >= 60) return { level: '活跃', color: 'bg-blue-500' }
    if (score >= 40) return { level: '一般', color: 'bg-yellow-500' }
    return { level: '不活跃', color: 'bg-gray-500' }
  }

  return (
    <Card className="border-border/40">
      <CardHeader>
        <CardTitle className="text-lg flex items-center gap-2">
          <Activity className="h-5 w-5 text-primary" />
          活动信息
        </CardTitle>
        <CardDescription>用户的最近活动和统计</CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        ) : error ? (
          <div className="text-center py-6 text-muted-foreground">
            <p>{error}</p>
          </div>
        ) : !activities.length && !stats ? (
          <div className="text-center py-6">
            <Activity className="h-12 w-12 mx-auto text-muted-foreground mb-3" />
            <h3 className="font-medium mb-1">无活动记录</h3>
            <p className="text-sm text-muted-foreground">
              该用户暂无活动记录
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            {stats && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card className="border border-border/30 bg-muted/20">
                  <CardContent className="p-4">
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="text-sm text-muted-foreground">总登录次数</p>
                        <p className="text-2xl font-semibold mt-1">{stats.loginCount}</p>
                      </div>
                      <Clock className="h-8 w-8 text-primary opacity-70" />
                    </div>
                  </CardContent>
                </Card>
                
                <Card className="border border-border/30 bg-muted/20">
                  <CardContent className="p-4">
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="text-sm text-muted-foreground">活跃评分</p>
                        <div className="flex items-center gap-2 mt-1">
                          <p className="text-2xl font-semibold">{stats.activityScore}</p>
                          <Badge className={`${getActivityLevel(stats.activityScore).color} text-white`}>
                            {getActivityLevel(stats.activityScore).level}
                          </Badge>
                        </div>
                      </div>
                      <BarChart3 className="h-8 w-8 text-primary opacity-70" />
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
            
            <div>
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium">最近活动</h3>
                <Badge variant="outline" className="bg-muted/30">最近30天</Badge>
              </div>
              
              {activities.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <p>暂无活动记录</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {activities.map(activity => (
                    <div 
                      key={activity.id} 
                      className="border-l-2 border-primary/20 pl-4 py-1"
                    >
                      <div className="flex justify-between">
                        <p className="font-medium">{activity.action}</p>
                        <Badge variant="outline" className="bg-muted/20">
                          {activity.module}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between mt-1">
                        <p className="text-sm text-muted-foreground">
                          {activity.details}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {formatDate(activity.timestamp)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default UserActivity; 
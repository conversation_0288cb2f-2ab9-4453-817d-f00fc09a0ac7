"use client"

import React, { ReactNode } from "react"
import { useRouter } from "next/navigation"
import { Breadcrumb, BreadcrumbItemType, BreadcrumbExtendedProps } from "@/components/common-custom/breadcrumb"
import { Separator } from "@/components/ui/separator"
import { SidebarTrigger } from "../navigation/sidebar"
import { cn } from "@/lib/utils"

// ============================================================================
// 项目特定面包屑组件 - 集成项目依赖和特定布局
// ============================================================================

/**
 * 项目特定的面包屑导航项（兼容现有接口）
 */
export type BreadcrumbItem = {
  href?: string
  label: string
  isCurrent?: boolean
}

/**
 * 带头部布局的面包屑组件属性
 */
interface HeaderWithBreadcrumbProps {
  /**
   * 导航项列表
   */
  items: BreadcrumbItem[]
  
  /**
   * 自定义类名
   */
  className?: string
  
  /**
   * 操作按钮区域
   */
  actions?: ReactNode

  /**
   * 面包屑组件的额外配置（继承通用组件的所有配置）
   */
  breadcrumbProps?: Omit<BreadcrumbExtendedProps, 'items' | 'onNavigate'>
}

/**
 * 带头部布局的面包屑导航组件
 * 
 * 这个组件集成了项目特定的依赖：
 * - SidebarTrigger: 侧边栏触发器
 * - useRouter: Next.js 路由导航
 * - 特定的头部布局和样式
 * 
 * 同时基于通用面包屑组件，提供了更好的可配置性
 */
export function HeaderWithBreadcrumb({
  items,
  className,
  actions,
  breadcrumbProps = {}
}: HeaderWithBreadcrumbProps) {
  const router = useRouter()

  // 转换项目特定的面包屑项为通用组件格式
  const breadcrumbItems: BreadcrumbItemType[] = items.map(item => ({
    href: item.href,
    label: item.label,
    isCurrent: item.isCurrent
  }))

  // 处理面包屑点击，使用客户端路由进行导航
  const handleBreadcrumbClick = (href: string, e: React.MouseEvent) => {
    e.preventDefault();
    router.push(href);
  };

  // 默认的面包屑配置，可以通过 breadcrumbProps 覆盖
  const defaultBreadcrumbProps: Partial<BreadcrumbExtendedProps> = {
    variant: 'collapsed',
    maxItems: 3,
    className: 'whitespace-nowrap',
    ...breadcrumbProps
  }
  
  return (
    <header className={cn("sticky top-0 z-10 flex h-14 shrink-0 items-center bg-background", className)}>
      <div className="flex items-center justify-between gap-2 px-6 w-full transition-all duration-200">
        <div className="flex items-center gap-2 flex-nowrap">
          <SidebarTrigger className="-ml-1" />
          <Separator
            orientation="vertical"
            className="mr-2 data-[orientation=vertical]:h-4"
          />
          <Breadcrumb
            items={breadcrumbItems}
            onNavigate={handleBreadcrumbClick}
            {...defaultBreadcrumbProps}
          />
        </div>

        <div className="flex items-center gap-2">
          {actions}
        </div>
      </div>
    </header>
  )
}

/**
 * 简化的面包屑组件（仅面包屑部分，无头部布局）
 * 基于通用组件，集成项目特定的路由导航
 */
export function ProjectBreadcrumb({
  items,
  ...props
}: Omit<BreadcrumbExtendedProps, 'onNavigate'>) {
  const router = useRouter()

  const handleNavigate = (href: string, e: React.MouseEvent) => {
    e.preventDefault();
    router.push(href);
  };

  return (
    <Breadcrumb
      items={items}
      onNavigate={handleNavigate}
      {...props}
    />
  )
}

// 导出类型以供其他组件使用
export type { BreadcrumbItemType, BreadcrumbExtendedProps }

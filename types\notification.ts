export type NotificationType = "sms" | "email" | "dingtalk"

export type ContentFormat = "text" | "html" | "markdown"

export type SMSProvider = "aliyun" | "tencent" | "huawei"

export type SMSType = "verification" | "marketing" | "notification"

export type EmailEncryption = "ssl" | "tls" | "starttls"

export type DingTalkMessageType = "text" | "link" | "actionCard" | "feedCard"

export interface SMSConfig {
  provider: SMSProvider
  templateId: string
  recipients: string[]
  smsType: SMSType
  content: string
  variables: Record<string, string>
}

export interface SMTPConfig {
  host: string
  port: number
  encryption: EmailEncryption
  username: string
  password: string
  useOAuth: boolean
}

export interface EmailConfig {
  smtp: SMTPConfig
  subject: string
  contentFormat: ContentFormat
  content: string
  attachments: string[]
  variables: Record<string, string>
  unsubscribeLink?: string
}

export interface DingTalkConfig {
  webhookUrl: string
  secret: string
  messageType: DingTalkMessageType
  content: string
  atMobiles: string[]
  variables: Record<string, string>
}

export interface NotificationConfig {
  id: string
  name: string
  description: string
  type: NotificationType
  enabled: boolean
  environment: "development" | "testing" | "production"
  config: SMSConfig | EmailConfig | DingTalkConfig
  createdAt: string
  updatedAt: string
  lastTestedAt?: string
}

export interface NotificationHistory {
  id: string
  configId: string
  configName: string
  type: NotificationType
  status: "success" | "failed" | "pending"
  recipient: string
  content: string
  sentAt: string
  error?: string
}

export interface SystemVariable {
  key: string
  description: string
  example: string
  category: string
}

export interface TestResult {
  success: boolean
  message: string
  details?: string
  timestamp: string
}

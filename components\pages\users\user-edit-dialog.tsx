"use client"

import { useEffect, useState } from "react"
import { usePathname, useRouter, useSearchParams } from "next/navigation"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Loader2, UserIcon, Mail, PhoneCall } from "lucide-react"
import { toast } from "sonner"
import { updateUserRequest, getUserByIdRequest } from "@/services/api/userRequestApi"
import { User } from "@/types/user"
import { Separator } from "@/components/ui/separator"

export default function UserEditDialog() {
  const searchParams = useSearchParams()
  const pathname = usePathname()
  const router = useRouter()
  
  // 对话框状态
  const [isOpen, setIsOpen] = useState<boolean>(false)
  
  // 用户状态
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [isSaving, setIsSaving] = useState<boolean>(false)
  
  // 表单状态
  const [nickname, setNickname] = useState<string>("")
  const [email, setEmail] = useState<string>("")
  const [mobile, setMobile] = useState<string>("")
  
  // 检查URL参数中是否有编辑请求
  useEffect(() => {
    const editUserId = searchParams.get("edit")
    if (editUserId) {
      setIsOpen(true)
      fetchUser(parseInt(editUserId, 10))
    } else {
      // 重置表单
      setUser(null)
      setNickname("")
      setEmail("")
      setMobile("")
      setIsOpen(false)
    }
  }, [searchParams])
  
  // 获取用户信息
  const fetchUser = async (userId: number) => {
    setIsLoading(true)
    
    try {
      const userData = await getUserByIdRequest(userId)
      if (userData) {
        setUser(userData)
        
        // 填充表单
        setNickname(userData.nickname || "")
        setEmail(userData.email || "")
        setMobile(userData.mobile || "")
      } else {
        toast.error("用户不存在或获取失败")
        closeDialog()
      }
    } catch (error) {
      console.error("获取用户信息失败", error)
      toast.error("获取用户信息失败，请稍后重试")
      closeDialog()
    } finally {
      setIsLoading(false)
    }
  }
  
  // 关闭对话框
  const closeDialog = () => {
    // 移除URL参数
    const params = new URLSearchParams(searchParams)
    params.delete("edit")
    
    router.replace(`${pathname}?${params.toString()}`)
  }
  
  // 处理保存
  const handleSave = async () => {
    if (!user) return
    
    setIsSaving(true)
    
    try {
      const updatedData = {
        id: user.id,
        nickname: nickname.trim(),
        email: email.trim(),
        mobile: mobile.trim(),
        status: user.status
      }
      
      const success = await updateUserRequest(updatedData)
      if (success) {
        toast.success("用户信息更新成功")
        closeDialog()
      } else {
        toast.error("更新用户信息失败")
      }
    } catch (error) {
      console.error("更新用户信息失败", error)
      toast.error("更新用户信息失败，请稍后重试")
    } finally {
      setIsSaving(false)
    }
  }
  
  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
      if (!open) closeDialog()
    }}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserIcon className="h-5 w-5 text-primary" />
            编辑用户
          </DialogTitle>
          <DialogDescription>
            修改用户的基本信息。完成后点击保存。
          </DialogDescription>
        </DialogHeader>
        
        {isLoading ? (
          <div className="py-8 flex justify-center items-center">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : (
          <div className="space-y-4 py-4">
            <div>
              <Label htmlFor="edit-account" className="flex items-center gap-2 text-muted-foreground">
                <UserIcon className="h-4 w-4" />
                账号
              </Label>
              <Input
                id="edit-account"
                value={user?.account || ""}
                disabled
                className="mt-2 bg-muted/30"
              />
              <p className="text-xs text-muted-foreground mt-1">账号不可修改</p>
            </div>
            
            <Separator className="my-4" />
            
            <div className="space-y-4">
              <div>
                <Label htmlFor="edit-nickname" className="flex items-center gap-2">
                  <UserIcon className="h-4 w-4 text-muted-foreground" />
                  用户名
                </Label>
                <Input
                  id="edit-nickname"
                  placeholder="请输入用户名"
                  value={nickname}
                  onChange={(e) => setNickname(e.target.value)}
                  className="mt-2"
                />
              </div>
              
              <div>
                <Label htmlFor="edit-email" className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  邮箱
                </Label>
                <Input
                  id="edit-email"
                  type="email"
                  placeholder="请输入邮箱"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="mt-2"
                />
              </div>
              
              <div>
                <Label htmlFor="edit-mobile" className="flex items-center gap-2">
                  <PhoneCall className="h-4 w-4 text-muted-foreground" />
                  手机号码
                </Label>
                <Input
                  id="edit-mobile"
                  placeholder="请输入手机号码"
                  value={mobile}
                  onChange={(e) => setMobile(e.target.value)}
                  className="mt-2"
                />
              </div>
            </div>
          </div>
        )}
        
        <DialogFooter className="border-t pt-4 mt-2">
          <Button
            variant="outline"
            onClick={closeDialog}
            disabled={isSaving}
          >
            取消
          </Button>
          <Button 
            onClick={handleSave}
            disabled={isLoading || isSaving}
            className="gap-2"
          >
            {isSaving ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>保存中...</span>
              </>
            ) : (
              <span>保存更改</span>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
import React from "react";
import exampleCollection, { ExampleKey } from "./examples";
import PreviewClient from "./preview-client";

interface PreviewPageProps {
  params: Promise<{
    exampleId: string;
  }>;
}

export default async function PreviewPage({ params }: PreviewPageProps) {
  const { exampleId } = await params;
  const exampleData = exampleCollection[exampleId as ExampleKey];

  if (!exampleData) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center space-y-4">
          <h1 className="text-xl font-semibold">示例不存在</h1>
          <p className="text-muted-foreground">找不到ID为 "{exampleId}" 的组件示例</p>
        </div>
      </div>
    );
  }

  // 纯粹的预览，没有任何额外元素
  return <PreviewClient code={exampleData.code} title={exampleData.title} />;
} 
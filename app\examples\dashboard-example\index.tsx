"use client"

import * as React from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  Users,
  ShoppingCart,
  DollarSign,
  Activity,
  TrendingUp,
  TrendingDown,
  BarChart3,
  PieChart,
  LineChart,
  LucideIcon,
} from "lucide-react"

interface StatCardProps {
  title: string
  value: string
  change?: string
  trend?: "up" | "down" | "neutral"
  Icon?: LucideIcon
}

// 统计卡片组件
function StatCard({ title, value, change, trend, Icon }: StatCardProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {Icon && <Icon className="h-4 w-4 text-muted-foreground" />}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {change && (
          <div className={`flex items-center text-xs ${trend === "up" ? "text-green-600" : trend === "down" ? "text-red-600" : "text-muted-foreground"}`}>
            {trend === "up" ? (
              <TrendingUp className="h-3 w-3 mr-1" />
            ) : trend === "down" ? (
              <TrendingDown className="h-3 w-3 mr-1" />
            ) : null}
            {change} 较上月
          </div>
        )}
      </CardContent>
    </Card>
  )
}

interface Product {
  name: string
  sales: number
  revenue: string
  trend: "up" | "down"
}

interface ProductRankingProps {
  products: Product[]
}

// 产品排行组件
function ProductRanking({ products }: ProductRankingProps) {
  return (
    <Card className="col-span-1 md:col-span-2">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>热销产品</CardTitle>
            <CardDescription>本月销量最高的产品</CardDescription>
          </div>
          <BarChart3 className="h-4 w-4 text-muted-foreground" />
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {products.map((product, index) => (
            <div key={index} className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-muted rounded flex items-center justify-center text-sm font-medium">
                  {index + 1}
                </div>
                <div>
                  <div className="font-medium">{product.name}</div>
                  <div className="text-sm text-muted-foreground">销量: {product.sales}</div>
                </div>
              </div>
              <div className="text-right">
                <div className="font-medium">{product.revenue}</div>
                <div
                  className={`flex items-center text-xs ${
                    product.trend === "up" ? "text-green-600" : "text-red-600"
                  }`}
                >
                  {product.trend === "up" ? (
                    <TrendingUp className="h-3 w-3 mr-1" />
                  ) : (
                    <TrendingDown className="h-3 w-3 mr-1" />
                  )}
                  {product.trend === "up" ? "上升" : "下降"}
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

interface SystemStatus {
  label: string
  value: number
}

interface SystemStatusProps {
  statuses: SystemStatus[]
}

// 系统状态组件
function SystemStatus({ statuses }: SystemStatusProps) {
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>系统状态</CardTitle>
          <Activity className="h-4 w-4 text-muted-foreground" />
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {statuses.map((status, index) => (
            <div key={index} className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="text-sm font-medium">{status.label}</div>
                <div className="text-sm text-muted-foreground">{status.value}%</div>
              </div>
              <Progress value={status.value} className="h-2" />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

interface ChartCardProps {
  title: string
  icon?: LucideIcon
  children?: React.ReactNode
}

// 图表卡片组件
function ChartCard({ title, icon: Icon, children }: ChartCardProps) {
  return (
    <Card className="col-span-1 md:col-span-2">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>{title}</CardTitle>
          {Icon && <Icon className="h-4 w-4 text-muted-foreground" />}
        </div>
      </CardHeader>
      <CardContent>
        {children || (
          <div className="flex items-center justify-center h-[200px] bg-muted/20 rounded-md">
            <p className="text-muted-foreground">图表数据</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export function DashboardExample() {
  // 统计数据
  const stats: StatCardProps[] = [
    {
      title: "总收入",
      value: "¥45,231.89",
      change: "+20.1%",
      trend: "up",
      Icon: DollarSign,
    },
    {
      title: "新用户",
      value: "+2,350",
      change: "+180.1%",
      trend: "up",
      Icon: Users,
    },
    {
      title: "订单数",
      value: "+12,234",
      change: "-19%",
      trend: "down",
      Icon: ShoppingCart,
    },
  ]

  // 热销产品数据
  const topProducts: Product[] = [
    { name: "无线蓝牙耳机", sales: 1234, revenue: "¥369,600", trend: "up" },
    { name: "智能手表", sales: 987, revenue: "¥887,130", trend: "up" },
    { name: "便携充电宝", sales: 756, revenue: "¥120,240", trend: "down" },
    { name: "智能音箱", sales: 543, revenue: "¥108,600", trend: "up" },
  ]

  // 系统状态数据
  const systemStatus: SystemStatus[] = [
    { label: "服务器负载", value: 45 },
    { label: "内存使用", value: 67 },
    { label: "存储空间", value: 23 },
    { label: "网络带宽", value: 89 },
  ]

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {stats.map((stat, index) => (
          <StatCard key={index} {...stat} />
        ))}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <ChartCard title="收入趋势" icon={LineChart} />
        <ChartCard title="用户分布" icon={PieChart} />
        <SystemStatus statuses={systemStatus} />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <ProductRanking products={topProducts} />
        <Card>
          <CardHeader>
            <CardTitle>最近活动</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[1, 2, 3].map((_, i) => (
                <div key={i} className="flex items-center gap-4">
                  <div className="w-8 h-8 rounded-full bg-muted" />
                  <div>
                    <div className="text-sm font-medium">用户活动 #{i + 1}</div>
                    <div className="text-xs text-muted-foreground">2 小时前</div>
                  </div>
                  <Badge className="ml-auto" variant="outline">
                    新
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 
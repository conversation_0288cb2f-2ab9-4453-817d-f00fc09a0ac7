"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Loader2, Save, RotateCcw } from "lucide-react";
import { toast } from "sonner";
import { Role } from "@/types/models";
import { updateRoleRequest } from "@/services/api/roleRequestApi";

interface RoleEditFormProps {
  role: Role;
}

export default function RoleEditForm({ role }: RoleEditFormProps) {
  const router = useRouter();
  const [isSaving, setIsSaving] = useState(false);
  
  // 表单状态
  const [formData, setFormData] = useState({
    name: role.name || "",
    description: role.description || "",
    code: role.code || ""
  });
  
  // 处理表单输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  // 重置表单
  const handleReset = () => {
    setFormData({
      name: role.name || "",
      description: role.description || "",
      code: role.code || ""
    });
    toast.info("表单已重置");
  };
  
  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      toast.error("角色名称不能为空");
      return;
    }
    
    setIsSaving(true);
    
    try {
      await updateRoleRequest(role._id, {
        name: formData.name.trim(),
        description: formData.description.trim(),
        code: formData.code.trim() || undefined
      });
      
      toast.success("角色更新成功");
      
      // 更新成功后返回详情页
      router.push(`/settings/roles/${role._id}`);
      router.refresh();
    } catch (error) {
      console.error("更新角色失败", error);
      toast.error("更新角色失败，请稍后重试");
    } finally {
      setIsSaving(false);
    }
  };
  
  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="name">
            角色名称 <span className="text-destructive">*</span>
          </Label>
          <Input
            id="name"
            name="name"
            value={formData.name}
            onChange={handleInputChange}
            placeholder="请输入角色名称"
            required
            className="border border-input/30"
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="code">角色编码</Label>
          <Input
            id="code"
            name="code"
            value={formData.code}
            onChange={handleInputChange}
            placeholder="请输入角色编码"
            className="border border-input/30"
            readOnly={role.code ? true : false}
          />
          <p className="text-xs text-muted-foreground">
            角色编码用于系统内部标识，只能在创建时设置
          </p>
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="description">角色描述</Label>
          <Textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            placeholder="请输入角色描述"
            rows={4}
            className="border border-input/30"
          />
        </div>
      </div>
      
      <div className="flex justify-end gap-2">
        <Button 
          type="button" 
          variant="outline" 
          onClick={handleReset}
          disabled={isSaving}
          className="cursor-pointer"
        >
          <RotateCcw className="h-4 w-4 mr-2" />
          重置更改
        </Button>
        <Button 
          type="submit"
          disabled={isSaving || !formData.name.trim()}
          className="cursor-pointer"
        >
          {isSaving ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              保存中...
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              保存修改
            </>
          )}
        </Button>
      </div>
    </form>
  );
} 
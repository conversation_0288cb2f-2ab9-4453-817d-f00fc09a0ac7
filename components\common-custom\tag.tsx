"use client"

import { ReactNode, useState, KeyboardEvent } from "react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { X, Plus, Tag as TagIcon, LucideIcon } from "lucide-react"
import { cn } from "@/lib/utils"

// ============================================================================
// 类型定义 - 单文件组件类型定义直接在组件文件内
// ============================================================================

/**
 * 标签属性
 */
export interface TagProps {
  /**
   * 标签文本
   */
  label: string

  /**
   * 标签值
   */
  value?: string

  /**
   * 标签图标
   */
  icon?: LucideIcon | ReactNode

  /**
   * 标签变体
   * @default "secondary"
   */
  variant?: "default" | "secondary" | "outline" | "destructive"

  /**
   * 标签大小
   * @default "md"
   */
  size?: "sm" | "md" | "lg"

  /**
   * 是否可关闭
   * @default false
   */
  closable?: boolean

  /**
   * 关闭回调
   */
  onClose?: () => void

  /**
   * 点击回调
   */
  onClick?: () => void

  /**
   * 是否禁用
   * @default false
   */
  disabled?: boolean

  /**
   * 是否选中
   * @default false
   */
  selected?: boolean

  /**
   * 自定义颜色
   */
  color?: string

  /**
   * 自定义类名
   */
  className?: string
}

/**
 * 标签组属性
 */
export interface TagGroupProps {
  /**
   * 标签列表
   */
  tags: TagProps[] | string[]

  /**
   * 最大显示数量
   */
  maxVisible?: number

  /**
   * 更多标签显示方式
   * @default "count"
   */
  moreTagsDisplay?: "count" | "popover"

  /**
   * 排序方式
   * @default "default"
   */
  sortBy?: "default" | "text" | "custom"

  /**
   * 变体
   * @default "secondary"
   */
  variant?: "default" | "secondary" | "outline" | "destructive"

  /**
   * 大小
   * @default "md"
   */
  size?: "sm" | "md" | "lg"

  /**
   * 是否可关闭
   * @default false
   */
  closable?: boolean

  /**
   * 关闭标签回调
   */
  onClose?: (tag: string | TagProps, index: number) => void

  /**
   * 点击标签回调
   */
  onClick?: (tag: string | TagProps, index: number) => void

  /**
   * 自定义类名
   */
  className?: string
}

/**
 * 颜色标签属性
 */
export interface ColorTagProps extends Omit<TagProps, 'variant'> {
  /**
   * 自定义颜色
   */
  color?: string

  /**
   * 预设颜色
   */
  preset?: "red" | "green" | "blue" | "yellow" | "purple" | "cyan" | "orange" | "pink" | "gray"

  /**
   * 填充样式
   * @default "light"
   */
  fill?: "solid" | "light" | "outline"
}

/**
 * 标签输入框属性
 */
export interface TagInputProps {
  /**
   * 标签值列表
   */
  value: string[]

  /**
   * 值变化回调
   */
  onChange: (value: string[]) => void

  /**
   * 输入框占位符
   * @default "输入标签..."
   */
  placeholder?: string

  /**
   * 是否禁用
   * @default false
   */
  disabled?: boolean

  /**
   * 标签分隔符
   * @default /[,，\s]/
   */
  delimiter?: string | RegExp

  /**
   * 最大标签数量
   */
  maxTags?: number

  /**
   * 最小标签长度
   * @default 1
   */
  minLength?: number

  /**
   * 最大标签长度
   * @default 50
   */
  maxLength?: number

  /**
   * 验证函数
   */
  validate?: (value: string) => boolean | string

  /**
   * 是否允许重复
   * @default false
   */
  allowDuplicates?: boolean

  /**
   * 标签编辑模式
   * @default false
   */
  editable?: boolean

  /**
   * 自动调整大小
   * @default true
   */
  autoResize?: boolean

  /**
   * 大小
   * @default "md"
   */
  size?: "sm" | "md" | "lg"

  /**
   * 标签变体
   * @default "secondary"
   */
  tagVariant?: "default" | "secondary" | "outline" | "destructive"

  /**
   * 自定义类名
   */
  className?: string
}

/**
 * 标签组件
 */
export function Tag({
  label,
  value,
  color,
  closable = false,
  onClose,
  onClick,
  icon,
  className,
  selected = false,
  disabled = false,
  size = "md",
  variant = "secondary",
}: TagProps) {
  const sizeClasses = {
    sm: "text-xs py-0 px-1.5 h-5",
    md: "text-sm py-0.5 px-2 h-6",
    lg: "text-base py-1 px-3 h-8",
  }
  
  const handleClick = () => {
    if (!disabled && onClick) {
      onClick()
    }
  }
  
  const handleClose = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (!disabled && onClose) {
      onClose()
    }
  }
  
  const customStyle = color ? {
    backgroundColor: selected ? color : `${color}33`, // 33 = 20% opacity
    color: selected ? "white" : color,
    borderColor: color,
  } : {}
  
  return (
    <Badge
      variant={variant}
      className={cn(
        "cursor-pointer transition-colors",
        sizeClasses[size],
        selected && "bg-primary text-primary-foreground",
        disabled && "opacity-50 cursor-not-allowed",
        className
      )}
      style={customStyle}
      onClick={handleClick}
    >
      {icon && <span className="mr-1">{icon}</span>}
      {label}
      {closable && (
        <Button
          variant="ghost"
          size="sm"
          className={cn(
            "h-4 w-4 p-0 ml-1 hover:bg-destructive hover:text-destructive-foreground",
            size === "sm" && "h-3 w-3"
          )}
          onClick={handleClose}
          disabled={disabled}
        >
          <X className={cn("h-3 w-3", size === "sm" && "h-2 w-2")} />
        </Button>
      )}
    </Badge>
  )
}

/**
 * 标签组组件
 */
export function TagGroup({
  tags,
  maxVisible,
  moreTagsDisplay = "count",
  sortBy = "default",
  variant = "secondary",
  size = "md",
  closable = false,
  onClose,
  onClick,
  className,
}: TagGroupProps) {
  // 处理标签数据，支持字符串数组和TagProps数组
  const processedTags = tags.map((tag, index) => {
    if (typeof tag === 'string') {
      return { label: tag, value: tag }
    }
    return tag
  })

  // 排序处理
  const sortedTags = sortBy === 'text'
    ? [...processedTags].sort((a, b) => a.label.localeCompare(b.label))
    : processedTags

  // 显示数量限制
  const visibleTags = maxVisible ? sortedTags.slice(0, maxVisible) : sortedTags
  const hiddenCount = maxVisible ? Math.max(0, sortedTags.length - maxVisible) : 0

  const handleTagClick = (tag: TagProps, index: number) => {
    if (onClick) {
      onClick(tag, index)
    }
  }

  const handleTagClose = (tag: TagProps, index: number) => {
    if (onClose) {
      onClose(tag, index)
    }
  }

  return (
    <div className={cn("flex flex-wrap gap-2", className)}>
      {visibleTags.map((tag, index) => (
        <Tag
          key={tag.value || tag.label || index}
          label={tag.label}
          value={tag.value}
          icon={tag.icon}
          variant={variant}
          size={size}
          closable={closable}
          onClose={closable ? () => handleTagClose(tag, index) : undefined}
          onClick={() => handleTagClick(tag, index)}
          disabled={tag.disabled}
          selected={tag.selected}
          color={tag.color}
          className={tag.className}
        />
      ))}

      {/* 显示更多标签提示 */}
      {hiddenCount > 0 && moreTagsDisplay === "count" && (
        <Badge variant="outline" className={cn(
          "cursor-default",
          size === "sm" && "text-xs py-0 px-1.5 h-5",
          size === "md" && "text-sm py-0.5 px-2 h-6",
          size === "lg" && "text-base py-1 px-3 h-8"
        )}>
          +{hiddenCount}
        </Badge>
      )}
    </div>
  )
}

/**
 * 颜色标签组件
 */
export function ColorTag({
  label,
  value,
  icon,
  color,
  preset = "blue",
  fill = "light",
  size = "md",
  closable = false,
  disabled = false,
  selected = false,
  onClose,
  onClick,
  className,
}: ColorTagProps) {
  // 处理颜色样式
  const getColorStyles = () => {
    if (color) {
      // 自定义颜色
      switch (fill) {
        case "solid":
          return {
            backgroundColor: color,
            color: "white",
            borderColor: color,
          }
        case "outline":
          return {
            backgroundColor: "transparent",
            color: color,
            borderColor: color,
          }
        case "light":
        default:
          return {
            backgroundColor: `${color}20`,
            color: color,
            borderColor: `${color}40`,
          }
      }
    } else if (preset && PRESET_COLORS[preset]) {
      // 预设颜色
      const presetColor = PRESET_COLORS[preset]
      switch (fill) {
        case "solid":
          return `${presetColor.bg.replace('100', '500')} ${presetColor.text.replace('800', '50')} ${presetColor.border.replace('200', '500')}`
        case "outline":
          return `bg-transparent ${presetColor.text} ${presetColor.border}`
        case "light":
        default:
          return `${presetColor.bg} ${presetColor.text} ${presetColor.border}`
      }
    }

    // 默认样式
    return "bg-gray-100 text-gray-800 border-gray-200"
  }

  const colorStyles = getColorStyles()
  const isCustomColor = typeof colorStyles === "object"

  const sizeClasses = {
    sm: "text-xs py-0.5 px-1.5 h-5",
    md: "text-sm py-0.5 px-2 h-6",
    lg: "text-base py-1 px-3 h-8"
  }

  const handleClick = () => {
    if (!disabled && onClick) {
      onClick(value || label, { label, value, selected })
    }
  }

  const handleClose = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (!disabled && onClose) {
      onClose(value || label, { label, value, selected })
    }
  }

  return (
    <Badge
      variant="outline"
      className={cn(
        "cursor-pointer transition-colors border",
        sizeClasses[size],
        !isCustomColor && colorStyles,
        selected && "ring-2 ring-primary ring-offset-1",
        disabled && "opacity-50 cursor-not-allowed",
        className
      )}
      style={isCustomColor ? colorStyles : undefined}
      onClick={handleClick}
    >
      {icon && <span className="mr-1">{icon}</span>}
      {label}
      {closable && (
        <Button
          variant="ghost"
          size="sm"
          className={cn(
            "h-4 w-4 p-0 ml-1 hover:bg-destructive hover:text-destructive-foreground",
            size === "sm" && "h-3 w-3"
          )}
          onClick={handleClose}
          disabled={disabled}
        >
          <X className={cn("h-3 w-3", size === "sm" && "h-2 w-2")} />
        </Button>
      )}
    </Badge>
  )
}

/**
 * 标签输入框组件
 */
export function TagInput({
  value,
  onChange,
  placeholder = "输入标签...",
  disabled = false,
  delimiter = /[,，\s]/,
  maxTags,
  minLength = 1,
  maxLength = 50,
  validate,
  allowDuplicates = false,
  editable = false,
  autoResize = true,
  size = "md",
  tagVariant = "secondary",
  className,
}: TagInputProps) {
  const [inputValue, setInputValue] = useState("")
  const [error, setError] = useState<string | null>(null)

  const sizeClasses = {
    sm: "text-sm",
    md: "text-base",
    lg: "text-lg"
  }

  const addTag = (tagValue: string) => {
    const trimmedValue = tagValue.trim()

    if (!trimmedValue) return

    // 检查长度限制
    if (trimmedValue.length < minLength || trimmedValue.length > maxLength) {
      setError(`标签长度应在 ${minLength}-${maxLength} 个字符之间`)
      return
    }

    // 检查重复
    if (!allowDuplicates && value.includes(trimmedValue)) {
      setError("标签已存在")
      return
    }

    // 检查数量限制
    if (maxTags && value.length >= maxTags) {
      setError(`最多只能添加 ${maxTags} 个标签`)
      return
    }

    // 自定义验证
    if (validate) {
      const validationResult = validate(trimmedValue)
      if (validationResult !== true) {
        setError(typeof validationResult === "string" ? validationResult : "标签格式不正确")
        return
      }
    }

    // 添加标签
    onChange([...value, trimmedValue])
    setInputValue("")
    setError(null)
  }

  const removeTag = (index: number) => {
    const newTags = [...value]
    newTags.splice(index, 1)
    onChange(newTags)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputVal = e.target.value
    setInputValue(inputVal)
    setError(null)

    // 检查分隔符
    if (delimiter && typeof delimiter !== "string") {
      const match = inputVal.match(delimiter)
      if (match) {
        const tagValue = inputVal.replace(delimiter, "").trim()
        if (tagValue) {
          addTag(tagValue)
        }
      }
    }
  }

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" || e.key === "Tab") {
      e.preventDefault()
      if (inputValue.trim()) {
        addTag(inputValue)
      }
    } else if (e.key === "Backspace" && !inputValue && value.length > 0) {
      removeTag(value.length - 1)
    }
  }

  const handleInputBlur = () => {
    if (inputValue.trim()) {
      addTag(inputValue)
    }
  }

  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex flex-wrap gap-2 p-2 border rounded-md min-h-[40px] focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2">
        {value.map((tag, index) => (
          <Tag
            key={index}
            label={tag}
            variant={tagVariant}
            size={size}
            closable={!disabled}
            onClose={() => removeTag(index)}
            disabled={disabled}
          />
        ))}

        {(!maxTags || value.length < maxTags) && (
          <Input
            value={inputValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            onBlur={handleInputBlur}
            placeholder={placeholder}
            disabled={disabled}
            className={cn(
              "border-none shadow-none focus-visible:ring-0 focus-visible:ring-offset-0 p-0 h-auto min-w-[120px] flex-1",
              sizeClasses[size]
            )}
            style={autoResize ? { width: `${Math.max(120, inputValue.length * 8 + 20)}px` } : undefined}
          />
        )}
      </div>

      {error && (
        <p className="text-sm text-destructive">{error}</p>
      )}
    </div>
  )
}

/**
 * 可编辑标签组属性
 */
export interface EditableTagGroupProps {
  /**
   * 标签列表
   */
  tags: string[]
  
  /**
   * 标签变化回调
   */
  onChange: (tags: string[]) => void
  
  /**
   * 自定义类名
   */
  className?: string
  
  /**
   * 标签变体
   */
  variant?: "default" | "secondary" | "outline" | "destructive"
  
  /**
   * 标签大小
   */
  size?: "sm" | "md" | "lg"
  
  /**
   * 占位符
   */
  placeholder?: string
  
  /**
   * 最大标签数
   */
  maxTags?: number
  
  /**
   * 是否禁用
   */
  disabled?: boolean
}

/**
 * 可编辑标签组组件
 */
export function EditableTagGroup({
  tags,
  onChange,
  className,
  variant = "secondary",
  size = "md",
  placeholder = "添加标签...",
  maxTags,
  disabled = false,
}: EditableTagGroupProps) {
  const [inputValue, setInputValue] = useState("")
  
  const handleRemoveTag = (index: number) => {
    const newTags = [...tags]
    newTags.splice(index, 1)
    onChange(newTags)
  }
  
  const handleAddTag = () => {
    if (inputValue.trim() && !tags.includes(inputValue.trim())) {
      if (maxTags && tags.length >= maxTags) return
      onChange([...tags, inputValue.trim()])
      setInputValue("")
    }
  }
  
  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault()
      handleAddTag()
    } else if (e.key === "Backspace" && !inputValue && tags.length > 0) {
      handleRemoveTag(tags.length - 1)
    }
  }
  
  const reachedMaxTags = maxTags ? tags.length >= maxTags : false
  
  return (
    <div className={cn("space-y-3", className)}>
      <div className="flex flex-wrap gap-2">
        {tags.map((tag, index) => (
          <Tag
            key={index}
            label={tag}
            variant={variant}
            size={size}
            closable
            onClose={() => handleRemoveTag(index)}
            disabled={disabled}
          />
        ))}
      </div>
      
      {!reachedMaxTags && !disabled && (
        <div className="flex gap-2">
          <Input
            placeholder={placeholder}
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyDown={handleKeyDown}
            className="flex-1"
            disabled={disabled}
          />
          <Button 
            onClick={handleAddTag} 
            disabled={!inputValue.trim() || disabled}
          >
            <Plus className="h-4 w-4" />
          </Button>
        </div>
      )}
    </div>
  )
}

/**
 * 标签云属性
 */
export interface TagCloudProps {
  /**
   * 标签列表
   */
  tags: Array<{
    name: string
    count: number
    size?: "xs" | "sm" | "md" | "lg"
  }>
  
  /**
   * 点击回调
   */
  onClick?: (tag: string) => void
  
  /**
   * 自定义类名
   */
  className?: string
  
  /**
   * 标签变体
   */
  variant?: "default" | "secondary" | "outline" | "destructive"
}

/**
 * 标签云组件
 */
export function TagCloud({
  tags,
  onClick,
  className,
  variant = "outline",
}: TagCloudProps) {
  return (
    <div className={cn("flex flex-wrap gap-2", className)}>
      {tags.map((tag) => (
        <Badge
          key={tag.name}
          variant={variant}
          className={cn(
            "cursor-pointer hover:bg-muted transition-colors",
            tag.size === "lg" && "text-lg px-3 py-1",
            tag.size === "md" && "text-base px-2 py-1",
            tag.size === "sm" && "text-sm",
            tag.size === "xs" && "text-xs",
          )}
          onClick={() => onClick?.(tag.name)}
        >
          {tag.name} ({tag.count})
        </Badge>
      ))}
    </div>
  )
}

/**
 * 分类标签组属性
 */
export interface CategoryTagGroupProps {
  /**
   * 分类标签数据
   */
  categories: Record<string, string[]>
  
  /**
   * 选中的标签
   */
  selected?: string[]
  
  /**
   * 选择变化回调
   */
  onChange?: (selected: string[]) => void
  
  /**
   * 是否多选
   */
  multiple?: boolean
  
  /**
   * 自定义类名
   */
  className?: string
  
  /**
   * 标签变体
   */
  variant?: "default" | "secondary" | "outline" | "destructive"
}

/**
 * 分类标签组组件
 */
export function CategoryTagGroup({
  categories,
  selected = [],
  onChange,
  multiple = true,
  className,
  variant = "outline",
}: CategoryTagGroupProps) {
  const handleTagClick = (tag: string) => {
    if (!onChange) return
    
    if (multiple) {
      const newSelected = selected.includes(tag)
        ? selected.filter(v => v !== tag)
        : [...selected, tag]
      onChange(newSelected)
    } else {
      onChange(selected.includes(tag) ? [] : [tag])
    }
  }
  
  return (
    <div className={cn("space-y-4", className)}>
      {Object.entries(categories).map(([category, tags]) => (
        <div key={category} className="space-y-2">
          <div className="flex items-center gap-2">
            <TagIcon className="h-4 w-4" />
            <span className="text-sm font-medium">{category}</span>
          </div>
          <div className="flex flex-wrap gap-2 ml-6">
            {tags.map((tag) => (
              <Badge
                key={tag}
                variant={variant}
                className={cn(
                  "cursor-pointer hover:bg-muted transition-colors",
                  selected.includes(tag) && "bg-primary text-primary-foreground"
                )}
                onClick={() => handleTagClick(tag)}
              >
                {tag}
              </Badge>
            ))}
          </div>
        </div>
      ))}
    </div>
  )
}

// ============================================================================
// 类型导出 - 类型已在定义时导出
// ============================================================================

// ============================================================================
// 常量导出
// ============================================================================

/**
 * 支持的标签尺寸
 */
export const TAG_SIZES = ['sm', 'md', 'lg'] as const

/**
 * 支持的标签变体
 */
export const TAG_VARIANTS = ['default', 'secondary', 'outline', 'destructive'] as const

/**
 * 支持的颜色预设
 */
export const COLOR_PRESETS = ['red', 'green', 'blue', 'yellow', 'purple', 'cyan', 'orange', 'pink', 'gray'] as const

/**
 * 支持的填充样式
 */
export const FILL_STYLES = ['solid', 'light', 'outline'] as const

/**
 * 支持的排序方式
 */
export const SORT_TYPES = ['default', 'text', 'custom'] as const

/**
 * 支持的更多标签显示方式
 */
export const MORE_TAGS_DISPLAY = ['count', 'popover'] as const

/**
 * 默认标签配置
 */
export const DEFAULT_TAG_CONFIG = {
  variant: 'secondary' as const,
  size: 'md' as const,
  closable: false,
  disabled: false,
  selected: false,
} satisfies Partial<TagProps>

/**
 * 默认标签组配置
 */
export const DEFAULT_TAG_GROUP_CONFIG = {
  moreTagsDisplay: 'count' as const,
  sortBy: 'default' as const,
  variant: 'secondary' as const,
  size: 'md' as const,
  closable: false,
} satisfies Partial<TagGroupProps>

/**
 * 默认颜色标签配置
 */
export const DEFAULT_COLOR_TAG_CONFIG = {
  fill: 'light' as const,
  preset: 'blue' as const,
} satisfies Partial<ColorTagProps>

/**
 * 默认标签输入框配置
 */
export const DEFAULT_TAG_INPUT_CONFIG = {
  placeholder: '输入标签...',
  disabled: false,
  delimiter: /[,，\s]/,
  minLength: 1,
  maxLength: 50,
  allowDuplicates: false,
  editable: false,
  autoResize: true,
  size: 'md' as const,
  tagVariant: 'secondary' as const,
} satisfies Partial<TagInputProps>

/**
 * 预设颜色映射
 */
export const PRESET_COLORS = {
  red: { bg: 'bg-red-100', text: 'text-red-800', border: 'border-red-200' },
  green: { bg: 'bg-green-100', text: 'text-green-800', border: 'border-green-200' },
  blue: { bg: 'bg-blue-100', text: 'text-blue-800', border: 'border-blue-200' },
  yellow: { bg: 'bg-yellow-100', text: 'text-yellow-800', border: 'border-yellow-200' },
  purple: { bg: 'bg-purple-100', text: 'text-purple-800', border: 'border-purple-200' },
  cyan: { bg: 'bg-cyan-100', text: 'text-cyan-800', border: 'border-cyan-200' },
  orange: { bg: 'bg-orange-100', text: 'text-orange-800', border: 'border-orange-200' },
  pink: { bg: 'bg-pink-100', text: 'text-pink-800', border: 'border-pink-200' },
  gray: { bg: 'bg-gray-100', text: 'text-gray-800', border: 'border-gray-200' },
} as const
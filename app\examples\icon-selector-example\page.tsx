"use client"

import React, { useState } from "react"
import { ComponentPreviewContainer } from "@/components/component-detail/preview-container"
import { IconSelector } from "@/components/common-custom/icon-selector"

// 基础图标选择器示例代码
const basicIconSelectorCode = `
import React, { useState } from "react";
import { IconSelector } from "@/components/common-custom/icon-selector";

function BasicIconSelector() {
  const [selectedIcon, setSelectedIcon] = useState("Settings");
  
  return (
    <div className="flex flex-col gap-4">
      <div className="flex gap-4 flex-wrap justify-center">
        <IconSelector 
          value={selectedIcon}
          onChange={setSelectedIcon}
          label="选择图标"
        />
      </div>
      <div className="mt-4 p-4 border rounded-md">
        <p className="mb-2 text-sm text-muted-foreground">已选图标: {selectedIcon || "无"}</p>
      </div>
    </div>
  );
}

render(<BasicIconSelector />);
`;

// 自定义标签图标选择器示例代码
const customLabelIconSelectorCode = `
import React from "react";
import { IconSelector } from "@/components/common-custom/icon-selector";

function CustomLabelIconSelector() {
  return (
    <div className="flex gap-4 flex-wrap justify-center">
      <IconSelector 
        value="Bell" 
        onChange={() => {}}
        label="通知图标"
      />
      <IconSelector 
        value="Mail" 
        onChange={() => {}}
        label="邮件图标"
      />
    </div>
  );
}

render(<CustomLabelIconSelector />);
`;

// 不同颜色图标选择器示例代码
const colorIconSelectorCode = `
import React from "react";
import { IconSelector } from "@/components/common-custom/icon-selector";

function ColorIconSelector() {
  return (
    <div className="flex gap-4 flex-wrap justify-center">
      <IconSelector 
        value="Heart" 
        onChange={() => {}}
        iconColor="#e11d48"
      />
      <IconSelector 
        value="Star" 
        onChange={() => {}}
        iconColor="#eab308"
      />
      <IconSelector 
        value="Shield" 
        onChange={() => {}}
        iconColor="#2563eb"
      />
    </div>
  );
}

render(<ColorIconSelector />);
`;

// API文档组件
function IconSelectorApiDocs() {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="font-medium text-lg mb-2">IconSelector</h3>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">value</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">当前选中的图标名称</td>
              </tr>
              <tr>
                <td className="p-2 border">onChange</td>
                <td className="p-2 border">(value: string) =&gt; void</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">图标选择变化时的回调函数</td>
              </tr>
              <tr>
                <td className="p-2 border">label</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">"选择图标"</td>
                <td className="p-2 border">选择器标签文本</td>
              </tr>
              <tr>
                <td className="p-2 border">iconColor</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">图标颜色</td>
              </tr>
              <tr>
                <td className="p-2 border">disabled</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">false</td>
                <td className="p-2 border">是否禁用选择器</td>
              </tr>
              <tr>
                <td className="p-2 border">className</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">自定义类名</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

export default function IconSelectorPreview() {
  const examples = [
    {
      id: "basic-icon-selector",
      title: "基础图标选择器",
      description: "基础的图标选择器，用于从图标库中选择图标",
      code: basicIconSelectorCode,
      scope: { 
        IconSelector, 
        useState: React.useState,
        React 
      }, 
    },
    {
      id: "custom-label-icon-selector",
      title: "自定义标签",
      description: "使用自定义标签的图标选择器",
      code: customLabelIconSelectorCode,
      scope: { 
        IconSelector,
        React 
      },
    },
    {
      id: "color-icon-selector",
      title: "不同颜色",
      description: "使用不同颜色的图标选择器",
      code: colorIconSelectorCode,
      scope: { 
        IconSelector,
        React 
      },
    },
  ];

  return (
    <ComponentPreviewContainer
      title="图标选择器 IconSelector"
      description="用于从图标库中选择图标的交互组件"
      whenToUse="当需要在表单或配置界面中选择图标时；当需要为用户提供图标选择功能时；当需要在内容编辑器中插入图标时。"
      examples={examples}
      apiDocs={<IconSelectorApiDocs />}
    />
  );
} 
 
 
 

 
"use client"

import * as React from "react"
import { ComponentDetailLayout } from "./layout"
import Detail, { ComponentDetailProps } from "./detail"

// 导出需要的组件
export { ComponentPreview } from "./preview"
export { ComponentPreviewContainer } from "./preview-container"
export { ComponentDetailLayout } from "./layout"
export { Detail }
export { Detail as ComponentDetail }

// 统一组件接口，保持向后兼容
export function UnifiedComponent(props: ComponentDetailProps) {
  return <Detail {...props} />
}

// 这个注释已不再适用，因为我们恢复了UnifiedComponent 
"use client"

import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import type { SearchTagsProps } from "@/types/search"

/**
 * 搜索标签组件
 * 显示可点击的搜索标签，用于快速筛选或搜索
 */
export function SearchTags({
  tags,
  onTagClick,
  className,
}: SearchTagsProps) {
  if (!tags.length) return null
  
  return (
    <div className={cn("flex flex-wrap gap-1.5", className)}>
      {tags.map((tag, index) => (
        <Badge
          key={index}
          variant="secondary"
          className="cursor-pointer hover:bg-secondary/80 transition-colors"
          onClick={() => onTagClick(tag)}
        >
          {tag}
        </Badge>
      ))}
    </div>
  )
} 
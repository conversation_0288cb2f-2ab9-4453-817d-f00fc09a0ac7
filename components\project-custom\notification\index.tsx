"use client"

import { toast } from "@/hooks/use-toast"
import { 
  showToast as commonShowToast, 
  ToastHandler, 
  NotificationCenter, 
  AnnouncementBanner, 
  NotificationSettings 
} from "@/components/common-custom/notification"
import type { ToastProps } from "@/types/notification"

// 创建项目特定的toast处理器
const projectToastHandler: ToastHandler = {
  show: (props) => {
    toast({
      title: props.title,
      description: props.description,
      variant: props.variant,
      duration: props.duration
    })
  }
}

/**
 * 项目特定的Toast显示函数
 * 集成了hooks/use-toast
 */
export function showToast(props: ToastProps) {
  commonShowToast(props, projectToastHandler)
}

// 重新导出通用组件
export { NotificationCenter, AnnouncementBanner, NotificationSettings } 
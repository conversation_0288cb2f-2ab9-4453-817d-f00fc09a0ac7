/**
 * 默认API请求客户端
 * 提供应用程序的全局API请求实例
 */
import { extend, RequestInstance } from '@/lib/request';
import { API_BASE_URL, API_RESPONSE_CODE } from './configApi';
import { message } from '@/lib/request/utils';
import { removeToken, removeTokenType } from './authService';

/**
 * 处理未授权情况，清除token并重定向到登录页
 */
function handleUnauthorized() {
  console.warn('用户未登录或会话已过期');
  
  // 清除token
  removeToken();
  removeTokenType();
  
  // 重定向到登录页面
  if (typeof window !== 'undefined' && !window.location.pathname.includes('/login')) {
    window.location.replace('/login');
  }
}

/**
 * 创建请求实例
 */
const apiClient: RequestInstance = extend({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
  requestStatusConfig: {
    defaultShowLoading: false,
    defaultShowSuccessMessage: false,
    defaultShowErrorMessage: true
  },
  errorConfig: {
    errorHandler: (error) => {
      // 业务错误由业务代码处理
      if (error.name === 'BizError') {
        return;
      }
      
      // 网络错误
      if (!error.response) {
        message.error('网络连接异常，请检查您的网络');
        return;
      }
      
      // HTTP 状态码错误
      const status = error.response.status;
      if (status === 401) {
        message.error('用户未登录或登录已过期，请重新登录');
        // 处理401未授权
        handleUnauthorized();
        return;
      }
      
      if (status === 403) {
        message.error('没有权限访问该资源');
        return;
      }
      
      if (status === 404) {
        message.error('请求的资源不存在');
        return;
      }
      
      if (status >= 500) {
        message.error('服务器错误，请稍后再试');
        return;
      }
      
      message.error(`请求失败: ${status}`);
    }
  }
});

// 添加响应拦截器，处理通用的响应格式
apiClient.interceptors.response.use((response) => {
  // 判断业务状态码
  const data = response.data;
  // 检查是否为未授权状态
  if (data && data.code === API_RESPONSE_CODE.UNAUTHORIZED) {
    handleUnauthorized();
    const error: any = new Error(data.msg || '用户未登录或登录已过期');
    error.name = 'BizError';
    error.data = data;
    error.info = {
      errorCode: data.code,
      errorMessage: data.msg,
      data: data
    };
    throw error;
  }

  // 标准响应结构检查
  if (data && data.code !== undefined) {
    // 业务操作成功或失败都返回完整响应，便于业务层处理
    if (data.code === API_RESPONSE_CODE.SUCCESS) {
      // 返回完整响应对象，包含code、msg、data等字段
      return data;
    }
    
    // 业务操作失败
    const error: any = new Error(data.msg || '操作失败');
    error.name = 'BizError';
    error.data = data;
    error.info = {
      errorCode: data.code,
      errorMessage: data.msg,
      data: data
    };
    throw error;
  }

  // 处理非标准响应结构
  // HTTP状态码成功(2xx)，但响应数据不符合标准格式，进行标准化处理
  if (response.status >= 200 && response.status < 300) {
    // 构造标准响应格式
    return {
      code: API_RESPONSE_CODE.SUCCESS,
      msg: '操作成功',
      data: data // 将整个响应数据作为data字段返回
    };
  }

  // 对于HTTP错误状态码，构造标准错误响应
  return {
    code: String(response.status) || API_RESPONSE_CODE.ERROR,
    msg: response.statusText || '请求失败',
    data: null
  };
});

// 默认导出
export default apiClient;
"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { use } from "react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { ArrowLeft } from "lucide-react"

// 模拟数据
const memberData = {
  user1: {
    id: "user1",
    name: "张三",
    avatar: "/placeholder.svg?height=128&width=128",
    role: "部门总监",
    email: "<EMAIL>",
    phone: "13800138001",
    department: "研发部",
    joinDate: "2023-01-15",
    position: "技术总监",
    skills: ["项目管理", "团队领导", "系统架构", "代码审查"],
    bio: "拥有10年软件开发经验，专注于大型企业应用系统的架构设计和团队管理。曾主导多个大型项目的开发和交付，擅长敏捷开发和团队协作。",
  },
  user2: {
    id: "user2",
    name: "李四",
    avatar: "/placeholder.svg?height=128&width=128",
    role: "技术经理",
    email: "<EMAIL>",
    phone: "13800138002",
    department: "研发部",
    joinDate: "2023-02-20",
    position: "前端技术经理",
    skills: ["前端架构", "React", "Vue", "性能优化"],
    bio: "8年前端开发经验，精通现代前端框架和工具链。负责公司前端技术栈的选型和标准制定，带领前端团队完成多个重要项目。",
  },
  user3: {
    id: "user3",
    name: "王五",
    avatar: "/placeholder.svg?height=128&width=128",
    role: "高级开发",
    email: "<EMAIL>",
    phone: "13800138003",
    department: "研发部",
    joinDate: "2023-03-10",
    position: "高级后端开发",
    skills: ["Java", "Spring Boot", "微服务", "数据库设计"],
    bio: "6年后端开发经验，专注于高性能、高可用的分布式系统开发。精通Java生态系统和微服务架构，对数据库优化和系统性能调优有深入研究。",
  },
  user4: {
    id: "user4",
    name: "赵六",
    avatar: "/placeholder.svg?height=128&width=128",
    role: "开发工程师",
    email: "<EMAIL>",
    phone: "13800138004",
    department: "研发部",
    joinDate: "2023-04-05",
    position: "全栈开发工程师",
    skills: ["JavaScript", "Node.js", "React", "MongoDB"],
    bio: "4年全栈开发经验，擅长使用JavaScript全栈技术栈构建现代Web应用。热爱开源社区，积极参与技术分享和知识传播。",
  },
  user5: {
    id: "user5",
    name: "钱七",
    avatar: "/placeholder.svg?height=128&width=128",
    role: "测试工程师",
    email: "<EMAIL>",
    phone: "13800138005",
    department: "研发部",
    joinDate: "2023-05-15",
    position: "高级测试工程师",
    skills: ["自动化测试", "性能测试", "测试用例设计", "CI/CD"],
    bio: "5年软件测试经验，专注于自动化测试和持续集成流程优化。擅长设计全面的测试策略和测试用例，确保产品质量和稳定性。",
  },
}

const roles = [
  { id: "role1", name: "管理员" },
  { id: "role2", name: "部门总监" },
  { id: "role3", name: "技术经理" },
  { id: "role4", name: "开发工程师" },
  { id: "role5", name: "测试工程师" },
]

const departments = [
  { id: "dept1", name: "研发部" },
  { id: "dept2", name: "市场部" },
  { id: "dept3", name: "人力资源部" },
  { id: "dept4", name: "财务部" },
]

interface MemberEditPageProps {
  params: Promise<{ id: string }> | { id: string };
}

export default function MemberEditPage({ params }: MemberEditPageProps) {
  const router = useRouter()
  
  // 使用React.use解包params
  const resolvedParams = params instanceof Promise ? use(params) : params;
  const memberId = resolvedParams.id;
  
  const member = memberData[memberId as keyof typeof memberData]

  if (!member) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold">成员不存在</h1>
          <p className="mt-2 text-muted-foreground">找不到该成员信息</p>
          <Button className="mt-4" onClick={() => router.push("/")}>
            返回首页
          </Button>
        </div>
      </div>
    )
  }

  const handleSave = () => {
    // 保存逻辑
    router.push(`/members/${memberId}`)
  }

  return (
    <div className="flex min-h-screen w-full flex-col">
      <main className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
        <div className="flex items-center">
          <Button variant="outline" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="ml-2 text-lg font-semibold md:text-2xl">编辑成员信息</h1>
        </div>

        <Card>
          <CardHeader>
            <div className="flex items-center gap-4">
              <Avatar className="h-16 w-16">
                <AvatarImage src={member.avatar || "/placeholder.svg"} alt={member.name} />
                <AvatarFallback>{member.name.slice(0, 2)}</AvatarFallback>
              </Avatar>
              <CardTitle>{member.name}</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="name">姓名</Label>
                <Input id="name" defaultValue={member.name} />
              </div>
              <div className="space-y-2">
                <Label htmlFor="position">职位</Label>
                <Input id="position" defaultValue={member.position} />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">邮箱</Label>
                <Input id="email" type="email" defaultValue={member.email} />
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone">电话</Label>
                <Input id="phone" defaultValue={member.phone} />
              </div>
              <div className="space-y-2">
                <Label htmlFor="department">部门</Label>
                <Select defaultValue={member.department}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择部门" />
                  </SelectTrigger>
                  <SelectContent>
                    {departments.map((dept) => (
                      <SelectItem key={dept.id} value={dept.name}>
                        {dept.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="role">角色</Label>
                <Select defaultValue={member.role}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择角色" />
                  </SelectTrigger>
                  <SelectContent>
                    {roles.map((role) => (
                      <SelectItem key={role.id} value={role.name}>
                        {role.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="skills">技能（用逗号分隔）</Label>
                <Input id="skills" defaultValue={member.skills.join(", ")} />
              </div>
              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="bio">个人简介</Label>
                <Textarea id="bio" rows={4} defaultValue={member.bio} />
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" onClick={() => router.back()}>
              取消
            </Button>
            <Button onClick={handleSave}>保存</Button>
          </CardFooter>
        </Card>
      </main>
    </div>
  )
}

"use client"

import { Loader2 } from "lucide-react"
import { cn } from "@/lib/utils"
import { useEffect, useState } from "react"

interface PageLoadingProps {
  /**
   * 是否显示加载指示器
   */
  isLoading?: boolean
  
  /**
   * 加载文本
   */
  loadingText?: string
  
  /**
   * 是否考虑侧边栏
   */
  considerSidebar?: boolean
  
  /**
   * 侧边栏状态
   */
  sidebarState?: "expanded" | "collapsed" | "none"
  
  /**
   * 是否为移动设备
   */
  isMobile?: boolean
  
  /**
   * 自定义类名
   */
  className?: string
  
  /**
   * @deprecated 标记是否为新组件，仅用于界面展示
   */
  isNew?: boolean
}

/**
 * 页面加载组件
 * 在页面过渡时显示加载指示器
 */
export function PageLoading({ 
  isLoading = true,
  loadingText = "页面加载中...",
  considerSidebar = false,
  sidebarState = "none",
  isMobile = false,
  className,
  isNew = true 
}: PageLoadingProps) {
  const [mounted, setMounted] = useState(false)
  
  // 解决水合不匹配问题
  useEffect(() => {
    setMounted(true)
  }, [])
  
  // 如果不应该显示或未挂载，则不显示
  if (!isLoading || !mounted) return null
  
  // 根据侧边栏状态确定左侧偏移量和宽度
  let leftOffset = "left-0"
  let width = "w-full"
  
  if (considerSidebar && !isMobile) {
    if (sidebarState === "expanded") {
      leftOffset = "left-[var(--sidebar-width)]"
      width = "w-[calc(100%-var(--sidebar-width))]"
    } else if (sidebarState === "collapsed") {
      leftOffset = "left-[var(--sidebar-width-icon)]"
      width = "w-[calc(100%-var(--sidebar-width-icon))]"
    }
  }

  return (
    <div className={cn(
      "fixed inset-y-0 right-0 z-50 flex items-center justify-center pointer-events-none",
      leftOffset,
      width,
      // 添加过渡效果
      "transition-all duration-300 ease-in-out",
      className
    )}>
      {/* 半透明背景 */}
      <div className="absolute inset-0 bg-background/80 backdrop-blur-[2px] transition-opacity animate-in fade-in" />
      
      {/* 加载指示器 */}
      <div className="relative z-10 flex items-center justify-center animate-in fade-in slide-in-from-bottom-4 duration-300">
        <div className="bg-card rounded-lg shadow-lg p-4 flex items-center gap-3">
          <Loader2 className="h-6 w-6 animate-spin text-primary" />
          <span className="text-sm font-medium">{loadingText}</span>
        </div>
      </div>
      
      {/* NEW 标识 */}
      {isNew && (
        <span className="absolute top-4 right-4 bg-primary text-primary-foreground text-[10px] font-medium px-1 py-0.5 rounded-full">
          NEW
        </span>
      )}
    </div>
  )
} 
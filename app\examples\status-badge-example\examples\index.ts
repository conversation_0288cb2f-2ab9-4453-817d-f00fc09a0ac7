/**
 * 状态徽章组件示例代码集合
 * 
 * 按照新规范管理示例代码，避免在页面文件中硬编码
 */

import React from "react"
import { 
  StatusBadge, 
  OnlineStatusBadge, 
  ProgressStatusBadge, 
  PriorityBadge 
} from "@/components/common-custom/status-badge"
import { CheckCircle, Clock, XCircle, AlertCircle } from "lucide-react"

// ============================================================================
// 基础状态徽章示例
// ============================================================================

export const basicStatusExample = {
  id: "basic-status-badge",
  title: "基础状态徽章",
  description: "展示不同状态的基础徽章样式",
  code: `
import React from "react";
import { StatusBadge } from "@/components/common-custom/status-badge";

function BasicStatusExample() {
  return (
    <div className="space-y-4">
      <div>
        <h4 className="text-sm font-medium mb-2">基础状态</h4>
        <div className="flex flex-wrap items-center gap-2">
          <StatusBadge status="success" />
          <StatusBadge status="warning" />
          <StatusBadge status="error" />
          <StatusBadge status="info" />
          <StatusBadge status="pending" />
        </div>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">不同尺寸</h4>
        <div className="flex flex-wrap items-center gap-2">
          <StatusBadge status="success" size="sm" />
          <StatusBadge status="success" size="md" />
          <StatusBadge status="success" size="lg" />
        </div>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">带状态点</h4>
        <div className="flex flex-wrap items-center gap-2">
          <StatusBadge status="success" showStatusDot />
          <StatusBadge status="warning" showStatusDot />
          <StatusBadge status="error" showStatusDot />
        </div>
      </div>
    </div>
  );
}

render(<BasicStatusExample />);
  `,
  scope: { StatusBadge, React },
}

// ============================================================================
// 在线状态徽章示例
// ============================================================================

export const onlineStatusExample = {
  id: "online-status-badge",
  title: "在线状态徽章",
  description: "展示用户在线状态的专用徽章",
  code: `
import React from "react";
import { OnlineStatusBadge } from "@/components/common-custom/status-badge";

function OnlineStatusExample() {
  return (
    <div className="space-y-4">
      <div>
        <h4 className="text-sm font-medium mb-2">在线状态</h4>
        <div className="flex flex-wrap items-center gap-2">
          <OnlineStatusBadge status="online" />
          <OnlineStatusBadge status="away" />
          <OnlineStatusBadge status="busy" />
          <OnlineStatusBadge status="offline" />
          <OnlineStatusBadge status="invisible" />
        </div>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">带脉冲动画</h4>
        <div className="flex flex-wrap items-center gap-2">
          <OnlineStatusBadge status="online" pulse />
          <OnlineStatusBadge status="busy" pulse />
        </div>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">不显示文字</h4>
        <div className="flex flex-wrap items-center gap-2">
          <OnlineStatusBadge status="online" showText={false} />
          <OnlineStatusBadge status="away" showText={false} />
          <OnlineStatusBadge status="busy" showText={false} />
          <OnlineStatusBadge status="offline" showText={false} />
        </div>
      </div>
    </div>
  );
}

render(<OnlineStatusExample />);
  `,
  scope: { OnlineStatusBadge, React },
}

// ============================================================================
// 进度状态徽章示例
// ============================================================================

export const progressStatusExample = {
  id: "progress-status-badge",
  title: "进度状态徽章",
  description: "展示任务或流程进度的状态徽章",
  code: `
import React from "react";
import { ProgressStatusBadge } from "@/components/common-custom/status-badge";

function ProgressStatusExample() {
  return (
    <div className="space-y-4">
      <div>
        <h4 className="text-sm font-medium mb-2">进度状态</h4>
        <div className="flex flex-wrap items-center gap-2">
          <ProgressStatusBadge status="pending" />
          <ProgressStatusBadge status="in-progress" />
          <ProgressStatusBadge status="completed" />
          <ProgressStatusBadge status="failed" />
          <ProgressStatusBadge status="cancelled" />
        </div>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">带进度百分比</h4>
        <div className="flex flex-wrap items-center gap-2">
          <ProgressStatusBadge status="in-progress" progress={25} showProgress />
          <ProgressStatusBadge status="in-progress" progress={60} showProgress />
          <ProgressStatusBadge status="in-progress" progress={85} showProgress />
          <ProgressStatusBadge status="completed" progress={100} showProgress />
        </div>
      </div>
    </div>
  );
}

render(<ProgressStatusExample />);
  `,
  scope: { ProgressStatusBadge, React },
}

// ============================================================================
// 优先级徽章示例
// ============================================================================

export const priorityBadgeExample = {
  id: "priority-badge",
  title: "优先级徽章",
  description: "展示任务或事项优先级的徽章",
  code: `
import React from "react";
import { PriorityBadge } from "@/components/common-custom/status-badge";

function PriorityBadgeExample() {
  return (
    <div className="space-y-4">
      <div>
        <h4 className="text-sm font-medium mb-2">优先级等级</h4>
        <div className="flex flex-wrap items-center gap-2">
          <PriorityBadge priority="low" />
          <PriorityBadge priority="medium" />
          <PriorityBadge priority="high" />
          <PriorityBadge priority="critical" />
        </div>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">数字优先级</h4>
        <div className="flex flex-wrap items-center gap-2">
          <PriorityBadge priority={1} />
          <PriorityBadge priority={2} />
          <PriorityBadge priority={3} />
          <PriorityBadge priority={4} />
          <PriorityBadge priority={5} />
        </div>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">不同样式</h4>
        <div className="flex flex-wrap items-center gap-2">
          <PriorityBadge priority="high" showIcon={false} />
          <PriorityBadge priority="critical" showText={false} />
          <PriorityBadge priority="medium" size="sm" />
          <PriorityBadge priority="high" size="lg" />
        </div>
      </div>
    </div>
  );
}

render(<PriorityBadgeExample />);
  `,
  scope: { PriorityBadge, React },
}

// ============================================================================
// 自定义状态映射示例
// ============================================================================

export const customMappingExample = {
  id: "custom-mapping-badge",
  title: "自定义状态映射",
  description: "展示如何自定义状态映射和样式",
  code: `
import React from "react";
import { StatusBadge } from "@/components/common-custom/status-badge";
import { CheckCircle, Clock, XCircle, AlertCircle } from "lucide-react";

function CustomMappingExample() {
  const customStatusMap = {
    approved: {
      label: "已批准",
      color: "bg-green-100 text-green-800",
      icon: CheckCircle,
    },
    reviewing: {
      label: "审核中",
      color: "bg-blue-100 text-blue-800",
      icon: Clock,
    },
    rejected: {
      label: "已拒绝",
      color: "bg-red-100 text-red-800",
      icon: XCircle,
    },
    draft: {
      label: "草稿",
      color: "bg-gray-100 text-gray-800",
      icon: AlertCircle,
    },
  };
  
  return (
    <div className="space-y-4">
      <div>
        <h4 className="text-sm font-medium mb-2">自定义审批状态</h4>
        <div className="flex flex-wrap items-center gap-2">
          <StatusBadge 
            status="approved" 
            statusMap={customStatusMap}
          />
          <StatusBadge 
            status="reviewing" 
            statusMap={customStatusMap}
          />
          <StatusBadge 
            status="rejected" 
            statusMap={customStatusMap}
          />
          <StatusBadge 
            status="draft" 
            statusMap={customStatusMap}
          />
        </div>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">带图标的自定义状态</h4>
        <div className="flex flex-wrap items-center gap-2">
          <StatusBadge 
            status="approved" 
            statusMap={customStatusMap}
            showStatusDot={false}
          />
          <StatusBadge 
            status="reviewing" 
            statusMap={customStatusMap}
            showStatusDot={false}
          />
        </div>
      </div>
    </div>
  );
}

render(<CustomMappingExample />);
  `,
  scope: { StatusBadge, CheckCircle, Clock, XCircle, AlertCircle, React },
}

// ============================================================================
// 统一导出所有示例
// ============================================================================

export const allExamples = [
  basicStatusExample,
  onlineStatusExample,
  progressStatusExample,
  priorityBadgeExample,
  customMappingExample,
]

// ============================================================================
// 按类别导出示例
// ============================================================================

export const basicExamples = [basicStatusExample, onlineStatusExample]
export const advancedExamples = [progressStatusExample, priorityBadgeExample]
export const customizationExamples = [customMappingExample]

// ============================================================================
// 示例元数据
// ============================================================================

export const exampleMetadata = {
  total: allExamples.length,
  categories: {
    basic: basicExamples.length,
    advanced: advancedExamples.length,
    customization: customizationExamples.length,
  },
  tags: ["status", "badge", "state", "priority", "progress", "online"],
  lastUpdated: "2024-01-01",
}

import { LucideIcon } from "lucide-react"
import { ReactNode } from "react"

/**
 * 统计卡片项属性
 */
export interface DashboardStatItem {
  /**
   * 卡片标题
   */
  title: string
  
  /**
   * 卡片图标
   */
  icon?: LucideIcon
  
  /**
   * 卡片数值
   */
  value: string | number
  
  /**
   * 变化值
   */
  change?: string
  
  /**
   * 变化趋势：上升或下降
   */
  trend?: "up" | "down" | "neutral"
  
  /**
   * 变化描述
   */
  description?: string
  
  /**
   * 自定义类名
   */
  className?: string
  
  /**
   * 卡片点击事件
   */
  onClick?: () => void
}

/**
 * 快捷操作项属性
 */
export interface QuickActionItem {
  /**
   * 操作标题
   */
  title: string
  
  /**
   * 操作图标
   */
  icon?: LucideIcon
  
  /**
   * 操作描述
   */
  description?: string
  
  /**
   * 操作链接
   */
  href?: string
  
  /**
   * 点击事件
   */
  onClick?: () => void
  
  /**
   * 是否禁用
   */
  disabled?: boolean
  
  /**
   * 自定义类名
   */
  className?: string
}

/**
 * 活动项属性
 */
export interface ActivityItem {
  /**
   * 活动ID
   */
  id: string | number
  
  /**
   * 活动标题
   */
  title: string
  
  /**
   * 活动描述
   */
  description?: string
  
  /**
   * 活动时间
   */
  time: string
  
  /**
   * 活动类型
   */
  type?: "info" | "success" | "warning" | "error"
  
  /**
   * 活动图标
   */
  icon?: LucideIcon
  
  /**
   * 用户头像
   */
  avatar?: string
  
  /**
   * 用户名
   */
  user?: string
  
  /**
   * 是否已读
   */
  read?: boolean
  
  /**
   * 点击事件
   */
  onClick?: () => void
}

/**
 * 任务项属性
 */
export interface TaskItem {
  /**
   * 任务ID
   */
  id: string | number
  
  /**
   * 任务标题
   */
  title: string
  
  /**
   * 任务描述
   */
  description?: string
  
  /**
   * 任务状态
   */
  status: "pending" | "in-progress" | "completed" | "cancelled"
  
  /**
   * 任务优先级
   */
  priority?: "low" | "medium" | "high" | "urgent"
  
  /**
   * 截止日期
   */
  dueDate?: string
  
  /**
   * 负责人
   */
  assignee?: string
  
  /**
   * 负责人头像
   */
  assigneeAvatar?: string
  
  /**
   * 任务标签
   */
  tags?: string[]
  
  /**
   * 进度百分比
   */
  progress?: number
  
  /**
   * 点击事件
   */
  onClick?: () => void
}

/**
 * 图表数据点
 */
export interface ChartDataPoint {
  /**
   * 数据标签
   */
  label: string
  
  /**
   * 数据值
   */
  value: number
  
  /**
   * 数据颜色
   */
  color?: string
}

/**
 * 图表配置
 */
export interface ChartConfig {
  /**
   * 图表类型
   */
  type: "line" | "bar" | "pie" | "area" | "donut"
  
  /**
   * 图表数据
   */
  data: ChartDataPoint[]
  
  /**
   * 图表高度
   */
  height?: number
  
  /**
   * 是否显示图例
   */
  showLegend?: boolean
  
  /**
   * 是否显示网格
   */
  showGrid?: boolean
  
  /**
   * 是否显示工具提示
   */
  showTooltip?: boolean
  
  /**
   * 颜色主题
   */
  colors?: string[]
}

/**
 * 产品排名项
 */
export interface ProductRankingItem {
  /**
   * 产品ID
   */
  id: string | number
  
  /**
   * 产品名称
   */
  name: string
  
  /**
   * 产品图片
   */
  image?: string
  
  /**
   * 销售额
   */
  sales: number
  
  /**
   * 销售变化
   */
  change: number
  
  /**
   * 排名
   */
  rank: number
  
  /**
   * 产品类别
   */
  category?: string
}

/**
 * 系统状态项
 */
export interface SystemStatusItem {
  /**
   * 服务名称
   */
  name: string

  /**
   * 显示标签
   */
  label: string

  /**
   * 状态值（百分比）
   */
  value: number

  /**
   * 服务状态
   */
  status: "online" | "offline" | "warning" | "maintenance"

  /**
   * 响应时间
   */
  responseTime?: number

  /**
   * 正常运行时间
   */
  uptime?: string

  /**
   * 最后检查时间
   */
  lastCheck?: string

  /**
   * 服务描述
   */
  description?: string
}

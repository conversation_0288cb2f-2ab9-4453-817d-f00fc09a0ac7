"use client"

import { useEffect, useRef } from 'react'

export function GradientBackground() {
  const canvasRef = useRef<HTMLCanvasElement>(null)

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // 设置画布尺寸为父容器大小
    const resizeCanvas = () => {
      const parent = canvas.parentElement
      if (parent) {
        canvas.width = parent.offsetWidth
        canvas.height = parent.offsetHeight
      }
    }

    // 监听窗口大小变化
    window.addEventListener('resize', resizeCanvas)
    resizeCanvas()

    // 粒子类
    class Particle {
      x: number
      y: number
      size: number
      speedX: number
      speedY: number
      color: string
      alpha: number
      pulseFactor: number
      pulseSpeed: number
      
      constructor(canvas: HTMLCanvasElement) {
        this.x = Math.random() * canvas.width
        this.y = Math.random() * canvas.height
        this.size = Math.random() * 3 + 1.0
        this.speedX = (Math.random() - 0.5) * 0.4
        this.speedY = (Math.random() - 0.5) * 0.4
        this.color = this.getRandomColor()
        this.alpha = Math.random() * 0.6 + 0.2
        this.pulseFactor = 0
        this.pulseSpeed = Math.random() * 0.02 + 0.005
      }
      
      getRandomColor() {
        // 改进黑白灰配色方案，增加一点亮度
        const colors = [
          'rgba(255, 255, 255, 0.9)',    // 白色
          'rgba(230, 230, 230, 0.85)',   // 浅灰色
          'rgba(210, 210, 210, 0.8)',    // 中灰色
        ]
        return colors[Math.floor(Math.random() * colors.length)]
      }
      
      update(canvas: HTMLCanvasElement) {
        this.x += this.speedX
        this.y += this.speedY
        
        // 边界检查
        if (this.x < 0 || this.x > canvas.width) {
          this.speedX = -this.speedX
        }
        if (this.y < 0 || this.y > canvas.height) {
          this.speedY = -this.speedY
        }
        
        // 随机微小变化
        if (Math.random() < 0.03) {
          this.speedX += (Math.random() - 0.5) * 0.1
          this.speedY += (Math.random() - 0.5) * 0.1
          
          // 限制速度
          this.speedX = Math.max(-0.5, Math.min(0.5, this.speedX))
          this.speedY = Math.max(-0.5, Math.min(0.5, this.speedY))
        }
        
        // 粒子大小脉动
        this.pulseFactor += this.pulseSpeed
        this.size = Math.random() * 2.5 + 1.0 + Math.sin(this.pulseFactor) * 0.5
      }
      
      draw(ctx: CanvasRenderingContext2D) {
        ctx.beginPath()
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2)
        ctx.fillStyle = this.color
        ctx.globalAlpha = this.alpha
        ctx.fill()
        
        // 添加一点发光效果
        ctx.shadowColor = this.color
        ctx.shadowBlur = 5
        ctx.fill()
        
        // 重置
        ctx.shadowBlur = 0
        ctx.globalAlpha = 1
      }
    }

    // 连线类
    class Connection {
      static draw(ctx: CanvasRenderingContext2D, p1: Particle, p2: Particle, distance: number, maxDistance: number) {
        const opacity = 1 - (distance / maxDistance)
        
        // 创建线性渐变 - 检查坐标是否有效
        try {
          // 检查坐标是否为有限数值
          if (isFinite(p1.x) && isFinite(p1.y) && isFinite(p2.x) && isFinite(p2.y)) {
            const gradient = ctx.createLinearGradient(p1.x, p1.y, p2.x, p2.y)
            gradient.addColorStop(0, `rgba(255, 255, 255, ${opacity * 0.5})`)
            gradient.addColorStop(1, `rgba(180, 180, 180, ${opacity * 0.3})`)
            
            ctx.beginPath()
            ctx.moveTo(p1.x, p1.y)
            ctx.lineTo(p2.x, p2.y)
            ctx.strokeStyle = gradient
            ctx.lineWidth = 0.8
            ctx.stroke()
          }
        } catch (error) {
          // 出错时使用纯色
          ctx.beginPath()
          ctx.moveTo(p1.x, p1.y)
          ctx.lineTo(p2.x, p2.y)
          ctx.strokeStyle = `rgba(255, 255, 255, ${opacity * 0.4})`
          ctx.lineWidth = 0.8
          ctx.stroke()
        }
      }
    }

    // 数据流类
    class DataStream {
      x: number
      y: number
      length: number
      speed: number
      characters: string[]
      characterSizes: number[]
      startTime: number
      color: string
      width: number
      
      constructor(canvas: HTMLCanvasElement) {
        this.x = Math.random() * canvas.width
        this.y = -200 - Math.random() * 500
        this.length = Math.floor(Math.random() * 15) + 8
        this.speed = Math.random() * 3 + 1.5
        this.characters = this.generateCharacters()
        this.characterSizes = this.generateSizes()
        this.startTime = Date.now() + Math.random() * 5000
        this.color = this.getRandomColor()
        this.width = Math.random() * 0.8 + 0.2
      }
      
      getRandomColor() {
        // 使用淡白色系，但更亮一些
        const shades = [
          'rgba(255, 255, 255, 0.95)',  // 明亮白色
          'rgba(235, 235, 235, 0.9)',   // 浅灰白色
          'rgba(215, 215, 215, 0.85)',  // 中灰白色
        ]
        return shades[Math.floor(Math.random() * shades.length)]
      }
      
      generateCharacters() {
        const chars: string[] = []
        for (let i = 0; i < this.length; i++) {
          const charType = Math.floor(Math.random() * 4)
          let char
          
          if (charType === 0) {
            // 数字
            char = String.fromCharCode(48 + Math.floor(Math.random() * 10))
          } else if (charType === 1) {
            // 大写字母
            char = String.fromCharCode(65 + Math.floor(Math.random() * 26))
          } else if (charType === 2) {
            // 小写字母
            char = String.fromCharCode(97 + Math.floor(Math.random() * 26))
          } else {
            // 特殊字符
            const specialChars = "!@#$%^&*()_+-=[]{}|;:,./<>?"
            char = specialChars[Math.floor(Math.random() * specialChars.length)]
          }
          chars.push(char)
        }
        return chars
      }
      
      generateSizes() {
        const sizes: number[] = []
        for (let i = 0; i < this.length; i++) {
          // 生成从16到22的随机大小
          sizes.push(Math.floor(Math.random() * 7) + 16)
        }
        return sizes
      }
      
      update(canvas: HTMLCanvasElement) {
        // 只有在开始时间后才更新
        if (Date.now() < this.startTime) return false
        
        // 更新位置
        this.y += this.speed
        
        // 如果已经超出画布底部，重置到顶部
        if (this.y > canvas.height + 200) {
          this.y = -200
          this.x = Math.random() * canvas.width
          this.characters = this.generateCharacters()
          this.characterSizes = this.generateSizes()
          this.speed = Math.random() * 3 + 1.5
          return false
        }
        
        // 随机更新字符
        if (Math.random() < 0.03) {
          const index = Math.floor(Math.random() * this.characters.length)
          const charType = Math.floor(Math.random() * 4)
          
          if (charType === 0) {
            this.characters[index] = String.fromCharCode(48 + Math.floor(Math.random() * 10))
          } else if (charType === 1) {
            this.characters[index] = String.fromCharCode(65 + Math.floor(Math.random() * 26))
          } else if (charType === 2) {
            this.characters[index] = String.fromCharCode(97 + Math.floor(Math.random() * 26))
          } else {
            const specialChars = "!@#$%^&*()_+-=[]{}|;:,./<>?"
            this.characters[index] = specialChars[Math.floor(Math.random() * specialChars.length)]
          }
        }
        
        return true
      }
      
      draw(ctx: CanvasRenderingContext2D) {
        if (Date.now() < this.startTime) return
        
        for (let i = 0; i < this.characters.length; i++) {
          const y = this.y + i * 22
          
          if (y < 0 || y > ctx.canvas.height) continue
          
          const opacity = Math.min(1, Math.max(0, (this.characters.length - i) / this.characters.length))
          // 使用流的颜色，但透明度不同
          const color = this.color.replace(/[\d.]+\)$/, `${opacity * 0.95})`)
          ctx.fillStyle = color
          
          // 使用字体大小
          const fontSize = this.characterSizes[i]
          ctx.font = `bold ${fontSize}px monospace`
          
          // 添加文字阴影效果
          ctx.shadowColor = 'rgba(255, 255, 255, 0.8)'
          ctx.shadowBlur = 3
          ctx.shadowOffsetX = 0
          ctx.shadowOffsetY = 0
          
          // 绘制字符
          ctx.globalAlpha = opacity
          ctx.fillText(this.characters[i], this.x, y)
          
          // 重置阴影
          ctx.shadowColor = 'transparent'
          ctx.shadowBlur = 0
          ctx.shadowOffsetX = 0
          ctx.shadowOffsetY = 0
          ctx.globalAlpha = 1
        }
      }
    }

    // 不规则形状类
    class IrregularShape {
      points: {x: number, y: number}[]
      x: number
      y: number
      rotation: number
      scale: number
      color: string
      rotationSpeed: number
      
      constructor(canvas: HTMLCanvasElement) {
        this.points = this.generatePoints()
        this.x = Math.random() * canvas.width
        this.y = Math.random() * canvas.height
        this.rotation = Math.random() * Math.PI * 2
        this.scale = Math.random() * 0.4 + 0.2
        // 使用稍亮的灰色调
        this.color = `rgba(230, 230, 230, ${Math.random() * 0.15 + 0.1})`
        this.rotationSpeed = (Math.random() - 0.5) * 0.002
      }
      
      generatePoints() {
        const points: {x: number, y: number}[] = []
        const numPoints = Math.floor(Math.random() * 3) + 5 // 5-7个点
        const baseRadius = 40
        
        for (let i = 0; i < numPoints; i++) {
          const angle = (i / numPoints) * Math.PI * 2
          const radius = baseRadius + Math.random() * 20 - 10
          points.push({
            x: Math.cos(angle) * radius,
            y: Math.sin(angle) * radius
          })
        }
        
        return points
      }
      
      update() {
        this.rotation += this.rotationSpeed
      }
      
      draw(ctx: CanvasRenderingContext2D) {
        ctx.save()
        ctx.translate(this.x, this.y)
        ctx.rotate(this.rotation)
        ctx.scale(this.scale, this.scale)
        
        ctx.beginPath()
        ctx.moveTo(this.points[0].x, this.points[0].y)
        
        for (let i = 1; i < this.points.length; i++) {
          ctx.lineTo(this.points[i].x, this.points[i].y)
        }
        
        ctx.closePath()
        ctx.strokeStyle = this.color
        ctx.lineWidth = 1.5
        ctx.stroke()
        
        ctx.restore()
      }
    }

    // 星光效果
    class Star {
      x: number
      y: number
      size: number
      opacity: number
      pulseFactor: number
      pulseSpeed: number
      
      constructor(canvas: HTMLCanvasElement) {
        this.x = Math.random() * canvas.width
        this.y = Math.random() * canvas.height
        this.size = Math.random() * 2 + 0.8
        this.opacity = Math.random() * 0.5 + 0.3
        this.pulseFactor = 0
        this.pulseSpeed = Math.random() * 0.02 + 0.005
      }
      
      update() {
        this.pulseFactor += this.pulseSpeed
        this.opacity = 0.5 + Math.sin(this.pulseFactor) * 0.3
      }
      
      draw(ctx: CanvasRenderingContext2D) {
        ctx.beginPath()
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2)
        ctx.fillStyle = `rgba(255, 255, 255, ${this.opacity})`
        
        // 添加发光效果
        ctx.shadowColor = 'rgba(255, 255, 255, 0.8)'
        ctx.shadowBlur = 10
        
        ctx.fill()
        
        // 重置阴影
        ctx.shadowBlur = 0
      }
    }

    // 添加网格线效果类
    class GridLines {
      spacing: number
      color: string
      speed: number
      offset: number
      
      constructor() {
        this.spacing = 120  // 增加间距，减少线条密度
        this.color = 'rgba(180, 180, 180, 0.07)'  // 降低不透明度
        this.speed = 0.15
        this.offset = 0
      }
      
      update() {
        this.offset = (this.offset + this.speed) % this.spacing
      }
      
      draw(ctx: CanvasRenderingContext2D, canvasWidth: number, canvasHeight: number) {
        ctx.beginPath()
        ctx.strokeStyle = this.color
        ctx.lineWidth = 0.5
        
        // 垂直线 - 减少数量
        for (let x = this.offset; x < canvasWidth; x += this.spacing) {
          ctx.moveTo(x, 0)
          ctx.lineTo(x, canvasHeight)
        }
        
        // 水平线 - 减少数量
        for (let y = this.offset; y < canvasHeight; y += this.spacing) {
          ctx.moveTo(0, y)
          ctx.lineTo(canvasWidth, y)
        }
        
        ctx.stroke()
      }
    }

    // 创建并绘制粒子、数据流和不规则形状
    const particles: Particle[] = []
    const particleCount = 80  // 减少粒子数量
    for (let i = 0; i < particleCount; i++) {
      particles.push(new Particle(canvas))
    }

    const dataStreams: DataStream[] = []
    const dataStreamCount = 8  // 减少数据流数量
    for (let i = 0; i < dataStreamCount; i++) {
      dataStreams.push(new DataStream(canvas))
    }

    const irregularShapes: IrregularShape[] = []
    const irregularShapeCount = 6  // 减少形状数量
    for (let i = 0; i < irregularShapeCount; i++) {
      irregularShapes.push(new IrregularShape(canvas))
    }

    const stars: Star[] = []
    const starCount = 120  // 适当减少星星数量
    for (let i = 0; i < starCount; i++) {
      stars.push(new Star(canvas))
    }
    
    // 创建网格线对象
    const gridLines = new GridLines()

    // 动画循环
    let animationTime = 0
    const maxDistance = 150  // 增加连线最大距离
    let animationFrameId: number

    const animate = () => {
      try {
        // 清空画布
        ctx.clearRect(0, 0, canvas.width, canvas.height)
        
        // 绘制背景
        const drawBackground = () => {
          try {
            // 创建更深邃的黑色渐变背景
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height)
            gradient.addColorStop(0, '#0A0A0A')      // 几乎纯黑
            gradient.addColorStop(0.3, '#121212')    // 深黑色
            gradient.addColorStop(0.7, '#181818')    // 稍亮的黑色
            gradient.addColorStop(1, '#222222')      // 深灰色
            
            ctx.fillStyle = gradient
            ctx.fillRect(0, 0, canvas.width, canvas.height)
            
            // 添加辉光效果 - 检查坐标是否有效
            if (isFinite(canvas.width) && isFinite(canvas.height)) {
              const radialGradient = ctx.createRadialGradient(
                canvas.width * 0.6, canvas.height * 0.4, 0,
                canvas.width * 0.6, canvas.height * 0.4, canvas.width * 0.8
              )
              radialGradient.addColorStop(0, 'rgba(50, 50, 50, 0.25)')
              radialGradient.addColorStop(0.5, 'rgba(40, 40, 40, 0.1)')
              radialGradient.addColorStop(1, 'rgba(30, 30, 30, 0)')
              
              ctx.fillStyle = radialGradient
              ctx.fillRect(0, 0, canvas.width, canvas.height)
            }
          } catch (error) {
            console.error("背景渲染错误:", error)
            // 出错时使用纯色背景
            ctx.fillStyle = '#121212'
            ctx.fillRect(0, 0, canvas.width, canvas.height)
          }
        }

        drawBackground()
        
        // 更新和绘制网格线 - 放在后面绘制更少的线条
        gridLines.update()
        gridLines.draw(ctx, canvas.width, canvas.height)
        
        // 绘制星光效果
        stars.forEach(star => {
          star.update()
          star.draw(ctx)
        })
        
        // 绘制不规则形状
        irregularShapes.forEach(shape => {
          shape.update()
          shape.draw(ctx)
        })
        
        // 更新并绘制粒子
        particles.forEach(particle => {
          particle.update(canvas)
          particle.draw(ctx)
        })
        
        // 绘制粒子之间的连线 - 减少连线密度
        const drawConnections = () => {
          for (let i = 0; i < particles.length; i++) {
            // 每个粒子最多连接3条线
            let connections = 0
            
            for (let j = 0; j < particles.length; j++) {
              if (i === j) continue
              
              try {
                const dx = particles[i].x - particles[j].x
                const dy = particles[i].y - particles[j].y
                
                // 确保距离计算有效
                if (!isFinite(dx) || !isFinite(dy)) continue
                
                const distance = Math.sqrt(dx * dx + dy * dy)
                
                if (isFinite(distance) && distance < maxDistance) {
                  Connection.draw(ctx, particles[i], particles[j], distance, maxDistance)
                  connections++
                  
                  // 限制连线数量
                  if (connections >= 3) break
                }
              } catch (error) {
                console.error("连线计算错误:", error)
                continue
              }
            }
          }
        }
        
        // 更新并绘制数据流
        dataStreams.forEach(stream => {
          stream.update(canvas)
          stream.draw(ctx)
        })
        
        // 添加扫描线效果 - 修复可能的错误
        const scanLine = () => {
          try {
            const scanY = (animationTime / 50) % canvas.height
            
            // 确保坐标有效
            if (isFinite(scanY)) {
              const gradient = ctx.createLinearGradient(0, scanY - 10, 0, scanY + 10)
              gradient.addColorStop(0, 'rgba(255, 255, 255, 0)')
              gradient.addColorStop(0.5, 'rgba(255, 255, 255, 0.03)')
              gradient.addColorStop(1, 'rgba(255, 255, 255, 0)')
              
              ctx.fillStyle = gradient
              ctx.fillRect(0, scanY - 10, canvas.width, 20)
            }
          } catch (error) {
            console.error("扫描线效果错误:", error)
          }
        }
        
        // 使用安全版本的绘制连线函数
        drawConnections()
        
        // 使用安全版本的扫描线效果
        scanLine()
        
        // 更新动画时间
        animationTime += 16
        
        // 请求下一帧
        animationFrameId = requestAnimationFrame(animate)
      } catch (error) {
        console.error("动画循环错误:", error)
        // 出错时尝试重新启动动画
        animationFrameId = requestAnimationFrame(animate)
      }
    }
    
    // 开始动画
    animate()
    
    // 清理
    return () => {
      window.removeEventListener('resize', resizeCanvas)
      cancelAnimationFrame(animationFrameId)
    }
  }, [])

  return (
    <canvas
      ref={canvasRef}
      className="absolute inset-0 w-full h-full bg-gradient-to-br from-[#050505] to-[#101010]"
    />
  )
} 
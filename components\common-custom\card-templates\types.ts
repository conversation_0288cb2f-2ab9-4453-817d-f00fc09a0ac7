import { ReactNode } from "react"
import { LucideIcon } from "lucide-react"

/**
 * 统计卡片项属性
 */
export interface StatCardItem {
  /**
   * 卡片标题
   */
  title: string
  
  /**
   * 卡片图标
   */
  icon?: LucideIcon
  
  /**
   * 卡片数值
   */
  value: string | number
  
  /**
   * 变化值
   */
  change?: string
  
  /**
   * 变化趋势：上升或下降
   */
  trend?: "up" | "down" | "neutral"
  
  /**
   * 变化描述
   */
  description?: string
  
  /**
   * 自定义类名
   */
  className?: string
  
  /**
   * 卡片点击事件
   */
  onClick?: () => void
}

/**
 * 产品卡片项属性
 */
export interface ProductCardItem {
  /**
   * 产品ID
   */
  id: string | number
  
  /**
   * 产品名称
   */
  name: string
  
  /**
   * 产品描述
   */
  description?: string
  
  /**
   * 产品图片
   */
  image?: string
  
  /**
   * 产品价格
   */
  price?: number
  
  /**
   * 原价
   */
  originalPrice?: number
  
  /**
   * 折扣
   */
  discount?: number
  
  /**
   * 产品评分
   */
  rating?: number
  
  /**
   * 评价数量
   */
  reviewCount?: number
  
  /**
   * 产品标签
   */
  tags?: string[]
  
  /**
   * 产品状态
   */
  status?: "available" | "out-of-stock" | "discontinued"
  
  /**
   * 产品类别
   */
  category?: string
  
  /**
   * 产品品牌
   */
  brand?: string
  
  /**
   * 是否收藏
   */
  isFavorite?: boolean
  
  /**
   * 是否新品
   */
  isNew?: boolean
  
  /**
   * 是否热销
   */
  isHot?: boolean
  
  /**
   * 产品链接
   */
  href?: string
  
  /**
   * 点击事件
   */
  onClick?: () => void
  
  /**
   * 收藏事件
   */
  onFavorite?: () => void
  
  /**
   * 添加到购物车事件
   */
  onAddToCart?: () => void
  
  /**
   * 自定义类名
   */
  className?: string
}

/**
 * 用户卡片项属性
 */
export interface UserCardItem {
  /**
   * 用户ID
   */
  id: string | number
  
  /**
   * 用户名
   */
  name: string
  
  /**
   * 用户邮箱
   */
  email?: string
  
  /**
   * 用户头像
   */
  avatar?: string
  
  /**
   * 用户角色
   */
  role?: string
  
  /**
   * 用户部门
   */
  department?: string
  
  /**
   * 用户职位
   */
  position?: string
  
  /**
   * 用户状态
   */
  status?: "online" | "offline" | "away" | "busy"
  
  /**
   * 用户简介
   */
  bio?: string
  
  /**
   * 用户技能
   */
  skills?: string[]
  
  /**
   * 用户联系方式
   */
  contact?: {
    phone?: string
    email?: string
    address?: string
  }
  
  /**
   * 用户社交媒体
   */
  social?: {
    twitter?: string
    linkedin?: string
    github?: string
  }
  
  /**
   * 加入时间
   */
  joinDate?: string
  
  /**
   * 最后活跃时间
   */
  lastActive?: string
  
  /**
   * 用户链接
   */
  href?: string
  
  /**
   * 点击事件
   */
  onClick?: () => void
  
  /**
   * 发送消息事件
   */
  onMessage?: () => void
  
  /**
   * 关注事件
   */
  onFollow?: () => void
  
  /**
   * 自定义类名
   */
  className?: string
}

/**
 * 文章卡片项属性
 */
export interface ArticleCardItem {
  /**
   * 文章ID
   */
  id: string | number
  
  /**
   * 文章标题
   */
  title: string
  
  /**
   * 文章摘要
   */
  excerpt?: string
  
  /**
   * 文章内容
   */
  content?: string
  
  /**
   * 文章封面
   */
  cover?: string
  
  /**
   * 文章作者
   */
  author?: {
    name: string
    avatar?: string
    bio?: string
  }
  
  /**
   * 发布时间
   */
  publishDate?: string
  
  /**
   * 更新时间
   */
  updateDate?: string
  
  /**
   * 文章分类
   */
  category?: string
  
  /**
   * 文章标签
   */
  tags?: string[]
  
  /**
   * 阅读时间
   */
  readTime?: number
  
  /**
   * 阅读量
   */
  views?: number
  
  /**
   * 点赞数
   */
  likes?: number
  
  /**
   * 评论数
   */
  comments?: number
  
  /**
   * 文章状态
   */
  status?: "published" | "draft" | "archived"
  
  /**
   * 是否置顶
   */
  isPinned?: boolean
  
  /**
   * 是否推荐
   */
  isFeatured?: boolean
  
  /**
   * 文章链接
   */
  href?: string
  
  /**
   * 点击事件
   */
  onClick?: () => void
  
  /**
   * 点赞事件
   */
  onLike?: () => void
  
  /**
   * 分享事件
   */
  onShare?: () => void
  
  /**
   * 自定义类名
   */
  className?: string
}

/**
 * 通知卡片项属性
 */
export interface NotificationCardItem {
  /**
   * 通知ID
   */
  id: string | number
  
  /**
   * 通知标题
   */
  title: string
  
  /**
   * 通知内容
   */
  message: string
  
  /**
   * 通知类型
   */
  type?: "info" | "success" | "warning" | "error"
  
  /**
   * 通知图标
   */
  icon?: LucideIcon
  
  /**
   * 通知时间
   */
  time: string
  
  /**
   * 是否已读
   */
  read?: boolean
  
  /**
   * 通知来源
   */
  source?: string
  
  /**
   * 通知链接
   */
  href?: string
  
  /**
   * 点击事件
   */
  onClick?: () => void
  
  /**
   * 标记已读事件
   */
  onMarkRead?: () => void
  
  /**
   * 删除事件
   */
  onDelete?: () => void
  
  /**
   * 自定义类名
   */
  className?: string
}

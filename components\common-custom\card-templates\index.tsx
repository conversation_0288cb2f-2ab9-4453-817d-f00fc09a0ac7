"use client"

/**
 * 卡片模板组件集合
 *
 * 按照新规范重新导出所有卡片模板组件和类型
 */

// ============================================================================
// 基础卡片组件导出
// ============================================================================

export { StatCard } from "./stat-card"
export { ProductCard } from "./product-card"
export { UserCard } from "./user-card"
export { InfoCard } from "./info-card"

// ============================================================================
// 模板卡片组件导出
// ============================================================================

export { BlogCard } from "./blog-card"
export { ProductCardTemplate } from "./product-card-template"
export { EventCard } from "./event-card"
export { DashboardCard } from "./dashboard-card"
export { TestimonialCard } from "./testimonial-card"

// ============================================================================
// 类型导出
// ============================================================================

// 从本地types文件导出所有类型（需要创建）
// export * from "./types"

// 从本地types文件导出所有类型
export * from "./types"

// ============================================================================
// 常量导出
// ============================================================================

/**
 * 支持的卡片尺寸
 */
export const CARD_SIZES = ['sm', 'md', 'lg', 'xl'] as const

/**
 * 支持的卡片变体
 */
export const CARD_VARIANTS = ['default', 'outline', 'filled', 'ghost'] as const

/**
 * 支持的卡片布局
 */
export const CARD_LAYOUTS = ['vertical', 'horizontal', 'grid'] as const

/**
 * 支持的统计卡片趋势
 */
export const STAT_CARD_TRENDS = ['up', 'down', 'neutral'] as const

/**
 * 支持的产品卡片状态
 */
export const PRODUCT_CARD_STATUSES = ['available', 'sold', 'pending', 'draft'] as const

/**
 * 支持的用户卡片状态
 */
export const USER_CARD_STATUSES = ['online', 'offline', 'away', 'busy'] as const

/**
 * 默认卡片配置
 */
export const DEFAULT_CARD_CONFIG = {
  size: 'md' as const,
  variant: 'default' as const,
  layout: 'vertical' as const,
  hoverable: true,
  clickable: false,
} as const

/**
 * 默认统计卡片配置
 */
export const DEFAULT_STAT_CARD_CONFIG = {
  showTrend: true,
  showIcon: true,
  showChange: true,
  trend: 'neutral' as const,
} as const

/**
 * 默认产品卡片配置
 */
export const DEFAULT_PRODUCT_CARD_CONFIG = {
  showPrice: true,
  showRating: true,
  showBadge: true,
  showActions: true,
  status: 'available' as const,
} as const

/**
 * 默认用户卡片配置
 */
export const DEFAULT_USER_CARD_CONFIG = {
  showStatus: true,
  showRole: true,
  showActions: true,
  showAvatar: true,
  status: 'offline' as const,
} as const

/**
 * 常用卡片阴影配置
 */
export const CARD_SHADOWS = {
  none: 'shadow-none',
  sm: 'shadow-sm',
  md: 'shadow-md',
  lg: 'shadow-lg',
  xl: 'shadow-xl',
} as const

/**
 * 常用卡片圆角配置
 */
export const CARD_RADIUSES = {
  none: 'rounded-none',
  sm: 'rounded-sm',
  md: 'rounded-md',
  lg: 'rounded-lg',
  xl: 'rounded-xl',
  full: 'rounded-full',
} as const
"use client"

import { useState } from "react"
import { NotificationConfigList } from "@/components/pages/noticeconfig/notification-config-list"
import { NotificationConfigForm } from "@/components/pages/noticeconfig/notification-config-form"
import { Button } from "@/components/ui/button"
import { Plus } from "lucide-react"
import type { NotificationConfig } from "@/types/notification"

export default function NotificationConfigPage() {
  const [editingConfig, setEditingConfig] = useState<NotificationConfig | null>(null)
  const [showForm, setShowForm] = useState(false)

  const handleCreateNew = () => {
    setEditingConfig(null)
    setShowForm(true)
  }

  const handleEdit = (config: NotificationConfig) => {
    setEditingConfig(config)
    setShowForm(true)
  }

  const handleFormClose = () => {
    setShowForm(false)
    setEditingConfig(null)
  }

  if (showForm) {
    return (
      <div className="container mx-auto p-6 max-w-7xl">
        <NotificationConfigForm config={editingConfig} onClose={handleFormClose} />
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 max-w-7xl space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">通知配置管理</h1>
          <p className="text-muted-foreground mt-2">管理短信、邮件和钉钉通知的配置，支持模板变量</p>
        </div>
        <div>
          <Button onClick={handleCreateNew} className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            新建配置
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <NotificationConfigList onEdit={handleEdit} />
    </div>
  )
}

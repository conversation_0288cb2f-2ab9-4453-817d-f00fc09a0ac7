---
description:
globs:
alwaysApply: true
---
# Next.js 组件和路由规则

## Server Components 与 Client Components
- 默认所有组件都是 Server Components
- 需要使用浏览器API、状态或事件处理的组件，使用 "use client" 指令标记为 Client Components
- Client Components 应该尽可能在组件树的叶子节点使用，避免将整个组件树标记为客户端组件

## 应用路由结构
- 使用 App Router 文件系统路由
- 根路由组件在 `app/page.tsx`
- 嵌套路由在 `app/<route>/page.tsx`
- 动态路由使用 `[param]` 语法，如 `app/users/[id]/page.tsx`
- 全局布局在 `app/layout.tsx`
- 嵌套布局在 `app/<route>/layout.tsx`
- 错误处理在 `app/error.tsx` 或嵌套路由的 `app/<route>/error.tsx`
- 加载状态在 `app/loading.tsx` 或嵌套路由的 `app/<route>/loading.tsx`

## 数据获取
- 优先使用 Server Components 中的异步/等待数据获取
- 使用 fetch 配合 Next.js 自动请求去重和缓存
- 对于需要服务端权限的数据访问，使用 Server Actions
- 客户端状态管理使用 React 的状态钩子或第三方库

## Meta 数据
- 页面元数据在 `app/<route>/page.tsx` 中使用 `generateMetadata` 函数
- 静态元数据在 `metadata` 对象中定义
- 动态元数据使用 `generateMetadata` 函数

## 静态与动态渲染
- 默认情况下页面会尽可能静态渲染
- 使用动态函数（如 cookies(), headers()）或动态 fetch() 会导致动态渲染
- 使用 `export const dynamic = 'force-dynamic'` 强制动态渲染
- 使用 `export const dynamic = 'force-static'` 强制静态渲染

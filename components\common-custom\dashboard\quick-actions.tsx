"use client"

import { ReactNode } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, Card<PERSON>itle, CardDescription } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { QuickActionItem } from "@/types/dashboard-templates"

/**
 * 快捷操作组件
 */
export function QuickActions({ 
  actions, 
  title, 
  description 
}: { 
  actions: QuickActionItem[], 
  title?: ReactNode, 
  description?: ReactNode 
}) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{title || "快捷操作"}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-3">
          {actions.map((action, index) => (
            <Button
              key={index}
              variant="outline"
              className="h-auto p-4 flex flex-col items-center gap-2 cursor-pointer hover:bg-muted/50"
              onClick={action.onClick}
            >
              <div className={`w-8 h-8 rounded-full ${action.color || "bg-primary"} flex items-center justify-center`}>
                <action.icon className="h-4 w-4 text-white" />
              </div>
              <span className="text-sm">{action.label}</span>
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  )
} 
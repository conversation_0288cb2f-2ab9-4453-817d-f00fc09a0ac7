"use client"

import { useEffect, useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, DialogHeader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Copy, Star, Globe, Eye, EyeOff } from "lucide-react"
import {useToast} from "@/components/ui/use-toast";
import { getPasswordDetailRequest, passwordConverter } from "@/services/api/passwordVaultRequestApi"
import { UIPasswordItem } from "@/types/passwordVault"

type PasswordDetailDialogProps = {
  passwordId: string
  open: boolean
  onOpenChange: (open: boolean) => void
  onDelete?: () => void
}

export function PasswordDetailDialog({ passwordId, open, onOpenChange }: PasswordDetailDialogProps) {
  const [showPassword, setShowPassword] = useState(false)
  const [password, setPassword] = useState<UIPasswordItem | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { toast } = useToast()

  useEffect(() => {
    const fetchPasswordDetail = async () => {
      if (!open || !passwordId) return;
      
      try {
        setLoading(true);
        setError(null);
        
        const response = await getPasswordDetailRequest(parseInt(passwordId, 10));
        
        if (response) {
          setPassword(passwordConverter.toUIFormat(response));
        } else {
          setError("未找到密码详情");
          toast({
            title: "获取失败",
            description: "未找到密码详情",
            variant: "destructive"
          });
        }
      } catch (err) {
        console.error("获取密码详情失败:", err);
        setError("获取密码详情失败，请稍后重试");
        toast({
          title: "获取失败",
          description: "无法加载密码详情，请稍后重试",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };
    
    fetchPasswordDetail();
  }, [passwordId, open]);

  const handleCopyPassword = () => {
    if (!password) return;
    
    try {
      // 使用document.execCommand作为备选方案
      const textArea = document.createElement("textarea");
      textArea.value = password.password;
      document.body.appendChild(textArea);
      textArea.select();
      const success = document.execCommand('copy');
      document.body.removeChild(textArea);
      
      if (success) {
        toast({
          title: "已复制",
          description: "密码已复制到剪贴板",
          duration: 2000,
        });
      } else {
        throw new Error("复制失败");
      }
    } catch (error) {
      console.error('复制失败:', error);
      toast({
        title: "复制失败",
        description: "无法复制密码到剪贴板，请手动选择并复制",
        variant: "destructive"
      });
    }
  }

  const handleCopyAccount = () => {
    if (!password) return;
    
    try {
      // 使用document.execCommand作为备选方案
      const textArea = document.createElement("textarea");
      textArea.value = password.account;
      document.body.appendChild(textArea);
      textArea.select();
      const success = document.execCommand('copy');
      document.body.removeChild(textArea);
      
      if (success) {
        toast({
          title: "已复制",
          description: "账号已复制到剪贴板",
          duration: 2000,
        });
      } else {
        throw new Error("复制失败");
      }
    } catch (error) {
      console.error('复制失败:', error);
      toast({
        title: "复制失败",
        description: "无法复制账号到剪贴板，请手动选择并复制",
        variant: "destructive"
      });
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("zh-CN", {
      year: "numeric",
      month: "long",
      day: "numeric",
    })
  }
  
  if (loading) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>加载中...</DialogTitle>
          </DialogHeader>
        </DialogContent>
      </Dialog>
    );
  }
  
  if (error || !password) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>出错了</DialogTitle>
          </DialogHeader>
          <p className="text-destructive">{error || "未找到密码详情"}</p>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <span className="text-2xl mr-2">{password.icon}</span>
            {password.name}
            {password.isFavorite && (
              <Star className="ml-2 h-4 w-4 text-yellow-400 fill-yellow-400" />
            )}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4 mt-2">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">账号</p>
              <div className="flex items-center justify-between bg-muted p-2 rounded-md">
                <p className="text-sm font-medium truncate">{password.account}</p>
                <Button variant="ghost" size="icon" onClick={handleCopyAccount} className="h-6 w-6">
                  <Copy className="h-3 w-3" />
                </Button>
              </div>
            </div>

            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">密码</p>
              <div className="flex items-center justify-between bg-muted p-2 rounded-md">
                <p className="text-sm font-mono truncate">{showPassword ? password.password : "••••••••••••"}</p>
                <div className="flex items-center">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setShowPassword(!showPassword)}
                    className="h-6 w-6"
                  >
                    {showPassword ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
                  </Button>
                  <Button variant="ghost" size="icon" onClick={handleCopyPassword} className="h-6 w-6">
                    <Copy className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </div>
          </div>

          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">平台</p>
            <div className="flex items-center justify-between bg-muted p-2 rounded-md">
              <div className="flex items-center gap-2">
                <Globe className="h-4 w-4 text-muted-foreground" />
                <p className="text-sm truncate">{password.platform || "未指定"}</p>
              </div>
            </div>
          </div>

          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">标签</p>
            <div className="flex flex-wrap gap-1">
              {password.tags && password.tags.length > 0 ? (
                password.tags.map((tag, index) => (
                  <Badge key={index} variant="outline">
                    {tag}
                  </Badge>
                ))
              ) : (
                <p className="text-sm text-muted-foreground">无标签</p>
              )}
            </div>
          </div>

          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">密码强度</p>
            <Badge
              variant={
                password.password_strength === 3
                  ? "default"
                  : password.password_strength === 2
                    ? "secondary"
                    : "destructive"
              }
            >
              {password.password_strength === 3 ? "强" : password.password_strength === 2 ? "中" : "弱"}
            </Badge>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">创建日期</p>
              <p className="text-sm">{formatDate(password.createdAt)}</p>
            </div>

            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">最后更新</p>
              <p className="text-sm">{formatDate(password.lastUpdated)}</p>
            </div>
          </div>

          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">备注</p>
            <div className="bg-muted p-2 rounded-md">
              <p className="text-sm">{password.notes || "无备注"}</p>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
} 

/**
 * 统计组件示例代码集合
 * 
 * 按照新规范管理示例代码，避免在页面文件中硬编码
 */

import React from "react"
import {
  StatsCard,
  KpiStatsCard,
  TrafficSourcesList,
  PerformanceMetricsGroup,
  SalesDataList,
  SalesOverview,
  RealtimeStatsCard,
  StatsContainer
} from "@/components/common-custom/stats"
import {
  Users,
  DollarSign,
  Target,
  Clock,
  Activity,
  Eye,
  MousePointer,
  BarChart3,
  PieChart,
  LineChart,
  ShoppingCart,
  TrendingUp,
  TrendingDown,
} from "lucide-react"

// ============================================================================
// 基础统计卡片示例
// ============================================================================

export const basicStatsCardExample = {
  id: "basic-stats-card",
  title: "基础统计卡片",
  description: "展示基础的统计数据卡片，包含数值、变化趋势和图标",
  code: `
import React from "react";
import { StatsCard } from "@/components/common-custom/stats";
import { Users, DollarSign, ShoppingCart, TrendingUp } from "lucide-react";

function BasicStatsCard() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 max-w-4xl">
      <StatsCard
        title="总销售额"
        value="¥128,430"
        change="+12.5%"
        trend="up"
        icon={DollarSign}
        description="较上月"
      />
      
      <StatsCard
        title="新增用户"
        value="2,340"
        change="+18.2%"
        trend="up"
        icon={Users}
        description="本周"
      />
      
      <StatsCard
        title="订单数量"
        value="1,240"
        change="-4.5%"
        trend="down"
        icon={ShoppingCart}
        description="昨日"
      />
      
      <StatsCard
        title="转化率"
        value="3.24%"
        change="+2.1%"
        trend="up"
        icon={TrendingUp}
        description="本周"
      />
    </div>
  );
}

render(<BasicStatsCard />);
  `,
  scope: { StatsCard, Users, DollarSign, ShoppingCart, TrendingUp, React },
}

// ============================================================================
// KPI 统计卡片示例
// ============================================================================

export const kpiStatsCardExample = {
  id: "kpi-stats-card",
  title: "KPI 统计卡片",
  description: "展示关键绩效指标的统计卡片，包含目标值和完成度",
  code: `
import React from "react";
import { KpiStatsCard } from "@/components/common-custom/stats";
import { Target, Activity, Eye } from "lucide-react";

function KpiStatsCardExample() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-3xl">
      <KpiStatsCard
        title="月度目标"
        value="85%"
        target="100%"
        progress={85}
        icon={Target}
        status="good"
        description="本月完成度"
      />
      
      <KpiStatsCard
        title="活跃用户"
        value="12,450"
        target="15,000"
        progress={83}
        icon={Activity}
        status="warning"
        description="日活跃用户"
      />
      
      <KpiStatsCard
        title="页面浏览"
        value="45,230"
        target="40,000"
        progress={113}
        icon={Eye}
        status="excellent"
        description="今日 PV"
      />
    </div>
  );
}

render(<KpiStatsCardExample />);
  `,
  scope: { KpiStatsCard, Target, Activity, Eye, React },
}

// ============================================================================
// 实时统计卡片示例
// ============================================================================

export const realtimeStatsCardExample = {
  id: "realtime-stats-card",
  title: "实时统计卡片",
  description: "展示实时更新的统计数据，包含动态效果",
  code: `
import React from "react";
import { RealtimeStatsCard } from "@/components/common-custom/stats";
import { Users, MousePointer, Clock } from "lucide-react";

function RealtimeStatsCardExample() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-3xl">
      <RealtimeStatsCard
        title="在线用户"
        value="1,234"
        icon={Users}
        isRealtime={true}
        updateInterval={5000}
        description="当前在线"
      />
      
      <RealtimeStatsCard
        title="实时点击"
        value="5,678"
        icon={MousePointer}
        isRealtime={true}
        updateInterval={3000}
        description="每分钟点击"
      />
      
      <RealtimeStatsCard
        title="响应时间"
        value="245ms"
        icon={Clock}
        isRealtime={true}
        updateInterval={2000}
        description="平均响应时间"
      />
    </div>
  );
}

render(<RealtimeStatsCardExample />);
  `,
  scope: { RealtimeStatsCard, Users, MousePointer, Clock, React },
}

// ============================================================================
// 统一导出所有示例
// ============================================================================

export const allExamples = [
  basicStatsCardExample,
  kpiStatsCardExample,
  realtimeStatsCardExample,
]

// ============================================================================
// 按类别导出示例
// ============================================================================

export const basicExamples = [basicStatsCardExample]
export const advancedExamples = [kpiStatsCardExample, realtimeStatsCardExample]

// ============================================================================
// 示例元数据
// ============================================================================

export const exampleMetadata = {
  total: allExamples.length,
  categories: {
    basic: basicExamples.length,
    advanced: advancedExamples.length,
  },
  tags: ["stats", "statistics", "kpi", "dashboard", "realtime", "metrics"],
  lastUpdated: "2024-01-01",
  description: "统计组件示例集合，展示了多种数据统计和展示场景的实现方式",
}

/**
 * 角色管理接口
 * 提供角色相关的API调用功能
 */
import apiClient from './requestApi';
import { ApiResponse, RolePaginationParams, PageResult } from '@/types/api';
import { Role } from '@/types/models';
import { toast } from "sonner";
import { processArrayResponse, processResponse } from '@/lib/request';

// API基础路径
const BASE_URL = '/api/auth/role';

/**
 * 分页查询角色
 * @param params 查询参数
 * @returns 角色分页数据
 */
export const getRolesPageRequest = async (params: RolePaginationParams): Promise<PageResult<Role>> => {
  try {
    const response = await apiClient.post<ApiResponse<PageResult<Role>>>(`${BASE_URL}/page`, params);
    
    return processResponse(response, {
      errorMessage: "获取角色列表失败",
      showErrorToast: true
    }) || {
      total: 0,
      list: [],
      pageNum: params.pageNum,
      pageSize: params.pageSize,
      pages: 0
    };
  } catch (error) {
    console.error("获取角色列表失败", error);
    toast.error("获取角色列表失败");
    return {
      total: 0,
      list: [],
      pageNum: params.pageNum,
      pageSize: params.pageSize,
      pages: 0
    };
  }
};

/**
 * 获取所有角色列表
 * @param params 查询参数
 * @returns 角色列表
 */
export const getAllRolesRequest = async (params?: {
  roleName?: string;
  roleCode?: string;
  teamCode?: string;
  status?: number;
}) => {
  try {
    const response = await apiClient.post<ApiResponse<Role[]>>(`${BASE_URL}/listAll`, params || {});
    
    return processArrayResponse(response, {
      errorMessage: "获取角色列表失败",
      showErrorToast: true
    });
  } catch (error) {
    console.error("获取角色列表失败", error);
    toast.error("获取角色列表失败");
    return [];
  }
};

/**
 * 根据ID获取角色详情
 * @param id 角色ID
 * @returns 角色详情
 * @example
 * 返回数据示例:
 * {
 *   "id": 1,
 *   "teamCode": "team_001",
 *   "teamName": "管理团队",
 *   "roleName": "系统管理员",
 *   "roleCode": "admin",
 *   "description": "系统管理员角色",
 *   "status": 1,
 *   "createTime": "2023-01-01T00:00:00",
 *   "permissionGroups": [
 *     { "id": "system", "code": "system", "name": "系统管理", "icon": "settings", "order": 1 },
 *     { "id": "project", "code": "project", "name": "项目管理", "icon": "folder", "order": 2 }
 *   ],
 *   "permissionTrees": {
 *     "system": [
 *       {
 *         "id": "system_settings",
 *         "name": "系统设置",
 *         "type": "Directory",
 *         "icon": "settings",
 *         "children": [
 *           {
 *             "id": "user_management",
 *             "name": "用户管理",
 *             "type": "Menu",
 *             "permissionId": "user:view",
 *             "icon": "users",
 *             "children": [
 *               {
 *                 "id": "user_add",
 *                 "name": "新增用户",
 *                 "type": "Button",
 *                 "permissionId": "user:add",
 *                 "children": []
 *               }
 *             ]
 *           }
 *         ]
 *       }
 *     ],
 *     "project": [
 *       {
 *         "id": "project_management",
 *         "name": "项目管理",
 *         "type": "Directory",
 *         "icon": "folder",
 *         "children": []
 *       }
 *     ]
 *   },
 *   "selectedPermissions": ["user:view", "user:add"]
 * }
 */
export const getRoleByIdRequest = async (id: string): Promise<Role | null> => {
  try {
    const response = await apiClient.get<ApiResponse<Role>>(`${BASE_URL}/getById?id=${id}`);
    
    return processResponse(response, {
      errorMessage: "获取角色详情失败",
      showErrorToast: true
    });
  } catch (error) {
    console.error("获取角色详情失败", error);
    toast.error("获取角色详情失败");
    return null;
  }
};

/**
 * 根据编码获取角色详情
 * @param roleCode 角色编码
 * @returns 角色详情
 */
export const getRoleByCodeRequest = async (roleCode: string): Promise<Role | null> => {
  try {
    const response = await apiClient.get<ApiResponse<Role>>(`${BASE_URL}/getByCode?roleCode=${roleCode}`);
    
    return processResponse(response, {
      errorMessage: "获取角色详情失败",
      showErrorToast: true
    });
  } catch (error) {
    console.error("获取角色详情失败", error);
    toast.error("获取角色详情失败");
    return null;
  }
};

/**
 * 创建角色
 * @param roleData 角色数据
 * @returns 创建结果
 */
export const createRoleRequest = async (roleData: {
  teamCode: string;
  roleName: string;
  roleCode: string;
  description?: string;
  status?: number;
}): Promise<number | null> => {
  try {
    const response = await apiClient.post<ApiResponse<number>>(`${BASE_URL}/create`, roleData);
    
    return processResponse(response, {
      successMessage: "角色创建成功",
      errorMessage: "角色创建失败",
      showSuccessToast: true,
      showErrorToast: true
    });
  } catch (error) {
    console.error("创建角色失败", error);
    toast.error("创建角色失败");
    return null;
  }
};

/**
 * 更新角色
 * @param roleData 角色数据
 * @returns 更新结果
 */
export const updateRoleRequest = async (roleData: {
  id: number;
  teamCode: string;
  roleName: string;
  roleCode: string;
  description?: string;
  status?: number;
  permissionCodes?: string[];
}): Promise<boolean> => {
  try {
    const response = await apiClient.put<ApiResponse<boolean>>(`${BASE_URL}/update`, roleData);
    
    return processResponse(response, {
      successMessage: "角色更新成功",
      errorMessage: "角色更新失败",
      showSuccessToast: true,
      showErrorToast: true
    }) || false;
  } catch (error) {
    console.error("更新角色失败", error);
    toast.error("更新角色失败");
    return false;
  }
};

/**
 * 删除角色
 * @param id 角色ID
 * @returns 删除结果
 */
export const deleteRoleRequest = async (id: string): Promise<boolean> => {
  try {
    const response = await apiClient.delete<ApiResponse<boolean>>(`${BASE_URL}/delete?id=${id}`);
    
    return processResponse(response, {
      successMessage: "角色删除成功",
      errorMessage: "角色删除失败",
      showSuccessToast: true,
      showErrorToast: true
    }) || false;
  } catch (error) {
    console.error("删除角色失败", error);
    toast.error("删除角色失败");
    return false;
  }
};

/**
 * 获取团队下的角色选项
 * @param teamCode 团队编码
 * @returns 角色选项列表
 */
export const getRoleOptionsRequest = async (teamCode: string): Promise<Array<{label: string, value: string}>> => {
  try {
    const response = await apiClient.get<ApiResponse<Array<{label: string, value: string}>>>(
      `${BASE_URL}/options?teamCode=${teamCode}`
    );
    
    return processArrayResponse(response, {
      errorMessage: "获取角色选项失败",
      showErrorToast: true
    });
  } catch (error) {
    console.error("获取角色选项失败", error);
    toast.error("获取角色选项失败");
    return [];
  }
};

/**
 * 为角色分配权限
 * @param roleCode 角色编码
 * @param permissionCodes 权限编码列表
 * @returns 分配结果
 */
export const assignPermissionsToRoleRequest = async (
  roleCode: string,
  permissionCodes: string[]
): Promise<boolean> => {
  try {
    const response = await apiClient.post<ApiResponse<boolean>>(
      `${BASE_URL}/assignPermissions`,
      { roleCode, permissionCodes }
    );
    
    return processResponse(response, {
      successMessage: "权限分配成功",
      errorMessage: "权限分配失败",
      showSuccessToast: true,
      showErrorToast: true
    }) || false;
  } catch (error) {
    console.error("分配权限失败", error);
    toast.error("分配权限失败");
    return false;
  }
};

/**
 * 获取角色的权限编码列表
 * @param roleCode 角色编码
 * @returns 权限编码列表
 */
export const getRolePermissionsRequest = async (roleCode: string): Promise<string[]> => {
  try {
    const response = await apiClient.get<ApiResponse<string[]>>(
      `${BASE_URL}/permissions?roleCode=${roleCode}`
    );
    
    return processArrayResponse(response, {
      errorMessage: "获取角色权限失败",
      showErrorToast: true
    });
  } catch (error) {
    console.error("获取角色权限失败", error);
    toast.error("获取角色权限失败");
    return [];
  }
};

/**
 * 获取角色关联的用户列表
 * @param roleId 角色ID
 * @returns 用户列表
 */
export const getRoleUsersRequest = async (roleId: string): Promise<any[]> => {
  // 临时实现，返回空数据
  console.log(`获取角色 ${roleId} 的用户列表`);
  return [];
};

/**
 * 获取角色名称
 * @param roleId 角色ID
 * @returns 角色名称
 */
export const getRoleNameById = async (roleId: string): Promise<string> => {
  const role = await getRoleByIdRequest(roleId);
  return role?.roleName || '未知角色';
};

/**
 * 获取角色名称 todo 不对，待修改
 * @param roleId 角色ID
 * @returns 角色名称
 */
export const removeUserFromRoleRequest = async (roleId: string): Promise<string> => {
  const role = await getRoleByIdRequest(roleId);
  return role?.roleName || '未知角色';
};
"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import type { NotificationType, ContentFormat } from "@/types/notification"

interface ContentPreviewProps {
  type: NotificationType
  content?: string
  subject?: string
  format?: ContentFormat
  variables?: Record<string, string>
}

export function ContentPreview({
  type,
  content = "",
  subject = "",
  format = "text",
  variables = {},
}: ContentPreviewProps) {
  const replaceVariables = (text: string) => {
    let result = text
    Object.entries(variables).forEach(([key, value]) => {
      result = result.replace(new RegExp(`\\$\\{${key}\\}`, "g"), value)
    })
    return result
  }

  // 清理和限制HTML内容，防止影响外部样式
  const sanitizeHtml = (html: string) => {
    // 移除可能影响布局的危险标签和属性
    const cleanHtml = html
      .replace(/<script[^>]*>.*?<\/script>/gi, "") // 移除script标签
      .replace(/<style[^>]*>.*?<\/style>/gi, "") // 移除style标签
      .replace(/on\w+="[^"]*"/gi, "") // 移除事件处理器
      .replace(/javascript:/gi, "") // 移除javascript协议
      .replace(/<link[^>]*>/gi, "") // 移除link标签
      .replace(/<meta[^>]*>/gi, "") // 移除meta标签
      .replace(/style\s*=\s*"[^"]*"/gi, "") // 移除内联样式
      .replace(/<iframe[^>]*>.*?<\/iframe>/gi, "") // 移除iframe
      .replace(/<object[^>]*>.*?<\/object>/gi, "") // 移除object
      .replace(/<embed[^>]*>/gi, "") // 移除embed

    return cleanHtml
  }

  const renderContent = (text: string) => {
    const processedContent = replaceVariables(text)

    if (format === "html") {
      const cleanHtml = sanitizeHtml(processedContent)
      return (
        <div
          className="preview-content prose prose-sm max-w-none"
          dangerouslySetInnerHTML={{ __html: cleanHtml }}
          style={{
            // 确保内容不会溢出预览区域
            wordBreak: "break-word",
            overflowWrap: "break-word",
            maxWidth: "100%",
          }}
        />
      )
    }

    if (format === "markdown") {
      // 简单的markdown渲染，更安全
      const htmlContent = processedContent
        .replace(/^# (.*$)/gim, "<h1 class='text-lg font-bold mb-2'>$1</h1>")
        .replace(/^## (.*$)/gim, "<h2 class='text-base font-semibold mb-2'>$1</h2>")
        .replace(/^### (.*$)/gim, "<h3 class='text-sm font-medium mb-1'>$1</h3>")
        .replace(/\*\*(.*?)\*\*/gim, "<strong class='font-semibold'>$1</strong>")
        .replace(/\*(.*?)\*/gim, "<em class='italic'>$1</em>")
        .replace(/`(.*?)`/gim, "<code class='bg-gray-100 px-1 rounded text-sm'>$1</code>")
        .replace(/\n\n/gim, "</p><p class='mb-2'>")
        .replace(/\n/gim, "<br>")

      return (
        <div
          className="preview-content"
          dangerouslySetInnerHTML={{ __html: `<p class='mb-2'>${htmlContent}</p>` }}
          style={{
            wordBreak: "break-word",
            overflowWrap: "break-word",
            maxWidth: "100%",
          }}
        />
      )
    }

    return (
      <pre
        className="whitespace-pre-wrap text-sm leading-relaxed font-mono"
        style={{
          wordBreak: "break-word",
          overflowWrap: "break-word",
          maxWidth: "100%",
          fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace',
        }}
      >
        {processedContent}
      </pre>
    )
  }

  const getTypeIcon = () => {
    switch (type) {
      case "sms":
        return "📱"
      case "email":
        return "📧"
      case "dingtalk":
        return "💬"
    }
  }

  const getTypeName = () => {
    switch (type) {
      case "sms":
        return "短信"
      case "email":
        return "邮件"
      case "dingtalk":
        return "钉钉"
    }
  }

  const getFormatName = () => {
    switch (format) {
      case "text":
        return "纯文本"
      case "html":
        return "HTML"
      case "markdown":
        return "Markdown"
    }
  }

  return (
    <Card className="h-fit">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <span>{getTypeIcon()}</span>
          实时预览
        </CardTitle>
        <CardDescription className="flex items-center gap-2">
          <Badge variant="outline">{getTypeName()}</Badge>
          <Badge variant="secondary">{getFormatName()}</Badge>
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Email Subject Preview */}
        {type === "email" && subject && (
          <>
            <div>
              <h4 className="text-sm font-medium mb-2">邮件主题</h4>
              <div className="border rounded-lg p-3 bg-muted/20">
                <div
                  className="text-sm font-medium"
                  style={{
                    wordBreak: "break-word",
                    overflowWrap: "break-word",
                  }}
                >
                  {replaceVariables(subject)}
                </div>
              </div>
            </div>
            <Separator />
          </>
        )}

        {/* Content Preview */}
        <div>
          <h4 className="text-sm font-medium mb-2">
            {type === "email" ? "邮件内容" : type === "sms" ? "短信内容" : "消息内容"}
          </h4>
          <div className="border rounded-lg bg-muted/20 relative">
            <ScrollArea className="h-[300px] w-full">
              <div className="p-4">
                {content ? (
                  <div className="preview-container">{renderContent(content)}</div>
                ) : (
                  <div className="text-muted-foreground text-center py-8">
                    <div className="text-sm">暂无内容预览</div>
                    <div className="text-xs mt-1">在左侧输入内容后将实时显示预览效果</div>
                  </div>
                )}
              </div>
            </ScrollArea>

            {/* Format Warning for HTML */}
            {format === "html" && content && (
              <div className="absolute top-2 right-2">
                <Badge variant="outline" className="text-xs bg-white/80 backdrop-blur-sm">
                  HTML预览
                </Badge>
              </div>
            )}
          </div>
        </div>

        {/* Content Length Info */}
        {content && (
          <div className="text-xs text-muted-foreground space-y-1">
            {type === "sms" && (
              <div className="flex justify-between">
                <span>字符数: {replaceVariables(content).length}</span>
                {replaceVariables(content).length > 70 && <span className="text-orange-500">建议控制在70字符以内</span>}
              </div>
            )}
            {type === "email" && (
              <div className="flex justify-between">
                <span>内容长度: {replaceVariables(content).length} 字符</span>
                {format === "html" && <span className="text-blue-500">HTML内容已安全处理</span>}
              </div>
            )}
            {type === "dingtalk" && (
              <div className="flex justify-between">
                <span>消息长度: {replaceVariables(content).length} 字符</span>
                {replaceVariables(content).length > 20000 ? (
                  <span className="text-red-500">超出钉钉消息长度限制</span>
                ) : replaceVariables(content).length > 15000 ? (
                  <span className="text-orange-500">接近长度限制</span>
                ) : null}
              </div>
            )}
          </div>
        )}

        {/* Security Notice for HTML */}
        {format === "html" && content && (
          <div className="text-xs text-muted-foreground bg-blue-50 p-2 rounded border-l-2 border-blue-200">
            <div className="font-medium text-blue-700 mb-1">安全提示</div>
            <div>HTML内容已自动清理，移除了可能影响页面的脚本、样式和危险标签</div>
          </div>
        )}
      </CardContent>

      <style jsx>{`
        .preview-container {
          /* 创建独立的样式作用域 */
          isolation: isolate;
          contain: layout style;
        }
        
        .preview-content {
          /* 限制内容样式的影响范围 */
          all: initial;
          font-family: inherit;
          color: inherit;
          line-height: 1.5;
          
          /* 确保内容不会溢出 */
          max-width: 100%;
          overflow-wrap: break-word;
          word-break: break-word;
        }
        
        .preview-content * {
          /* 重置所有子元素的样式 */
          max-width: 100% !important;
          box-sizing: border-box !important;
        }
        
        .preview-content h1,
        .preview-content h2,
        .preview-content h3,
        .preview-content h4,
        .preview-content h5,
        .preview-content h6 {
          margin: 0.5em 0 !important;
          font-weight: 600 !important;
        }
        
        .preview-content p {
          margin: 0.5em 0 !important;
        }
        
        .preview-content img {
          max-width: 100% !important;
          height: auto !important;
        }
        
        .preview-content table {
          width: 100% !important;
          border-collapse: collapse !important;
          margin: 0.5em 0 !important;
        }
        
        .preview-content td,
        .preview-content th {
          padding: 0.25em 0.5em !important;
          border: 1px solid #e5e7eb !important;
        }
        
        /* 防止绝对定位元素逃出预览区域 */
        .preview-content *[style*="position: absolute"],
        .preview-content *[style*="position: fixed"] {
          position: relative !important;
        }
        
        /* 防止z-index影响外部元素 */
        .preview-content * {
          z-index: auto !important;
        }
      `}</style>
    </Card>
  )
}

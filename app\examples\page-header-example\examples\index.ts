import { PageHeader } from "@/components/common-custom/page-header"
import { <PERSON><PERSON><PERSON>, Bell, HelpCircle, User, LogOut } from "lucide-react"
import React from "react"

// 标准布局示例
export const standardExample = {
  id: "standard-header",
  title: "标准布局",
  description: "包含Logo、导航、搜索、操作和用户区域的完整页头",
  code: `
import React from "react";
import { PageHeader } from "@/components/common-custom/page-header";
import { Settings, Bell } from "lucide-react";

function StandardHeader() {
  return (
    <div className="border rounded-lg overflow-hidden max-w-4xl">
      <PageHeader
        variant="standard"
        height="sm"
        logo={{
          text: "业务组件库",
          href: "/",
          showText: true
        }}
        navigation={{
          showBreadcrumb: true,
          breadcrumbItems: [
            { label: "首页", href: "/" },
            { label: "组件", href: "/components" },
            { label: "页头", isCurrent: true }
          ]
        }}
        search={{
          enabled: true,
          placeholder: "搜索组件、页面...",
          onSearch: (query) => console.log("搜索:", query),
          defaultExpanded: false,
          expandDirection: "left"
        }}
        actions={{
          showThemeToggle: true,
          showNotifications: true,
          notificationCount: 3,
          items: [
            {
              id: "settings",
              label: "设置",
              icon: Settings,
              href: "/settings"
            }
          ]
        }}
        user={{
          name: "张三",
          email: "<EMAIL>",
          role: "开发者",
          avatar: "/placeholder-user.jpg",
          onSignOut: () => console.log("退出登录")
        }}
      />
    </div>
  );
}

render(<StandardHeader />);
  `,
  scope: { PageHeader, Settings, Bell, React },
}

// 简洁布局示例
export const simpleExample = {
  id: "simple-header",
  title: "简洁布局",
  description: "适用于内容页面的简洁页头布局",
  code: `
import React from "react";
import { PageHeader } from "@/components/common-custom/page-header";

function SimpleHeader() {
  return (
    <div className="border rounded-lg overflow-hidden max-w-4xl">
      <PageHeader
        variant="simple"
        height="sm"
        logo={{
          text: "业务组件库",
          href: "/"
        }}
        navigation={{
          showBreadcrumb: true,
          breadcrumbItems: [
            { label: "首页", href: "/" },
            { label: "文档", href: "/docs" },
            { label: "API参考", isCurrent: true }
          ]
        }}
        actions={{
          showThemeToggle: true
        }}
        user={{
          name: "李四",
          avatar: "/placeholder-user.jpg"
        }}
      />
    </div>
  );
}

render(<SimpleHeader />);
  `,
  scope: { PageHeader, React },
}

// 仪表板布局示例
export const dashboardExample = {
  id: "dashboard-header",
  title: "仪表板布局",
  description: "突出搜索功能的仪表板页头布局",
  code: `
import React from "react";
import { PageHeader } from "@/components/common-custom/page-header";
import { Bell, Settings } from "lucide-react";

function DashboardHeader() {
  return (
    <div className="border rounded-lg overflow-hidden max-w-4xl">
      <PageHeader
        variant="dashboard"
        height="sm"
        logo={{
          text: "管理后台",
          href: "/dashboard"
        }}
        search={{
          enabled: true,
          placeholder: "全局搜索...",
          onSearch: (query) => console.log("搜索:", query),
          defaultExpanded: true,
          expandDirection: "left"
        }}
        actions={{
          showThemeToggle: true,
          showNotifications: true,
          notificationCount: 12,
          items: [
            {
              id: "settings",
              label: "系统设置",
              icon: Settings,
              href: "/admin/settings"
            }
          ]
        }}
        user={{
          name: "管理员",
          email: "<EMAIL>",
          role: "系统管理员",
          avatar: "/placeholder-user.jpg",
          menuItems: [
            {
              id: "profile",
              label: "个人资料",
              icon: User,
              href: "/profile"
            },
            {
              id: "help",
              label: "帮助中心",
              icon: HelpCircle,
              href: "/help"
            }
          ],
          onSignOut: () => console.log("管理员退出")
        }}
      />
    </div>
  );
}

render(<DashboardHeader />);
  `,
  scope: { PageHeader, Bell, Settings, User, HelpCircle, React },
}

// 移动端布局示例
export const mobileExample = {
  id: "mobile-header",
  title: "移动端布局",
  description: "适用于移动端的简化页头布局",
  code: `
import React from "react";
import { PageHeader } from "@/components/common-custom/page-header";

function MobileHeader() {
  return (
    <div className="border rounded-lg overflow-hidden max-w-sm">
      <PageHeader
        variant="mobile"
        logo={{
          text: "App",
          href: "/"
        }}
        actions={{
          showThemeToggle: false,
          showNotifications: true,
          notificationCount: 5
        }}
        user={{
          name: "用户",
          avatar: "/placeholder-user.jpg",
          showUserInfo: false
        }}
        height="sm"
      />
    </div>
  );
}

render(<MobileHeader />);
  `,
  scope: { PageHeader, React },
}

// 自定义配置示例
export const customExample = {
  id: "custom-header",
  title: "自定义配置",
  description: "展示各种自定义配置选项",
  code: `
import React from "react";
import { PageHeader } from "@/components/common-custom/page-header";
import { Settings, Bell, HelpCircle } from "lucide-react";

function CustomHeader() {
  return (
    <div className="border rounded-lg overflow-hidden max-w-5xl">
      <PageHeader
        variant="standard"
        height="sm"
        logo={{
          src: "/placeholder.svg",
          alt: "Logo",
          text: "自定义Logo",
          href: "/",
          showText: true
        }}
        navigation={{
          items: [
            {
              label: "产品",
              href: "/products",
              children: [
                { label: "组件库", href: "/components" },
                { label: "设计系统", href: "/design" }
              ]
            },
            {
              label: "文档",
              href: "/docs"
            },
            {
              label: "社区",
              href: "/community"
            }
          ]
        }}
        search={{
          enabled: true,
          placeholder: "搜索任何内容...",
          showShortcut: true,
          onSearch: (query) => alert(\`搜索: \${query}\`)
        }}
        actions={{
          showThemeToggle: true,
          showNotifications: true,
          notificationCount: 99,
          items: [
            {
              id: "help",
              label: "帮助",
              icon: HelpCircle,
              onClick: () => alert("打开帮助")
            },
            {
              id: "settings",
              label: "设置",
              icon: Settings,
              href: "/settings",
              badge: 2
            }
          ]
        }}
        user={{
          name: "王五",
          email: "<EMAIL>",
          role: "产品经理",
          avatar: "/placeholder-user.jpg",
          menuItems: [
            {
              id: "workspace",
              label: "工作空间",
              onClick: () => console.log("切换工作空间")
            },
            {
              id: "billing",
              label: "账单管理",
              href: "/billing"
            },
            {
              id: "separator",
              label: "",
              separator: true
            },
            {
              id: "signout",
              label: "退出登录",
              icon: LogOut,
              destructive: true,
              onClick: () => console.log("退出登录")
            }
          ]
        }}
        className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950 dark:to-indigo-950"
      />
    </div>
  );
}

render(<CustomHeader />);
  `,
  scope: { PageHeader, Settings, Bell, HelpCircle, LogOut, React },
}

// 只有面包屑的页头示例
export const breadcrumbOnlyExample = {
  id: "breadcrumb-only-header",
  title: "面包屑页头",
  description: "只包含面包屑导航的简洁页头，展示深度嵌套路径",
  code: `
import React from "react";
import { PageHeader } from "@/components/common-custom/page-header";

function BreadcrumbOnlyHeader() {
  return (
    <div className="border rounded-lg overflow-hidden max-w-4xl">
      <PageHeader
        variant="simple"
        navigation={{
          showBreadcrumb: true,
          breadcrumbItems: [
            { label: "首页", href: "/" },
            { label: "产品", href: "/products" },
            { label: "组件库", href: "/components" },
            { label: "通用组件", href: "/components/common" },
            { label: "导航组件", href: "/components/navigation" },
            { label: "页头组件", isCurrent: true }
          ]
        }}
        height="sm"
        bordered={false}
      />
    </div>
  );
}

render(<BreadcrumbOnlyHeader />);
  `,
  scope: { PageHeader, React },
}

// 导出所有示例
export const allExamples = [
  standardExample,
  simpleExample,
  breadcrumbOnlyExample,
  dashboardExample,
  mobileExample,
  customExample,
]

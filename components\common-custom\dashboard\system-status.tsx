"use client"

import { ReactNode } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Settings } from "lucide-react"
import { Button } from "@/components/ui/button"
import { SystemStatusItem } from "@/types/dashboard-templates"

/**
 * 系统状态组件
 */
export function SystemStatus({
  statuses = [],
  title
}: {
  statuses?: SystemStatusItem[],
  title?: ReactNode
}) {
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>{title || "系统状态"}</CardTitle>
          <Button variant="ghost" size="sm" className="cursor-pointer">
            <Settings className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {statuses && statuses.length > 0 ? (
            statuses.map((status, index) => (
              <div key={index}>
                <div className="flex items-center justify-between mb-1">
                  <div className="text-sm font-medium">{status.label || status.name}</div>
                  <div className="text-sm text-muted-foreground">{status.value || 0}%</div>
                </div>
                <Progress value={status.value || 0} className="h-1.5" />
              </div>
            ))
          ) : (
            <div className="text-sm text-muted-foreground text-center py-4">
              暂无系统状态数据
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
} 
"use client"

import React from "react"
import { ComponentPreviewContainer } from "@/components/component-detail/preview-container"
import { Tooltip, IconTooltip, Popover, HoverCard } from "@/components/common-custom/tooltip"
import { allExamples } from "./examples"

// ============================================================================
// API文档组件
// ============================================================================

function TooltipApiDocs() {
  return (
    <div className="space-y-6">
      {/* Tooltip 组件API */}
      <div>
        <h3 className="font-medium text-lg mb-2">Tooltip</h3>
        <p className="text-muted-foreground mb-4">基础提示框组件，用于显示简短的帮助信息</p>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">content</td>
                <td className="p-2 border">ReactNode</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">提示内容</td>
              </tr>
              <tr>
                <td className="p-2 border">children</td>
                <td className="p-2 border">ReactNode</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">触发元素</td>
              </tr>
              <tr>
                <td className="p-2 border">side</td>
                <td className="p-2 border">"top" | "right" | "bottom" | "left"</td>
                <td className="p-2 border">"top"</td>
                <td className="p-2 border">显示位置</td>
              </tr>
              <tr>
                <td className="p-2 border">align</td>
                <td className="p-2 border">"start" | "center" | "end"</td>
                <td className="p-2 border">"center"</td>
                <td className="p-2 border">对齐方式</td>
              </tr>
              <tr>
                <td className="p-2 border">trigger</td>
                <td className="p-2 border">"hover" | "click" | "focus" | "manual"</td>
                <td className="p-2 border">"hover"</td>
                <td className="p-2 border">触发方式</td>
              </tr>
              <tr>
                <td className="p-2 border">delayDuration</td>
                <td className="p-2 border">number</td>
                <td className="p-2 border">300</td>
                <td className="p-2 border">显示延迟(毫秒)</td>
              </tr>
              <tr>
                <td className="p-2 border">disabled</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">false</td>
                <td className="p-2 border">是否禁用</td>
              </tr>
              <tr>
                <td className="p-2 border">interactive</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">false</td>
                <td className="p-2 border">是否可交互</td>
              </tr>
              <tr>
                <td className="p-2 border">showArrow</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">true</td>
                <td className="p-2 border">是否显示箭头</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* IconTooltip 组件API */}
      <div>
        <h3 className="font-medium text-lg mb-2">IconTooltip</h3>
        <p className="text-muted-foreground mb-4">图标提示框组件，为文本标签提供帮助提示</p>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">content</td>
                <td className="p-2 border">ReactNode</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">提示内容</td>
              </tr>
              <tr>
                <td className="p-2 border">icon</td>
                <td className="p-2 border">LucideIcon | ReactNode</td>
                <td className="p-2 border">HelpCircle</td>
                <td className="p-2 border">显示的图标</td>
              </tr>
              <tr>
                <td className="p-2 border">iconSize</td>
                <td className="p-2 border">"xs" | "sm" | "md" | "lg"</td>
                <td className="p-2 border">"sm"</td>
                <td className="p-2 border">图标大小</td>
              </tr>
              <tr>
                <td className="p-2 border">iconColor</td>
                <td className="p-2 border">"default" | "muted" | "primary" | "secondary" | "destructive"</td>
                <td className="p-2 border">"muted"</td>
                <td className="p-2 border">图标颜色</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* 使用指南 */}
      <div>
        <h3 className="font-medium text-lg mb-2">使用指南</h3>
        <div className="space-y-4 text-sm">
          <div>
            <h4 className="font-medium mb-2">基本用法</h4>
            <p className="text-muted-foreground mb-2">
              最简单的提示框使用方式：
            </p>
            <pre className="bg-muted p-3 rounded-md overflow-x-auto">
              <code>{`<Tooltip content="提示内容">
  <Button>悬停查看提示</Button>
</Tooltip>`}</code>
            </pre>
          </div>
          
          <div>
            <h4 className="font-medium mb-2">图标提示</h4>
            <p className="text-muted-foreground mb-2">
              为标签添加帮助图标：
            </p>
            <pre className="bg-muted p-3 rounded-md overflow-x-auto">
              <code>{`<div className="flex items-center gap-2">
  <span>用户名</span>
  <IconTooltip content="请输入您的用户名" />
</div>`}</code>
            </pre>
          </div>
        </div>
      </div>
    </div>
  )
}

// ============================================================================
// 主预览组件
// ============================================================================

export default function TooltipExamplePage() {
  return (
    <ComponentPreviewContainer
      title="提示框 Tooltip"
      description="用于显示帮助信息和交互内容的提示框组件集合，支持基础提示框、图标提示框和悬浮卡片"
      whenToUse="当需要为用户提供额外的帮助信息时使用；适用于表单字段说明、功能介绍、操作指引等场景；支持多种触发方式和显示位置"
      examples={allExamples}
      apiDocs={<TooltipApiDocs />}
    />
  )
}

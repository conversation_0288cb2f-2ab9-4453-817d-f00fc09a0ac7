"use client"

import { useState, useEffect } from "react"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card"
import { 
  Search, 
  Plus, 
  LayoutGrid, 
  List, 
  Calendar,
  Filter,
  ArrowUpDown,
  Folder,
  Clock,
  BarChart3,
  CheckCircle2,
  CircleDashed,
  Ban,
  Loader2,
  Users,
  AlertTriangle,
  FileText,
  Layers,
  Grid3X3
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Progress } from "@/components/ui/progress"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { HeaderWithBreadcrumb, type BreadcrumbItem } from "@/components/custom/breadcrumb"
import { NavigationButton, NavigationLink } from "@/components/custom"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuSeparator,
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import { 
  Tooltip, 
  TooltipContent, 
  TooltipProvider, 
  TooltipTrigger 
} from "@/components/ui/tooltip"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { TruncateText } from "@/components/custom/truncate-text"

// 项目状态类型
type ProjectStatus = "进行中" | "已完成" | "规划中" | "已暂停"

// 项目类型
interface Project {
  id: string;
  name: string;
  description: string;
  status: ProjectStatus;
  progress: number;
  dueDate: string;
  createdAt: string;
  updatedAt: string;
  teamId?: string;
  teamName?: string;
  priority: "高" | "中" | "低";
  memberCount: number;
  tags: string[];
}

// 模拟项目数据
const mockProjects: Project[] = [
  {
    id: "1",
    name: "Web应用重构",
    description: "重构现有Web应用的前端架构，提高性能和用户体验",
    status: "进行中",
    progress: 65,
    dueDate: "2024-09-15",
    createdAt: "2024-03-01",
    updatedAt: "2024-06-05",
    teamId: "1",
    teamName: "前端团队",
    priority: "高",
    memberCount: 5,
    tags: ["前端", "React", "重构"]
  },
  {
    id: "2",
    name: "移动端App开发",
    description: "开发iOS和Android原生应用，实现核心业务功能",
    status: "规划中",
    progress: 15,
    dueDate: "2024-12-30",
    createdAt: "2024-05-10",
    updatedAt: "2024-06-01",
    teamId: "2",
    teamName: "移动开发团队",
    priority: "高",
    memberCount: 4,
    tags: ["移动端", "iOS", "Android"]
  },
  {
    id: "3",
    name: "数据分析平台",
    description: "构建内部数据分析平台，支持业务决策和用户行为分析",
    status: "进行中",
    progress: 35,
    dueDate: "2024-08-20",
    createdAt: "2024-04-15",
    updatedAt: "2024-06-02",
    teamId: "3",
    teamName: "数据团队",
    priority: "中",
    memberCount: 3,
    tags: ["数据分析", "可视化", "后端"]
  },
  {
    id: "4",
    name: "用户管理系统优化",
    description: "优化现有用户管理系统，提升安全性和性能",
    status: "已完成",
    progress: 100,
    dueDate: "2024-05-31",
    createdAt: "2024-03-10",
    updatedAt: "2024-05-29",
    teamId: "1",
    teamName: "前端团队",
    priority: "中",
    memberCount: 2,
    tags: ["系统优化", "安全", "后端"]
  },
  {
    id: "5",
    name: "内容管理系统",
    description: "开发灵活可扩展的内容管理系统，支持多种内容类型",
    status: "规划中",
    progress: 10,
    dueDate: "2025-01-15",
    createdAt: "2024-05-20",
    updatedAt: "2024-05-30",
    teamId: "4",
    teamName: "产品团队",
    priority: "低",
    memberCount: 3,
    tags: ["CMS", "内容", "全栈"]
  },
  {
    id: "6",
    name: "API网关实现",
    description: "设计和实现API网关，统一管理微服务接口",
    status: "已暂停",
    progress: 45,
    dueDate: "2024-07-10",
    createdAt: "2024-02-15",
    updatedAt: "2024-04-20",
    teamId: "5",
    teamName: "架构团队",
    priority: "高",
    memberCount: 2,
    tags: ["微服务", "网关", "架构"]
  }
];

// 项目统计数据
const projectStats = {
  total: mockProjects.length,
  inProgress: mockProjects.filter(p => p.status === "进行中").length,
  completed: mockProjects.filter(p => p.status === "已完成").length,
  planned: mockProjects.filter(p => p.status === "规划中").length,
  paused: mockProjects.filter(p => p.status === "已暂停").length,
  upcomingDeadlines: mockProjects.filter(p => {
    const dueDate = new Date(p.dueDate);
    const now = new Date();
    const diffDays = Math.ceil((dueDate.getTime() - now.getTime()) / (1000 * 3600 * 24));
    return diffDays > 0 && diffDays <= 7 && p.status !== "已完成";
  }).length
};

export default function ProjectsPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [filterStatus, setFilterStatus] = useState<string>("all")
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [sortOrder, setSortOrder] = useState<string>("dueDate-asc")
  const [loading, setLoading] = useState(true)
  
  // 面包屑项目
  const breadcrumbItems: BreadcrumbItem[] = [
    { label: "首页", href: "/" },
    { label: "项目管理", isCurrent: true }
  ]
  
  // 模拟加载效果
  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false)
    }, 800)
    
    return () => clearTimeout(timer)
  }, [])
  
  // 根据筛选条件过滤项目
  const filteredProjects = mockProjects.filter(project => {
    // 状态筛选
    const statusMatch = filterStatus === "all" || project.status === filterStatus
    
    // 搜索筛选
    const searchMatch = 
      searchQuery === "" || 
      project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      project.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      project.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
    
    return statusMatch && searchMatch
  })
  
  // 项目排序
  const sortedProjects = [...filteredProjects].sort((a, b) => {
    if (sortOrder === "name-asc") return a.name.localeCompare(b.name)
    if (sortOrder === "name-desc") return b.name.localeCompare(a.name)
    if (sortOrder === "dueDate-asc") return new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime()
    if (sortOrder === "dueDate-desc") return new Date(b.dueDate).getTime() - new Date(a.dueDate).getTime()
    if (sortOrder === "progress-asc") return a.progress - b.progress
    if (sortOrder === "progress-desc") return b.progress - a.progress
    return 0
  })
  
  // 获取状态标签颜色
  const getStatusColor = (status: ProjectStatus) => {
    switch (status) {
      case "进行中": return "bg-blue-500"
      case "已完成": return "bg-green-500"
      case "规划中": return "bg-amber-500"
      case "已暂停": return "bg-slate-500"
      default: return "bg-gray-500"
    }
  }
  
  // 获取优先级标签颜色
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "高": return "text-red-600 bg-red-100"
      case "中": return "text-amber-600 bg-amber-100"
      case "低": return "text-blue-600 bg-blue-100"
      default: return "text-gray-600 bg-gray-100"
    }
  }
  
  // 获取进度条颜色
  const getProgressColor = (progress: number) => {
    if (progress >= 100) return "bg-green-500"
    if (progress >= 70) return "bg-blue-500"
    if (progress >= 30) return "bg-amber-500"
    return "bg-slate-500"
  }
  
  // 格式化日期
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
  }
  
  // 卡片视图
  const renderCardView = () => {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5">
        {sortedProjects.map(project => (
          <Card key={project.id} className="hover:shadow-md transition-all duration-200 border border-border/30 overflow-hidden">
            <CardHeader className="p-5 pb-4">
              <div className="flex justify-between items-start">
                <div className="space-y-1">
                  <CardTitle className="text-lg font-semibold">
                    <TruncateText text={project.name} maxWidth="220px" />
                  </CardTitle>
                  {project.teamName && (
                    <div className="flex items-center text-xs text-muted-foreground">
                      <Users className="h-3 w-3 mr-1" />
                      <span>{project.teamName}</span>
                    </div>
                  )}
                </div>
                <Badge className={getStatusColor(project.status)}>
                  {project.status}
                </Badge>
              </div>
              <CardDescription className="mt-2">
                <TruncateText text={project.description} lines={2} />
              </CardDescription>
            </CardHeader>
            <CardContent className="p-5 pt-0 pb-2">
              <div className="space-y-4">
                <div>
                  <div className="flex items-center justify-between text-sm mb-1">
                    <span className="text-muted-foreground">进度</span>
                    <span>{project.progress}%</span>
                  </div>
                  <Progress value={project.progress} className="h-2" 
                    style={{backgroundColor: '#e5e7eb'}}
                    indicatorClassName={getProgressColor(project.progress)} 
                  />
                </div>
                
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center text-muted-foreground">
                    <Calendar className="h-3.5 w-3.5 mr-1.5" />
                    <span>截止日期: {formatDate(project.dueDate)}</span>
                  </div>
                  <Badge variant="outline" className={`text-xs px-1.5 ${getPriorityColor(project.priority)}`}>
                    {project.priority}优先级
                  </Badge>
                </div>
              </div>
            </CardContent>
            <CardFooter className="px-5 py-3 border-t border-border/20 flex justify-between items-center">
              <div className="flex -space-x-2">
                {[...Array(Math.min(3, project.memberCount))].map((_, i) => (
                  <Avatar key={i} className="h-7 w-7 border-2 border-background">
                    <AvatarFallback className="text-xs bg-blue-100 text-blue-600">
                      {String.fromCharCode(65 + i)}
                    </AvatarFallback>
                  </Avatar>
                ))}
                {project.memberCount > 3 && (
                  <div className="h-7 w-7 rounded-full bg-muted flex items-center justify-center text-xs border-2 border-background">
                    +{project.memberCount - 3}
                  </div>
                )}
              </div>
              <div className="flex gap-2">
                <Button variant="outline" size="sm" className="h-8 px-2 cursor-pointer" asChild>
                  <NavigationLink href={`/projects/${project.id}`}>
                    查看详情
                  </NavigationLink>
                </Button>
              </div>
            </CardFooter>
          </Card>
        ))}
      </div>
    )
  }
  
  // 列表视图
  const renderListView = () => {
    return (
      <div className="border border-border/30 rounded-lg overflow-hidden">
        <Table>
          <TableHeader className="bg-muted/30">
            <TableRow className="hover:bg-transparent">
              <TableHead className="w-[300px]">
                <div className="flex gap-2 items-center">
                  项目名称
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    className="h-4 w-4 rounded-sm cursor-pointer"
                    onClick={() => setSortOrder(sortOrder === "name-asc" ? "name-desc" : "name-asc")}
                  >
                    <ArrowUpDown className="h-3 w-3" />
                  </Button>
                </div>
              </TableHead>
              <TableHead className="w-[120px]">状态</TableHead>
              <TableHead className="hidden lg:table-cell">团队</TableHead>
              <TableHead className="w-[100px]">
                <div className="flex gap-2 items-center">
                  进度
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    className="h-4 w-4 rounded-sm cursor-pointer"
                    onClick={() => setSortOrder(sortOrder === "progress-asc" ? "progress-desc" : "progress-asc")}
                  >
                    <ArrowUpDown className="h-3 w-3" />
                  </Button>
                </div>
              </TableHead>
              <TableHead className="hidden md:table-cell w-[140px]">
                <div className="flex gap-2 items-center">
                  截止日期
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    className="h-4 w-4 rounded-sm cursor-pointer"
                    onClick={() => setSortOrder(sortOrder === "dueDate-asc" ? "dueDate-desc" : "dueDate-asc")}
                  >
                    <ArrowUpDown className="h-3 w-3" />
                  </Button>
                </div>
              </TableHead>
              <TableHead className="hidden xl:table-cell">标签</TableHead>
              <TableHead className="w-[100px] text-right">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {sortedProjects.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="h-24 text-center">
                  没有符合条件的项目
                </TableCell>
              </TableRow>
            ) : (
              sortedProjects.map(project => (
                <TableRow key={project.id} className="hover:bg-muted/20 transition-colors">
                  <TableCell>
                    <div className="flex items-start gap-3">
                      <div className="h-9 w-9 flex items-center justify-center rounded-md bg-primary/10 text-primary flex-shrink-0">
                        <Folder className="h-5 w-5" />
                      </div>
                      <div>
                        <div className="font-medium">{project.name}</div>
                        <div className="text-xs text-muted-foreground mt-0.5 hidden sm:block">
                          <TruncateText text={project.description} maxWidth="240px" />
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(project.status)}>
                      {project.status}
                    </Badge>
                  </TableCell>
                  <TableCell className="hidden lg:table-cell text-muted-foreground">
                    {project.teamName || "未分配"}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Progress value={project.progress} className="h-2 w-12 sm:w-16 md:w-20" 
                        style={{backgroundColor: '#e5e7eb'}}
                        indicatorClassName={getProgressColor(project.progress)} 
                      />
                      <span className="text-xs">{project.progress}%</span>
                    </div>
                  </TableCell>
                  <TableCell className="hidden md:table-cell text-muted-foreground">
                    {formatDate(project.dueDate)}
                  </TableCell>
                  <TableCell className="hidden xl:table-cell">
                    <div className="flex flex-wrap gap-1">
                      {project.tags.slice(0, 2).map((tag, index) => (
                        <Badge key={index} variant="outline" className="text-xs bg-muted/50">
                          {tag}
                        </Badge>
                      ))}
                      {project.tags.length > 2 && (
                        <Badge variant="outline" className="text-xs bg-muted/50">
                          +{project.tags.length - 2}
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <Button variant="ghost" size="sm" asChild className="cursor-pointer">
                      <NavigationLink href={`/projects/${project.id}`}>
                        查看
                      </NavigationLink>
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    )
  }
  
  return (
    <div className="flex min-h-screen w-full flex-col bg-gradient-to-b from-background to-background/80">
      <HeaderWithBreadcrumb items={breadcrumbItems} />
      
      <div className="p-4 md:p-6 max-w-7xl mx-auto w-full">
        <main className="space-y-6">
          {/* 页面标题和操作按钮 */}
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
            <div>
              <h1 className="text-2xl font-bold tracking-tight">项目管理</h1>
              <p className="text-muted-foreground mt-1">管理和跟踪团队项目进度</p>
            </div>
            <NavigationButton 
              href="/projects/new" 
              variant="default" 
              className="shadow-sm cursor-pointer"
            >
              <Plus className="h-4 w-4 mr-1" />
              创建项目
            </NavigationButton>
          </div>
          
          {/* 统计卡片 */}
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-4">
            <Card className="border border-border/30">
              <CardContent className="p-4 flex flex-col justify-between h-full">
                <div className="text-muted-foreground text-xs font-medium">全部项目</div>
                <div className="flex items-end justify-between mt-1">
                  <div className="text-2xl font-bold">{projectStats.total}</div>
                  <Layers className="h-4 w-4 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>
            
            <Card className="border border-border/30">
              <CardContent className="p-4 flex flex-col justify-between h-full">
                <div className="text-muted-foreground text-xs font-medium">进行中</div>
                <div className="flex items-end justify-between mt-1">
                  <div className="text-2xl font-bold">{projectStats.inProgress}</div>
                  <CircleDashed className="h-4 w-4 text-blue-500" />
                </div>
              </CardContent>
            </Card>
            
            <Card className="border border-border/30">
              <CardContent className="p-4 flex flex-col justify-between h-full">
                <div className="text-muted-foreground text-xs font-medium">已完成</div>
                <div className="flex items-end justify-between mt-1">
                  <div className="text-2xl font-bold">{projectStats.completed}</div>
                  <CheckCircle2 className="h-4 w-4 text-green-500" />
                </div>
              </CardContent>
            </Card>
            
            <Card className="border border-border/30">
              <CardContent className="p-4 flex flex-col justify-between h-full">
                <div className="text-muted-foreground text-xs font-medium">规划中</div>
                <div className="flex items-end justify-between mt-1">
                  <div className="text-2xl font-bold">{projectStats.planned}</div>
                  <Calendar className="h-4 w-4 text-amber-500" />
                </div>
              </CardContent>
            </Card>
            
            <Card className="border border-border/30">
              <CardContent className="p-4 flex flex-col justify-between h-full">
                <div className="text-muted-foreground text-xs font-medium">已暂停</div>
                <div className="flex items-end justify-between mt-1">
                  <div className="text-2xl font-bold">{projectStats.paused}</div>
                  <Ban className="h-4 w-4 text-slate-500" />
                </div>
              </CardContent>
            </Card>
            
            <Card className="border border-border/30">
              <CardContent className="p-4 flex flex-col justify-between h-full">
                <div className="text-muted-foreground text-xs font-medium">即将截止</div>
                <div className="flex items-end justify-between mt-1">
                  <div className="text-2xl font-bold">{projectStats.upcomingDeadlines}</div>
                  <Clock className="h-4 w-4 text-red-500" />
                </div>
              </CardContent>
            </Card>
          </div>
          
          {/* 搜索和筛选栏 */}
          <div className="flex flex-col gap-4 md:flex-row md:items-center justify-between">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="搜索项目名称、描述或标签..."
                className="pl-8 h-9 border border-input/30 bg-background/50 ring-1 ring-border/5 hover:border-input/50 transition-colors"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            
            <div className="flex flex-wrap gap-2 items-center">
              <Tabs defaultValue="all" value={filterStatus} onValueChange={setFilterStatus} className="h-9">
                <TabsList className="h-9 bg-muted/40">
                  <TabsTrigger value="all" className="h-8 px-3 text-xs">全部</TabsTrigger>
                  <TabsTrigger value="进行中" className="h-8 px-3 text-xs">进行中</TabsTrigger>
                  <TabsTrigger value="已完成" className="h-8 px-3 text-xs">已完成</TabsTrigger>
                  <TabsTrigger value="规划中" className="h-8 px-3 text-xs">规划中</TabsTrigger>
                  <TabsTrigger value="已暂停" className="h-8 px-3 text-xs">已暂停</TabsTrigger>
                </TabsList>
              </Tabs>
              
              <div className="flex items-center gap-2 ml-2">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button 
                        variant={viewMode === "list" ? "default" : "outline"} 
                        size="icon"
                        className="h-9 w-9 cursor-pointer"
                        onClick={() => setViewMode("list")}
                      >
                        <List className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="bottom">列表视图</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button 
                        variant={viewMode === "grid" ? "default" : "outline"} 
                        size="icon"
                        className="h-9 w-9 cursor-pointer"
                        onClick={() => setViewMode("grid")}
                      >
                        <Grid3X3 className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="bottom">卡片视图</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm" className="h-9 cursor-pointer ml-2">
                      <ArrowUpDown className="h-3.5 w-3.5 mr-1.5" />
                      排序
                      {sortOrder === "name-asc" && " (名称 A-Z)"}
                      {sortOrder === "name-desc" && " (名称 Z-A)"}
                      {sortOrder === "dueDate-asc" && " (截止日期 最近)"}
                      {sortOrder === "dueDate-desc" && " (截止日期 最远)"}
                      {sortOrder === "progress-asc" && " (进度 最低)"}
                      {sortOrder === "progress-desc" && " (进度 最高)"}
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-[180px]">
                    <DropdownMenuItem 
                      onClick={() => setSortOrder("name-asc")}
                      className={sortOrder === "name-asc" ? "bg-primary/10 font-medium cursor-pointer" : "cursor-pointer"}
                    >
                      名称 (A-Z)
                      {sortOrder === "name-asc" && <CheckCircle2 className="ml-2 h-3.5 w-3.5" />}
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      onClick={() => setSortOrder("name-desc")}
                      className={sortOrder === "name-desc" ? "bg-primary/10 font-medium cursor-pointer" : "cursor-pointer"}
                    >
                      名称 (Z-A)
                      {sortOrder === "name-desc" && <CheckCircle2 className="ml-2 h-3.5 w-3.5" />}
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem 
                      onClick={() => setSortOrder("dueDate-asc")}
                      className={sortOrder === "dueDate-asc" ? "bg-primary/10 font-medium cursor-pointer" : "cursor-pointer"}
                    >
                      截止日期 (最近)
                      {sortOrder === "dueDate-asc" && <CheckCircle2 className="ml-2 h-3.5 w-3.5" />}
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      onClick={() => setSortOrder("dueDate-desc")}
                      className={sortOrder === "dueDate-desc" ? "bg-primary/10 font-medium cursor-pointer" : "cursor-pointer"}
                    >
                      截止日期 (最远)
                      {sortOrder === "dueDate-desc" && <CheckCircle2 className="ml-2 h-3.5 w-3.5" />}
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem 
                      onClick={() => setSortOrder("progress-asc")}
                      className={sortOrder === "progress-asc" ? "bg-primary/10 font-medium cursor-pointer" : "cursor-pointer"}
                    >
                      进度 (最低)
                      {sortOrder === "progress-asc" && <CheckCircle2 className="ml-2 h-3.5 w-3.5" />}
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      onClick={() => setSortOrder("progress-desc")}
                      className={sortOrder === "progress-desc" ? "bg-primary/10 font-medium cursor-pointer" : "cursor-pointer"}
                    >
                      进度 (最高)
                      {sortOrder === "progress-desc" && <CheckCircle2 className="ml-2 h-3.5 w-3.5" />}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </div>
          
          {/* 项目列表 */}
          <div className="min-h-[300px] relative">
            {/* 加载动画 */}
            {loading && (
              <div className="absolute inset-0 flex items-center justify-center bg-background/50 backdrop-blur-[1px] z-10">
                <div className="flex flex-col items-center gap-2">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  <p className="text-sm text-muted-foreground">加载项目数据中...</p>
                </div>
              </div>
            )}
            
            {/* 无数据提示 */}
            {!loading && sortedProjects.length === 0 && (
              <Alert className="border border-border/30">
                <AlertTitle className="flex items-center">
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  未找到项目
                </AlertTitle>
                <AlertDescription>
                  没有符合当前筛选条件的项目，请尝试更改筛选条件或创建新项目。
                </AlertDescription>
              </Alert>
            )}
            
            {/* 项目列表内容 */}
            {!loading && sortedProjects.length > 0 && (
              viewMode === "grid" ? renderCardView() : renderListView()
            )}
          </div>
        </main>
      </div>
    </div>
  )
} 
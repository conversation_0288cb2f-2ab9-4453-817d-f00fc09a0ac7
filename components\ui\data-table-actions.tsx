"use client"

import { But<PERSON> } from "@/components/ui/button"
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu"
import { MoreHorizontal, Eye, Edit, Trash2 } from "lucide-react"
import { DataTableActionsProps } from "@/components/common-custom/data-table/types"
import { ReactNode } from "react"

/**
 * 数据表格默认操作菜单组件
 * 提供查看、编辑、删除三个常用操作，或者自定义操作列表
 */
export function DataTableActions<T>({ 
  row,
  onView, 
  onEdit, 
  onDelete,
  actions,
  className
}: DataTableActionsProps<T>) {
  // 如果提供了自定义操作列表，则使用它
  const hasCustomActions = actions && actions.length > 0
  // 如果没有任何操作，则不渲染
  if (!hasCustomActions && !onView && !onEdit && !onDelete) {
    return null
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className={`h-8 w-8 flex-shrink-0 ${className || ""}`}>
          <MoreHorizontal className="h-4 w-4" />
          <span className="sr-only">操作菜单</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[160px]">
        <DropdownMenuLabel>操作</DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        {/* 默认操作 */}
        {!hasCustomActions && (
          <>
            {onView && (
              <DropdownMenuItem onClick={() => onView(row)}>
                <Eye className="w-4 h-4 mr-2" />
                查看详情
              </DropdownMenuItem>
            )}
            {onEdit && (
              <DropdownMenuItem onClick={() => onEdit(row)}>
                <Edit className="w-4 h-4 mr-2" />
                编辑
              </DropdownMenuItem>
            )}
            {onDelete && (
              <DropdownMenuItem className="text-destructive" onClick={() => onDelete(row)}>
                <Trash2 className="w-4 h-4 mr-2" />
                删除
              </DropdownMenuItem>
            )}
          </>
        )}
        
        {/* 自定义操作 */}
        {hasCustomActions && actions.map((action, index) => {
          // 检查是否应该显示该操作
          if (action.isShown && !action.isShown(row)) {
            return null
          }
          
          // 检查是否禁用
          const isDisabled = typeof action.disabled === 'function' 
            ? action.disabled(row) 
            : action.disabled

          return (
            <DropdownMenuItem 
              key={index}
              onClick={() => action.onClick(row)}
              disabled={isDisabled}
              className={action.isDanger ? "text-destructive" : ""}
            >
              {action.icon && <span className="mr-2">{action.icon}</span>}
              {action.label}
            </DropdownMenuItem>
          )
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  )
} 
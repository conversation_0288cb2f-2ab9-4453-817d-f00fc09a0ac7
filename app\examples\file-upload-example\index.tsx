"use client"

import * as React from "react"
import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Upload, File, X, Download, Eye, RefreshCw } from "lucide-react"

interface FileUploadItem {
  id: string
  name: string
  size: number
  type: string
  progress: number
  status: "uploading" | "completed" | "error"
  url?: string
  error?: string
}

export function FileUploadExample() {
  // 文件列表状态
  const [files, setFiles] = useState<FileUploadItem[]>([
    {
      id: "1",
      name: "项目方案.pdf",
      size: 2500000,
      type: "application/pdf",
      progress: 100,
      status: "completed",
      url: "#",
    },
    {
      id: "2",
      name: "产品设计.png",
      size: 1200000,
      type: "image/png",
      progress: 100,
      status: "completed",
      url: "#",
    },
  ])

  // 处理文件上传
  const handleFilesSelected = (selectedFiles: FileList | null) => {
    if (!selectedFiles || selectedFiles.length === 0) return

    // 模拟文件上传过程
    const newFiles = Array.from(selectedFiles).map((file) => {
      const id = Math.random().toString(36).substring(2, 9)
      return {
        id,
        name: file.name,
        size: file.size,
        type: file.type,
        progress: 0,
        status: "uploading" as const,
      }
    })

    setFiles((prev) => [...prev, ...newFiles])

    // 模拟上传进度
    newFiles.forEach((file) => {
      const interval = setInterval(() => {
        setFiles((prevFiles) => {
          const fileIndex = prevFiles.findIndex((f) => f.id === file.id)
          if (fileIndex === -1) return prevFiles

          const updatedFiles = [...prevFiles]
          const currentFile = updatedFiles[fileIndex]

          // 更新进度
          if (currentFile.progress < 100) {
            updatedFiles[fileIndex] = {
              ...currentFile,
              progress: Math.min(currentFile.progress + 20, 100),
            }
          } else {
            updatedFiles[fileIndex] = {
              ...currentFile,
              status: "completed",
              url: "#",
            }
            clearInterval(interval)
          }

          return updatedFiles
        })
      }, 500)
    })
  }

  // 处理文件删除
  const handleDeleteFile = (id: string) => {
    setFiles((prev) => prev.filter((file) => file.id !== id))
  }

  // 处理文件重试
  const handleRetryFile = (id: string) => {
    setFiles((prev) =>
      prev.map((file) =>
        file.id === id
          ? { ...file, progress: 0, status: "uploading" }
          : file
      )
    )

    // 模拟重新上传
    const interval = setInterval(() => {
      setFiles((prevFiles) => {
        const fileIndex = prevFiles.findIndex((f) => f.id === id)
        if (fileIndex === -1) return prevFiles

        const updatedFiles = [...prevFiles]
        const currentFile = updatedFiles[fileIndex]

        if (currentFile.progress < 100) {
          updatedFiles[fileIndex] = {
            ...currentFile,
            progress: Math.min(currentFile.progress + 20, 100),
          }
        } else {
          updatedFiles[fileIndex] = {
            ...currentFile,
            status: "completed",
            url: "#",
          }
          clearInterval(interval)
        }

        return updatedFiles
      })
    }, 500)
  }

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes"
    const k = 1024
    const sizes = ["Bytes", "KB", "MB", "GB"]
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>文件上传</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="border-2 border-dashed rounded-lg p-8 text-center">
            <Upload className="h-10 w-10 mx-auto mb-4 text-muted-foreground" />
            <h4 className="text-lg font-medium mb-2">拖拽文件到此处上传</h4>
            <p className="text-muted-foreground mb-4">支持 JPG、PNG、PDF、DOC 等格式，单个文件不超过 10MB</p>
            <div>
              <input
                type="file"
                id="file-upload"
                className="hidden"
                multiple
                onChange={(e) => handleFilesSelected(e.target.files)}
              />
              <Button asChild>
                <label htmlFor="file-upload" className="cursor-pointer">选择文件</label>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {files.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>已上传文件</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {files.map((file) => (
                <div key={file.id} className="flex items-center gap-4 p-4 border rounded-lg">
                  <div className="flex items-center gap-3 flex-1">
                    <File className="h-6 w-6 text-blue-500" />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium truncate">{file.name}</span>
                        <span
                          className={`text-xs px-2 py-0.5 rounded-full ${
                            file.status === "completed"
                              ? "bg-green-100 text-green-800"
                              : file.status === "error"
                              ? "bg-red-100 text-red-800"
                              : "bg-blue-100 text-blue-800"
                          }`}
                        >
                          {file.status === "completed"
                            ? "已完成"
                            : file.status === "error"
                            ? "失败"
                            : "上传中"}
                        </span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <span>{formatFileSize(file.size)}</span>
                        {file.status === "uploading" && (
                          <span>• {Math.round(file.progress)}%</span>
                        )}
                      </div>
                      {file.status === "uploading" && (
                        <Progress value={file.progress} className="h-2 mt-2" />
                      )}
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    {file.status === "completed" && (
                      <>
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Download className="h-4 w-4" />
                        </Button>
                      </>
                    )}
                    {file.status === "error" && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRetryFile(file.id)}
                      >
                        <RefreshCw className="h-4 w-4" />
                      </Button>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteFile(file.id)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
} 
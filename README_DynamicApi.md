# MongoDB通用动态CRUD接口 - 使用指南

本项目提供对MongoDB数据库的通用动态CRUD操作，无需预先定义实体类，适用于需要灵活操作MongoDB集合的场景。

> **重要提示**：以下所有示例中的URL、集合名称、数据等均为演示用途，实际使用时请根据自己的项目情况进行替换。

## 基础信息

### 接口基础URL
```
http://127.0.0.1:8413/template/api/dynamic/{collection}
```

其中，`{collection}` 需要替换为您要操作的MongoDB集合名称。

### 响应格式

所有接口统一返回以下格式的JSON数据：

```json
{
  "success": true,      // 操作是否成功，布尔值
  "code": 200,          // 状态码，成功为200，失败为对应错误码
  "message": "操作成功",  // 操作结果描述
  "data": {}            // 返回的数据，不同接口返回内容不同
}
```

## 详细接口说明

### 1. 创建文档

**接口**: `POST /api/dynamic/{collection}`

**功能**: 向指定集合中添加一个新文档

**入参示例**:

```json
{
  "name": "张三",
  "age": 28,
  "email": "<EMAIL>",
  "address": {
    "city": "北京",
    "district": "海淀区"
  },
  "tags": ["开发", "Java", "MongoDB"],
  "createTime": "2023-01-01T10:00:00"
}
```

**出参示例**:

```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {
    "_id": "65a6c59c3c124e0001b4f1e9",
    "name": "张三",
    "age": 28,
    "email": "<EMAIL>",
    "address": {
      "city": "北京",
      "district": "海淀区"
    },
    "tags": ["开发", "Java", "MongoDB"],
    "createTime": "2023-01-01T10:00:00"
  }
}
```

**curl命令示例**:

```bash
curl -X POST "http://127.0.0.1:8413/template/api/dynamic/users" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "张三",
    "age": 28,
    "email": "<EMAIL>",
    "address": {
      "city": "北京",
      "district": "海淀区"
    },
    "tags": ["开发", "Java", "MongoDB"],
    "createTime": "2023-01-01T10:00:00"
  }'
```

### 2. 批量创建文档

**接口**: `POST /api/dynamic/{collection}/batch`

**功能**: 向指定集合中批量添加多个文档

**入参示例**:

```json
[
  {
    "name": "李四",
    "age": 32,
    "email": "<EMAIL>",
    "address": {
      "city": "上海",
      "district": "浦东新区"
    },
    "tags": ["测试", "Python"],
    "createTime": "2023-01-02T11:00:00"
  },
  {
    "name": "王五",
    "age": 25,
    "email": "<EMAIL>",
    "address": {
      "city": "广州",
      "district": "天河区"
    },
    "tags": ["产品", "设计"],
    "createTime": "2023-01-03T12:00:00"
  }
]
```

**出参示例**:

```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "_id": "65a6c59c3c124e0001b4f1ea",
      "name": "李四",
      "age": 32,
      "email": "<EMAIL>",
      "address": {
        "city": "上海",
        "district": "浦东新区"
      },
      "tags": ["测试", "Python"],
      "createTime": "2023-01-02T11:00:00"
    },
    {
      "_id": "65a6c59c3c124e0001b4f1eb",
      "name": "王五",
      "age": 25,
      "email": "<EMAIL>",
      "address": {
        "city": "广州",
        "district": "天河区"
      },
      "tags": ["产品", "设计"],
      "createTime": "2023-01-03T12:00:00"
    }
  ]
}
```

**curl命令示例**:

```bash
curl -X POST "http://127.0.0.1:8413/template/api/dynamic/users/batch" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "name": "李四",
      "age": 32,
      "email": "<EMAIL>",
      "address": {
        "city": "上海",
        "district": "浦东新区"
      },
      "tags": ["测试", "Python"],
      "createTime": "2023-01-02T11:00:00"
    },
    {
      "name": "王五",
      "age": 25,
      "email": "<EMAIL>",
      "address": {
        "city": "广州",
        "district": "天河区"
      },
      "tags": ["产品", "设计"],
      "createTime": "2023-01-03T12:00:00"
    }
  ]'
```

### 3. 根据ID查询文档

**接口**: `GET /api/dynamic/{collection}/{id}`

**功能**: 根据文档ID查询单个文档

**入参**: 路径参数中的集合名称和文档ID

**出参示例**:

```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {
    "_id": "65a6c59c3c124e0001b4f1e9",
    "name": "张三",
    "age": 28,
    "email": "<EMAIL>",
    "address": {
      "city": "北京",
      "district": "海淀区"
    },
    "tags": ["开发", "Java", "MongoDB"],
    "createTime": "2023-01-01T10:00:00"
  }
}
```

**失败出参示例**:

```json
{
  "success": false,
  "code": 404,
  "message": "未找到指定文档",
  "data": null
}
```

**curl命令示例**:

```bash
# 注意: 需要替换为实际的文档ID
curl -X GET "http://127.0.0.1:8413/template/api/dynamic/users/65a6c59c3c124e0001b4f1e9"
```

### 4. 查询所有文档

**接口**: `GET /api/dynamic/{collection}`

**功能**: 查询集合中的所有文档

**入参**: 路径参数中的集合名称

**出参示例**:

```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "_id": "65a6c59c3c124e0001b4f1e9",
      "name": "张三",
      "age": 28,
      "email": "<EMAIL>",
      "address": {
        "city": "北京",
        "district": "海淀区"
      },
      "tags": ["开发", "Java", "MongoDB"],
      "createTime": "2023-01-01T10:00:00"
    },
    {
      "_id": "65a6c59c3c124e0001b4f1ea",
      "name": "李四",
      "age": 32,
      "email": "<EMAIL>",
      "address": {
        "city": "上海",
        "district": "浦东新区"
      },
      "tags": ["测试", "Python"],
      "createTime": "2023-01-02T11:00:00"
    }
    // ... 更多文档
  ]
}
```

**curl命令示例**:

```bash
curl -X GET "http://127.0.0.1:8413/template/api/dynamic/users"
```

### 5. 条件查询（分页）

**接口**: `GET /api/dynamic/{collection}/query`

**功能**: 根据条件查询文档，支持分页和排序

**查询参数**:

| 参数名    | 类型   | 是否必须 | 说明                                   |
|-----------|--------|----------|----------------------------------------|
| query     | String | 否       | JSON格式的查询条件，需进行URL编码      |
| sort      | String | 否       | 排序字段                               |
| direction | String | 否       | 排序方向，"asc"或"desc"，默认为"asc"   |
| page      | int    | 否       | 页码，从1开始，默认为0（不分页）       |
| size      | int    | 否       | 每页大小，默认为0（不限制）            |

**入参示例1**: 简单条件查询

查询年龄大于25岁的用户，URL参数中的query值为：`{"age":{"$gt":25}}`，需URL编码后传递。

**入参示例2**: 分页和排序查询

查询年龄大于25岁的用户，按年龄降序排列，查询第1页，每页10条记录。

**出参示例**:

```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {
    "data": [
      {
        "_id": "65a6c59c3c124e0001b4f1ea",
        "name": "李四",
        "age": 32,
        "email": "<EMAIL>",
        "address": {
          "city": "上海",
          "district": "浦东新区"
        },
        "tags": ["测试", "Python"],
        "createTime": "2023-01-02T11:00:00"
      },
      {
        "_id": "65a6c59c3c124e0001b4f1e9",
        "name": "张三",
        "age": 28,
        "email": "<EMAIL>",
        "address": {
          "city": "北京",
          "district": "海淀区"
        },
        "tags": ["开发", "Java", "MongoDB"],
        "createTime": "2023-01-01T10:00:00"
      }
      // ... 更多文档，最多10条
    ],
    "total": 25,  // 总记录数
    "page": 1,    // 当前页码
    "size": 10    // 每页大小
  }
}
```

**curl命令示例**:

```bash
# 不带分页的简单查询
curl -X GET "http://127.0.0.1:8413/template/api/dynamic/users/query?query=%7B%22age%22%3A%7B%22%24gt%22%3A25%7D%7D"

# 带分页和排序的查询（查询年龄大于25岁的用户，按年龄降序排列，第1页，每页10条）
curl -X GET "http://127.0.0.1:8413/template/api/dynamic/users/query?query=%7B%22age%22%3A%7B%22%24gt%22%3A25%7D%7D&sort=age&direction=desc&page=1&size=10"
```

> URL编码前的query参数：`{"age":{"$gt":25}}`

### 6. 根据ID更新文档

**接口**: `PUT /api/dynamic/{collection}/{id}`

**功能**: 根据ID更新文档的指定字段

**入参示例**:

```json
{
  "age": 29,
  "address": {
    "city": "北京",
    "district": "朝阳区"
  }
}
```

**出参示例**:

```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": true
}
```

**失败出参示例**:

```json
{
  "success": false,
  "code": 404,
  "message": "未找到要更新的文档",
  "data": null
}
```

**curl命令示例**:

```bash
# 注意: 需要替换为实际的文档ID
curl -X PUT "http://127.0.0.1:8413/template/api/dynamic/users/65a6c59c3c124e0001b4f1e9" \
  -H "Content-Type: application/json" \
  -d '{
    "age": 29,
    "address": {
      "city": "北京",
      "district": "朝阳区"
    }
  }'
```

### 7. 条件更新文档

**接口**: `PUT /api/dynamic/{collection}`

**功能**: 根据条件更新符合条件的文档

**入参示例**:

```json
{
  "query": {
    "age": {"$gt": 30}
  },
  "update": {
    "$set": {
      "status": "资深"
    }
  }
}
```

**出参示例**:

```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {
    "updatedCount": 5
  }
}
```

**curl命令示例**:

```bash
curl -X PUT "http://127.0.0.1:8413/template/api/dynamic/users" \
  -H "Content-Type: application/json" \
  -d '{
    "query": {
      "age": {"$gt": 30}
    },
    "update": {
      "$set": {
        "status": "资深"
      }
    }
  }'
```

### 8. 根据ID删除文档

**接口**: `DELETE /api/dynamic/{collection}/{id}`

**功能**: 根据ID删除单个文档

**入参**: 路径参数中的集合名称和文档ID

**出参示例**:

```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": true
}
```

**失败出参示例**:

```json
{
  "success": false,
  "code": 404,
  "message": "未找到要删除的文档",
  "data": null
}
```

**curl命令示例**:

```bash
# 注意: 需要替换为实际的文档ID
curl -X DELETE "http://127.0.0.1:8413/template/api/dynamic/users/65a6c59c3c124e0001b4f1e9"
```

### 9. 条件删除文档

**接口**: `DELETE /api/dynamic/{collection}`

**功能**: 根据条件删除符合条件的文档

**查询参数**:

| 参数名 | 类型   | 是否必须 | 说明                              |
|--------|--------|----------|-----------------------------------|
| query  | String | 是       | JSON格式的查询条件，需进行URL编码 |

**出参示例**:

```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {
    "deletedCount": 3
  }
}
```

**curl命令示例**:

```bash
# 删除年龄小于18岁的用户
curl -X DELETE "http://127.0.0.1:8413/template/api/dynamic/users?query=%7B%22age%22%3A%7B%22%24lt%22%3A18%7D%7D"
```

> URL编码前的query参数：`{"age":{"$lt":18}}`

## 高级查询和操作示例

### MongoDB查询操作符示例

MongoDB支持丰富的查询操作符，以下是一些常用查询操作符的示例：

| 操作符    | 说明           | 示例                                  |
|-----------|----------------|---------------------------------------|
| $eq       | 等于           | `{"age": {"$eq": 25}}`               |
| $gt       | 大于           | `{"age": {"$gt": 25}}`               |
| $gte      | 大于等于       | `{"age": {"$gte": 25}}`              |
| $lt       | 小于           | `{"age": {"$lt": 25}}`               |
| $lte      | 小于等于       | `{"age": {"$lte": 25}}`              |
| $ne       | 不等于         | `{"age": {"$ne": 25}}`               |
| $in       | 在数组中       | `{"age": {"$in": [25, 30, 35]}}`     |
| $nin      | 不在数组中     | `{"age": {"$nin": [25, 30, 35]}}`    |
| $exists   | 字段存在       | `{"age": {"$exists": true}}`         |
| $regex    | 正则表达式匹配 | `{"name": {"$regex": "^张"}}`        |

### 1. 复杂条件查询示例

**需求**: 查询年龄在25-35岁之间，且来自北京或上海的用户

**查询参数(解码前)**:
```json
{
  "age": {
    "$gte": 25,
    "$lte": 35
  },
  "address.city": {
    "$in": ["北京", "上海"]
  }
}
```

**curl命令**:
```bash
curl -X GET "http://127.0.0.1:8413/template/api/dynamic/users/query?query=%7B%22age%22%3A%7B%22%24gte%22%3A25%2C%22%24lte%22%3A35%7D%2C%22address.city%22%3A%7B%22%24in%22%3A%5B%22%E5%8C%97%E4%BA%AC%22%2C%22%E4%B8%8A%E6%B5%B7%22%5D%7D%7D"
```

### 2. 嵌套字段查询示例

**需求**: 查询居住在北京的用户

**查询参数(解码前)**:
```json
{"address.city": "北京"}
```

**curl命令**:
```bash
curl -X GET "http://127.0.0.1:8413/template/api/dynamic/users/query?query=%7B%22address.city%22%3A%22%E5%8C%97%E4%BA%AC%22%7D"
```

### 3. 数组字段查询示例

**需求**: 查询标签中包含"Java"的用户

**查询参数(解码前)**:
```json
{"tags": "Java"}
```

**curl命令**:
```bash
curl -X GET "http://127.0.0.1:8413/template/api/dynamic/users/query?query=%7B%22tags%22%3A%22Java%22%7D"
```

### MongoDB更新操作符示例

MongoDB支持多种更新操作符，以下是一些常用更新操作符的示例：

| 操作符    | 说明                 | 示例                                     |
|-----------|----------------------|------------------------------------------|
| $set      | 设置字段值           | `{"$set": {"status": "资深"}}`          |
| $inc      | 增加数值             | `{"$inc": {"age": 1}}`                  |
| $push     | 向数组添加元素       | `{"$push": {"tags": "Spring"}}`         |
| $pull     | 从数组移除元素       | `{"$pull": {"tags": "MongoDB"}}`        |
| $unset    | 移除字段             | `{"$unset": {"temporary": ""}}`         |
| $rename   | 重命名字段           | `{"$rename": {"old_field": "new_field"}}` |

### 1. 使用$inc增加数值示例

**需求**: 将用户"张三"的年龄增加1，登录次数增加1

**请求体**:
```json
{
  "query": {
    "name": "张三"
  },
  "update": {
    "$inc": {
      "age": 1,
      "loginCount": 1
    }
  }
}
```

**curl命令**:
```bash
curl -X PUT "http://127.0.0.1:8413/template/api/dynamic/users" \
  -H "Content-Type: application/json" \
  -d '{
    "query": {
      "name": "张三"
    },
    "update": {
      "$inc": {
        "age": 1,
        "loginCount": 1
      }
    }
  }'
```

### 2. 使用$push添加数组元素示例

**需求**: 向用户"张三"的标签数组中添加"Spring"标签

**请求体**:
```json
{
  "query": {
    "name": "张三"
  },
  "update": {
    "$push": {
      "tags": "Spring"
    }
  }
}
```

**curl命令**:
```bash
curl -X PUT "http://127.0.0.1:8413/template/api/dynamic/users" \
  -H "Content-Type: application/json" \
  -d '{
    "query": {
      "name": "张三"
    },
    "update": {
      "$push": {
        "tags": "Spring"
      }
    }
  }'
```

### 3. 使用$pull删除数组元素示例

**需求**: 从用户"张三"的标签数组中移除"MongoDB"标签

**请求体**:
```json
{
  "query": {
    "name": "张三"
  },
  "update": {
    "$pull": {
      "tags": "MongoDB"
    }
  }
}
```

**curl命令**:
```bash
curl -X PUT "http://127.0.0.1:8413/template/api/dynamic/users" \
  -H "Content-Type: application/json" \
  -d '{
    "query": {
      "name": "张三"
    },
    "update": {
      "$pull": {
        "tags": "MongoDB"
      }
    }
  }'
```

## 注意事项

1. **安全性**：确保在生产环境中适当限制此接口的访问权限，避免未授权访问或数据泄露风险。

2. **性能考虑**：
   - 查询大量数据时，建议使用分页功能
   - 复杂查询可能会影响性能，请根据实际需求合理设计查询条件
   - 对于高频访问的集合，建议创建适当的索引

3. **集合名称限制**：MongoDB集合名称有一些限制，例如：
   - 不能为空
   - 不能包含系统保留前缀 "system."
   - 不能包含 $ 字符
   - 不能以 . 或 $ 开头
   - 长度不能超过120个字符

4. **ID格式**：MongoDB的ObjectId是24个字符的十六进制字符串，请确保在使用ID操作时提供正确格式的ID。

5. **URL编码**：在通过URL参数传递JSON查询条件时，请确保进行正确的URL编码，以避免特殊字符导致的问题。 

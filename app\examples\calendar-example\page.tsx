"use client"

import React from "react"
import { ComponentPreviewContainer } from "@/components/component-detail/preview-container"
import { allExamples } from "./examples"

/**
 * 日历组件示例页面
 */
export default function CalendarExamplePage() {
  return (
    <ComponentPreviewContainer
      title="Calendar 日历"
      description="基于 react-day-picker 的增强日历组件，支持事件显示、多种选择模式、图例等功能。"
      whenToUse="当需要用户选择日期、显示日程事件、或需要日历视图展示数据时使用。"
      examples={allExamples}
      apiDocs={
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-3">主要属性</h3>
            <div className="space-y-4">
              <div className="border rounded-lg p-4">
                <h4 className="font-medium mb-2">mode</h4>
                <p className="text-sm text-muted-foreground mb-2">
                  日历选择模式，支持单选、多选、范围选择
                </p>
                <code className="text-xs bg-muted px-2 py-1 rounded">
                  "single" | "multiple" | "range"
                </code>
                <p className="text-xs text-muted-foreground mt-1">默认: "single"</p>
              </div>
              
              <div className="border rounded-lg p-4">
                <h4 className="font-medium mb-2">value</h4>
                <p className="text-sm text-muted-foreground mb-2">
                  选中的值，根据模式不同类型也不同
                </p>
                <code className="text-xs bg-muted px-2 py-1 rounded">
                  Date | Date[] | {`{ from: Date; to?: Date }`} | undefined
                </code>
              </div>
              
              <div className="border rounded-lg p-4">
                <h4 className="font-medium mb-2">events</h4>
                <p className="text-sm text-muted-foreground mb-2">
                  日历事件列表
                </p>
                <code className="text-xs bg-muted px-2 py-1 rounded">
                  CalendarEvent[]
                </code>
              </div>
              
              <div className="border rounded-lg p-4">
                <h4 className="font-medium mb-2">legends</h4>
                <p className="text-sm text-muted-foreground mb-2">
                  图例配置列表
                </p>
                <code className="text-xs bg-muted px-2 py-1 rounded">
                  CalendarLegendItem[]
                </code>
              </div>
            </div>
          </div>
          
          <div>
            <h3 className="text-lg font-semibold mb-3">事件回调</h3>
            <div className="space-y-4">
              <div className="border rounded-lg p-4">
                <h4 className="font-medium mb-2">handlers.onSelect</h4>
                <p className="text-sm text-muted-foreground mb-2">
                  日期选择回调函数
                </p>
                <code className="text-xs bg-muted px-2 py-1 rounded">
                  (value: CalendarValue) =&gt; void
                </code>
              </div>
              
              <div className="border rounded-lg p-4">
                <h4 className="font-medium mb-2">handlers.onEventClick</h4>
                <p className="text-sm text-muted-foreground mb-2">
                  事件点击回调函数
                </p>
                <code className="text-xs bg-muted px-2 py-1 rounded">
                  (event: CalendarEvent, date: Date) =&gt; void
                </code>
              </div>
              
              <div className="border rounded-lg p-4">
                <h4 className="font-medium mb-2">handlers.onDateClick</h4>
                <p className="text-sm text-muted-foreground mb-2">
                  日期点击回调函数
                </p>
                <code className="text-xs bg-muted px-2 py-1 rounded">
                  (date: Date) =&gt; void
                </code>
              </div>
            </div>
          </div>
          
          <div>
            <h3 className="text-lg font-semibold mb-3">功能配置</h3>
            <div className="space-y-4">
              <div className="border rounded-lg p-4">
                <h4 className="font-medium mb-2">features.showEvents</h4>
                <p className="text-sm text-muted-foreground mb-2">
                  是否显示事件
                </p>
                <code className="text-xs bg-muted px-2 py-1 rounded">boolean</code>
                <p className="text-xs text-muted-foreground mt-1">默认: true</p>
              </div>
              
              <div className="border rounded-lg p-4">
                <h4 className="font-medium mb-2">features.showLegend</h4>
                <p className="text-sm text-muted-foreground mb-2">
                  是否显示图例
                </p>
                <code className="text-xs bg-muted px-2 py-1 rounded">boolean</code>
                <p className="text-xs text-muted-foreground mt-1">默认: false</p>
              </div>
              
              <div className="border rounded-lg p-4">
                <h4 className="font-medium mb-2">features.legendPosition</h4>
                <p className="text-sm text-muted-foreground mb-2">
                  图例位置
                </p>
                <code className="text-xs bg-muted px-2 py-1 rounded">
                  "top" | "bottom" | "left" | "right"
                </code>
                <p className="text-xs text-muted-foreground mt-1">默认: "bottom"</p>
              </div>
              
              <div className="border rounded-lg p-4">
                <h4 className="font-medium mb-2">features.maxEventsPerDay</h4>
                <p className="text-sm text-muted-foreground mb-2">
                  每日最大显示事件数量
                </p>
                <code className="text-xs bg-muted px-2 py-1 rounded">number</code>
                <p className="text-xs text-muted-foreground mt-1">默认: 3</p>
              </div>
            </div>
          </div>
          
          <div>
            <h3 className="text-lg font-semibold mb-3">样式配置</h3>
            <div className="space-y-4">
              <div className="border rounded-lg p-4">
                <h4 className="font-medium mb-2">style.className</h4>
                <p className="text-sm text-muted-foreground mb-2">
                  日历容器自定义类名
                </p>
                <code className="text-xs bg-muted px-2 py-1 rounded">string</code>
              </div>
              
              <div className="border rounded-lg p-4">
                <h4 className="font-medium mb-2">style.showBorder</h4>
                <p className="text-sm text-muted-foreground mb-2">
                  是否显示边框
                </p>
                <code className="text-xs bg-muted px-2 py-1 rounded">boolean</code>
                <p className="text-xs text-muted-foreground mt-1">默认: true</p>
              </div>
              
              <div className="border rounded-lg p-4">
                <h4 className="font-medium mb-2">style.showShadow</h4>
                <p className="text-sm text-muted-foreground mb-2">
                  是否显示阴影
                </p>
                <code className="text-xs bg-muted px-2 py-1 rounded">boolean</code>
                <p className="text-xs text-muted-foreground mt-1">默认: false</p>
              </div>
            </div>
          </div>
        </div>
      }
    />
  )
}

/**
 * 示例代码集合
 * 
 * 这是示例代码的标准管理模板，展示了如何组织和管理组件的各种使用示例
 */

import React from "react"
import { Button } from "@/components/ui/button"
import { Star, Heart, Settings, User, Download, Search } from "lucide-react"

// ============================================================================
// 基础示例
// ============================================================================

export const basicExample = {
  id: "basic-example",
  title: "基础用法",
  description: "组件的基本使用方式",
  code: `
import React from "react";
import { Button } from "@/components/ui/button";

function BasicExample() {
  return (
    <div className="flex flex-wrap items-center gap-4">
      <Button>默认按钮</Button>
      <Button variant="default">主要按钮</Button>
      <Button variant="secondary">次要按钮</Button>
      <Button variant="destructive">危险按钮</Button>
    </div>
  );
}

render(<BasicExample />);
  `,
  scope: { Button, React },
}

// ============================================================================
// 尺寸示例
// ============================================================================

export const sizeExample = {
  id: "size-example",
  title: "不同尺寸",
  description: "展示组件的不同尺寸选项",
  code: `
import React from "react";
import { Button } from "@/components/ui/button";

function SizeExample() {
  return (
    <div className="flex flex-wrap items-center gap-4">
      <Button size="sm" variant="default">小型按钮</Button>
      <Button size="default" variant="default">中型按钮</Button>
      <Button size="lg" variant="default">大型按钮</Button>
    </div>
  );
}

render(<SizeExample />);
  `,
  scope: { Button, React },
}

// ============================================================================
// 图标示例
// ============================================================================

export const iconExample = {
  id: "icon-example",
  title: "带图标的按钮",
  description: "展示如何在组件中使用图标",
  code: `
import React from "react";
import { Button } from "@/components/ui/button";
import { Star, Heart, Settings, User, Download, Search } from "lucide-react";

function IconExample() {
  return (
    <div className="flex flex-wrap items-center gap-4">
      <Button variant="default">
        <Star className="mr-2 h-4 w-4" />
        收藏
      </Button>
      <Button variant="secondary">
        <Heart className="mr-2 h-4 w-4" />
        喜欢
      </Button>
      <Button variant="outline">
        <Settings className="mr-2 h-4 w-4" />
        设置
      </Button>
      <Button variant="ghost">
        <User className="mr-2 h-4 w-4" />
        用户
      </Button>
      <Button variant="default">
        <Download className="mr-2 h-4 w-4" />
        下载
      </Button>
      <Button variant="secondary">
        <Search className="mr-2 h-4 w-4" />
        搜索
      </Button>
    </div>
  );
}

render(<IconExample />);
  `,
  scope: { Button, Star, Heart, Settings, User, Download, Search, React },
}

// ============================================================================
// 状态示例
// ============================================================================

export const stateExample = {
  id: "state-example",
  title: "不同状态",
  description: "展示组件的不同状态，包括禁用状态",
  code: `
import React from "react";
import { Button } from "@/components/ui/button";
import { Star } from "lucide-react";

function StateExample() {
  return (
    <div className="space-y-4">
      <div className="flex flex-wrap items-center gap-4">
        <Button variant="default">正常状态</Button>
        <Button variant="default" disabled>禁用状态</Button>
      </div>

      <div className="flex flex-wrap items-center gap-4">
        <Button variant="secondary">
          <Star className="mr-2 h-4 w-4" />
          带图标
        </Button>
        <Button variant="secondary" disabled>
          <Star className="mr-2 h-4 w-4" />
          禁用带图标
        </Button>
      </div>

      <div className="flex flex-wrap items-center gap-4">
        <Button variant="outline">只有文本</Button>
        <Button variant="default" size="icon">
          <Star className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}

render(<StateExample />);
  `,
  scope: { Button, Star, React },
}

// ============================================================================
// 交互示例
// ============================================================================

export const interactiveExample = {
  id: "interactive-example",
  title: "交互功能",
  description: "展示组件的交互功能和事件处理",
  code: `
import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Star, Heart } from "lucide-react";

function InteractiveExample() {
  const [clickCount, setClickCount] = useState(0);
  const [lastClicked, setLastClicked] = useState("");

  const handleClick = (value) => {
    setClickCount(prev => prev + 1);
    setLastClicked(value);
    console.log('点击了:', value);
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-wrap items-center gap-4">
        <Button
          variant="default"
          onClick={() => handleClick("星星按钮")}
        >
          <Star className="mr-2 h-4 w-4" />
          点击我
        </Button>
        <Button
          variant="secondary"
          onClick={() => handleClick("爱心按钮")}
        >
          <Heart className="mr-2 h-4 w-4" />
          也点击我
        </Button>
      </div>

      <div className="p-4 bg-muted rounded-md">
        <p className="text-sm">
          <strong>点击次数:</strong> {clickCount}
        </p>
        <p className="text-sm">
          <strong>最后点击:</strong> {lastClicked || "无"}
        </p>
      </div>
    </div>
  );
}

render(<InteractiveExample />);
  `,
  scope: { Button, Star, Heart, React, useState: React.useState },
}

// ============================================================================
// 自定义样式示例
// ============================================================================

export const customStyleExample = {
  id: "custom-style-example",
  title: "自定义样式",
  description: "展示如何通过className自定义组件样式",
  code: `
import React from "react";
import { Button } from "@/components/ui/button";
import { Star } from "lucide-react";

function CustomStyleExample() {
  return (
    <div className="flex flex-wrap items-center gap-4">
      <Button
        variant="outline"
        className="border-2 border-blue-500 hover:border-blue-600"
      >
        自定义边框
      </Button>
      <Button
        variant="default"
        className="rounded-full"
      >
        圆角按钮
      </Button>
      <Button
        variant="secondary"
        className="shadow-lg hover:shadow-xl transition-shadow"
      >
        阴影效果
      </Button>
      <Button
        variant="outline"
        className="bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:from-purple-600 hover:to-pink-600 border-0"
      >
        渐变背景
      </Button>
    </div>
  );
}

render(<CustomStyleExample />);
  `,
  scope: { Button, Star, React },
}

// ============================================================================
// 统一导出所有示例
// ============================================================================

export const allExamples = [
  basicExample,
  sizeExample,
  iconExample,
  stateExample,
  interactiveExample,
  customStyleExample,
]

// ============================================================================
// 按类别导出示例
// ============================================================================

export const basicExamples = [basicExample, sizeExample]
export const advancedExamples = [iconExample, stateExample, interactiveExample]
export const customizationExamples = [customStyleExample]

// ============================================================================
// 示例元数据
// ============================================================================

export const exampleMetadata = {
  total: allExamples.length,
  categories: {
    basic: basicExamples.length,
    advanced: advancedExamples.length,
    customization: customizationExamples.length,
  },
  tags: ["button", "interactive", "icon", "customizable"],
  lastUpdated: "2024-01-01",
}

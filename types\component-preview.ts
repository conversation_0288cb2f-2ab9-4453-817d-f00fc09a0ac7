export interface CodeFile {
  name: string;
  language: string;
  code: string;
}

export interface ComponentExample {
  id: string;
  title: string;
  description?: string;
  code: string;
  extraFiles?: CodeFile[];
  scope?: Record<string, any>;
}

export interface ComponentPreviewProps {
  code: string;
  scope?: Record<string, any>;
  className?: string;
}

export interface NavItem {
  id: string;
  title: string;
  isSubItem?: boolean;
}

export interface ComponentPreviewContainerProps {
  title: string;
  description?: string;
  whenToUse?: string;
  examples: ComponentExample[];
  apiDocs?: React.ReactNode;
  className?: string;
} 
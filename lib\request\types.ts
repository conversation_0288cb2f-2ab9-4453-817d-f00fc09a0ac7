import type { AxiosRequestConfig, AxiosResponse as OriginalAxiosResponse, AxiosError, CancelTokenSource, AxiosRequestHeaders, AxiosResponseHeaders } from 'axios';

/**
 * 扩展 AxiosResponse 接口，确保包含所有必要字段
 */
export interface AxiosResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: AxiosResponseHeaders;
  config: AxiosRequestConfig;
  request?: any;
}

/**
 * 请求配置选项，扩展自 AxiosRequestConfig
 */
export interface RequestOptionsInit extends Omit<AxiosRequestConfig, 'data'> {
  /** 请求URL */
  url?: string;
  /** 请求方法 */
  method?: string;
  /** 请求参数 */
  params?: any;
  /** 请求头 */
  headers?: Record<string, string>;
  /** 取消令牌 */
  cancelToken?: any;
  /** 请求信号 */
  signal?: AbortSignal;
  /** 是否跳过错误处理 */
  skipErrorHandler?: boolean;
  /** 是否获取原始响应对象 */
  getResponse?: boolean;
  /** 请求拦截器 */
  requestInterceptors?: RequestInterceptor[];
  /** 响应拦截器 */
  responseInterceptors?: ResponseInterceptor[];
  /** 请求体数据，任意类型 */
  data?: any;
  /** 是否显示全局loading */
  showLoading?: boolean;
  /** 加载提示文本 */
  loadingText?: string;
  /** 是否显示成功提示 */
  showSuccessMessage?: boolean;
  /** 成功提示文本 */
  successMessage?: string;
  /** 是否显示错误提示 */
  showErrorMessage?: boolean;
  /** 错误提示文本 */
  errorMessage?: string;
}

/**
 * 请求配置
 */
export interface RequestConfig extends RequestOptionsInit {
  /** 错误处理配置 */
  errorConfig?: {
    /** 错误处理函数 */
    errorHandler?: (error: ResponseError) => void;
    /** 错误抛出函数 */
    errorThrower?: (res: any) => void;
  };
  /** 请求拦截器 */
  requestInterceptors?: RequestInterceptor[];
  /** 响应拦截器 */
  responseInterceptors?: ResponseInterceptor[];
  /** 全局请求状态配置 */
  requestStatusConfig?: {
    /** 默认是否显示全局loading */
    defaultShowLoading?: boolean;
    /** 默认是否显示成功提示 */
    defaultShowSuccessMessage?: boolean;
    /** 默认是否显示错误提示 */
    defaultShowErrorMessage?: boolean;
  };
}

/**
 * 请求选项（不包含URL）
 */
export type RequestOptions = Omit<RequestOptionsInit, 'url'>;

/**
 * 请求拦截器类型
 */
export type RequestInterceptor = 
  | ((config: RequestOptionsInit) => RequestOptionsInit | Promise<RequestOptionsInit>)
  | [(config: RequestOptionsInit) => RequestOptionsInit | Promise<RequestOptionsInit>, (error: any) => any]
  | [(config: RequestOptionsInit) => RequestOptionsInit | Promise<RequestOptionsInit>];

/**
 * 响应拦截器类型
 */
export type ResponseInterceptor = 
  | ((response: AxiosResponse) => AxiosResponse | Promise<AxiosResponse>)
  | [(response: AxiosResponse) => AxiosResponse | Promise<AxiosResponse>, (error: any) => any]
  | [(response: AxiosResponse) => AxiosResponse | Promise<AxiosResponse>];

/**
 * 错误类型的响应状态码枚举
 */
export enum ErrorShowType {
  SILENT = 0, // 不提示错误
  WARN_MESSAGE = 1, // 警告提示
  ERROR_MESSAGE = 2, // 错误提示
  NOTIFICATION = 3, // 通知提示
  REDIRECT = 9, // 页面跳转
}

/**
 * 响应错误接口
 */
export interface ResponseError {
  name: string;
  message: string;
  msg?: string;
  code?: string;
  data?: any;
  response?: AxiosResponse;
  request?: RequestOptionsInit;
  isAxiosError?: boolean;
  info?: {
    errorCode?: string;
    errorMessage?: string;
    showType?: ErrorShowType;
    data?: any;
  };
  type?: string;
  toJSON: () => object;
}

/**
 * 响应数据标准结构接口
 */
export interface ResponseStructure<T = any> {
  code: string;
  success: boolean;
  data: T;
  msg?: string;
  message?: string;
  errorCode?: string;
  errorMessage?: string;
  showType?: ErrorShowType;
  traceId?: string;
  host?: string;
}

/**
 * 请求响应格式类型
 */
export interface RequestResponse<T = any> {
  data: T;
  response: AxiosResponse;
}

/**
 * 请求取消令牌源接口
 */
export interface RequestCancelTokenSource {
  token: any;
  cancel: (reason?: string) => void;
}

/**
 * 基础请求方法类型
 */
export type RequestFunctionBase = <T = any>(url: string, data?: any, options?: Omit<RequestOptionsInit, 'data'>) => Promise<T>;

/**
 * HTTP方法类型
 */
export type HttpMethod = 'get' | 'post' | 'delete' | 'put' | 'patch' | 'head' | 'options';

/**
 * 请求方法类型
 * - GET/HEAD/DELETE/OPTIONS: 数据通过params传递 (url, options)
 * - POST/PUT/PATCH: 数据通过data传递 (url, data, options)
 */
export type HttpRequestFunction = {
  <T = any>(url: string, options?: RequestOptionsInit): Promise<T>;
  <T = any>(url: string, data: any, options?: Omit<RequestOptionsInit, 'data'>): Promise<T>;
};

/**
 * 拦截器实例接口
 */
export interface Interceptors {
  request: {
    use: (
      onFulfilled?: ((config: RequestOptionsInit) => RequestOptionsInit | Promise<RequestOptionsInit>) | null,
      onRejected?: ((error: any) => any) | null,
      options?: { global: boolean }
    ) => number;
    eject: (id: number) => void;
  };
  response: {
    use: (
      onFulfilled?: ((response: AxiosResponse) => AxiosResponse | Promise<AxiosResponse>) | null,
      onRejected?: ((error: any) => any) | null,
      options?: { global: boolean }
    ) => number;
    eject: (id: number) => void;
  };
}

/**
 * 取消令牌相关方法
 */
export interface CancelTokenStatic {
  source: () => RequestCancelTokenSource;
  new(executor: (cancel: (reason?: string) => void) => void): any;
}

/**
 * 完整的请求方法接口，包含所有HTTP方法
 */
export interface RequestMethod {
  <T = any>(url: string, options?: RequestOptionsInit): Promise<T>;
  <T = any>(url: string, data: any, options?: Omit<RequestOptionsInit, 'data'>): Promise<T>;
  
  // HTTP方法 - 统一支持两种调用方式
  get: HttpRequestFunction;
  post: HttpRequestFunction;
  delete: HttpRequestFunction;
  put: HttpRequestFunction;
  patch: HttpRequestFunction;
  head: HttpRequestFunction;
  options: HttpRequestFunction;
}

/**
 * 扩展的请求实例，包含拦截器和其他扩展方法
 */
export interface RequestInstance extends RequestMethod {
  interceptors: Interceptors;
  CancelToken: CancelTokenStatic;
  isCancel: (value: any) => boolean;
  extendOptions: (options: RequestConfig) => void;
  request: HttpRequestFunction;
} 
"use client"

import { ReactNode } from "react"
import { cn } from "@/lib/utils"
import { Skeleton } from "@/components/ui/skeleton"
import { Card, CardContent } from "@/components/ui/card"

// ============================================================================
// 类型定义 - 单文件组件类型定义直接在组件文件内
// ============================================================================

/**
 * 骨架屏动画类型
 */
export type SkeletonAnimation = "pulse" | "wave" | "none"

/**
 * 文本骨架屏组件属性
 */
export interface SkeletonTextProps {
  /**
   * 行数
   * @default 3
   */
  lines?: number

  /**
   * 行高
   * @default "1rem"
   */
  lineHeight?: string

  /**
   * 行间距
   * @default "0.5rem"
   */
  spacing?: string

  /**
   * 最后一行宽度百分比
   * @default 70
   */
  lastLineWidth?: number

  /**
   * 是否加载中
   * @default true
   */
  loading?: boolean

  /**
   * 子组件（加载完成后显示）
   */
  children?: ReactNode

  /**
   * 自定义类名
   */
  className?: string

  /**
   * 动画类型
   * @default "pulse"
   */
  animation?: SkeletonAnimation

  /**
   * 自定义行宽度数组
   */
  lineWidths?: string[]
}

/**
 * 卡片骨架屏组件属性
 */
export interface SkeletonCardProps {
  /**
   * 是否显示头像
   * @default true
   */
  showAvatar?: boolean

  /**
   * 头像尺寸
   * @default "3rem"
   */
  avatarSize?: string

  /**
   * 是否显示标题
   * @default true
   */
  showTitle?: boolean

  /**
   * 标题高度
   * @default "1.5rem"
   */
  titleHeight?: string

  /**
   * 内容行数
   * @default 3
   */
  lines?: number

  /**
   * 内容行高
   * @default "1rem"
   */
  lineHeight?: string

  /**
   * 是否加载中
   * @default true
   */
  loading?: boolean

  /**
   * 子组件（加载完成后显示）
   */
  children?: ReactNode

  /**
   * 自定义类名
   */
  className?: string

  /**
   * 动画类型
   * @default "pulse"
   */
  animation?: SkeletonAnimation

  /**
   * 是否显示操作按钮
   * @default false
   */
  showActions?: boolean

  /**
   * 操作按钮数量
   * @default 2
   */
  actionCount?: number
}

/**
 * 列表骨架屏组件属性
 */
export interface SkeletonListProps {
  /**
   * 列表项数量
   * @default 3
   */
  count?: number

  /**
   * 列表项高度
   * @default "auto"
   */
  itemHeight?: string

  /**
   * 列表项间距
   * @default "1rem"
   */
  spacing?: string

  /**
   * 是否显示头像
   * @default true
   */
  showAvatar?: boolean

  /**
   * 头像尺寸
   * @default "2.5rem"
   */
  avatarSize?: string

  /**
   * 是否加载中
   * @default true
   */
  loading?: boolean

  /**
   * 子组件（加载完成后显示）
   */
  children?: ReactNode

  /**
   * 自定义类名
   */
  className?: string

  /**
   * 动画类型
   * @default "pulse"
   */
  animation?: SkeletonAnimation

  /**
   * 是否显示操作按钮
   * @default true
   */
  showActions?: boolean
}

/**
 * 表格骨架屏组件属性
 */
export interface SkeletonTableProps {
  /**
   * 表格行数
   * @default 5
   */
  rows?: number

  /**
   * 表格列数
   * @default 4
   */
  columns?: number

  /**
   * 是否显示表头
   * @default true
   */
  showHeader?: boolean

  /**
   * 表头高度
   * @default "1.5rem"
   */
  headerHeight?: string

  /**
   * 单元格高度
   * @default "1rem"
   */
  cellHeight?: string

  /**
   * 是否加载中
   * @default true
   */
  loading?: boolean

  /**
   * 子组件（加载完成后显示）
   */
  children?: ReactNode

  /**
   * 自定义类名
   */
  className?: string

  /**
   * 动画类型
   * @default "pulse"
   */
  animation?: SkeletonAnimation

  /**
   * 自定义列宽度数组
   */
  columnWidths?: string[]
}

// ============================================================================
// 组件实现
// ============================================================================

/**
 * 文本骨架屏组件
 *
 * 用于模拟文本内容的加载状态
 *
 * @example
 * ```tsx
 * // 基础用法
 * <SkeletonText lines={3} />
 *
 * // 自定义行宽度
 * <SkeletonText
 *   lines={4}
 *   lineWidths={["100%", "80%", "60%", "40%"]}
 * />
 *
 * // 条件渲染
 * <SkeletonText loading={isLoading}>
 *   <p>实际内容</p>
 * </SkeletonText>
 * ```
 */
export function SkeletonText({
  lines = 3,
  lineHeight = "1rem",
  spacing = "0.5rem",
  lastLineWidth = 70,
  loading = true,
  children,
  className,
  animation = "pulse",
  lineWidths
}: SkeletonTextProps) {
  if (!loading) return <>{children}</>

  return (
    <div className={cn("space-y-2", className)}>
      {Array.from({ length: lines }).map((_, i) => {
        const isLastLine = i === lines - 1
        let width: string

        if (lineWidths && lineWidths[i]) {
          width = lineWidths[i]
        } else {
          width = isLastLine ? `${lastLineWidth}%` : "100%"
        }

        return (
          <Skeleton
            key={i}
            className={cn(
              animation === "pulse" ? "animate-pulse" : "",
              animation === "wave" ? "animate-[skeleton-wave_2s_infinite]" : "",
              "block"
            )}
            style={{
              height: lineHeight,
              width,
              marginBottom: i === lines - 1 ? 0 : spacing
            }}
          />
        )
      })}
    </div>
  )
}

/**
 * 卡片骨架屏组件
 *
 * 用于模拟卡片内容的加载状态
 *
 * @example
 * ```tsx
 * // 基础用法
 * <SkeletonCard />
 *
 * // 不显示头像
 * <SkeletonCard showAvatar={false} />
 *
 * // 带操作按钮
 * <SkeletonCard showActions={true} actionCount={3} />
 * ```
 */
export function SkeletonCard({
  showAvatar = true,
  avatarSize = "3rem",
  showTitle = true,
  titleHeight = "1.5rem",
  lines = 3,
  lineHeight = "1rem",
  loading = true,
  children,
  className,
  animation = "pulse",
  showActions = false,
  actionCount = 2
}: SkeletonCardProps) {
  if (!loading) return <>{children}</>

  const commonClassName = cn(
    animation === "pulse" ? "animate-pulse" : "",
    animation === "wave" ? "animate-[skeleton-wave_2s_infinite]" : ""
  )

  return (
    <div className={cn("w-full", className)}>
      <div className="flex gap-4">
        {showAvatar && (
          <Skeleton
            className={commonClassName}
            style={{
              width: avatarSize,
              height: avatarSize,
              borderRadius: "50%"
            }}
          />
        )}

        <div className="flex-1 space-y-2">
          {showTitle && (
            <Skeleton
              className={commonClassName}
              style={{
                height: titleHeight,
                width: "60%"
              }}
            />
          )}

          <SkeletonText
            lines={lines}
            lineHeight={lineHeight}
            animation={animation}
          />

          {showActions && (
            <div className="flex gap-2 pt-2">
              {Array.from({ length: actionCount }).map((_, i) => (
                <Skeleton
                  key={i}
                  className={commonClassName}
                  style={{
                    height: "2rem",
                    width: "4rem"
                  }}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

/**
 * 表格骨架屏
 */
export function SkeletonTable({
  rows = 5,
  columns = 4,
  showHeader = true,
  headerHeight = "1.5rem",
  cellHeight = "1rem",
  loading = true,
  children,
  className,
  animation = "pulse"
}: SkeletonTableProps) {
  if (!loading) return <>{children}</>

  const commonClassName = cn(
    animation === "pulse" ? "animate-pulse" : "",
    animation === "wave" ? "animate-[skeleton-wave_2s_infinite]" : ""
  )

  return (
    <div className={cn("w-full space-y-3", className)}>
      {/* 表头 */}
      {showHeader && (
        <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
          {Array.from({ length: columns }).map((_, i) => (
            <Skeleton 
              key={i}
              className={commonClassName}
              style={{ height: headerHeight }} 
            />
          ))}
        </div>
      )}
      
      {/* 表格行 */}
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div 
          key={rowIndex}
          className="grid gap-4" 
          style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}
        >
          {Array.from({ length: columns }).map((_, colIndex) => (
            <Skeleton 
              key={colIndex}
              className={commonClassName}
              style={{ height: cellHeight }} 
            />
          ))}
        </div>
      ))}
    </div>
  )
}

/**
 * 列表骨架屏
 */
export function SkeletonList({
  count = 3,
  itemHeight = "auto",
  spacing = "1rem",
  showAvatar = true,
  avatarSize = "2.5rem",
  loading = true,
  children,
  className,
  animation = "pulse"
}: SkeletonListProps) {
  if (!loading) return <>{children}</>

  const commonClassName = cn(
    animation === "pulse" ? "animate-pulse" : "",
    animation === "wave" ? "animate-[skeleton-wave_2s_infinite]" : ""
  )

  return (
    <div 
      className={cn("w-full", className)}
      style={{ 
        display: "flex", 
        flexDirection: "column", 
        gap: spacing
      }}
    >
      {Array.from({ length: count }).map((_, i) => (
        <div 
          key={i}
          className="flex items-start gap-4"
          style={{ height: itemHeight !== "auto" ? itemHeight : undefined }}
        >
          {showAvatar && (
            <Skeleton 
              className={commonClassName}
              style={{ 
                width: avatarSize, 
                height: avatarSize,
                borderRadius: "0.5rem"
              }} 
            />
          )}
          
          <div className="flex-1 space-y-2">
            <Skeleton className={commonClassName} style={{ height: "1rem", width: "50%" }} />
            <Skeleton className={commonClassName} style={{ height: "0.75rem", width: "75%" }} />
            <Skeleton className={commonClassName} style={{ height: "0.75rem", width: "33%" }} />
          </div>
          
          <Skeleton className={commonClassName} style={{ height: "2rem", width: "4rem" }} />
        </div>
      ))}
    </div>
  )
}

/**
 * 预定义的骨架屏组件集合
 */
export const SkeletonComponents = {
  /**
   * 用户卡片骨架屏
   */
  UserCard: ({ className }: { className?: string }) => (
    <Card className={className}>
      <CardContent className="p-6">
        <div className="flex items-center space-x-4">
          <Skeleton className="h-12 w-12 rounded-full" />
          <div className="space-y-2 flex-1">
            <Skeleton className="h-4 w-1/3" />
            <Skeleton className="h-3 w-1/2" />
          </div>
        </div>
      </CardContent>
    </Card>
  ),

  /**
   * 文章卡片骨架屏
   */
  ArticleCard: ({ className }: { className?: string }) => (
    <Card className={className}>
      <CardContent className="p-6 space-y-4">
        <Skeleton className="h-48 w-full rounded-md" />
        <div className="space-y-2">
          <Skeleton className="h-6 w-3/4" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-2/3" />
        </div>
        <div className="flex items-center space-x-4">
          <Skeleton className="h-8 w-8 rounded-full" />
          <div className="space-y-1 flex-1">
            <Skeleton className="h-3 w-1/4" />
            <Skeleton className="h-3 w-1/3" />
          </div>
        </div>
      </CardContent>
    </Card>
  ),

  /**
   * 表格骨架屏
   */
  Table: ({ className }: { className?: string }) => (
    <Card className={className}>
      <CardContent className="p-6">
        <SkeletonTable rows={4} columns={4} />
      </CardContent>
    </Card>
  ),

  /**
   * 列表骨架屏
   */
  List: ({ className }: { className?: string }) => (
    <Card className={className}>
      <CardContent className="p-6">
        <SkeletonList count={3} />
      </CardContent>
    </Card>
  )
}

// ============================================================================
// 类型导出
// ============================================================================

export type {
  SkeletonAnimation,
  SkeletonTextProps,
  SkeletonCardProps,
  SkeletonListProps,
  SkeletonTableProps
}

// ============================================================================
// 常量导出
// ============================================================================

/**
 * 支持的骨架屏动画类型
 */
export const SKELETON_ANIMATIONS = ['pulse', 'wave', 'none'] as const

/**
 * 常用骨架屏尺寸
 */
export const SKELETON_SIZES = {
  xs: '0.75rem',
  sm: '1rem',
  md: '1.25rem',
  lg: '1.5rem',
  xl: '2rem',
  '2xl': '2.5rem',
} as const

/**
 * 常用头像尺寸
 */
export const AVATAR_SIZES = {
  xs: '1.5rem',
  sm: '2rem',
  md: '2.5rem',
  lg: '3rem',
  xl: '4rem',
} as const

/**
 * 默认骨架屏文本配置
 */
export const DEFAULT_SKELETON_TEXT_CONFIG = {
  lines: 3,
  lineHeight: '1rem',
  spacing: '0.5rem',
  lastLineWidth: 70,
  animation: 'pulse' as const,
} satisfies Partial<SkeletonTextProps>

/**
 * 默认骨架屏卡片配置
 */
export const DEFAULT_SKELETON_CARD_CONFIG = {
  showAvatar: true,
  avatarSize: '3rem',
  showTitle: true,
  titleHeight: '1.5rem',
  lines: 3,
  lineHeight: '1rem',
  animation: 'pulse' as const,
  showActions: false,
  actionCount: 2,
} satisfies Partial<SkeletonCardProps>

/**
 * 默认骨架屏列表配置
 */
export const DEFAULT_SKELETON_LIST_CONFIG = {
  count: 3,
  itemHeight: 'auto',
  spacing: '1rem',
  showAvatar: true,
  avatarSize: '2.5rem',
  animation: 'pulse' as const,
  showActions: true,
} satisfies Partial<SkeletonListProps>

/**
 * 默认骨架屏表格配置
 */
export const DEFAULT_SKELETON_TABLE_CONFIG = {
  rows: 5,
  columns: 4,
  showHeader: true,
  headerHeight: '1.5rem',
  cellHeight: '1rem',
  animation: 'pulse' as const,
} satisfies Partial<SkeletonTableProps>

/**
 * 常用行宽度模式
 */
export const LINE_WIDTH_PATTERNS = {
  decreasing: ['100%', '80%', '60%', '40%'],
  random: ['100%', '70%', '90%', '50%'],
  uniform: ['100%', '100%', '100%', '100%'],
  title: ['60%', '100%', '80%'],
} as const
/**
 * 加载组件示例代码集合
 * 
 * 按照新规范管理示例代码，避免在页面文件中硬编码
 */

import React from "react"
import { 
  LoadingSpinner, 
  LoadingButton, 
  SkeletonList, 
  SkeletonCard, 
  FullPageLoader, 
  ProgressLoader,
  LoadingContainer
} from "@/components/common-custom/loading"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

// ============================================================================
// 加载旋转器示例
// ============================================================================

export const spinnerExample = {
  id: "loading-spinner",
  title: "加载旋转器",
  description: "不同大小和样式的加载旋转器",
  code: `
import React from "react";
import { LoadingSpinner } from "@/components/common-custom/loading";

function SpinnerExample() {
  return (
    <div className="space-y-6">
      <div>
        <h4 className="text-sm font-medium mb-3">不同大小</h4>
        <div className="flex items-center gap-4">
          <LoadingSpinner size="xs" />
          <LoadingSpinner size="sm" />
          <LoadingSpinner size="md" />
          <LoadingSpinner size="lg" />
          <LoadingSpinner size="xl" />
        </div>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-3">不同颜色</h4>
        <div className="flex items-center gap-4">
          <LoadingSpinner variant="default" />
          <LoadingSpinner variant="primary" />
          <LoadingSpinner variant="success" />
          <LoadingSpinner variant="warning" />
          <LoadingSpinner variant="danger" />
        </div>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-3">带文本</h4>
        <div className="flex gap-6">
          <LoadingSpinner showText text="加载中..." />
          <LoadingSpinner variant="circular" showText text="处理中..." />
        </div>
      </div>
    </div>
  );
}

render(<SpinnerExample />);
  `,
  scope: { LoadingSpinner, React },
}

// ============================================================================
// 加载按钮示例
// ============================================================================

export const buttonExample = {
  id: "loading-button",
  title: "加载按钮",
  description: "支持加载状态和成功状态的按钮",
  code: `
import React, { useState } from "react";
import { LoadingButton } from "@/components/common-custom/loading";

function ButtonExample() {
  const [loading1, setLoading1] = useState(false);
  const [loading2, setLoading2] = useState(false);
  const [success, setSuccess] = useState(false);
  
  const handleClick1 = () => {
    setLoading1(true);
    setTimeout(() => setLoading1(false), 2000);
  };
  
  const handleClick2 = () => {
    setLoading2(true);
    setTimeout(() => {
      setLoading2(false);
      setSuccess(true);
      setTimeout(() => setSuccess(false), 1500);
    }, 2000);
  };
  
  return (
    <div className="space-y-4">
      <div className="flex gap-4">
        <LoadingButton 
          loading={loading1} 
          onClick={handleClick1}
        >
          基础加载
        </LoadingButton>
        
        <LoadingButton 
          loading={loading2}
          success={success}
          successText="已保存"
          onClick={handleClick2}
          variant="default"
        >
          保存数据
        </LoadingButton>
      </div>
      
      <div className="flex gap-4">
        <LoadingButton 
          loading={true} 
          loadingText="提交中..."
          variant="default"
        >
          提交表单
        </LoadingButton>
        
        <LoadingButton 
          success={true}
          successText="操作成功"
          variant="outline"
        >
          完成操作
        </LoadingButton>
      </div>
    </div>
  );
}

render(<ButtonExample />);
  `,
  scope: { LoadingButton, React, useState: React.useState },
}

// ============================================================================
// 骨架屏示例
// ============================================================================

export const skeletonExample = {
  id: "skeleton-loading",
  title: "骨架屏加载",
  description: "用于内容加载时的占位显示",
  code: `
import React from "react";
import { SkeletonList, SkeletonCard } from "@/components/common-custom/loading";
import { Card, CardContent } from "@/components/ui/card";

function SkeletonExample() {
  return (
    <div className="space-y-6">
      <div>
        <h4 className="text-sm font-medium mb-3">列表骨架屏</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardContent className="pt-6">
              <h5 className="text-sm font-medium mb-3">带头像</h5>
              <SkeletonList count={3} withAvatar={true} />
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <h5 className="text-sm font-medium mb-3">无头像</h5>
              <SkeletonList count={3} withAvatar={false} />
            </CardContent>
          </Card>
        </div>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-3">卡片骨架屏</h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="pt-6">
              <SkeletonCard withImage={false} />
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <SkeletonCard withImage={true} imageHeight="h-32" />
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <SkeletonCard withAction={false} withImage={true} />
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

render(<SkeletonExample />);
  `,
  scope: { SkeletonList, SkeletonCard, Card, CardContent, React },
}

// ============================================================================
// 全页面加载示例
// ============================================================================

export const fullPageExample = {
  id: "full-page-loading",
  title: "全页面加载",
  description: "用于整个页面或大区域的加载状态",
  code: `
import React, { useState } from "react";
import { FullPageLoader } from "@/components/common-custom/loading";
import { Button } from "@/components/ui/button";

function FullPageExample() {
  const [status, setStatus] = useState("loading");
  
  const handleRetry = () => {
    setStatus("loading");
    setTimeout(() => {
      setStatus(Math.random() > 0.5 ? "success" : "error");
    }, 2000);
  };
  
  return (
    <div className="space-y-6">
      <div className="flex gap-2">
        <Button 
          size="sm" 
          variant="outline"
          onClick={() => setStatus("loading")}
        >
          加载状态
        </Button>
        <Button 
          size="sm" 
          variant="outline"
          onClick={() => setStatus("success")}
        >
          成功状态
        </Button>
        <Button 
          size="sm" 
          variant="outline"
          onClick={() => setStatus("error")}
        >
          错误状态
        </Button>
      </div>
      
      <div className="border rounded-lg min-h-[300px]">
        <FullPageLoader 
          status={status}
          text={
            status === "loading" ? "加载中..." :
            status === "success" ? "加载成功" : "加载失败"
          }
          subText={
            status === "loading" ? "正在获取数据，请稍候" :
            status === "success" ? "数据已成功加载" : "请检查网络连接"
          }
          showRetry={status === "error"}
          onRetry={handleRetry}
        />
      </div>
    </div>
  );
}

render(<FullPageExample />);
  `,
  scope: { FullPageLoader, Button, React, useState: React.useState },
}

// ============================================================================
// 统一导出所有示例
// ============================================================================

export const allExamples = [
  spinnerExample,
  buttonExample,
  skeletonExample,
  fullPageExample,
]

// ============================================================================
// 按类别导出示例
// ============================================================================

export const basicExamples = [spinnerExample, buttonExample]
export const advancedExamples = [skeletonExample, fullPageExample]

// ============================================================================
// 示例元数据
// ============================================================================

export const exampleMetadata = {
  total: allExamples.length,
  categories: {
    basic: basicExamples.length,
    advanced: advancedExamples.length,
  },
  tags: ["loading", "spinner", "skeleton", "button", "progress"],
  lastUpdated: "2024-01-01",
}

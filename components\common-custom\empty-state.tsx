"use client"

import { cn } from "@/lib/utils"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { LucideIcon } from "lucide-react"
import { ReactNode } from "react"

// ============================================================================
// 类型定义 - 单文件组件类型定义直接在组件文件内
// ============================================================================

/**
 * 空状态组件属性
 */
export interface EmptyStateProps {
  /**
   * 图标组件
   */
  icon?: LucideIcon

  /**
   * 自定义图标内容
   */
  customIcon?: ReactNode

  /**
   * 标题
   */
  title: string

  /**
   * 描述文本
   */
  description?: string

  /**
   * 操作按钮文本
   */
  actionLabel?: string

  /**
   * 操作按钮图标
   */
  actionIcon?: LucideIcon

  /**
   * 操作按钮点击回调
   */
  onAction?: () => void

  /**
   * 自定义操作内容
   */
  customAction?: ReactNode

  /**
   * 图标颜色
   * @default "text-muted-foreground"
   */
  iconColor?: string

  /**
   * 图标背景颜色
   * @default "bg-muted"
   */
  iconBgColor?: string

  /**
   * 图标大小
   * @default "md"
   */
  iconSize?: "sm" | "md" | "lg" | "xl" | number

  /**
   * 自定义类名
   */
  className?: string

  /**
   * 是否居中显示
   * @default true
   */
  centered?: boolean

  /**
   * 最大宽度
   * @default "24rem"
   */
  maxWidth?: string

  /**
   * 内边距
   * @default "py-8"
   */
  padding?: string

  /**
   * 是否显示边框
   * @default false
   */
  bordered?: boolean

  /**
   * 变体样式
   * @default "default"
   */
  variant?: "default" | "card" | "minimal"
}

export function EmptyState({
  icon: Icon,
  customIcon,
  title,
  description,
  actionLabel,
  actionIcon: ActionIcon,
  onAction,
  customAction,
  iconColor = "text-muted-foreground",
  iconBgColor = "bg-muted",
  iconSize = "md",
  className,
  centered = true,
  maxWidth = "24rem",
  padding = "py-8",
}: EmptyStateProps) {
  // 处理图标大小
  const getIconSize = () => {
    if (typeof iconSize === "number") {
      return `h-${iconSize} w-${iconSize}`
    }
    
    switch (iconSize) {
      case "sm":
        return "h-6 w-6"
      case "lg":
        return "h-10 w-10"
      case "md":
      default:
        return "h-8 w-8"
    }
  }
  
  const iconSizeClass = getIconSize()
  
  return (
    <div
      className={cn(
        "flex flex-col",
        centered && "items-center justify-center text-center",
        padding,
        className
      )}
      style={{ maxWidth }}
    >
      {/* 图标 */}
      {(Icon || customIcon) && (
        <div className={cn("mb-4 p-3 rounded-full", iconBgColor)}>
          {customIcon || (Icon && <Icon className={cn(iconSizeClass, iconColor)} />)}
        </div>
      )}
      
      {/* 标题 */}
      <h3 className="text-lg font-semibold mb-2">{title}</h3>
      
      {/* 描述 */}
      {description && (
        <p className="text-sm text-muted-foreground mb-4 max-w-sm">{description}</p>
      )}
      
      {/* 操作按钮 */}
      {customAction || (actionLabel && (
        <Button onClick={onAction} size="sm">
          {ActionIcon && <ActionIcon className="mr-2 h-4 w-4" />}
          {actionLabel}
        </Button>
      ))}
    </div>
  )
}

/**
 * 卡片式空状态组件
 */
export function EmptyStateCard({
  title,
  ...props
}: EmptyStateProps & { cardTitle?: string }) {
  return (
    <Card>
      <CardContent className="pt-6">
        <EmptyState
          title={title}
          {...props}
        />
      </CardContent>
    </Card>
  )
}

// ============================================================================
// 类型导出
// ============================================================================

export type { EmptyStateProps }

// ============================================================================
// 常量导出
// ============================================================================

/**
 * 支持的图标大小
 */
export const EMPTY_STATE_ICON_SIZES = ['sm', 'md', 'lg', 'xl'] as const

/**
 * 支持的变体样式
 */
export const EMPTY_STATE_VARIANTS = ['default', 'card', 'minimal'] as const

/**
 * 默认空状态配置
 */
export const DEFAULT_EMPTY_STATE_CONFIG = {
  iconColor: 'text-muted-foreground',
  iconBgColor: 'bg-muted',
  iconSize: 'md' as const,
  centered: true,
  maxWidth: '24rem',
  padding: 'py-8',
  bordered: false,
  variant: 'default' as const,
} satisfies Partial<EmptyStateProps>

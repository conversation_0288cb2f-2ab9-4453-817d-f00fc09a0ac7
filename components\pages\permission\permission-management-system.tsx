"use client"

import { useState, useEffect } from "react"
import { type PermissionNode, PermissionType, type PermissionConfig } from "@/types/permission"
import PermissionTree from "./permission-tree"
import PermissionForm from "./permission-form"
import { toast } from "@/components/ui/use-toast"
import { Button } from "@/components/ui/button"
import { Plus, RotateCcw, RefreshCw, Save } from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import DndProviderWrapper from "./dnd-provider"
import {Toaster, toast as sonnerToast} from "sonner";
import {
  fetchPermissionTree, 
  createPermissions,
  createNewPermissionNode,
} from "@/services/api/permissionRequestApi"
import PermissionConfigSelector from "@/components/pages/permission/permission-config-selector";
import { nanoid } from "nanoid";
import {getGroupsRequest} from "@/services/api/groupRequestApi";

export default function PermissionManagementSystem() {
  const [configs, setConfigs] = useState<PermissionConfig[]>([])
  const [activeConfigId, setActiveConfigId] = useState<string>("")
  const [selectedNode, setSelectedNode] = useState<PermissionNode | null>(null)
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [isResetDialogOpen, setIsResetDialogOpen] = useState(false);
  const [originalPermissions, setOriginalPermissions] = useState<PermissionNode[]>([]);

  // 初始化加载数据
  useEffect(() => {
    loadData()
  }, [])

  // 加载初始权限数据时保存原始副本
  useEffect(() => {
    if (activeConfig.permissions && activeConfig.permissions.length > 0) {
      setOriginalPermissions(JSON.parse(JSON.stringify(activeConfig.permissions)));
    }
  }, [activeConfigId]);

  // 修改加载数据函数，使用API服务
  const loadData = () => {
    setIsLoading(true)
    
    // 先获取权限配置列表
    getGroupsRequest({"moduleCode":"permission"})
      .then(async response => {
        // 处理API响应数据
        const groupData = response || [];

        if (groupData.length > 0) {
          // 将后端Group数据转换为PermissionConfig类型
          const configs: PermissionConfig[] = groupData.map(group => ({
            id: String(group.id), // 确保id是字符串类型
            code: group.code,
            name: group.name,
            description: group.description || '',
            permissions: [], // 初始化空的权限数组，后续加载
          }));

          setConfigs(configs)

          // 设置默认选中第一个配置
          const activeConfig = configs[0]
          setActiveConfigId(activeConfig.id)

          // 获取第一个配置的权限树
          try {
            const permissions = await fetchPermissionTree(activeConfig.code)

            // 更新权限树数据
            setConfigs(prev =>
                prev.map(config =>
                    config.id === activeConfig.id
                        ? {...config, permissions}
                        : config
                )
            )
          } catch (error) {
            console.error("获取权限树失败:", error)
            toast({
              title: "加载失败",
              description: "无法获取权限树数据",
              variant: "destructive",
            })
          }
        }
      })
      .catch(error => {
        console.error("Failed to load permissions data:", error)
        toast({
          title: "加载失败",
          description: "无法加载权限数据",
          variant: "destructive",
        })
      })
      .finally(() => {
        setIsLoading(false)
      })
  }

  // 获取当前活动的配置
  const activeConfig = configs.find((config) => config.id === activeConfigId) || {
    id: "",
    code: "",
    name: "",
    permissions: [],
  }

  // 获取节点的完整路径
  const getNodePath = (nodeId: string): string => {
    const path: string[] = []

    const findPath = (nodes: PermissionNode[], id: string, currentPath: string[] = []): boolean => {
      for (const node of nodes) {
        const newPath = [...currentPath, node.name]

        if (node.id === id) {
          path.push(...newPath)
          return true
        }

        if (node.children && node.children.length > 0) {
          if (findPath(node.children, id, newPath)) {
            return true
          }
        }
      }

      return false
    }

    findPath(activeConfig.permissions, nodeId)
    return path.join(" / ")
  }

  // 处理节点选择
  const handleNodeSelect = (node: PermissionNode) => {
    // 从树中查找最新的节点数据
    const findCurrentNode = (tree: PermissionNode[], id: string): PermissionNode | null => {
      for (const n of tree) {
        if (n.id === id) return n
        if (n.children && n.children.length > 0) {
          const found = findCurrentNode(n.children, id)
          if (found) return found
        }
      }
      return null
    }

    const currentNode = findCurrentNode(activeConfig.permissions, node.id) || node
    setSelectedNode(currentNode)
  }

  // 处理节点启用/禁用状态切换
  const handleToggleNodeEnabled = (node: PermissionNode) => {
    const updatedNode = { ...node, enabled: node.enabled === false }
    handleSavePermission(updatedNode)
  }

  // 生成权限标识
  const generatePermissionIdentifier = (node: PermissionNode): string => {
    // 如果是顶级节点
    if (!node.parentId) {
      return nanoid(5)
    }

    // 查找父节点的权限标识
    const findParentIdentifier = (nodes: PermissionNode[], parentId: string): string => {
      for (const n of nodes) {
        if (n.id === parentId) {
          return n.permissionId || ""
        }
        if (n.children && n.children.length > 0) {
          const found = findParentIdentifier(n.children, parentId)
          if (found) return found
        }
      }
      return ""
    }

    const parentIdentifier = findParentIdentifier(activeConfig.permissions, node.parentId)
    return `${parentIdentifier}:${nanoid(5)}`
  }

  // 处理保存权限
  const handleSavePermission = (updatedNode: PermissionNode) => {
    // 验证层级规则
    const validationError = validateNodeHierarchy(updatedNode)
    if (validationError) {
      toast({
        title: "操作失败",
        description: validationError,
        variant: "destructive",
      })
      return
    }

    // 检查是否更改了父级权限
    if (selectedNode && updatedNode.parentId !== selectedNode?.parentId) {
      // 如果更改了父级权限，需要从原位置移除并添加到新位置
      const newPermissions = deleteNodeFromTree(JSON.parse(JSON.stringify(activeConfig.permissions)), updatedNode.id)

      // 更新权限标识
      updatedNode.permissionId = generatePermissionIdentifier(updatedNode)

      if (updatedNode.parentId) {
        // 添加到新的父节点下
        const result = addNodeToParent(newPermissions, updatedNode.parentId, updatedNode)
        updateActiveConfigPermissions(result)
      } else {
        // 如果是目录或菜单且没有父级，添加到顶层
        if (updatedNode.type === PermissionType.Directory || updatedNode.type === PermissionType.Menu) {
          updateActiveConfigPermissions([...newPermissions, updatedNode])
        } else {
          // 按钮不能没有父级
          toast({
            title: "操作失败",
            description: "按钮必须有父级节点",
            variant: "destructive",
          })
          return
        }
      }
    } else {
      // 如果没有更改父级权限，只更新节点属性
      updateActiveConfigPermissions(updateNodeInTree(activeConfig.permissions, updatedNode))
    }

    setSelectedNode(updatedNode)
  }

  // 保存数据到服务器
  const handleSaveToStorage = async () => {
    try {
      // 验证所有节点的层级规则
      const validateAllNodes = (nodes: PermissionNode[]): string | null => {
        for (const node of nodes) {
          const error = validateNodeHierarchy(node)
          if (error) {
            return `节点 "${node.name}" ${error}`
          }
          if (node.children && node.children.length > 0) {
            const childError = validateAllNodes(node.children)
            if (childError) return childError
          }
        }
        return null
      }

      const validationError = validateAllNodes(activeConfig.permissions)
      if (validationError) {
        toast({
          title: "保存失败",
          description: validationError,
          variant: "destructive",
        })
        return
      }

      setIsLoading(true)
      
      // 为所有权限节点添加当前配置的groupCode
      const permissionsWithGroupCode = activeConfig.permissions.map(node => {
        // 使用深拷贝确保不修改原始对象
        const processedNode = JSON.parse(JSON.stringify(node));
        
        // 递归函数为每个节点添加groupCode
        const addGroupCodeToNode = (node: PermissionNode) => {
          node.groupCode = activeConfig.code;
          
          if (node.children && node.children.length > 0) {
            node.children.forEach(addGroupCodeToNode);
          }
          
          return node;
        };
        
        return addGroupCodeToNode(processedNode);
      });
      
      // 保存权限树数据
      const response = await createPermissions(permissionsWithGroupCode, activeConfig.code);
      
      if (response && response.code === "200" && response.data) {
        // 保存原始数据副本
        setOriginalPermissions(JSON.parse(JSON.stringify(permissionsWithGroupCode)));
        
        // 使用UI组件库的toast
        toast({
          title: "保存成功",
          description: `权限配置 "${activeConfig.name}" 已成功保存`,
        });
        
        // 同时使用sonner提供一个浮动通知
        sonnerToast.success("权限数据已成功保存", {
          description: `配置 "${activeConfig.name}" 包含 ${activeConfig.permissions.length} 个顶级权限项`,
          position: "top-center",
          duration: 3000,
        });
      } else {
        toast({
          title: "保存失败",
          description: response?.msg || "服务器拒绝了保存请求，请检查权限数据是否正确",
          variant: "destructive",
        });
        
        sonnerToast.error("保存失败", {
          description: "服务器无法处理保存请求",
          position: "top-center",
        });
      }
    } catch (error) {
      console.error("Failed to save permissions data:", error)
      toast({
        title: "保存失败",
        description: "保存权限数据时发生错误，请稍后再试",
        variant: "destructive",
      })
      
      sonnerToast.error("保存错误", {
        description: error instanceof Error ? error.message : "未知错误",
        position: "top-center",
      });
    } finally {
      setIsLoading(false)
    }
  }

  // 刷新数据
  const handleRefresh = () => {
    if (!activeConfigId || !activeConfig.code) {
      toast({
        title: "刷新失败",
        description: "无法确定当前配置信息",
        variant: "destructive",
      })
      return;
    }
    
    setIsLoading(true)
    
    // 直接获取当前配置的权限树
    fetchPermissionTree(activeConfig.code)
      .then(permissions => {
        // 更新权限树数据
        setConfigs(prev => 
          prev.map(c => 
            c.id === activeConfigId 
              ? { ...c, permissions } 
              : c
          )
        )
        
        // 保存原始权限数据副本
        setOriginalPermissions(JSON.parse(JSON.stringify(permissions)));
        
        toast({
          title: "刷新成功",
          description: "权限数据已刷新",
        })
      })
      .catch(error => {
        console.error("获取权限树失败:", error)
        toast({
          title: "刷新失败", 
          description: "无法获取权限树数据",
          variant: "destructive",
        })
      })
      .finally(() => {
        setIsLoading(false)
      })
  }

  // 打开重置确认对话框
  const handleOpenResetDialog = () => {
    setIsResetDialogOpen(true);
  }

  // 确认重置
  const handleConfirmReset = async () => {
    if (activeConfigId) {
      setIsLoading(true);
      
      try {
        // 如果有原始数据，则恢复原始数据
        if (originalPermissions.length > 0) {
          // 使用已保存的原始数据恢复，不再重新请求
          const restoredPermissions = JSON.parse(JSON.stringify(originalPermissions));
          
          // 更新权限树数据
          setConfigs((prevConfigs) =>
            prevConfigs.map((config) => (config.id === activeConfigId ? { ...config, permissions: restoredPermissions } : config)),
          );
          
          toast({
            title: "重置成功",
            description: "当前权限配置已重置为上次保存的状态",
          });
        } else {
          // 没有原始数据，需要重新获取
          try {
            const permissions = await fetchPermissionTree(activeConfig.code);
            
            // 更新权限树数据
            setConfigs((prevConfigs) =>
              prevConfigs.map((config) => (config.id === activeConfigId ? { ...config, permissions } : config)),
            );
            
            // 保存获取的数据作为原始副本
            setOriginalPermissions(JSON.parse(JSON.stringify(permissions)));
            
            toast({
              title: "重置成功",
              description: "当前权限配置已重置",
            });
          } catch (error) {
            console.error("获取权限树失败:", error);
            toast({
              title: "重置失败",
              description: "无法获取权限配置数据",
              variant: "destructive",
            });
          }
        }
        
        setSelectedNode(null);
      } catch (error) {
        console.error("重置权限失败:", error);
        toast({
          title: "重置失败",
          description: "无法重置权限配置",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    }
    
    setIsResetDialogOpen(false);
  }

  // 添加新的权限配置
  const handleAddConfig = async (newConfig: Omit<PermissionConfig, "id" | "permissions">) => {
    try {
      setIsLoading(true)
      
      const id = `config-${Date.now()}`
      const config: PermissionConfig = {
        ...newConfig,
        id,
        permissions: [],
      }

      // 直接添加到本地列表，不再调用保存接口
      setConfigs(prev => [...prev, config]);
      setActiveConfigId(id);
      
      toast({
        title: "添加成功",
        description: `已添加新配置: ${newConfig.name}`,
      });
    } catch (error) {
      console.error("Failed to add configuration:", error)
      toast({
        title: "添加失败",
        description: "添加权限配置时发生错误",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 删除权限配置
  const handleDeleteConfig = async (configId: string) => {
    try {
      setIsLoading(true)
      
      // 检查删除的是否是当前活动配置
      const isActiveConfig = activeConfigId === configId
      
      // 如果删除的是当前活动配置，找到下一个要选择的配置
      let nextConfigId = ""
      if (isActiveConfig) {
        // 找出下一个要选择的配置
        const remainingConfigs = configs.filter(config => config.id !== configId)
        nextConfigId = remainingConfigs.length > 0 ? remainingConfigs[0].id : ""
      }
      
      // 更新本地配置列表
      setConfigs(prevConfigs => prevConfigs.filter(config => config.id !== configId))
      
      // 如果删除的是当前活动配置，切换到下一个配置并刷新权限树
      if (isActiveConfig && nextConfigId) {
        setActiveConfigId(nextConfigId)
        
        // 找到下一个配置对象
        const nextConfig = configs.find(config => config.id === nextConfigId)
        if (nextConfig) {
          // 如果下一个配置没有权限数据，加载权限树
          if (!nextConfig.permissions || nextConfig.permissions.length === 0) {
            try {
              const permissions = await fetchPermissionTree(nextConfig.code)
              
              // 更新配置的权限数据
              setConfigs(prev => 
                prev.map(c => 
                  c.id === nextConfigId 
                    ? { ...c, permissions } 
                    : c
                )
              )
              
              // 保存原始权限数据副本
              setOriginalPermissions(JSON.parse(JSON.stringify(permissions)))
            } catch (error) {
              console.error("获取权限树失败:", error)
              toast({
                title: "加载失败",
                description: "无法获取权限树数据",
                variant: "destructive",
              })
            }
          } else {
            // 使用已有的权限数据
            setOriginalPermissions(JSON.parse(JSON.stringify(nextConfig.permissions)))
          }
        }
        
        // 重置选中节点
        setSelectedNode(null)
      }
      
      toast({
        title: "删除成功",
        description: "权限配置已成功删除",
      })
    } catch (error) {
      console.error("Failed to delete configuration:", error)
      toast({
        title: "删除失败",
        description: "删除权限配置时发生错误",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 选择配置
  const handleSelectConfig = async (config: PermissionConfig) => {
    setActiveConfigId(config.id)
    setSelectedNode(null)
    
    // 如果配置没有权限数据，尝试获取
    if (!config.permissions || config.permissions.length === 0) {
      setIsLoading(true)
      
      try {
        const permissions = await fetchPermissionTree(config.code)
        
        // 更新配置的权限数据
        setConfigs(prev => 
          prev.map(c => 
            c.id === config.id 
              ? { ...c, permissions } 
              : c
          )
        )
        
        // 保存原始权限数据副本
        setOriginalPermissions(JSON.parse(JSON.stringify(permissions)))
      } catch (error) {
        console.error("获取权限树失败:", error)
        toast({
          title: "加载失败",
          description: "无法获取权限树数据",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    } else {
      // 已有权限数据时，更新原始数据副本
      setOriginalPermissions(JSON.parse(JSON.stringify(config.permissions)))
    }
  }

  // 验证层级规则
  const validateNodeHierarchy = (node: PermissionNode): string | null => {
    // 目录可以放在目录下，不再强制作为顶级节点
    if (node.type === PermissionType.Directory && node.parentId) {
      const findParentType = (nodes: PermissionNode[], id: string): PermissionType | null => {
        for (const n of nodes) {
          if (n.id === id) {
            return n.type
          }
          if (n.children && n.children.length > 0) {
            const found = findParentType(n.children, id)
            if (found) return found
          }
        }
        return null
      }

      const parentType = findParentType(activeConfig.permissions, node.parentId)
      if (parentType !== PermissionType.Directory) {
        return "目录只能添加在目录下或作为顶级节点"
      }
    }

    // 按钮必须有父级且父级必须是菜单
    if (node.type === PermissionType.Button) {
      if (!node.parentId) {
        return "按钮必须有父级节点"
      }

      const findParentType = (nodes: PermissionNode[], id: string): PermissionType | null => {
        for (const n of nodes) {
          if (n.id === id) {
            return n.type
          }
          if (n.children && n.children.length > 0) {
            const found = findParentType(n.children, id)
            if (found) return found
          }
        }
        return null
      }

      const parentType = findParentType(activeConfig.permissions, node.parentId)
      if (parentType !== PermissionType.Menu) {
        return "按钮只能添加在菜单下"
      }
    }

    // 菜单可以作为顶级或者目录/菜单的子级
    if (node.type === PermissionType.Menu && node.parentId) {
      const findParentType = (nodes: PermissionNode[], id: string): PermissionType | null => {
        for (const n of nodes) {
          if (n.id === id) {
            return n.type
          }
          if (n.children && n.children.length > 0) {
            const found = findParentType(n.children, id)
            if (found) return found
          }
        }
        return null
      }

      const parentType = findParentType(activeConfig.permissions, node.parentId)
      if (parentType !== PermissionType.Directory && parentType !== PermissionType.Menu) {
        return "菜单只能添加在目录或菜单下"
      }
    }

    return null
  }

  // 更新当前活动配置的权限
  const updateActiveConfigPermissions = (newPermissions: PermissionNode[]) => {
    setConfigs((prevConfigs) =>
      prevConfigs.map((config) => (config.id === activeConfigId ? { ...config, permissions: newPermissions } : config)),
    )
  }

  // 更新节点在树中的位置
  const updateNodeInTree = (tree: PermissionNode[], updatedNode: PermissionNode): PermissionNode[] => {
    return tree.map((node) => {
      if (node.id === updatedNode.id) {
        return updatedNode
      }
      if (node.children && node.children.length > 0) {
        // 根据 order 排序子节点
        const updatedChildren = updateNodeInTree(node.children, updatedNode)
        return {
          ...node,
          children: updatedChildren.sort((a, b) => (a.order || 0) - (b.order || 0)),
        }
      }
      return node
    })
  }

  // 检查是否可以添加到父节点
  const canAddToParent = (parentType: PermissionType | null, childType: PermissionType): boolean => {
    // 目录可以作为顶级节点或其他目录的子级
    if (childType === PermissionType.Directory) {
      return parentType === null || parentType === PermissionType.Directory
    }

    // 菜单可以作为顶级，或者目录/菜单的子级
    if (childType === PermissionType.Menu) {
      return parentType === null || parentType === PermissionType.Directory || parentType === PermissionType.Menu
    }

    // 按钮只能添加到菜单下
    if (childType === PermissionType.Button) {
      return parentType === PermissionType.Menu
    }

    return false
  }

  // 获取节点深度
  const getNodeDepth = (tree: PermissionNode[], nodeId: string): number => {
    const getDepth = (nodes: PermissionNode[], id: string, currentDepth = 0): number => {
      for (const node of nodes) {
        if (node.id === id) return currentDepth
        if (node.children && node.children.length > 0) {
          const depth = getDepth(node.children, id, currentDepth + 1)
          if (depth !== -1) return depth
        }
      }
      return -1
    }
    return getDepth(tree, nodeId)
  }

  // 处理添加权限
  const handleAddPermission = (type: PermissionType, parentId?: string) => {
    // 查找父节点类型
    let parentType: PermissionType | null = null
    let order = 0

    if (parentId) {
      // 获取父节点类型
      const findParentType = (nodes: PermissionNode[]): PermissionType | null => {
        for (const node of nodes) {
          if (node.id === parentId) {
            // 如果找到父节点，获取其下最大的order值
            if (node.children && node.children.length > 0) {
              const maxOrder = Math.max(...node.children.map((child) => child.order || 0))
              order = maxOrder + 1
            }
            return node.type
          }
          if (node.children && node.children.length > 0) {
            const result = findParentType(node.children)
            if (result !== null) return result
          }
        }
        return null
      }

      parentType = findParentType(activeConfig.permissions)

      if (!canAddToParent(parentType, type)) {
        const errorMessage = getErrorMessageForAddition(type, parentType)
        toast({
          title: "操作失败",
          description: errorMessage,
          variant: "destructive",
        })
        return
      }

      // 获取父节点的深度，检查是否到达最大深度
      const parentDepth = getNodeDepth(activeConfig.permissions, parentId)
      if (parentDepth >= 4) {
        toast({
          title: "操作失败",
          description: "不能创建超过5层的节点结构",
          variant: "destructive",
        })
        return
      }
    } else {
      // 判断顶级节点类型
      if (type === PermissionType.Button) {
        toast({
          title: "操作失败",
          description: "按钮必须位于菜单下",
          variant: "destructive",
        })
        return
      }

      // 获取顶级节点中的最大order值
      if (activeConfig.permissions.length > 0) {
        const maxOrder = Math.max(...activeConfig.permissions.map((node) => node.order || 0))
        order = maxOrder + 1
      }
    }

    try {
      // 创建新节点
      const newNode = createNewPermissionNode(type, parentId, activeConfig.code)
      newNode.order = order // 设置排序值

      // 生成权限标识
      newNode.permissionId = generatePermissionIdentifier(newNode)

      if (!parentId) {
        updateActiveConfigPermissions([...activeConfig.permissions, newNode])
        setSelectedNode(newNode)
        return
      }

      updateActiveConfigPermissions(addNodeToParent(activeConfig.permissions, parentId, newNode))
      setSelectedNode(newNode)
    } catch (error) {
      console.error("Failed to add permission:", error)
      toast({
        title: "添加失败",
        description: "添加权限时发生错误",
        variant: "destructive",
      })
    }
  }

  // 获取错误信息
  const getErrorMessageForAddition = (childType: PermissionType, parentType: PermissionType | null): string => {
    if (childType === PermissionType.Directory && parentType !== null && parentType !== PermissionType.Directory) {
      return "目录只能作为顶级节点或添加在目录下"
    }
    if (childType === PermissionType.Button && parentType !== PermissionType.Menu) {
      return "按钮只能添加在菜单下"
    }
    if (
      childType === PermissionType.Menu &&
      parentType !== PermissionType.Directory &&
      parentType !== PermissionType.Menu &&
      parentType !== null
    ) {
      return "菜单只能添加在目录下、菜单下或作为顶级节点"
    }
    return "无法添加该类型的节点到目标位置"
  }

  // 添加节点到父节点
  const addNodeToParent = (tree: PermissionNode[], parentId: string, newNode: PermissionNode): PermissionNode[] => {
    return tree.map((node) => {
      if (node.id === parentId) {
        // 添加新节点并按顺序排序
        const updatedChildren = [...(node.children || []), newNode].sort((a, b) => (a.order || 0) - (b.order || 0))
        return {
          ...node,
          children: updatedChildren,
        }
      }
      if (node.children && node.children.length > 0) {
        return {
          ...node,
          children: addNodeToParent(node.children, parentId, newNode),
        }
      }
      return node
    })
  }

  // 删除权限
  const handleDeletePermission = (nodeId: string) => {
    updateActiveConfigPermissions(deleteNodeFromTree(activeConfig.permissions, nodeId))
    if (selectedNode && selectedNode.id === nodeId) {
      setSelectedNode(null)
    }
  }

  // 从树中删除节点
  const deleteNodeFromTree = (tree: PermissionNode[], nodeId: string): PermissionNode[] => {
    return tree.filter((node) => {
      if (node.id === nodeId) {
        return false
      }
      if (node.children && node.children.length > 0) {
        node.children = deleteNodeFromTree(node.children, nodeId)
      }
      return true
    })
  }

  // 查找节点
  const findNode = (tree: PermissionNode[], id: string): PermissionNode | null => {
    for (const node of tree) {
      if (node.id === id) return node
      if (node.children && node.children.length > 0) {
        const found = findNode(node.children, id)
        if (found) return found
      }
    }
    return null
  }

  // 移动节点
  const handleMoveNode = (nodeId: string, targetId: string, position: "before" | "after" | "inside") => {
    // 找到要移动的节点
    let nodeToMove: PermissionNode | null = null
    const newPermissions = deleteNodeFromTree(JSON.parse(JSON.stringify(activeConfig.permissions)), nodeId)

    // 在原始树中找到节点
    nodeToMove = findNode(activeConfig.permissions, nodeId)
    if (!nodeToMove) return

    // 如果是移动到根级别
    if (targetId === "root") {
      // 检查节点类型是否可以作为顶级节点
      if (nodeToMove.type !== PermissionType.Directory && nodeToMove.type !== PermissionType.Menu) {
        toast({
          title: "操作失败",
          description: "只有目录和菜单可以作为顶级节点",
          variant: "destructive",
        })
        return
      }

      // 更新节点的父级ID
      const updatedNode = { ...nodeToMove, parentId: undefined }

      // 计算新的顺序
      updatedNode.order = newPermissions.length + 1

      // 更新权限标识
      updatedNode.permissionId = nanoid(5)

      // 添加到顶级
      updateActiveConfigPermissions([...newPermissions, updatedNode])
      setSelectedNode(updatedNode)
      return
    }

    // 找到目标节点
    let targetNode: PermissionNode | null = null
    targetNode = findNode(newPermissions, targetId)
    if (!targetNode) return

    // 检查移动规则
    if (position === "inside") {
      // 检查深度限制
      const targetDepth = getNodeDepth(newPermissions, targetId)
      if (targetDepth >= 5) { // 最大深度增加到6层，目标深度为5时已达到限制
        toast({
          title: "操作失败",
          description: "已达到最大嵌套深度 (6层)",
          variant: "destructive",
        })
        return
      }

      // 检查是否可以添加到目标节点内部
      if (!canAddToParent(targetNode.type, nodeToMove.type)) {
        toast({
          title: "操作失败",
          description: getErrorMessageForAddition(nodeToMove.type, targetNode.type),
          variant: "destructive",
        })
        return
      }

      // 更新父级ID
      nodeToMove.parentId = targetNode.id

      // 更新权限标识
      nodeToMove.permissionId = `${targetNode.permissionId}:${nanoid(5)}`

      // 计算新的顺序
      if (targetNode.children) {
        nodeToMove.order = Math.max(0, ...targetNode.children.map((child) => child.order || 0)) + 1
      } else {
        nodeToMove.order = 1
      }
    } else {
      // 对于before和after，需要找到目标节点的父节点
      const findParentNode = (
        tree: PermissionNode[],
        childId: string,
      ): { parent: PermissionNode | null; index: number } => {
        for (const [index, node] of tree.entries()) {
          if (node.id === childId) {
            return { parent: null, index }
          }
          if (node.children && node.children.length > 0) {
            for (const [childIndex, childNode] of node.children.entries()) {
              if (childNode.id === childId) {
                return { parent: node, index: childIndex }
              }
            }
            const result = findParentNode(node.children, childId)
            if (result.parent !== null) {
              return result
            }
          }
        }
        return { parent: null, index: -1 }
      }

      const { parent: targetParent } = findParentNode(newPermissions, targetId)

      // 如果目标节点是顶级节点
      if (!targetParent) {
        // 检查移动的节点是否可以作为顶级节点
        if (nodeToMove.type !== PermissionType.Directory && nodeToMove.type !== PermissionType.Menu) {
          toast({
            title: "操作失败",
            description: "只有目录和菜单可以作为顶级节点",
            variant: "destructive",
          })
          return
        }

        // 更新父级ID
        nodeToMove.parentId = undefined

        // 更新权限标识
        nodeToMove.permissionId = nanoid(5)

        // 计算新的顺序
        if (position === "before") {
          nodeToMove.order = targetNode.order || 0
          // 更新后续节点的顺序
          newPermissions.forEach((node) => {
            if ((node.order || 0) >= (nodeToMove.order || 0)) {
              node.order = (node.order || 0) + 1
            }
          })
        } else if (position === "after") {
          nodeToMove.order = (targetNode.order || 0) + 1
          // 更新后续节点的顺序
          newPermissions.forEach((node) => {
            if ((node.order || 0) >= (nodeToMove.order || 0)) {
              node.order = (node.order || 0) + 1
            }
          })
        }

        // 添加到顶级
        updateActiveConfigPermissions([...newPermissions, nodeToMove])
        setSelectedNode(nodeToMove)
        return
      }

      // 检查是否可以添加到目标节点的父节点
      if (!canAddToParent(targetParent.type, nodeToMove.type)) {
        toast({
          title: "操作失败",
          description: getErrorMessageForAddition(nodeToMove.type, targetParent.type),
          variant: "destructive",
        })
        return
      }

      // 更新父级ID
      nodeToMove.parentId = targetParent.id

      // 更新权限标识
      nodeToMove.permissionId = `${targetParent.permissionId}:${nanoid(5)}`

      // 计算新的顺序
      if (position === "before") {
        nodeToMove.order = targetNode.order || 0
        // 更新后续节点的顺序
        if (targetParent.children) {
          targetParent.children.forEach((child) => {
            if ((child.order || 0) >= (nodeToMove.order || 0)) {
              child.order = (child.order || 0) + 1
            }
          })
        }
      } else if (position === "after") {
        nodeToMove.order = (targetNode.order || 0) + 1
        // 更新后续节点的顺序
        if (targetParent.children) {
          targetParent.children.forEach((child) => {
            if ((child.order || 0) >= (nodeToMove.order || 0)) {
              child.order = (child.order || 0) + 1
            }
          })
        }
      }
    }

    // 添加节点到新的位置
    if (nodeToMove.parentId) {
      const result = addNodeToParent(newPermissions, nodeToMove.parentId, nodeToMove)
      updateActiveConfigPermissions(result)
    } else {
      updateActiveConfigPermissions([...newPermissions, nodeToMove])
    }

    setSelectedNode(nodeToMove)
  }

  // 初始化权限
  const handleInitPermissions = () => {
    const dirId = `dir_${nanoid(6)}`;
    const menuId = `menu_${nanoid(6)}`;
    const btnId = `btn_${nanoid(6)}`;
    
    const initialPermissions: PermissionNode[] = [
      {
        id: dirId,
        name: "系统管理",
        type: PermissionType.Directory,
        permissionId: "system",
        icon: "Settings",
        enabled: true,
        order: 1,
        children: [
          {
            id: menuId,
            name: "用户管理",
            type: PermissionType.Menu,
            permissionId: "system:user",
            icon: "Users",
            enabled: true,
            parentId: dirId,
            order: 1,
            path: "/system/user",
            componentPath: "/pages/system/user",
            children: [
              {
                id: btnId,
                name: "添加用户",
                type: PermissionType.Button,
                permissionId: "system:user:add",
                icon: "UserPlus",
                enabled: true,
                parentId: menuId,
                order: 1,
                children: [],
              }
            ],
          }
        ],
      }
    ];

    updateActiveConfigPermissions(initialPermissions)
    toast({
      title: "初始化成功",
      description: "已初始化基础权限结构",
    })
  }

  return (
    <>
      <div className="space-y-4">
        {/* 操作栏 */}
        <div className="flex justify-between items-center bg-background rounded-md p-4 border">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">权限配置：</span>
              <PermissionConfigSelector
                configs={configs}
                activeConfig={activeConfig}
                onSelectConfig={handleSelectConfig}
                onAddConfig={handleAddConfig}
                onRefresh={handleRefresh}
                onDeleteConfig={handleDeleteConfig}
              />
            </div>
            {activeConfig.description && (
              <span className="text-sm text-muted-foreground">{activeConfig.description}</span>
            )}
            {isLoading && (
              <span className="text-sm text-muted-foreground flex items-center gap-1">
                <RefreshCw className="h-3.5 w-3.5 animate-spin" />
                数据加载中...
              </span>
            )}
          </div>
          <div className="flex gap-2">
            {configs.length === 0 ? (
              <div className="text-sm text-muted-foreground">
                请先添加权限配置
              </div>
            ) : activeConfig.permissions.length === 0 ? (
              <Button variant="default" onClick={handleInitPermissions} disabled={isLoading}>
                {isLoading ? (
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Plus className="h-4 w-4 mr-2" />
                )}
                初始化权限
              </Button>
            ) : (
              <>
                <Button variant="outline" onClick={handleOpenResetDialog} disabled={isLoading}>
                  <RotateCcw className="h-4 w-4 mr-2" />
                  重置
                </Button>
                <Button variant="default" onClick={handleSaveToStorage} disabled={isLoading}>
                  {isLoading ? (
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Save className="h-4 w-4 mr-2" />
                  )}
                  保存
                </Button>
              </>
            )}
          </div>
        </div>

        {/* 内容区域 */}
        <div className="grid grid-cols-12 gap-4 min-h-[600px]">
          <div className="col-span-4">
            <DndProviderWrapper>
              <PermissionTree
                permissions={activeConfig.permissions}
                onNodeSelect={handleNodeSelect}
                selectedNodeId={selectedNode?.id}
                onAddPermission={handleAddPermission}
                onDeletePermission={handleDeletePermission}
                onMoveNode={handleMoveNode}
                onToggleNodeEnabled={handleToggleNodeEnabled}
              />
            </DndProviderWrapper>
          </div>

          <div className="col-span-8">
            <PermissionForm
              selectedNode={selectedNode}
              permissions={activeConfig.permissions}
              onSave={handleSavePermission}
              onCancel={() => setSelectedNode(null)}
              getNodePath={getNodePath}
            />
          </div>
        </div>
      </div>
      
      {/* 重置确认对话框 */}
      <Dialog open={isResetDialogOpen} onOpenChange={setIsResetDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认重置权限</DialogTitle>
            <DialogDescription>
              重置操作将清空当前修改，恢复到最近一次保存的权限结构。此操作不可撤销，确定要继续吗？
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsResetDialogOpen(false)}>
              取消
            </Button>
            <Button variant="destructive" onClick={handleConfirmReset} disabled={isLoading}>
              {isLoading ? <RefreshCw className="h-4 w-4 mr-2 animate-spin" /> : <RotateCcw className="h-4 w-4 mr-2" />}
              确认重置
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      <Toaster />
    </>
  )
}

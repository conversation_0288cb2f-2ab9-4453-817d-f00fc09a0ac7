import React from "react"
import { Calendar } from "@/components/common-custom/calendar"

export const eventsExample = {
  id: "events-calendar",
  title: "事件日历",
  description: "支持事件显示和交互的现代化日历组件",
  code: `
import React, { useState } from "react";
import { Calendar } from "@/components/common-custom/calendar";

function EventsCalendar() {
  const [date, setDate] = useState<Date | undefined>(new Date());

  const events = [
    {
      id: "1",
      date: new Date(),
      title: "团队会议",
      description: "讨论项目进度和下一步计划",
      type: "primary" as const,
    },
    {
      id: "2",
      date: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000),
      title: "代码评审",
      description: "前端代码质量评审",
      type: "success" as const,
    },
    {
      id: "3",
      date: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
      title: "项目截止",
      description: "完成初步设计稿提交",
      type: "danger" as const,
    },
    {
      id: "4",
      date: new Date(Date.now() + 4 * 24 * 60 * 60 * 1000),
      title: "团建活动",
      description: "团队聚餐和娱乐活动",
      type: "warning" as const,
    },
  ];

  const handleEventClick = (event: any, date: Date) => {
    console.log("点击了事件:", event.title, "日期:", date.toLocaleDateString());
  };

  return (
    <div className="w-fit">
      <Calendar
        mode="single"
        value={date}
        events={events}
        handlers={{
          onSelect: setDate,
          onEventClick: handleEventClick
        }}
        features={{
          showEvents: true,
          showEventDetails: true,
          maxEventsPerDay: 3,
          enableEventTooltip: true
        }}
        style={{
          showBorder: true,
          className: "rounded-lg shadow-sm bg-background"
        }}
      />
    </div>
  );
}

render(<EventsCalendar />);
  `,
  scope: { Calendar, React, useState: React.useState },
}

export const legendExample = {
  id: "legend-calendar",
  title: "带图例的日历",
  description: "显示图例说明的日历",
  code: `
import React, { useState } from "react";
import { Calendar } from "@/components/common-custom/calendar";

function LegendCalendar() {
  const [date, setDate] = useState<Date | undefined>(new Date());

  const events = [
    {
      id: "1",
      date: new Date(2024, 0, 15),
      title: "会议",
      type: "primary" as const,
    },
    {
      id: "2",
      date: new Date(2024, 0, 18),
      title: "截止日期",
      type: "danger" as const,
    },
    {
      id: "3",
      date: new Date(2024, 0, 22),
      title: "培训",
      type: "success" as const,
    },
  ];

  const legends = [
    {
      id: "meeting",
      label: "会议",
      type: "primary" as const,
      description: "各类会议安排"
    },
    {
      id: "deadline",
      label: "截止日期",
      type: "danger" as const,
      description: "重要截止时间"
    },
    {
      id: "training",
      label: "培训",
      type: "success" as const,
      description: "培训课程"
    },
  ];

  return (
    <Calendar
      mode="single"
      value={date}
      events={events}
      legends={legends}
      handlers={{
        onSelect: setDate
      }}
      features={{
        showEvents: true,
        showLegend: true,
        legendPosition: "bottom"
      }}
      style={{
        showBorder: true,
        className: "w-fit"
      }}
    />
  );
}

render(<LegendCalendar />);
  `,
  scope: { Calendar, React, useState: React.useState },
}

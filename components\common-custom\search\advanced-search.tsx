"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Search, X, Filter, History, ChevronDown, ChevronUp } from "lucide-react"
import { cn } from "@/lib/utils"
import { SearchInput } from "./search-input"
import { SearchTags } from "./search-tags"

export interface SearchFilter {
  id: string
  label: string
  value: string | boolean | number | null
  type: "text" | "select" | "checkbox" | "date" | "number"
  options?: { label: string; value: string }[]
}

export interface AdvancedSearchProps {
  /**
   * 搜索值
   */
  value: string
  
  /**
   * 搜索值变化回调
   */
  onChange: (value: string) => void
  
  /**
   * 占位符
   */
  placeholder?: string
  
  /**
   * 过滤器列表
   */
  filters?: SearchFilter[]
  
  /**
   * 过滤器变化回调
   */
  onFilterChange?: (filters: SearchFilter[]) => void
  
  /**
   * 是否显示历史记录
   */
  showHistory?: boolean
  
  /**
   * 历史记录列表
   */
  history?: string[]
  
  /**
   * 历史记录点击回调
   */
  onHistoryClick?: (term: string) => void
  
  /**
   * 清除历史记录回调
   */
  onClearHistory?: (term: string) => void
  
  /**
   * 搜索提交回调
   */
  onSearch?: (value: string, filters: SearchFilter[]) => void
  
  /**
   * 自定义类名
   */
  className?: string
}

/**
 * 高级搜索组件
 * 提供多条件搜索、过滤器和搜索历史功能
 */
export function AdvancedSearch({
  value,
  onChange,
  placeholder = "搜索...",
  filters = [],
  onFilterChange,
  showHistory = true,
  history = [],
  onHistoryClick,
  onClearHistory,
  onSearch,
  className,
}: AdvancedSearchProps) {
  const [showFilters, setShowFilters] = useState(false)
  const [activeFilters, setActiveFilters] = useState<SearchFilter[]>(filters)
  const [localFilters, setLocalFilters] = useState<SearchFilter[]>(filters)
  
  // 同步外部过滤器变化
  useEffect(() => {
    setLocalFilters(filters)
  }, [filters])
  
  // 处理过滤器值变化
  const handleFilterChange = (id: string, value: any) => {
    const updatedFilters = localFilters.map(filter => 
      filter.id === id ? { ...filter, value } : filter
    )
    setLocalFilters(updatedFilters)
  }
  
  // 应用过滤器
  const applyFilters = () => {
    setActiveFilters(localFilters)
    onFilterChange?.(localFilters)
  }
  
  // 重置过滤器
  const resetFilters = () => {
    const resetedFilters = localFilters.map(filter => ({ ...filter, value: null }))
    setLocalFilters(resetedFilters)
    setActiveFilters(resetedFilters)
    onFilterChange?.(resetedFilters)
  }
  
  // 提交搜索
  const handleSubmit = () => {
    onSearch?.(value, activeFilters)
  }
  
  // 获取激活的过滤器数量
  const activeFilterCount = activeFilters.filter(f => 
    f.value !== null && f.value !== "" && f.value !== false
  ).length
  
  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex flex-col sm:flex-row gap-2">
        <div className="flex-1">
          <SearchInput
            value={value}
            onChange={onChange}
            placeholder={placeholder}
            onSubmit={handleSubmit}
            size="lg"
          />
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            className={cn(
              "flex items-center gap-1",
              activeFilterCount > 0 && "border-primary text-primary"
            )}
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter className="h-4 w-4" />
            筛选
            {activeFilterCount > 0 && (
              <Badge variant="secondary" className="ml-1 h-5 px-1">
                {activeFilterCount}
              </Badge>
            )}
            {showFilters ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
          <Button onClick={handleSubmit}>
            <Search className="h-4 w-4 mr-2" />
            搜索
          </Button>
        </div>
      </div>
      
      {/* 过滤器面板 */}
      {showFilters && (
        <Card className="shadow-sm">
          <CardContent className="p-4 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
            {localFilters.map((filter) => (
              <div key={filter.id} className="space-y-2">
                <Label htmlFor={filter.id}>{filter.label}</Label>
                
                {filter.type === "text" && (
                  <Input
                    id={filter.id}
                    value={filter.value as string || ""}
                    onChange={(e) => handleFilterChange(filter.id, e.target.value)}
                    placeholder={`输入${filter.label}`}
                  />
                )}
                
                {filter.type === "select" && (
                  <Select
                    value={filter.value as string || ""}
                    onValueChange={(value) => handleFilterChange(filter.id, value)}
                  >
                    <SelectTrigger id={filter.id}>
                      <SelectValue placeholder={`选择${filter.label}`} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部</SelectItem>
                      {filter.options?.map((option) => (
                        <SelectItem key={option.value} value={option.value || "none"}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
                
                {filter.type === "checkbox" && (
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id={filter.id}
                      checked={filter.value as boolean || false}
                      onCheckedChange={(checked) => handleFilterChange(filter.id, checked)}
                    />
                    <label
                      htmlFor={filter.id}
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      是
                    </label>
                  </div>
                )}
                
                {filter.type === "number" && (
                  <Input
                    id={filter.id}
                    type="number"
                    value={filter.value as number || ""}
                    onChange={(e) => handleFilterChange(filter.id, e.target.valueAsNumber)}
                    placeholder={`输入${filter.label}`}
                  />
                )}
                
                {filter.type === "date" && (
                  <Input
                    id={filter.id}
                    type="date"
                    value={filter.value as string || ""}
                    onChange={(e) => handleFilterChange(filter.id, e.target.value)}
                  />
                )}
              </div>
            ))}
            
            <div className="col-span-full flex justify-end gap-2 mt-4">
              <Button variant="outline" onClick={resetFilters}>
                重置
              </Button>
              <Button onClick={applyFilters}>
                应用筛选
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
      
      {/* 搜索历史 */}
      {showHistory && history.length > 0 && (
        <div className="flex items-center gap-2">
          <History className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm text-muted-foreground">最近搜索:</span>
          <SearchTags
            tags={history}
            onTagClick={(tag) => onHistoryClick?.(tag)}
            className="flex-1"
          />
        </div>
      )}
    </div>
  )
} 
"use client"

import * as React from "react"
import { Check, ChevronLeft, ChevronRight, Pencil, Search, X } from "lucide-react"
import * as LucideIcons from "lucide-react"
import { cn } from "@/lib/utils"

import { Input } from "@/components/ui/input"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

// 图标分类 - 扩展更多图标
const iconCategories: Record<string, string[]> = {
  common: [
    "Home", "Settings", "User", "Users", "FileText", "File", "Folder", "FolderOpen", 
    "Mail", "Calendar", "Bell", "Bookmark", "Heart", "Star", "Sun", "Moon", "Clock",
    "Info", "AlertCircle", "HelpCircle", "MessageCircle", "MessageSquare", "Send",
    "Phone", "Pin", "Map", "MapPin", "Locate", "Navigation", "Globe", "Eye", "EyeOff",
    "Zap", "Award", "Gift", "Crown", "ShieldCheck", "ShieldAlert", "Verified"
  ],
  arrows: [
    "ArrowUp", "ArrowDown", "ArrowLeft", "ArrowRight", 
    "ChevronUp", "ChevronDown", "ChevronLeft", "ChevronRight",
    "ArrowUpCircle", "ArrowDownCircle", "ArrowLeftCircle", "ArrowRightCircle",
    "MoveHorizontal", "MoveVertical", "Move", "MoveUp", "MoveDown", "MoveLeft", "MoveRight",
    "ChevronsUp", "ChevronsDown", "ChevronsLeft", "ChevronsRight",
    "CornerLeftUp", "CornerLeftDown", "CornerRightUp", "CornerRightDown",
    "ArrowUpLeft", "ArrowUpRight", "ArrowDownLeft", "ArrowDownRight",
    "RefreshCw", "RefreshCcw", "RotateCw", "RotateCcw", "SwitchCamera"
  ],
  actions: [
    "Plus", "Minus", "Check", "X", "Edit", "Trash", "Save", "Download", "Upload", 
    "Refresh", "RotateCcw", "RotateCw", "Search", "Filter", "Share", "Copy", "Clipboard", 
    "Send", "Reply", "Forward", "Archive", "Undo", "Redo", "Link", "LinkBreak", "ExternalLink",
    "Maximize", "Minimize", "ZoomIn", "ZoomOut", "Lock", "Unlock", "Power", "LogIn", "LogOut",
    "CirclePlus", "CircleMinus", "CircleCheck", "CircleX", "CircleAlert",
    "ThumbsUp", "ThumbsDown", "PanelLeft", "PanelRight", "PanelTop", "PanelBottom"
  ],
  data: [
    "BarChart", "PieChart", "LineChart", "Activity", "TrendingUp", "TrendingDown", 
    "Database", "Table", "List", "ListChecks", "ListOrdered", "Grid", "Layers", "Package",
    "ShoppingBag", "ShoppingCart", "Percent", "DollarSign", "CreditCard", "Tag", "Tags",
    "Flag", "FilePlus", "FileCheck", "FileText", "FileCode", "FileCog", "FileSearch",
    "Wallet", "Receipt", "ClipboardList", "ClipboardCheck", "Calculator", "CalendarDays",
    "BarChart2", "BarChart3", "BarChart4", "LineChart", "TrendingUp", "TrendingDown"
  ],
  media: [
    "Image", "Video", "Music", "Play", "Pause", "SkipBack", "SkipForward", "Volume", 
    "VolumeX", "Volume1", "Volume2", "Camera", "Mic", "MicOff", "Film", "Tv", "Speaker",
    "Headphones", "Radio", "Monitor", "Cast", "Youtube", "Facebook", "Twitter", "Instagram",
    "Gitlab", "Github", "Figma", "Framer", "Dribbble", "Discord", "Slack", "Twitch",
    "MessageSquare", "MessageCircle", "Paperclip", "PenTool", "Brush", "Palette"
  ],
  devices: [
    "Smartphone", "Tablet", "Monitor", "Laptop", "Printer", "Wifi", "Bluetooth", 
    "Battery", "BatteryCharging", "BatteryLow", "BatteryFull", "Server", "Cpu", 
    "HardDrive", "Save", "Keyboard", "Mouse", "Signal", "SignalHigh", "SignalLow",
    "SignalZero", "Thermometer", "Cloud", "CloudRain", "CloudSnow", "CloudLightning",
    "CloudOff", "Sliders", "SlidersHorizontal", "Plug", "PlugZap", "Usb", "Webcam"
  ],
  layout: [
    "LayoutGrid", "LayoutList", "Layout", "LayoutDashboard", "Rows", "Columns",
    "PanelLeft", "PanelRight", "PanelTop", "PanelBottom", "Split", "SplitSquareVertical",
    "AlignLeft", "AlignCenter", "AlignRight", "AlignJustify", "Sidebar", "SidebarClose",
    "Menu", "MenuSquare", "Maximize", "Minimize", "Expand", "Shrink"
  ]
}

export interface IconSelectorProps {
  value: string
  onChange: (value: string) => void
  label?: string
  showRoute?: boolean
  routePath?: string
  componentName?: string
  displayOrder?: number
  className?: string
  iconColor?: string
  /**
   * @deprecated 标记是否为新组件，仅用于界面展示
   */
  isNew?: boolean
}

export function IconSelector({ 
  value, 
  onChange, 
  label = "选择图标",
  showRoute = false,
  routePath = "",
  componentName = "",
  displayOrder = 0,
  className,
  iconColor,
  isNew = false
}: IconSelectorProps) {
  const [open, setOpen] = React.useState(false)
  const [searchValue, setSearchValue] = React.useState("")
  const [activeTab, setActiveTab] = React.useState("common")
  const [scrollPosition, setScrollPosition] = React.useState(0)
  const tabsRef = React.useRef<HTMLDivElement>(null)
  const searchInputRef = React.useRef<HTMLInputElement>(null)
  const tabsListRef = React.useRef<HTMLDivElement>(null)

  // 当弹出框打开时，聚焦搜索框
  React.useEffect(() => {
    if (open && searchInputRef.current) {
      setTimeout(() => {
        searchInputRef.current?.focus()
      }, 100)
    }
  }, [open])

  // 滚动控制
  const scrollTabs = (direction: 'left' | 'right') => {
    if (tabsListRef.current) {
      const { scrollWidth, clientWidth } = tabsListRef.current
      const maxScroll = scrollWidth - clientWidth
      const scrollAmount = clientWidth * 0.5
      
      let newPosition = scrollPosition + (direction === 'right' ? scrollAmount : -scrollAmount)
      newPosition = Math.max(0, Math.min(newPosition, maxScroll))
      
      tabsListRef.current.scrollTo({
        left: newPosition,
        behavior: 'smooth'
      })
      
      setScrollPosition(newPosition)
    }
  }

  // 检查是否可以滚动
  const canScrollLeft = scrollPosition > 0
  const canScrollRight = tabsListRef.current ? 
    scrollPosition < tabsListRef.current.scrollWidth - tabsListRef.current.clientWidth : 
    false

  // 动态获取Lucide图标组件
  const getIconComponent = (iconName: string): React.ElementType | null => {
    // 安全地将LucideIcons转换为包含图标组件的记录
    const iconComponent = (LucideIcons as unknown as Record<string, React.ElementType | undefined>)[iconName]
    return iconComponent || null
  }

  // 获取当前选中的图标组件
  const SelectedIcon = value ? getIconComponent(value) : null

  // 过滤图标
  const getFilteredIcons = (category: string) => {
    const icons = iconCategories[category as keyof typeof iconCategories] || []
    
    if (searchValue) {
      return icons.filter((icon) => 
        icon.toLowerCase().includes(searchValue.toLowerCase())
      )
    }
    
    return icons
  }

  const filteredIcons = getFilteredIcons(activeTab)

  // 清除选择
  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation()
    onChange("")
    setOpen(false)
  }

  const handleTabsScroll = () => {
    if (tabsListRef.current) {
      setScrollPosition(tabsListRef.current.scrollLeft)
    }
  }

  // 监听滚动事件
  React.useEffect(() => {
    const tabsList = tabsListRef.current
    if (tabsList) {
      tabsList.addEventListener('scroll', handleTabsScroll)
      return () => {
        tabsList.removeEventListener('scroll', handleTabsScroll)
      }
    }
  }, [])

  return (
    <div className={cn("flex items-center relative", className)}>
      {/* 图标预览区 - 点击图标也能打开弹窗 */}
      <Popover open={open} onOpenChange={setOpen}>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <PopoverTrigger asChild>
                <div 
                  className="flex-shrink-0 w-10 h-10 rounded-md flex items-center justify-center bg-muted/20 cursor-pointer hover:bg-muted/40 transition-colors"
                  onClick={() => setOpen(true)}
                >
                  {SelectedIcon ? (
                    <SelectedIcon className="h-5 w-5" style={iconColor ? { color: iconColor } : {}} />
                  ) : (
                    <div className="text-muted-foreground text-xs text-center">无图标</div>
                  )}
                </div>
              </PopoverTrigger>
            </TooltipTrigger>
            <TooltipContent side="top">
              <p>{value || "选择图标"}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        
        <PopoverContent className="w-[340px] p-0" align="start">
          {/* 搜索和操作区域 */}
          <div className="p-3 border-b flex items-center gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground pointer-events-none" />
              <Input
                ref={searchInputRef}
                placeholder="搜索图标..."
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
                className="h-9 pl-8"
              />
            </div>
            
            {/* 清除按钮 */}
            {value && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="h-9 px-2 gap-1"
                      onClick={handleClear}
                    >
                      <X className="h-3.5 w-3.5" />
                      <span className="text-xs">清除</span>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="top">
                    <p>清除选中的图标</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </div>

          {/* 类别选择器 */}
          <Tabs defaultValue="common" value={activeTab} onValueChange={setActiveTab} className="w-full">
            <div className="border-b relative" ref={tabsRef}>
              {/* 左右滚动按钮 */}
              <Button 
                variant="ghost" 
                size="icon" 
                className={cn(
                  "absolute left-0 top-1/2 -translate-y-1/2 h-6 w-6 rounded-full z-10 bg-background/80 transition-opacity",
                  !canScrollLeft && "opacity-0 pointer-events-none"
                )}
                onClick={() => scrollTabs('left')}
              >
                <ChevronLeft className="h-3 w-3" />
              </Button>
              
              <Button 
                variant="ghost" 
                size="icon" 
                className={cn(
                  "absolute right-0 top-1/2 -translate-y-1/2 h-6 w-6 rounded-full z-10 bg-background/80 transition-opacity", 
                  !canScrollRight && "opacity-0 pointer-events-none"
                )}
                onClick={() => scrollTabs('right')}
              >
                <ChevronRight className="h-3 w-3" />
              </Button>
              
              {/* Tab列表 - 使用内部div滚动，保持tabs固定 */}
              <div 
                className="px-2 py-1 overflow-x-auto scrollbar-thin scrollbar-track-transparent scrollbar-thumb-muted"
                ref={tabsListRef}
                style={{ 
                  scrollbarWidth: 'thin',
                  msOverflowStyle: 'none'
                }}
              >
                <TabsList className="inline-flex h-8 bg-transparent w-auto whitespace-nowrap gap-1">
                  <TabsTrigger 
                    value="common" 
                    className="text-xs py-1 px-2.5 h-auto data-[state=active]:bg-primary data-[state=active]:text-primary-foreground hover:bg-muted/60 transition-colors"
                  >
                    常用
                  </TabsTrigger>
                  <TabsTrigger 
                    value="arrows" 
                    className="text-xs py-1 px-2.5 h-auto data-[state=active]:bg-primary data-[state=active]:text-primary-foreground hover:bg-muted/60 transition-colors"
                  >
                    箭头
                  </TabsTrigger>
                  <TabsTrigger 
                    value="actions" 
                    className="text-xs py-1 px-2.5 h-auto data-[state=active]:bg-primary data-[state=active]:text-primary-foreground hover:bg-muted/60 transition-colors"
                  >
                    操作
                  </TabsTrigger>
                  <TabsTrigger 
                    value="data" 
                    className="text-xs py-1 px-2.5 h-auto data-[state=active]:bg-primary data-[state=active]:text-primary-foreground hover:bg-muted/60 transition-colors"
                  >
                    数据
                  </TabsTrigger>
                  <TabsTrigger 
                    value="media" 
                    className="text-xs py-1 px-2.5 h-auto data-[state=active]:bg-primary data-[state=active]:text-primary-foreground hover:bg-muted/60 transition-colors"
                  >
                    媒体
                  </TabsTrigger>
                  <TabsTrigger 
                    value="devices" 
                    className="text-xs py-1 px-2.5 h-auto data-[state=active]:bg-primary data-[state=active]:text-primary-foreground hover:bg-muted/60 transition-colors"
                  >
                    设备
                  </TabsTrigger>
                  <TabsTrigger 
                    value="layout" 
                    className="text-xs py-1 px-2.5 h-auto data-[state=active]:bg-primary data-[state=active]:text-primary-foreground hover:bg-muted/60 transition-colors"
                  >
                    布局
                  </TabsTrigger>
                </TabsList>
              </div>
            </div>

            {/* 图标展示区 */}
            <TabsContent value={activeTab} className="mt-0">
              <ScrollArea className="h-[320px] scrollbar-thin scrollbar-thumb-muted/30">
                <div className="grid grid-cols-6 gap-1 p-3">
                  {filteredIcons.map((iconName) => {
                    const IconComponent = getIconComponent(iconName)
                    if (!IconComponent) return null
                    
                    return (
                      <div
                        key={iconName}
                        className={cn(
                          "flex items-center justify-center p-2 rounded-md cursor-pointer hover:bg-accent transition-colors relative",
                          value === iconName ? "bg-primary/20" : "",
                        )}
                        onClick={() => {
                          onChange(iconName)
                          setOpen(false)
                          setSearchValue("")
                        }}
                        title={iconName}
                      >
                        <IconComponent className="h-5 w-5" />
                        {value === iconName && (
                          <Check className="absolute bottom-1 right-1 h-3 w-3 text-primary" />
                        )}
                      </div>
                    )
                  })}
                  {filteredIcons.length === 0 && (
                    <div className="col-span-6 py-8 text-center text-muted-foreground">未找到匹配的图标</div>
                  )}
                </div>
              </ScrollArea>
            </TabsContent>
          </Tabs>

          {/* 可选的路由和组件信息区域 */}
          {showRoute && (
            <div className="border-t p-3 space-y-3">
              {routePath && (
                <div className="space-y-1.5">
                  <label className="text-xs text-muted-foreground">路由地址</label>
                  <Input 
                    value={routePath} 
                    readOnly 
                    className="h-8 text-sm bg-muted/40"
                  />
                </div>
              )}
              
              {componentName && (
                <div className="space-y-1.5">
                  <label className="text-xs text-muted-foreground">组件路径</label>
                  <Input 
                    value={componentName} 
                    readOnly 
                    className="h-8 text-sm bg-muted/40"
                  />
                </div>
              )}
              
              {displayOrder > 0 && (
                <div className="space-y-1.5">
                  <label className="text-xs text-muted-foreground">显示顺序</label>
                  <Input 
                    value={displayOrder.toString()} 
                    readOnly 
                    className="h-8 text-sm bg-muted/40"
                  />
                </div>
              )}
            </div>
          )}
        </PopoverContent>
      </Popover>
      
      {/* NEW 标识 */}
      {isNew && (
        <span className="absolute -top-2 -right-2 bg-primary text-primary-foreground text-[10px] font-medium px-1 py-0.5 rounded-full">
          NEW
        </span>
      )}
    </div>
  )
} 
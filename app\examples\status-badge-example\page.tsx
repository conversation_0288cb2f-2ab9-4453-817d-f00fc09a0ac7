"use client"

import React from "react"
import { ComponentPreviewContainer } from "@/components/component-detail/preview-container"
import { StatusBadge, OnlineStatusBadge, ProgressStatusBadge, PriorityBadge } from "@/components/common-custom/status-badge"
import { allExamples } from "./examples"

// ============================================================================
// API文档组件
// ============================================================================

function StatusBadgeApiDocs() {
  return (
    <div className="space-y-6">
      {/* StatusBadge 组件API */}
      <div>
        <h3 className="font-medium text-lg mb-2">StatusBadge</h3>
        <p className="text-muted-foreground mb-4">基础状态徽章组件，用于展示各种状态信息</p>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">status</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">状态值</td>
              </tr>
              <tr>
                <td className="p-2 border">label</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">自定义状态文本</td>
              </tr>
              <tr>
                <td className="p-2 border">showStatusDot</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">false</td>
                <td className="p-2 border">显示状态点</td>
              </tr>
              <tr>
                <td className="p-2 border">showText</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">true</td>
                <td className="p-2 border">显示状态文本</td>
              </tr>
              <tr>
                <td className="p-2 border">size</td>
                <td className="p-2 border">"sm" | "md" | "lg"</td>
                <td className="p-2 border">"md"</td>
                <td className="p-2 border">徽章大小</td>
              </tr>
              <tr>
                <td className="p-2 border">variant</td>
                <td className="p-2 border">"default" | "secondary" | "outline" | "destructive"</td>
                <td className="p-2 border">"default"</td>
                <td className="p-2 border">徽章变体</td>
              </tr>
              <tr>
                <td className="p-2 border">statusMap</td>
                <td className="p-2 border">Record&lt;string, StatusConfig&gt;</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">自定义状态映射</td>
              </tr>
              <tr>
                <td className="p-2 border">className</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">自定义CSS类名</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* OnlineStatusBadge 组件API */}
      <div>
        <h3 className="font-medium text-lg mb-2">OnlineStatusBadge</h3>
        <p className="text-muted-foreground mb-4">在线状态徽章组件，专用于显示用户在线状态</p>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">status</td>
                <td className="p-2 border">"online" | "offline" | "away" | "busy" | "invisible"</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">在线状态</td>
              </tr>
              <tr>
                <td className="p-2 border">showText</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">true</td>
                <td className="p-2 border">显示状态文本</td>
              </tr>
              <tr>
                <td className="p-2 border">pulse</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">false</td>
                <td className="p-2 border">脉冲动画效果</td>
              </tr>
              <tr>
                <td className="p-2 border">size</td>
                <td className="p-2 border">"sm" | "md" | "lg"</td>
                <td className="p-2 border">"md"</td>
                <td className="p-2 border">徽章大小</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* 使用指南 */}
      <div>
        <h3 className="font-medium text-lg mb-2">使用指南</h3>
        <div className="space-y-4 text-sm">
          <div>
            <h4 className="font-medium mb-2">基本用法</h4>
            <p className="text-muted-foreground mb-2">
              最简单的状态徽章使用方式：
            </p>
            <pre className="bg-muted p-3 rounded-md overflow-x-auto">
              <code>{`<StatusBadge status="success" />
<StatusBadge status="warning" />
<StatusBadge status="error" />`}</code>
            </pre>
          </div>

          <div>
            <h4 className="font-medium mb-2">自定义状态映射</h4>
            <p className="text-muted-foreground mb-2">
              通过statusMap属性自定义状态样式：
            </p>
            <pre className="bg-muted p-3 rounded-md overflow-x-auto">
              <code>{`const customStatusMap = {
  approved: {
    label: "已批准",
    color: "bg-green-100 text-green-800",
    icon: CheckCircle,
  },
};

<StatusBadge status="approved" statusMap={customStatusMap} />`}</code>
            </pre>
          </div>

          <div>
            <h4 className="font-medium mb-2">在线状态</h4>
            <p className="text-muted-foreground mb-2">
              使用专用的在线状态徽章：
            </p>
            <pre className="bg-muted p-3 rounded-md overflow-x-auto">
              <code>{`<OnlineStatusBadge status="online" pulse />
<OnlineStatusBadge status="away" />
<OnlineStatusBadge status="busy" showText={false} />`}</code>
            </pre>
          </div>
        </div>
      </div>
    </div>
  )
}

// ============================================================================
// 主预览组件
// ============================================================================

export default function StatusBadgeExamplePage() {
  return (
    <ComponentPreviewContainer
      title="状态徽章 StatusBadge"
      description="用于展示不同状态、进度和优先级的徽章组件集合，支持多种预设状态和自定义配置"
      whenToUse="当需要显示状态信息时使用；适用于任务状态、用户在线状态、进度状态、优先级标识等场景；支持自定义状态映射和样式配置"
      examples={allExamples}
      apiDocs={<StatusBadgeApiDocs />}
    />
  )
}

"use client"

import React, { useState } from "react"
import { ComponentPreviewContainer } from "@/components/component-detail/preview-container"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { 
  SkeletonText, 
  SkeletonCard, 
  SkeletonTable, 
  SkeletonList,
  SkeletonComponents 
} from "@/components/common-custom/skeleton"

// 基础骨架屏示例代码
const basicSkeletonCode = `
import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { SkeletonText } from "@/components/common-custom/skeleton";

function BasicSkeleton() {
  const [loading, setLoading] = useState(true);
  
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">基础骨架屏</h3>
        <div className="flex items-center space-x-2">
          <Switch
            id="loading-mode-basic"
            checked={loading}
            onCheckedChange={setLoading}
          />
          <Label htmlFor="loading-mode-basic">切换加载状态</Label>
        </div>
      </div>
      
      <Card>
        <CardContent className="space-y-3 py-4">
          <SkeletonText 
            lines={3} 
            loading={loading}
            lastLineWidth={75}
          >
            <div className="space-y-2">
              <p>这是已加载的内容，可以切换开关查看骨架屏效果。</p>
              <p>骨架屏可以提高用户体验，减少用户等待的焦虑感。</p>
              <p>通过合理设计，让用户知道内容正在加载。</p>
            </div>
          </SkeletonText>
        </CardContent>
      </Card>
      
      <div className="mt-4">
        <h4 className="text-sm font-medium mb-2">其他参数</h4>
        <Card>
          <CardContent className="py-4 grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h5 className="text-sm font-medium mb-2">动画效果</h5>
              <SkeletonText 
                lines={2} 
                loading={true}
                animation="pulse"
              />
            </div>
            <div>
              <h5 className="text-sm font-medium mb-2">波浪动画</h5>
              <SkeletonText 
                lines={2} 
                loading={true}
                animation="wave"
              />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

render(<BasicSkeleton />);
`;

// 卡片骨架屏示例代码
const cardSkeletonCode = `
import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { SkeletonCard } from "@/components/common-custom/skeleton";

function CardSkeleton() {
  const [loading, setLoading] = useState(true);
  
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">卡片骨架屏</h3>
        <div className="flex items-center space-x-2">
          <Switch
            id="loading-mode-card"
            checked={loading}
            onCheckedChange={setLoading}
          />
          <Label htmlFor="loading-mode-card">切换加载状态</Label>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardContent className="py-4">
            <h4 className="text-sm font-medium mb-2">用户卡片</h4>
            <SkeletonCard 
              loading={loading}
              avatarSize="3rem"
              lines={2}
            >
              <div className="flex items-center space-x-4">
                <img 
                  src="/placeholder-user.jpg" 
                  alt="用户头像" 
                  className="h-12 w-12 rounded-full object-cover" 
                />
                <div>
                  <h4 className="font-medium">张三</h4>
                  <p className="text-sm text-muted-foreground">产品设计师</p>
                </div>
              </div>
            </SkeletonCard>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="py-4">
            <h4 className="text-sm font-medium mb-2">无头像卡片</h4>
            <SkeletonCard 
              loading={loading}
              showAvatar={false}
              lines={3}
            >
              <div>
                <h4 className="font-medium">设计系统介绍</h4>
                <p className="text-sm text-muted-foreground mt-1">设计系统是确保产品设计一致性的关键工具，它包含组件、样式和规范。</p>
              </div>
            </SkeletonCard>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

render(<CardSkeleton />);
`;

// 表格骨架屏示例代码
const tableSkeletonCode = `
import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { SkeletonTable } from "@/components/common-custom/skeleton";

function TableSkeleton() {
  const [loading, setLoading] = useState(true);
  
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">表格骨架屏</h3>
        <div className="flex items-center space-x-2">
          <Switch
            id="loading-mode-table"
            checked={loading}
            onCheckedChange={setLoading}
          />
          <Label htmlFor="loading-mode-table">切换加载状态</Label>
        </div>
      </div>
      
      <Card>
        <CardContent className="py-4">
          <SkeletonTable 
            rows={4} 
            columns={4} 
            loading={loading}
          >
            <div className="border rounded-md overflow-hidden">
              <table className="w-full text-left">
                <thead className="bg-muted">
                  <tr>
                    <th className="p-2">名称</th>
                    <th className="p-2">状态</th>
                    <th className="p-2">价格</th>
                    <th className="p-2">操作</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-t">
                    <td className="p-2">产品 A</td>
                    <td className="p-2">已发布</td>
                    <td className="p-2">¥100</td>
                    <td className="p-2">
                      <Button size="sm" variant="outline">查看</Button>
                    </td>
                  </tr>
                  <tr className="border-t">
                    <td className="p-2">产品 B</td>
                    <td className="p-2">草稿</td>
                    <td className="p-2">¥200</td>
                    <td className="p-2">
                      <Button size="sm" variant="outline">查看</Button>
                    </td>
                  </tr>
                  <tr className="border-t">
                    <td className="p-2">产品 C</td>
                    <td className="p-2">已发布</td>
                    <td className="p-2">¥150</td>
                    <td className="p-2">
                      <Button size="sm" variant="outline">查看</Button>
                    </td>
                  </tr>
                  <tr className="border-t">
                    <td className="p-2">产品 D</td>
                    <td className="p-2">已发布</td>
                    <td className="p-2">¥120</td>
                    <td className="p-2">
                      <Button size="sm" variant="outline">查看</Button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </SkeletonTable>
        </CardContent>
      </Card>
    </div>
  );
}

render(<TableSkeleton />);
`;

// 列表骨架屏示例代码
const listSkeletonCode = `
import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { SkeletonList } from "@/components/common-custom/skeleton";

function ListSkeleton() {
  const [loading, setLoading] = useState(true);
  
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">列表骨架屏</h3>
        <div className="flex items-center space-x-2">
          <Switch
            id="loading-mode-list"
            checked={loading}
            onCheckedChange={setLoading}
          />
          <Label htmlFor="loading-mode-list">切换加载状态</Label>
        </div>
      </div>
      
      <Card>
        <CardContent className="py-4">
          <SkeletonList 
            count={3}
            loading={loading}
          >
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="flex items-start space-x-4">
                  <div className="h-10 w-10 rounded-md bg-primary/10 flex items-center justify-center">
                    {i}
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium">任务 {i}</h4>
                    <p className="text-sm text-muted-foreground">这是任务 {i} 的描述信息</p>
                    <p className="text-xs text-muted-foreground mt-1">2024-06-{10 + i}</p>
                  </div>
                  <Button size="sm" variant="outline">查看</Button>
                </div>
              ))}
            </div>
          </SkeletonList>
        </CardContent>
      </Card>
    </div>
  );
}

render(<ListSkeleton />);
`;

// 预定义骨架屏示例代码
const predefinedSkeletonCode = `
import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { SkeletonComponents } from "@/components/common-custom/skeleton";

function PredefinedSkeleton() {
  const [loading, setLoading] = useState(true);
  
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">预定义骨架屏</h3>
        <div className="flex items-center space-x-2">
          <Switch
            id="loading-mode-predefined"
            checked={loading}
            onCheckedChange={setLoading}
          />
          <Label htmlFor="loading-mode-predefined">切换加载状态</Label>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <h4 className="text-sm font-medium mb-2">用户卡片</h4>
          {loading ? (
            <SkeletonComponents.UserCard />
          ) : (
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <img 
                    src="/placeholder-user.jpg" 
                    alt="用户头像" 
                    className="h-12 w-12 rounded-full object-cover" 
                  />
                  <div>
                    <h4 className="font-medium">王五</h4>
                    <p className="text-sm text-muted-foreground">前端开发工程师</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
        
        <div>
          <h4 className="text-sm font-medium mb-2">文章卡片</h4>
          {loading ? (
            <SkeletonComponents.ArticleCard />
          ) : (
            <Card>
              <CardContent className="p-6 space-y-4">
                <img 
                  src="/placeholder.jpg" 
                  alt="文章配图" 
                  className="h-40 w-full rounded-md object-cover" 
                />
                <div className="space-y-2">
                  <h3 className="text-lg font-semibold">如何设计优秀的用户界面</h3>
                  <p className="text-muted-foreground">本文探讨了用户界面设计的核心原则和最佳实践。</p>
                </div>
                <div className="flex items-center space-x-4">
                  <img 
                    src="/placeholder-user.jpg" 
                    alt="作者头像" 
                    className="h-8 w-8 rounded-full object-cover" 
                  />
                  <div>
                    <p className="text-sm font-medium">李四</p>
                    <p className="text-xs text-muted-foreground">2024-06-15</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}

render(<PredefinedSkeleton />);
`;

// API文档组件
function SkeletonApiDocs() {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="font-medium text-lg mb-2">骨架屏组件API</h3>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">组件</th>
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border" rowSpan={6}>SkeletonText</td>
                <td className="p-2 border">lines</td>
                <td className="p-2 border">number</td>
                <td className="p-2 border">3</td>
                <td className="p-2 border">文本行数</td>
              </tr>
              <tr>
                <td className="p-2 border">lineHeight</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">1rem</td>
                <td className="p-2 border">行高</td>
              </tr>
              <tr>
                <td className="p-2 border">spacing</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">0.5rem</td>
                <td className="p-2 border">行间距</td>
              </tr>
              <tr>
                <td className="p-2 border">lastLineWidth</td>
                <td className="p-2 border">number</td>
                <td className="p-2 border">70</td>
                <td className="p-2 border">最后一行宽度百分比</td>
              </tr>
              <tr>
                <td className="p-2 border">loading</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">true</td>
                <td className="p-2 border">是否显示骨架屏</td>
              </tr>
              <tr>
                <td className="p-2 border">animation</td>
                <td className="p-2 border">pulse | wave</td>
                <td className="p-2 border">pulse</td>
                <td className="p-2 border">动画效果</td>
              </tr>
              <tr>
                <td className="p-2 border" rowSpan={7}>SkeletonCard</td>
                <td className="p-2 border">showAvatar</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">true</td>
                <td className="p-2 border">是否显示头像</td>
              </tr>
              <tr>
                <td className="p-2 border">avatarSize</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">3rem</td>
                <td className="p-2 border">头像尺寸</td>
              </tr>
              <tr>
                <td className="p-2 border">showTitle</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">true</td>
                <td className="p-2 border">是否显示标题</td>
              </tr>
              <tr>
                <td className="p-2 border">titleHeight</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">1.5rem</td>
                <td className="p-2 border">标题高度</td>
              </tr>
              <tr>
                <td className="p-2 border">lines</td>
                <td className="p-2 border">number</td>
                <td className="p-2 border">3</td>
                <td className="p-2 border">内容行数</td>
              </tr>
              <tr>
                <td className="p-2 border">loading</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">true</td>
                <td className="p-2 border">是否显示骨架屏</td>
              </tr>
              <tr>
                <td className="p-2 border">animation</td>
                <td className="p-2 border">pulse | wave</td>
                <td className="p-2 border">pulse</td>
                <td className="p-2 border">动画效果</td>
              </tr>
              <tr>
                <td className="p-2 border" rowSpan={6}>SkeletonTable</td>
                <td className="p-2 border">rows</td>
                <td className="p-2 border">number</td>
                <td className="p-2 border">5</td>
                <td className="p-2 border">行数</td>
              </tr>
              <tr>
                <td className="p-2 border">columns</td>
                <td className="p-2 border">number</td>
                <td className="p-2 border">4</td>
                <td className="p-2 border">列数</td>
              </tr>
              <tr>
                <td className="p-2 border">showHeader</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">true</td>
                <td className="p-2 border">是否显示表头</td>
              </tr>
              <tr>
                <td className="p-2 border">headerHeight</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">1.5rem</td>
                <td className="p-2 border">表头高度</td>
              </tr>
              <tr>
                <td className="p-2 border">cellHeight</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">1rem</td>
                <td className="p-2 border">单元格高度</td>
              </tr>
              <tr>
                <td className="p-2 border">loading</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">true</td>
                <td className="p-2 border">是否显示骨架屏</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

export default function SkeletonPreview() {
  const examples = [
    {
      id: "basic-skeleton",
      title: "基础骨架屏",
      description: "基础文本骨架屏，用于显示段落加载状态",
      code: basicSkeletonCode,
      scope: { 
        React, 
        useState: React.useState,
        Card,
        CardContent,
        Switch,
        Label,
        SkeletonText
      },
    },
    {
      id: "card-skeleton",
      title: "卡片骨架屏",
      description: "用于卡片内容的加载状态，支持头像和文本",
      code: cardSkeletonCode,
      scope: { 
        React, 
        useState: React.useState,
        Card,
        CardContent,
        Switch,
        Label,
        SkeletonCard
      },
    },
    {
      id: "table-skeleton",
      title: "表格骨架屏",
      description: "适用于表格数据加载的骨架屏",
      code: tableSkeletonCode,
      scope: { 
        React, 
        useState: React.useState,
        Card,
        CardContent,
        Switch,
        Label,
        Button,
        SkeletonTable
      },
    },
    {
      id: "list-skeleton",
      title: "列表骨架屏",
      description: "用于列表数据加载的骨架屏",
      code: listSkeletonCode,
      scope: { 
        React, 
        useState: React.useState,
        Card,
        CardContent,
        Switch,
        Label,
        Button,
        SkeletonList
      },
    },
    {
      id: "predefined-skeleton",
      title: "预定义骨架屏",
      description: "预定义的骨架屏组件，包括用户卡片和文章卡片",
      code: predefinedSkeletonCode,
      scope: { 
        React, 
        useState: React.useState,
        Card,
        CardContent,
        Switch,
        Label,
        SkeletonComponents
      },
    },
  ];

  return (
    <ComponentPreviewContainer
      title="骨架屏 Skeleton"
      description="在内容加载过程中显示的占位符，提供更好的用户体验"
      whenToUse="当页面内容需要一段时间加载时；当需要降低用户等待焦虑感时；当希望提供更平滑的加载体验时"
      examples={examples}
      apiDocs={<SkeletonApiDocs />}
    />
  );
} 
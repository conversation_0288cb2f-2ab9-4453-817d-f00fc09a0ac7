/**
 * 标签组件示例代码集合
 * 
 * 按照新规范管理示例代码，避免在页面文件中硬编码
 */

import React from "react"
import { Tag, TagGroup, ColorTag, TagInput } from "@/components/common-custom/tag"
import { Star, Heart, Bookmark, User, Settings } from "lucide-react"

// ============================================================================
// 基础标签示例
// ============================================================================

export const basicTagExample = {
  id: "basic-tag",
  title: "基础标签",
  description: "展示不同变体和尺寸的基础标签",
  code: `
import React, { useState } from "react";
import { Tag } from "@/components/common-custom/tag";
import { Star, Heart, Bookmark } from "lucide-react";

function BasicTagExample() {
  const [selectedTags, setSelectedTags] = useState(new Set(["react"]));
  
  const handleTagClick = (tag) => {
    const newSelected = new Set(selectedTags);
    if (newSelected.has(tag)) {
      newSelected.delete(tag);
    } else {
      newSelected.add(tag);
    }
    setSelectedTags(newSelected);
  };
  
  return (
    <div className="space-y-6">
      <div>
        <h4 className="text-sm font-medium mb-2">不同变体</h4>
        <div className="flex flex-wrap items-center gap-2">
          <Tag label="默认" variant="default" />
          <Tag label="次要" variant="secondary" />
          <Tag label="轮廓" variant="outline" />
          <Tag label="危险" variant="destructive" />
        </div>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">不同尺寸</h4>
        <div className="flex flex-wrap items-center gap-2">
          <Tag label="小标签" size="sm" />
          <Tag label="中标签" size="md" />
          <Tag label="大标签" size="lg" />
        </div>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">带图标</h4>
        <div className="flex flex-wrap items-center gap-2">
          <Tag label="收藏" icon={Star} />
          <Tag label="喜欢" icon={Heart} />
          <Tag label="书签" icon={Bookmark} />
        </div>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">可关闭</h4>
        <div className="flex flex-wrap items-center gap-2">
          <Tag label="JavaScript" closable onClose={() => console.log('关闭 JavaScript')} />
          <Tag label="TypeScript" closable onClose={() => console.log('关闭 TypeScript')} />
          <Tag label="React" closable onClose={() => console.log('关闭 React')} />
        </div>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">可选择</h4>
        <div className="flex flex-wrap items-center gap-2">
          {["react", "vue", "angular", "svelte"].map((tag) => (
            <Tag
              key={tag}
              label={tag}
              selected={selectedTags.has(tag)}
              onClick={() => handleTagClick(tag)}
              variant={selectedTags.has(tag) ? "default" : "outline"}
            />
          ))}
        </div>
      </div>
    </div>
  );
}

render(<BasicTagExample />);
  `,
  scope: { Tag, Star, Heart, Bookmark, React, useState: React.useState },
}

// ============================================================================
// 标签组示例
// ============================================================================

export const tagGroupExample = {
  id: "tag-group",
  title: "标签组",
  description: "展示标签组的使用和配置",
  code: `
import React, { useState } from "react";
import { TagGroup } from "@/components/common-custom/tag";

function TagGroupExample() {
  const [tags, setTags] = useState([
    "JavaScript", "TypeScript", "React", "Vue", "Angular", 
    "Node.js", "Express", "MongoDB", "PostgreSQL", "Redis"
  ]);
  
  const handleTagClose = (tag, index) => {
    const newTags = tags.filter((_, i) => i !== index);
    setTags(newTags);
  };
  
  const handleTagClick = (tag, index) => {
    console.log(\`点击了标签: \${tag}\`);
  };
  
  return (
    <div className="space-y-6">
      <div>
        <h4 className="text-sm font-medium mb-2">基础标签组</h4>
        <TagGroup
          tags={["前端", "后端", "全栈", "移动端", "DevOps"]}
          onClick={handleTagClick}
        />
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">可关闭标签组</h4>
        <TagGroup
          tags={tags}
          closable
          onClose={handleTagClose}
          onClick={handleTagClick}
        />
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">限制显示数量</h4>
        <TagGroup
          tags={[
            "HTML", "CSS", "JavaScript", "TypeScript", "React", 
            "Vue", "Angular", "Svelte", "Node.js", "Python"
          ]}
          maxVisible={5}
          moreTagsDisplay="count"
        />
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">不同变体</h4>
        <div className="space-y-3">
          <TagGroup
            tags={["默认变体", "标签1", "标签2"]}
            variant="default"
          />
          <TagGroup
            tags={["轮廓变体", "标签1", "标签2"]}
            variant="outline"
          />
        </div>
      </div>
    </div>
  );
}

render(<TagGroupExample />);
  `,
  scope: { TagGroup, React, useState: React.useState },
}

// ============================================================================
// 颜色标签示例
// ============================================================================

export const colorTagExample = {
  id: "color-tag",
  title: "颜色标签",
  description: "展示不同颜色和填充样式的标签",
  code: `
import React from "react";
import { ColorTag } from "@/components/common-custom/tag";

function ColorTagExample() {
  const presetColors = [
    "red", "green", "blue", "yellow", "purple", 
    "cyan", "orange", "pink", "gray"
  ];
  
  return (
    <div className="space-y-6">
      <div>
        <h4 className="text-sm font-medium mb-2">预设颜色</h4>
        <div className="flex flex-wrap items-center gap-2">
          {presetColors.map((color) => (
            <ColorTag
              key={color}
              label={color}
              preset={color}
              fill="light"
            />
          ))}
        </div>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">不同填充样式</h4>
        <div className="space-y-3">
          <div>
            <span className="text-xs text-muted-foreground mb-1 block">浅色填充</span>
            <div className="flex flex-wrap items-center gap-2">
              <ColorTag label="成功" preset="green" fill="light" />
              <ColorTag label="警告" preset="yellow" fill="light" />
              <ColorTag label="错误" preset="red" fill="light" />
            </div>
          </div>
          
          <div>
            <span className="text-xs text-muted-foreground mb-1 block">实色填充</span>
            <div className="flex flex-wrap items-center gap-2">
              <ColorTag label="成功" preset="green" fill="solid" />
              <ColorTag label="警告" preset="yellow" fill="solid" />
              <ColorTag label="错误" preset="red" fill="solid" />
            </div>
          </div>
          
          <div>
            <span className="text-xs text-muted-foreground mb-1 block">轮廓样式</span>
            <div className="flex flex-wrap items-center gap-2">
              <ColorTag label="成功" preset="green" fill="outline" />
              <ColorTag label="警告" preset="yellow" fill="outline" />
              <ColorTag label="错误" preset="red" fill="outline" />
            </div>
          </div>
        </div>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">自定义颜色</h4>
        <div className="flex flex-wrap items-center gap-2">
          <ColorTag 
            label="自定义1" 
            color="#ff6b6b" 
            fill="light"
          />
          <ColorTag 
            label="自定义2" 
            color="#4ecdc4" 
            fill="solid"
          />
          <ColorTag 
            label="自定义3" 
            color="#45b7d1" 
            fill="outline"
          />
        </div>
      </div>
    </div>
  );
}

render(<ColorTagExample />);
  `,
  scope: { ColorTag, React },
}

// ============================================================================
// 标签输入框示例
// ============================================================================

export const tagInputExample = {
  id: "tag-input",
  title: "标签输入框",
  description: "展示标签输入框的功能和配置",
  code: `
import React, { useState } from "react";
import { TagInput } from "@/components/common-custom/tag";

function TagInputExample() {
  const [basicTags, setBasicTags] = useState(["React", "TypeScript"]);
  const [limitedTags, setLimitedTags] = useState(["JavaScript"]);
  const [validatedTags, setValidatedTags] = useState([]);
  
  const validateTag = (value) => {
    if (value.length < 2) {
      return "标签长度至少2个字符";
    }
    if (!/^[a-zA-Z\u4e00-\u9fa5]+$/.test(value)) {
      return "只能包含字母和中文";
    }
    return true;
  };
  
  return (
    <div className="space-y-6">
      <div>
        <h4 className="text-sm font-medium mb-2">基础用法</h4>
        <TagInput
          value={basicTags}
          onChange={setBasicTags}
          placeholder="输入标签，按回车或逗号分隔"
        />
        <p className="text-xs text-muted-foreground mt-1">
          当前标签: {basicTags.join(", ")}
        </p>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">限制数量</h4>
        <TagInput
          value={limitedTags}
          onChange={setLimitedTags}
          placeholder="最多3个标签"
          maxTags={3}
        />
        <p className="text-xs text-muted-foreground mt-1">
          已添加 {limitedTags.length}/3 个标签
        </p>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">验证规则</h4>
        <TagInput
          value={validatedTags}
          onChange={setValidatedTags}
          placeholder="只能输入字母和中文，至少2个字符"
          validate={validateTag}
          minLength={2}
          maxLength={10}
        />
        <p className="text-xs text-muted-foreground mt-1">
          验证规则: 只能包含字母和中文，长度2-10个字符
        </p>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">不同尺寸</h4>
        <div className="space-y-3">
          <TagInput
            value={["小尺寸"]}
            onChange={() => {}}
            placeholder="小尺寸输入框"
            size="sm"
          />
          <TagInput
            value={["中尺寸"]}
            onChange={() => {}}
            placeholder="中尺寸输入框"
            size="md"
          />
          <TagInput
            value={["大尺寸"]}
            onChange={() => {}}
            placeholder="大尺寸输入框"
            size="lg"
          />
        </div>
      </div>
    </div>
  );
}

render(<TagInputExample />);
  `,
  scope: { TagInput, React, useState: React.useState },
}

// ============================================================================
// 统一导出所有示例
// ============================================================================

export const allExamples = [
  basicTagExample,
  tagGroupExample,
  colorTagExample,
  tagInputExample,
]

// ============================================================================
// 按类别导出示例
// ============================================================================

export const basicExamples = [basicTagExample, tagGroupExample]
export const advancedExamples = [colorTagExample, tagInputExample]

// ============================================================================
// 示例元数据
// ============================================================================

export const exampleMetadata = {
  total: allExamples.length,
  categories: {
    basic: basicExamples.length,
    advanced: advancedExamples.length,
  },
  tags: ["tag", "label", "input", "group", "color", "filter"],
  lastUpdated: "2024-01-01",
}

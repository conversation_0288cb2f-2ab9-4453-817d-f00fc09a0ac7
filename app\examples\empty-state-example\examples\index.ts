/**
 * 空状态组件示例代码集合
 * 
 * 按照新规范管理示例代码，避免在页面文件中硬编码
 */

import React from "react"
import { EmptyState } from "@/components/common-custom/empty-state"
import { FileX, Plus, Search, Inbox, Users, ShoppingCart, Image, Wifi } from "lucide-react"

// ============================================================================
// 基础空状态示例
// ============================================================================

export const basicEmptyExample = {
  id: "basic-empty-state",
  title: "基础空状态",
  description: "展示基础的空状态组件",
  code: `
import React from "react";
import { EmptyState } from "@/components/common-custom/empty-state";
import { FileX, Plus } from "lucide-react";

function BasicEmptyExample() {
  return (
    <div className="space-y-8">
      <EmptyState
        icon={FileX}
        title="暂无数据"
        description="当前没有任何数据，请添加一些内容"
        actionLabel="添加数据"
        actionIcon={Plus}
        onAction={() => console.log("添加数据")}
      />
    </div>
  );
}

render(<BasicEmptyExample />);
  `,
  scope: { EmptyState, FileX, Plus, React },
}

// ============================================================================
// 搜索空状态示例
// ============================================================================

export const searchEmptyExample = {
  id: "search-empty-state",
  title: "搜索空状态",
  description: "搜索无结果时的空状态",
  code: `
import React from "react";
import { EmptyState } from "@/components/common-custom/empty-state";
import { Search } from "lucide-react";

function SearchEmptyExample() {
  return (
    <EmptyState
      icon={Search}
      title="未找到相关内容"
      description="尝试调整搜索关键词或筛选条件"
      actionLabel="清除筛选"
      onAction={() => console.log("清除筛选")}
    />
  );
}

render(<SearchEmptyExample />);
  `,
  scope: { EmptyState, Search, React },
}

// ============================================================================
// 网络错误空状态示例
// ============================================================================

export const networkEmptyExample = {
  id: "network-empty-state",
  title: "网络错误空状态",
  description: "网络连接失败时的空状态",
  code: `
import React from "react";
import { EmptyState } from "@/components/common-custom/empty-state";
import { Wifi } from "lucide-react";

function NetworkEmptyExample() {
  return (
    <EmptyState
      icon={Wifi}
      title="网络连接失败"
      description="请检查网络连接后重试"
      actionLabel="重新加载"
      onAction={() => console.log("重新加载")}
    />
  );
}

render(<NetworkEmptyExample />);
  `,
  scope: { EmptyState, Wifi, React },
}

// ============================================================================
// 购物车空状态示例
// ============================================================================

export const cartEmptyExample = {
  id: "cart-empty-state",
  title: "购物车空状态",
  description: "购物车为空时的状态",
  code: `
import React from "react";
import { EmptyState } from "@/components/common-custom/empty-state";
import { ShoppingCart, Plus } from "lucide-react";

function CartEmptyExample() {
  return (
    <EmptyState
      icon={ShoppingCart}
      title="购物车是空的"
      description="快去挑选你喜欢的商品吧"
      actionLabel="去购物"
      actionIcon={Plus}
      onAction={() => console.log("去购物")}
    />
  );
}

render(<CartEmptyExample />);
  `,
  scope: { EmptyState, ShoppingCart, Plus, React },
}

// ============================================================================
// 统一导出所有示例
// ============================================================================

export const allExamples = [
  basicEmptyExample,
  searchEmptyExample,
  networkEmptyExample,
  cartEmptyExample,
]

// ============================================================================
// 按类别导出示例
// ============================================================================

export const basicExamples = [basicEmptyExample]
export const scenarioExamples = [searchEmptyExample, networkEmptyExample, cartEmptyExample]

// ============================================================================
// 示例元数据
// ============================================================================

export const exampleMetadata = {
  total: allExamples.length,
  categories: {
    basic: basicExamples.length,
    scenarios: scenarioExamples.length,
  },
  tags: ["empty", "state", "placeholder", "no-data", "search", "error"],
  lastUpdated: new Date().toISOString().split('T')[0],
}

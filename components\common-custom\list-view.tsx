"use client"

import { <PERSON><PERSON>N<PERSON>, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Input } from "@/components/ui/input"
import { cn } from "@/lib/utils"
import {
  Search,
  Filter,
  MoreHorizontal,
  MessageCircle,
  Share2,
  Clock,
  Star,
  Eye,
  ThumbsUp,
  Calendar,
  MapPin,
  X,
  Check,
  ChevronDown,
} from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Checkbox } from "@/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { format } from "date-fns"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import type {
  MessageItemProps,
  MessageListProps,
  NotificationItemProps,
  NotificationListProps,
  CommentItemProps,
  CommentListProps,
  UserItemProps,
  UserListProps,
  ListViewControlsProps,
  FilterConfig,
  FilterOption
} from "@/types/list-view"

/**
 * 消息项组件
 */
export function MessageItem({
  sender,
  avatar,
  subject,
  preview,
  time,
  unread = false,
  important = false,
  onAction,
  className,
}: MessageItemProps) {
  return (
    <div
      className={cn(
        "flex items-start gap-3 p-4 rounded-lg hover:bg-muted/50 cursor-pointer transition-colors",
        unread ? "bg-blue-50 border-l-2 border-l-blue-500" : "",
        className
      )}
    >
      <Avatar className="h-10 w-10">
        {avatar && <AvatarImage src={avatar} alt={sender} />}
        <AvatarFallback>{sender.charAt(0)}</AvatarFallback>
      </Avatar>
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between mb-1">
          <div className="flex items-center gap-2">
            <span className={cn("font-medium", unread ? "text-primary" : "")}>{sender}</span>
            {important && <span className="text-red-500">⭐</span>}
            {unread && <div className="w-2 h-2 bg-blue-500 rounded-full" />}
          </div>
          <span className="text-xs text-muted-foreground">{time}</span>
        </div>
        <h4 className={cn("font-medium mb-1", unread ? "text-foreground" : "text-muted-foreground")}>
          {subject}
        </h4>
        <p className="text-sm text-muted-foreground line-clamp-2">{preview}</p>
      </div>
      <Button variant="ghost" size="sm" onClick={onAction} className="cursor-pointer">
        <MoreHorizontal className="h-4 w-4" />
      </Button>
    </div>
  )
}

/**
 * 消息列表组件
 */
export function MessageList({
  messages,
  title,
  description,
  onMessageAction,
  className,
}: MessageListProps) {
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>{title || "消息列表"}</CardTitle>
        <CardDescription>{description || "您的最新消息和对话"}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-1">
          {messages.map((message) => (
            <MessageItem
              key={message.id}
              sender={message.sender}
              avatar={message.avatar}
              subject={message.subject}
              preview={message.preview}
              time={message.time}
              unread={message.unread}
              important={message.important}
              onAction={() => onMessageAction?.(message)}
            />
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

/**
 * 通知项组件
 */
export function NotificationItem({
  type,
  title,
  message,
  time,
  read = false,
  onClick,
  className,
}: NotificationItemProps) {
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "system":
        return "🔧"
      case "user":
        return "👤"
      case "order":
        return "🛒"
      case "security":
        return "🔒"
      default:
        return "📢"
    }
  }

  return (
    <div
      onClick={onClick}
      className={cn(
        "flex items-start gap-3 p-4 rounded-lg hover:bg-muted/50 cursor-pointer transition-colors",
        !read ? "bg-blue-50" : "",
        className
      )}
    >
      <div className="text-2xl">{getNotificationIcon(type)}</div>
      <div className="flex-1">
        <div className="flex items-center justify-between mb-1">
          <h4 className={cn("font-medium", !read ? "text-primary" : "")}>{title}</h4>
          <div className="flex items-center gap-2">
            <span className="text-xs text-muted-foreground">{time}</span>
            {!read && <div className="w-2 h-2 bg-blue-500 rounded-full" />}
          </div>
        </div>
        <p className="text-sm text-muted-foreground">{message}</p>
      </div>
    </div>
  )
}

/**
 * 通知列表组件
 */
export function NotificationList({
  notifications,
  title,
  description,
  onNotificationClick,
  className,
}: NotificationListProps) {
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>{title || "通知中心"}</CardTitle>
        <CardDescription>{description || "系统通知和重要提醒"}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-1">
          {notifications.map((notification) => (
            <NotificationItem
              key={notification.id}
              type={notification.type}
              title={notification.title}
              message={notification.message}
              time={notification.time}
              read={notification.read}
              onClick={() => onNotificationClick?.(notification)}
            />
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

/**
 * 评论项组件
 */
export function CommentItem({
  author,
  avatar,
  content,
  time,
  likes = 0,
  replies = 0,
  rating = 0,
  onLike,
  onReply,
  onShare,
  className,
}: CommentItemProps) {
  return (
    <div className={cn("border rounded-lg p-4", className)}>
      <div className="flex items-start gap-3">
        <Avatar className="h-10 w-10">
          {avatar && <AvatarImage src={avatar} alt={author} />}
          <AvatarFallback>{author.charAt(0)}</AvatarFallback>
        </Avatar>
        <div className="flex-1">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <span className="font-medium">{author}</span>
              {rating > 0 && (
                <div className="flex items-center gap-1">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <Star
                      key={i}
                      className={cn(
                        "h-3 w-3",
                        i < rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
                      )}
                    />
                  ))}
                </div>
              )}
            </div>
            <span className="text-xs text-muted-foreground">{time}</span>
          </div>
          <p className="text-sm mb-3">{content}</p>
          <div className="flex items-center gap-4">
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={onLike}
              className="cursor-pointer"
            >
              <ThumbsUp className="h-4 w-4 mr-1" />
              {likes}
            </Button>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={onReply}
              className="cursor-pointer"
            >
              <MessageCircle className="h-4 w-4 mr-1" />
              {replies}
            </Button>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={onShare}
              className="cursor-pointer"
            >
              <Share2 className="h-4 w-4 mr-1" />
              分享
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

/**
 * 评论列表组件
 */
export function CommentList({
  comments,
  title,
  description,
  onCommentLike,
  onCommentReply,
  onCommentShare,
  className,
}: CommentListProps) {
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>{title || "用户评论"}</CardTitle>
        <CardDescription>{description || "最新的用户反馈和评论"}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {comments.map((comment) => (
            <CommentItem
              key={comment.id}
              author={comment.author}
              avatar={comment.avatar}
              content={comment.content}
              time={comment.time}
              likes={comment.likes}
              replies={comment.replies}
              rating={comment.rating}
              onLike={() => onCommentLike?.(comment)}
              onReply={() => onCommentReply?.(comment)}
              onShare={() => onCommentShare?.(comment)}
            />
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

/**
 * 用户项组件
 */
export function UserItem({
  name,
  avatar,
  role,
  department,
  location,
  joinDate,
  status,
  projects,
  onMessage,
  onViewDetails,
  className,
}: UserItemProps) {
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "online":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">在线</Badge>
      case "offline":
        return <Badge variant="secondary">离线</Badge>
      case "busy":
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">忙碌</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  return (
    <div
      className={cn(
        "flex items-center gap-4 p-4 rounded-lg hover:bg-muted/50 cursor-pointer transition-colors",
        className
      )}
    >
      <Avatar className="h-12 w-12">
        {avatar && <AvatarImage src={avatar} alt={name} />}
        <AvatarFallback>{name.charAt(0)}</AvatarFallback>
      </Avatar>
      <div className="flex-1">
        <div className="flex items-center justify-between mb-1">
          <div className="flex items-center gap-2">
            <h4 className="font-medium">{name}</h4>
            {getStatusBadge(status)}
          </div>
          <div className="text-right">
            <div className="text-sm font-medium">{projects} 个项目</div>
            <div className="text-xs text-muted-foreground">参与项目</div>
          </div>
        </div>
        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          <span>{role}</span>
          <span>•</span>
          <span>{department}</span>
          <span>•</span>
          <div className="flex items-center gap-1">
            <MapPin className="h-3 w-3" />
            {location}
          </div>
          <span>•</span>
          <div className="flex items-center gap-1">
            <Calendar className="h-3 w-3" />
            入职 {joinDate}
          </div>
        </div>
      </div>
      <div className="flex gap-2">
        <Button 
          variant="outline" 
          size="sm" 
          onClick={onMessage}
          className="cursor-pointer"
        >
          <MessageCircle className="h-4 w-4 mr-2" />
          消息
        </Button>
        <Button 
          variant="outline" 
          size="sm" 
          onClick={onViewDetails}
          className="cursor-pointer"
        >
          <Eye className="h-4 w-4 mr-2" />
          详情
        </Button>
      </div>
    </div>
  )
}

/**
 * 用户列表组件
 */
export function UserList({
  users,
  title,
  description,
  onUserMessage,
  onUserDetails,
  className,
}: UserListProps) {
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>{title || "团队成员"}</CardTitle>
        <CardDescription>{description || "团队成员信息和状态"}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-1">
          {users.map((user) => (
            <UserItem
              key={user.id}
              name={user.name}
              avatar={user.avatar}
              role={user.role}
              department={user.department}
              location={user.location}
              joinDate={user.joinDate}
              status={user.status}
              projects={user.projects}
              onMessage={() => onUserMessage?.(user)}
              onViewDetails={() => onUserDetails?.(user)}
            />
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

/**
 * 筛选器组件
 */
function FilterItem({ 
  filter, 
  onFilterChange 
}: { 
  filter: FilterConfig, 
  onFilterChange: (id: string, value: any) => void 
}) {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(
    filter.value as Date | undefined
  );
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({
    from: Array.isArray(filter.value) && filter.value[0] ? new Date(filter.value[0] as string) : undefined,
    to: Array.isArray(filter.value) && filter.value[1] ? new Date(filter.value[1] as string) : undefined,
  });

  switch (filter.type) {
    case 'select':
      return (
        <div className="space-y-2">
          <Label htmlFor={filter.id}>{filter.label}</Label>
          <Select
            defaultValue={String(filter.value || "all")}
            onValueChange={(value) => onFilterChange(filter.id, value)}
          >
            <SelectTrigger id={filter.id} className="w-full">
              <SelectValue placeholder={filter.placeholder || `选择${filter.label}`} />
            </SelectTrigger>
            <SelectContent>
              {filter.options?.map((option) => (
                <SelectItem 
                  key={String(option.value)} 
                  value={String(option.value) === "" ? "none" : String(option.value)}
                >
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      );
      
    case 'multiSelect':
      return (
        <div className="space-y-2">
          <Label>{filter.label}</Label>
          <div className="space-y-2">
            {filter.options?.map((option) => (
              <div key={option.value.toString()} className="flex items-center space-x-2">
                <Checkbox
                  id={`${filter.id}-${option.value}`}
                  checked={Array.isArray(filter.value) && filter.value.includes(option.value)}
                  onCheckedChange={(checked) => {
                    const currentValues = Array.isArray(filter.value) ? [...filter.value] : [];
                    if (checked) {
                      onFilterChange(filter.id, [...currentValues, option.value]);
                    } else {
                      onFilterChange(
                        filter.id,
                        currentValues.filter((v) => v !== option.value)
                      );
                    }
                  }}
                />
                <Label htmlFor={`${filter.id}-${option.value}`} className="text-sm font-normal">
                  {option.label}
                </Label>
              </div>
            ))}
          </div>
        </div>
      );
      
    case 'checkbox':
      return (
        <div className="flex items-center space-x-2">
          <Checkbox
            id={filter.id}
            checked={filter.value as boolean}
            onCheckedChange={(checked) => onFilterChange(filter.id, checked)}
          />
          <Label htmlFor={filter.id} className="text-sm font-normal">
            {filter.label}
          </Label>
        </div>
      );
      
    case 'radio':
      return (
        <div className="space-y-2">
          <Label>{filter.label}</Label>
          <RadioGroup
            defaultValue={filter.value as string}
            onValueChange={(value) => onFilterChange(filter.id, value)}
          >
            <div className="space-y-2">
              {filter.options?.map((option) => (
                <div key={option.value.toString()} className="flex items-center space-x-2">
                  <RadioGroupItem
                    value={option.value.toString()}
                    id={`${filter.id}-${option.value}`}
                  />
                  <Label htmlFor={`${filter.id}-${option.value}`} className="text-sm font-normal">
                    {option.label}
                  </Label>
                </div>
              ))}
            </div>
          </RadioGroup>
        </div>
      );
      
    case 'date':
      return (
        <div className="space-y-2">
          <Label>{filter.label}</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="w-full justify-start text-left font-normal"
              >
                <Calendar className="mr-2 h-4 w-4" />
                {selectedDate ? (
                  format(selectedDate, 'yyyy-MM-dd')
                ) : (
                  <span className="text-muted-foreground">{filter.placeholder || "选择日期"}</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <CalendarComponent
                mode="single"
                selected={selectedDate}
                onSelect={(date) => {
                  setSelectedDate(date);
                  onFilterChange(filter.id, date);
                }}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>
      );
      
    case 'dateRange':
      return (
        <div className="space-y-2">
          <Label>{filter.label}</Label>
          <div className="grid gap-2">
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full justify-start text-left font-normal"
                >
                  <Calendar className="mr-2 h-4 w-4" />
                  {dateRange.from ? (
                    dateRange.to ? (
                      <>
                        {format(dateRange.from, 'yyyy-MM-dd')} 至 {format(dateRange.to, 'yyyy-MM-dd')}
                      </>
                    ) : (
                      format(dateRange.from, 'yyyy-MM-dd')
                    )
                  ) : (
                    <span className="text-muted-foreground">{filter.placeholder || "选择日期范围"}</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <CalendarComponent
                  mode="range"
                  selected={{
                    from: dateRange.from,
                    to: dateRange.to,
                  }}
                  onSelect={(range) => {
                    setDateRange(range || { from: undefined, to: undefined });
                    onFilterChange(filter.id, [range?.from, range?.to]);
                  }}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>
      );
      
    default:
      return null;
  }
}

/**
 * 筛选器对话框
 */
function FilterDialog({
  filters = [],
  onFilterChange,
  onFilterReset,
  onFilterApply,
  trigger,
}: {
  filters: FilterConfig[];
  onFilterChange: (id: string, value: any) => void;
  onFilterReset?: () => void;
  onFilterApply?: () => void;
  trigger: React.ReactNode;
}) {
  const [open, setOpen] = useState(false);
  
  const handleApply = () => {
    onFilterApply?.();
    setOpen(false);
  };
  
  const handleReset = () => {
    onFilterReset?.();
  };
  
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>筛选</DialogTitle>
          <DialogDescription>
            设置筛选条件以缩小列表范围
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4 max-h-[60vh] overflow-y-auto">
          {filters.map((filter) => (
            <FilterItem
              key={filter.id}
              filter={filter}
              onFilterChange={onFilterChange}
            />
          ))}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={handleReset}>
            重置
          </Button>
          <Button onClick={handleApply}>应用</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

/**
 * 列表视图控制组件
 */
export function ListViewControls({
  viewTypes = [],
  currentView,
  onViewChange,
  searchValue,
  onSearchChange,
  filters = [],
  onFilterChange = () => {},
  onFilterReset,
  onFilterApply,
  onFilter,
  className,
}: ListViewControlsProps) {
  // 计算已应用的筛选器数量
  const appliedFiltersCount = filters.filter(filter => {
    if (filter.value === undefined || filter.value === null) return false;
    if (Array.isArray(filter.value) && filter.value.length === 0) return false;
    if (filter.value === "" || filter.value === false) return false;
    return true;
  }).length;

  return (
    <div className={cn("flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mb-4", className)}>
      <div className="flex flex-wrap items-center gap-2">
        {viewTypes.map((viewType) => (
          <Button
            key={viewType.value}
            variant={currentView === viewType.value ? "default" : "outline"}
            size="sm"
            onClick={() => onViewChange(viewType.value)}
          >
            {viewType.label}
          </Button>
        ))}
      </div>
      <div className="flex flex-wrap items-center gap-2 w-full sm:w-auto">
        <div className="relative flex-1 sm:flex-none">
          <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索..."
            value={searchValue}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-8 h-9 w-full sm:w-[200px]"
          />
        </div>
        
        {filters.length > 0 ? (
          <FilterDialog
            filters={filters}
            onFilterChange={onFilterChange}
            onFilterReset={onFilterReset}
            onFilterApply={onFilterApply}
            trigger={
              <Button variant="outline" size="sm" className="h-9">
                <Filter className="h-4 w-4 mr-2" />
                筛选
                {appliedFiltersCount > 0 && (
                  <Badge variant="secondary" className="ml-2">
                    {appliedFiltersCount}
                  </Badge>
                )}
              </Button>
            }
          />
        ) : onFilter ? (
          <Button variant="outline" size="sm" className="h-9" onClick={onFilter}>
            <Filter className="h-4 w-4 mr-2" />
            筛选
          </Button>
        ) : null}
      </div>
    </div>
  )
}

/**
 * 加载更多按钮组件
 */
export function LoadMoreButton({
  onClick,
  loading = false,
  className,
}: {
  onClick: () => void
  loading?: boolean
  className?: string
}) {
  return (
    <div className={cn("text-center", className)}>
      <Button 
        variant="outline" 
        onClick={onClick}
        disabled={loading}
        className="cursor-pointer"
      >
        {loading ? (
          <>
            <Clock className="h-4 w-4 mr-2 animate-spin" />
            加载中...
          </>
        ) : (
          <>
            <Clock className="h-4 w-4 mr-2" />
            加载更多
          </>
        )}
      </Button>
    </div>
  )
} 
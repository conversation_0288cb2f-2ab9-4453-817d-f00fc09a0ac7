"use client"

import React, { useState, useEffect } from "react"
import { 
  Shield, 
  CheckCircle2, 
  Loader2
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList
} from "@/components/ui/command"

import { getAllRolesRequest } from "@/services/api/roleRequestApi"

interface Role {
  code: string;
  name: string;
  description?: string;
  roleCode?: string;
  roleName?: string;
}

interface RoleSelectorProps {
  userId: number;
  onSelect: (roleCodes: string[]) => void;
  onCancel: () => void;
}

export function RoleSelector({ userId, onSelect, onCancel }: RoleSelectorProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [roles, setRoles] = useState<Role[]>([])
  const [selectedRoles, setSelectedRoles] = useState<Set<string>>(new Set())
  
  // 获取角色列表
  useEffect(() => {
    const loadRoles = async () => {
      setIsLoading(true)
      setError(null)
      try {
        const data = await getAllRolesRequest()
        if (data && data.length > 0) {
          // 转换API返回的数据格式
          const formattedRoles = data.map(role => ({
            code: role.roleCode || "",
            name: role.roleName || "",
            description: role.description
          }))
          setRoles(formattedRoles)
        } else {
          // 模拟数据，实际项目中应删除
          setRoles([
            { code: "admin", name: "管理员", description: "系统管理员，拥有所有权限" },
            { code: "user", name: "普通用户", description: "普通用户，拥有基本权限" },
            { code: "viewer", name: "访客", description: "访客用户，仅有查看权限" },
            { code: "editor", name: "编辑者", description: "可以编辑内容的用户" },
            { code: "manager", name: "经理", description: "部门经理用户" }
          ])
        }
      } catch (err) {
        console.error("获取角色列表失败", err)
        setError("获取角色列表失败")
        
        // 模拟数据，实际项目中应删除
        setRoles([
          { code: "admin", name: "管理员", description: "系统管理员，拥有所有权限" },
          { code: "user", name: "普通用户", description: "普通用户，拥有基本权限" },
          { code: "viewer", name: "访客", description: "访客用户，仅有查看权限" },
          { code: "editor", name: "编辑者", description: "可以编辑内容的用户" },
          { code: "manager", name: "经理", description: "部门经理用户" }
        ])
      } finally {
        setIsLoading(false)
      }
    }
    
    loadRoles()
  }, [])
  
  // 切换角色选中状态
  const toggleRole = (code: string) => {
    const newSelected = new Set(selectedRoles)
    if (newSelected.has(code)) {
      newSelected.delete(code)
    } else {
      newSelected.add(code)
    }
    setSelectedRoles(newSelected)
  }
  
  // 确认选择
  const handleConfirm = () => {
    onSelect(Array.from(selectedRoles))
  }
  
  return (
    <div className="space-y-4 pt-4 pb-2">
      {isLoading ? (
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : error ? (
        <div className="text-center py-6 text-muted-foreground">
          <p>{error}</p>
        </div>
      ) : (
        <div className="space-y-4">
          <Command className="rounded-lg border shadow-md">
            <CommandInput placeholder="搜索角色..." />
            <CommandList>
              <CommandEmpty>未找到角色</CommandEmpty>
              <CommandGroup heading="可选角色">
                {roles.map((role) => (
                  <CommandItem 
                    key={role.code}
                    value={role.name}
                    onSelect={() => toggleRole(role.code)}
                    className="flex items-center justify-between px-2 py-3 cursor-pointer"
                  >
                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id={`role-${role.code}`} 
                        checked={selectedRoles.has(role.code)}
                        onCheckedChange={() => toggleRole(role.code)}
                      />
                      <div>
                        <label 
                          htmlFor={`role-${role.code}`}
                          className="font-medium text-sm cursor-pointer"
                        >
                          {role.name}
                        </label>
                        {role.description && (
                          <p className="text-xs text-muted-foreground">
                            {role.description}
                          </p>
                        )}
                      </div>
                    </div>
                    {selectedRoles.has(role.code) && (
                      <CheckCircle2 className="h-4 w-4 text-primary" />
                    )}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
          
          <div className="flex items-center justify-between pt-2">
            <div className="text-sm">
              已选择 <span className="font-medium">{selectedRoles.size}</span> 个角色
            </div>
            <div className="flex gap-3">
              <Button 
                variant="outline" 
                onClick={onCancel}
              >
                取消
              </Button>
              <Button 
                onClick={handleConfirm}
                disabled={selectedRoles.size === 0}
              >
                <Shield className="mr-2 h-4 w-4" />
                分配角色
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default RoleSelector; 
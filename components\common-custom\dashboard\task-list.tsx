"use client"

import { <PERSON>actN<PERSON>, useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Plus } from "lucide-react"
import { TaskItem as BaseTaskItem } from "@/types/dashboard-templates"

// 扩展TaskItem接口以匹配实际使用
interface ExtendedTaskItem extends BaseTaskItem {
  id: string | number
  completed: boolean
  deadline?: string
}

/**
 * 任务列表组件
 */
export function TaskList({ 
  tasks: initialTasks, 
  title 
}: { 
  tasks: ExtendedTaskItem[], 
  title?: ReactNode 
}) {
  const [tasks, setTasks] = useState<ExtendedTaskItem[]>(initialTasks)

  const toggleTaskStatus = (taskId: string | number) => {
    setTasks((prevTasks) =>
      prevTasks.map((task) =>
        task.id === taskId ? { ...task, completed: !task.completed } : task
      )
    )
  }

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case "high":
      case "高":
        return "bg-red-500"
      case "medium":
      case "中":
        return "bg-yellow-500"
      case "low":
      case "低":
        return "bg-green-500"
      default:
        return "bg-blue-500"
    }
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>{title || "待办事项"}</CardTitle>
          <Button size="sm" className="cursor-pointer">
            <Plus className="h-4 w-4 mr-1" />
            添加
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-1">
        {tasks.map((task) => (
          <div
            key={task.id}
            className="flex items-center justify-between py-2 border-b last:border-0"
          >
            <div className="flex items-center gap-2">
              <Checkbox
                checked={task.completed}
                onCheckedChange={() => toggleTaskStatus(task.id)}
                aria-label={`标记 ${task.title} 为${task.completed ? '未完成' : '已完成'}`}
              />
              <span className={task.completed ? "line-through text-muted-foreground" : ""}>
                {task.title}
              </span>
            </div>
            <div className="flex items-center gap-2">
              {task.deadline && (
                <span className="text-xs text-muted-foreground">{task.deadline}</span>
              )}
              {task.priority && (
                <Badge
                  variant="outline"
                  className="flex items-center gap-1 px-2 py-0.5 text-xs"
                >
                  <div className={`w-1.5 h-1.5 rounded-full ${getPriorityColor(task.priority)}`} />
                  {task.priority}
                </Badge>
              )}
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  )
} 
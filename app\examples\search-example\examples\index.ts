/**
 * 搜索组件示例代码集合
 * 
 * 按照新规范管理示例代码，避免在页面文件中硬编码
 */

import React from "react"
import {
  SearchInput,
  SearchResults,
  GlobalSearch,
  CommandBar,
  SearchSuggestions,
  SearchTags,
  AdvancedSearch,
  SemanticSearch,
  CollapsibleSearch
} from "@/components/common-custom/search"
import {
  Search,
  User,
  FileText,
  Settings,
  Home,
  LayoutDashboard,
  Users,
  HelpCircle,
  Sparkles,
  Building,
  Calendar,
  FileCode,
  Clock,
  TrendingUp,
  Filter
} from "lucide-react"

// ============================================================================
// 基础搜索示例
// ============================================================================

export const basicSearchExample = {
  id: "basic-search",
  title: "基础搜索",
  description: "基本的搜索输入组件，支持清除和提交功能",
  code: `
import React, { useState } from "react";
import { SearchInput } from "@/components/common-custom/search";
import { Search } from "lucide-react";

function BasicSearchExample() {
  const [searchTerm, setSearchTerm] = useState("");
  
  const handleSearch = (query) => {
    console.log("搜索:", query);
    // 执行搜索逻辑
  };
  
  const handleChange = (e) => {
    setSearchTerm(e.target.value);
  };
  
  return (
    <div className="space-y-4">
      <SearchInput
        value={searchTerm}
        onChange={handleChange}
        onSearch={handleSearch}
        placeholder="搜索用户、文档、设置..."
        clearable
        onClear={() => setSearchTerm("")}
      />
      
      {searchTerm && (
        <div className="p-4 border rounded-md bg-muted/50">
          <p className="text-sm">
            <strong>搜索关键词:</strong> {searchTerm}
          </p>
        </div>
      )}
    </div>
  );
}

render(<BasicSearchExample />);
  `,
  scope: { SearchInput, Search, React, useState: React.useState },
}

// ============================================================================
// 可折叠搜索示例
// ============================================================================

export const collapsibleSearchExample = {
  id: "collapsible-search",
  title: "可折叠搜索",
  description: "支持展开收起的搜索框，节省空间的同时提供完整搜索功能",
  code: `
import React, { useState } from "react";
import { CollapsibleSearch } from "@/components/common-custom/search";

function CollapsibleSearchExample() {
  const [searchTerm, setSearchTerm] = useState("");
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-4">
        <span className="text-sm text-muted-foreground">搜索：</span>
        <CollapsibleSearch
          value={searchTerm}
          onChange={setSearchTerm}
          placeholder="点击展开搜索..."
          onSearch={(query) => console.log("搜索:", query)}
          onExpandedChange={setIsExpanded}
          size="md"
          expandDirection="right"
        />
        <span className="text-xs text-muted-foreground">
          状态: {isExpanded ? "展开" : "收起"}
        </span>
      </div>

      {searchTerm && (
        <div className="p-4 border rounded-md bg-muted/50">
          <p className="text-sm">
            <strong>搜索关键词:</strong> {searchTerm}
          </p>
        </div>
      )}
    </div>
  );
}

render(<CollapsibleSearchExample />);
  `,
  scope: { CollapsibleSearch, React, useState: React.useState },
}

// ============================================================================
// 搜索结果示例
// ============================================================================

export const searchResultsExample = {
  id: "search-results",
  title: "搜索结果",
  description: "展示搜索结果列表，支持分组和高亮显示",
  code: `
import React, { useState } from "react";
import { SearchInput, SearchResults } from "@/components/common-custom/search";
import { User, FileText, Settings, Building } from "lucide-react";

function SearchResultsExample() {
  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(false);
  
  // 模拟搜索结果
  const results = searchTerm ? [
    {
      id: 1,
      title: "张三",
      description: "前端开发工程师 - 负责React组件开发",
      icon: User,
      tags: ["开发", "前端", "React"],
      type: "user"
    },
    {
      id: 2,
      title: "项目文档",
      description: "React组件库设计文档和API说明",
      icon: FileText,
      tags: ["文档", "设计", "API"],
      type: "document"
    },
    {
      id: 3,
      title: "组件设置",
      description: "React组件的配置和主题设置",
      icon: Settings,
      tags: ["设置", "配置", "主题"],
      type: "setting"
    },
    {
      id: 4,
      title: "技术部门",
      description: "负责React技术栈的开发团队",
      icon: Building,
      tags: ["部门", "团队", "技术"],
      type: "department"
    }
  ] : [];
  
  const handleSearch = (query) => {
    setLoading(true);
    setSearchTerm(query);
    // 模拟搜索延迟
    setTimeout(() => setLoading(false), 500);
  };
  
  const handleItemSelect = (item) => {
    console.log("选择了:", item);
  };
  
  return (
    <div className="space-y-4">
      <SearchInput
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        onSearch={handleSearch}
        placeholder="搜索用户、文档、设置..."
        loading={loading}
        clearable
        onClear={() => setSearchTerm("")}
      />
      
      <SearchResults
        results={results}
        loading={loading}
        empty={!loading && searchTerm && results.length === 0}
        emptyText="未找到相关结果"
        onItemSelect={handleItemSelect}
      />
    </div>
  );
}

render(<SearchResultsExample />);
  `,
  scope: { 
    SearchInput, 
    SearchResults, 
    User, 
    FileText, 
    Settings, 
    Building,
    React, 
    useState: React.useState 
  },
}

// ============================================================================
// 搜索建议示例
// ============================================================================

export const searchSuggestionsExample = {
  id: "search-suggestions",
  title: "搜索建议",
  description: "智能搜索建议，包括历史记录和热门搜索",
  code: `
import React, { useState } from "react";
import { SearchInput, SearchSuggestions } from "@/components/common-custom/search";
import { Search, Clock, TrendingUp, Filter } from "lucide-react";

function SearchSuggestionsExample() {
  const [searchTerm, setSearchTerm] = useState("");
  const [showSuggestions, setShowSuggestions] = useState(false);
  
  const suggestions = [
    { id: "1", text: "React组件", type: "recent", icon: Clock, count: 5 },
    { id: "2", text: "TypeScript", type: "popular", icon: TrendingUp, count: 12 },
    { id: "3", text: "用户管理", type: "recent", icon: Clock, count: 3 },
    { id: "4", text: "API文档", type: "popular", icon: TrendingUp, count: 8 },
    { id: "5", text: "状态:已完成", type: "filter", icon: Filter, count: 2 },
  ];
  
  const handleSuggestionSelect = (suggestion) => {
    setSearchTerm(suggestion.text);
    setShowSuggestions(false);
    console.log("选择建议:", suggestion);
  };
  
  const handleFocus = () => {
    setShowSuggestions(true);
  };
  
  const handleBlur = () => {
    // 延迟隐藏，允许点击建议
    setTimeout(() => setShowSuggestions(false), 200);
  };
  
  return (
    <div className="relative space-y-4">
      <SearchInput
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        placeholder="搜索或选择建议..."
        onFocus={handleFocus}
        onBlur={handleBlur}
        clearable
        onClear={() => setSearchTerm("")}
      />
      
      <SearchSuggestions
        suggestions={suggestions}
        visible={showSuggestions}
        onSelect={handleSuggestionSelect}
        onClear={() => console.log("清除建议")}
      />
    </div>
  );
}

render(<SearchSuggestionsExample />);
  `,
  scope: {
    SearchInput,
    SearchSuggestions,
    Search,
    Clock,
    TrendingUp,
    Filter,
    React,
    useState: React.useState
  },
}

// ============================================================================
// 全局搜索示例
// ============================================================================

export const globalSearchExample = {
  id: "global-search",
  title: "全局搜索",
  description: "全局搜索组件，支持快捷键和多种内容类型",
  code: `
import React, { useState } from "react";
import { GlobalSearch } from "@/components/common-custom/search";
import { User, FileText, Settings, Home, LayoutDashboard } from "lucide-react";

function GlobalSearchExample() {
  const [isOpen, setIsOpen] = useState(false);
  
  const searchData = [
    {
      title: "用户",
      items: [
        { id: "u1", title: "张三", description: "前端开发工程师", icon: User },
        { id: "u2", title: "李四", description: "产品经理", icon: User },
      ]
    },
    {
      title: "页面",
      items: [
        { id: "p1", title: "首页", description: "系统主页面", icon: Home },
        { id: "p2", title: "仪表板", description: "数据统计面板", icon: LayoutDashboard },
      ]
    },
    {
      title: "文档",
      items: [
        { id: "d1", title: "API文档", description: "接口说明文档", icon: FileText },
        { id: "d2", title: "用户手册", description: "系统使用指南", icon: FileText },
      ]
    }
  ];
  
  const handleSelect = (item) => {
    console.log("选择了:", item);
    setIsOpen(false);
  };
  
  return (
    <div className="space-y-4">
      <div className="flex items-center gap-4">
        <button
          onClick={() => setIsOpen(true)}
          className="px-4 py-2 border rounded-md hover:bg-muted transition-colors"
        >
          打开全局搜索 (Ctrl+K)
        </button>
        
        <p className="text-sm text-muted-foreground">
          点击按钮或按 Ctrl+K 打开全局搜索
        </p>
      </div>
      
      <GlobalSearch
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        data={searchData}
        onSelect={handleSelect}
        placeholder="搜索用户、页面、文档..."
      />
    </div>
  );
}

render(<GlobalSearchExample />);
  `,
  scope: { 
    GlobalSearch, 
    User, 
    FileText, 
    Settings, 
    Home, 
    LayoutDashboard,
    React, 
    useState: React.useState 
  },
}

// ============================================================================
// 统一导出所有示例
// ============================================================================

export const allExamples = [
  basicSearchExample,
  collapsibleSearchExample,
  searchResultsExample,
  searchSuggestionsExample,
  globalSearchExample,
]

// ============================================================================
// 按类别导出示例
// ============================================================================

export const basicExamples = [basicSearchExample, collapsibleSearchExample, searchResultsExample]
export const advancedExamples = [searchSuggestionsExample, globalSearchExample]

// ============================================================================
// 示例元数据
// ============================================================================

export const exampleMetadata = {
  total: allExamples.length,
  categories: {
    basic: basicExamples.length,
    advanced: advancedExamples.length,
  },
  tags: ["search", "input", "results", "suggestions", "global"],
  lastUpdated: "2024-01-01",
}

"use client"

import React, { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { use } from "react"
import { 
  ArrowLeft, 
  Save, 
  Loader2,
  AlertCircle,
  UserCircle
} from "lucide-react"

import { Button } from "@/components/ui/button"
import { HeaderWithBreadcrumb } from "@/components/custom/breadcrumb"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { updateUserRequest, getUserByIdRequest } from "@/services/api/userRequestApi"
import { User } from "@/types/user"
import { toast } from "sonner"
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

// 导入自定义编辑组件
import { UserBasicInfoEdit } from "@/components/pages/user-detail/user-basic-info-edit"
import { UserRoles } from "@/components/pages/user-detail/user-roles"
import { RoleSelector } from "@/components/pages/user-detail/role-selector"

interface UserEditPageProps {
  params: Promise<{ id: string }> | { id: string };
}

export default function UserEditPage({ params }: UserEditPageProps) {
  const router = useRouter()
  
  // 使用React.use解包params
  const resolvedParams = params instanceof Promise ? use(params) : params;
  const userId = resolvedParams.id;
  
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState("profile")
  
  // 弹窗状态
  const [roleDialogOpen, setRoleDialogOpen] = useState(false)
  
  // 用户数据
  const [userData, setUserData] = useState<User | null>(null)
  // 编辑表单状态
  const [formData, setFormData] = useState<Partial<User>>({})
  const [formErrors, setFormErrors] = useState<Record<string, string>>({})
  
  // 初始化加载数据
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true)
      setError(null)
      try {
        const user = await getUserByIdRequest(parseInt(userId, 10))
        if (user) {
          setUserData(user)
          setFormData({
            id: user.id,
            account: user.account,
            nickname: user.nickname,
            email: user.email,
            mobile: user.mobile,
            status: user.status,
            avatarUrl: user.avatarUrl
          })
        } else {
          setError("未找到用户信息")
        }
      } catch (err) {
        console.error("加载用户数据失败", err)
        setError("加载用户数据失败，请稍后重试")
      } finally {
        setIsLoading(false)
      }
    }
    
    loadData()
  }, [userId, router])
  
  // 处理表单字段变更
  const handleFieldChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    
    // 清除该字段的错误
    if (formErrors[field]) {
      setFormErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[field]
        return newErrors
      })
    }
  }
  
  // 处理添加角色
  const handleAddRole = () => {
    setRoleDialogOpen(true)
  }
  
  // 处理角色选择
  const handleRoleSelect = (roleCodes: string[]) => {
    // 这里应该调用API分配角色
    setRoleDialogOpen(false)
    toast.success("角色分配成功")
  }
  
  // 处理删除角色
  const handleRemoveRole = (roleCode: string) => {
    // 这里应该调用API删除角色
    toast.success(`已移除角色 ${roleCode}`)
  }
  
  // 表单验证
  const validateForm = () => {
    const errors: Record<string, string> = {}
    
    if (!formData.nickname?.trim()) {
      errors.nickname = "用户名不能为空"
    }
    
    if (!formData.email?.trim()) {
      errors.email = "邮箱不能为空"
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = "邮箱格式不正确"
    }
    
    // 如果有手机号，验证格式
    if (formData.mobile && !/^1[3-9]\d{9}$/.test(formData.mobile)) {
      errors.mobile = "手机号格式不正确"
    }
    
    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }
  
  // 表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // 验证表单
    if (!validateForm()) {
      return
    }
    
    setIsSaving(true)
    
    try {
      // 更新用户
      const success = await updateUserRequest(formData as User)
      if (success) {
        toast.success("用户信息更新成功")
        router.push(`/user/detail/${userId}`)
      } else {
        setError("更新用户信息失败")
        toast.error("更新用户信息失败")
      }
    } catch (err) {
      console.error("保存用户失败", err)
      setError("保存用户失败，请稍后重试")
      toast.error("保存用户失败，请稍后重试")
    } finally {
      setIsSaving(false)
    }
  }
  
  // 面包屑
  const breadcrumbItems = [
    { label: "设置", href: "/settings" },
    { label: "用户管理", href: "/settings/users" },
    { label: userData?.nickname ? `${userData.nickname}` : "用户详情", href: `/user/detail/${userId}` },
    { label: "编辑", active: true }
  ]
  
  // 加载状态
  if (isLoading) {
    return (
      <div className="flex flex-col min-h-screen">
        <HeaderWithBreadcrumb items={breadcrumbItems}/>
        <main className="flex-1">
          <div className="container mx-auto px-6 py-6 max-w-7xl">
            <div className="flex justify-center items-center h-[400px]">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          </div>
        </main>
      </div>
    )
  }
  
  return (
    <div className="flex flex-col min-h-screen">
      <HeaderWithBreadcrumb items={breadcrumbItems}/>
      <main className="flex-1">
        <div className="container mx-auto px-6 py-6 max-w-4xl">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-2">
              <Button 
                variant="ghost" 
                size="icon" 
                onClick={() => router.back()}
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <div>
                <h1 className="text-2xl font-bold tracking-tight">编辑用户信息</h1>
                <p className="text-sm text-muted-foreground">更新用户的基本信息及状态</p>
              </div>
            </div>
            <Button
              onClick={handleSubmit}
              disabled={isSaving}
              className="gap-2"
            >
              {isSaving ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>保存中...</span>
                </>
              ) : (
                <>
                  <Save className="h-4 w-4" />
                  <span>保存</span>
                </>
              )}
            </Button>
          </div>
          
          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>错误</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 gap-6">
              <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                <div className="border-b border-border/40 mb-4">
                  <TabsList className="bg-transparent h-auto p-0 mb-0">
                    <TabsTrigger value="profile" className="rounded-none relative pb-2 pt-2 px-4 data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:border-0">
                      个人资料
                      <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-primary transition-transform data-[state=active]:scale-100 scale-0" />
                    </TabsTrigger>
                    <TabsTrigger value="roles" className="rounded-none relative pb-2 pt-2 px-4 data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:border-0">
                      角色管理
                      <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-primary transition-transform data-[state=active]:scale-100 scale-0" />
                    </TabsTrigger>
                  </TabsList>
                </div>
                
                {/* 个人资料选项卡 */}
                <TabsContent value="profile" className="space-y-6 mt-0">
                  <UserBasicInfoEdit 
                    user={formData as User} 
                    errors={formErrors}
                    onChange={handleFieldChange}
                  />
                </TabsContent>
                
                {/* 角色管理选项卡 */}
                <TabsContent value="roles" className="space-y-6 mt-0">
                  {userData && (
                    <>
                      <UserRoles 
                        userId={userData.id} 
                        isEditing={true}
                        onAddRole={handleAddRole}
                        onRemoveRole={handleRemoveRole}
                      />
                    </>
                  )}
                </TabsContent>
              </Tabs>
            </div>
            
            <div className="flex justify-end">
              <div className="flex gap-3">
                <Button
                  variant="outline"
                  onClick={() => router.back()}
                  type="button"
                >
                  取消
                </Button>
                <Button
                  type="submit"
                  disabled={isSaving}
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      保存
                    </>
                  ) : "保存更改"}
                </Button>
              </div>
            </div>
          </form>
          
          {/* 角色选择弹窗 */}
          <Dialog open={roleDialogOpen} onOpenChange={setRoleDialogOpen}>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>添加用户角色</DialogTitle>
                <DialogDescription>
                  为用户分配系统角色
                </DialogDescription>
              </DialogHeader>
              {userData && (
                <RoleSelector 
                  userId={userData.id}
                  onSelect={handleRoleSelect}
                  onCancel={() => setRoleDialogOpen(false)}
                />
              )}
            </DialogContent>
          </Dialog>
        </div>
      </main>
    </div>
  )
} 
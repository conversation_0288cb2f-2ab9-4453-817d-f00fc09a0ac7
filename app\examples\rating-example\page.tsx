"use client"

import React from "react"
import { ComponentPreviewContainer } from "@/components/component-detail/preview-container"
import { Rating, RatingDisplay, RatingStatistics } from "@/components/common-custom/rating"
import { allExamples } from "./examples"

// ============================================================================
// API文档组件
// ============================================================================

function RatingApiDocs() {
  return (
    <div className="space-y-6">
      {/* Rating 组件API */}
      <div>
        <h3 className="font-medium text-lg mb-2">Rating</h3>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">value</td>
                <td className="p-2 border">number</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">当前评分值</td>
              </tr>
              <tr>
                <td className="p-2 border">onChange</td>
                <td className="p-2 border">(value: number) =&gt; void</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">评分变化回调函数</td>
              </tr>
              <tr>
                <td className="p-2 border">max</td>
                <td className="p-2 border">number</td>
                <td className="p-2 border">5</td>
                <td className="p-2 border">最大评分值</td>
              </tr>
              <tr>
                <td className="p-2 border">size</td>
                <td className="p-2 border">"sm" | "md" | "lg"</td>
                <td className="p-2 border">"md"</td>
                <td className="p-2 border">评分图标大小</td>
              </tr>
              <tr>
                <td className="p-2 border">readonly</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">false</td>
                <td className="p-2 border">是否只读</td>
              </tr>
              <tr>
                <td className="p-2 border">icon</td>
                <td className="p-2 border">"star" | "heart"</td>
                <td className="p-2 border">"star"</td>
                <td className="p-2 border">评分图标类型</td>
              </tr>
              <tr>
                <td className="p-2 border">allowHalf</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">false</td>
                <td className="p-2 border">允许半星评分</td>
              </tr>
              <tr>
                <td className="p-2 border">clearable</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">true</td>
                <td className="p-2 border">是否可清除评分</td>
              </tr>
              <tr>
                <td className="p-2 border">highlightColor</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">"text-yellow-400"</td>
                <td className="p-2 border">高亮颜色</td>
              </tr>
              <tr>
                <td className="p-2 border">className</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">自定义CSS类名</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* RatingDisplay 组件API */}
      <div>
        <h3 className="font-medium text-lg mb-2">RatingDisplay</h3>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">value</td>
                <td className="p-2 border">number</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">评分值</td>
              </tr>
              <tr>
                <td className="p-2 border">max</td>
                <td className="p-2 border">number</td>
                <td className="p-2 border">5</td>
                <td className="p-2 border">最大评分值</td>
              </tr>
              <tr>
                <td className="p-2 border">showValue</td>
                <td className="p-2 border">boolean</td>
                <td className="p-2 border">false</td>
                <td className="p-2 border">是否显示评分值</td>
              </tr>
              <tr>
                <td className="p-2 border">valueFormat</td>
                <td className="p-2 border">"decimal" | "fraction" | "percent"</td>
                <td className="p-2 border">"decimal"</td>
                <td className="p-2 border">评分值显示格式</td>
              </tr>
              <tr>
                <td className="p-2 border">count</td>
                <td className="p-2 border">number</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">评分数量</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* RatingStatistics 组件API */}
      <div>
        <h3 className="font-medium text-lg mb-2">RatingStatistics</h3>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted text-left">
                <th className="p-2 border">参数</th>
                <th className="p-2 border">类型</th>
                <th className="p-2 border">默认值</th>
                <th className="p-2 border">说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="p-2 border">distribution</td>
                <td className="p-2 border">number[]</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">评分分布数组（1-5星）</td>
              </tr>
              <tr>
                <td className="p-2 border">average</td>
                <td className="p-2 border">number</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">平均评分</td>
              </tr>
              <tr>
                <td className="p-2 border">count</td>
                <td className="p-2 border">number</td>
                <td className="p-2 border">-</td>
                <td className="p-2 border">评分总数</td>
              </tr>
              <tr>
                <td className="p-2 border">highlightColor</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">"bg-yellow-400"</td>
                <td className="p-2 border">进度条高亮颜色</td>
              </tr>
              <tr>
                <td className="p-2 border">backgroundColor</td>
                <td className="p-2 border">string</td>
                <td className="p-2 border">"bg-muted"</td>
                <td className="p-2 border">进度条背景颜色</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* 使用指南 */}
      <div>
        <h3 className="font-medium text-lg mb-2">使用指南</h3>
        <div className="space-y-4 text-sm">
          <div>
            <h4 className="font-medium mb-2">基本用法</h4>
            <p className="text-muted-foreground mb-2">
              最简单的评分组件使用方式：
            </p>
            <pre className="bg-muted p-3 rounded-md overflow-x-auto">
              <code>{`<Rating value={3} onChange={(value) => console.log(value)} />`}</code>
            </pre>
          </div>

          <div>
            <h4 className="font-medium mb-2">只读展示</h4>
            <p className="text-muted-foreground mb-2">
              用于展示评分结果，不可交互：
            </p>
            <pre className="bg-muted p-3 rounded-md overflow-x-auto">
              <code>{`<RatingDisplay value={4.5} showValue count={89} />`}</code>
            </pre>
          </div>

          <div>
            <h4 className="font-medium mb-2">评分统计</h4>
            <p className="text-muted-foreground mb-2">
              显示评分分布情况：
            </p>
            <pre className="bg-muted p-3 rounded-md overflow-x-auto">
              <code>{`<RatingStatistics
  distribution={[45, 28, 12, 3, 1]}
  average={4.3}
  count={89}
/>`}</code>
            </pre>
          </div>
        </div>
      </div>
    </div>
  )
}

// ============================================================================
// 主预览组件
// ============================================================================

export default function RatingExamplePage() {
  return (
    <ComponentPreviewContainer
      title="评分 Rating"
      description="用于收集用户反馈或展示评价结果的星级评分组件，支持多种图标、尺寸和交互模式"
      whenToUse="当需要用户对内容进行评价时使用；当需要展示内容的评价结果时使用；适用于产品评价、服务评价、内容评分、用户反馈等场景；支持星形和心形图标，可配置颜色和大小"
      examples={allExamples}
      apiDocs={<RatingApiDocs />}
    />
  )
}
// 所有可用的示例代码集合
const examples = {
  "basic-button": {
    title: "基础按钮",
    code: `
import React from "react";
import { Button } from "@/components/ui/button";

function BasicButton() {
  return (
    <div className="flex flex-wrap items-center gap-4">
      <Button>默认按钮</Button>
      <Button variant="secondary">次要按钮</Button>
      <Button variant="destructive">危险按钮</Button>
      <Button variant="outline">线框按钮</Button>
      <Button variant="ghost">幽灵按钮</Button>
      <Button variant="link">链接按钮</Button>
    </div>
  );
}

render(<BasicButton />);
`,
  },
  "size-button": {
    title: "按钮尺寸",
    code: `
import React from "react";
import { Button } from "@/components/ui/button";

function SizeButton() {
  return (
    <div className="flex items-center gap-4">
      <Button size="lg">大型按钮</Button>
      <Button>默认按钮</Button>
      <Button size="sm">小型按钮</Button>
    </div>
  );
}

render(<SizeButton />);
`,
  },
  "disabled-button": {
    title: "禁用状态",
    code: `
import React from "react";
import { Button } from "@/components/ui/button";

function DisabledButton() {
  return (
    <div className="flex items-center gap-4">
      <Button disabled>禁用按钮</Button>
      <Button variant="secondary" disabled>禁用次要按钮</Button>
      <Button variant="destructive" disabled>禁用危险按钮</Button>
    </div>
  );
}

render(<DisabledButton />);
`,
  },
  "icon-button": {
    title: "图标按钮",
    code: `
import React from "react";
import { Button } from "@/components/ui/button";
import { Search, Mail, PlusCircle } from "lucide-react";

function IconButton() {
  return (
    <div className="flex flex-wrap items-center gap-4">
      <Button>
        <PlusCircle className="mr-2 h-4 w-4" /> 添加
      </Button>
      <Button variant="outline">
        <Search className="mr-2 h-4 w-4" /> 搜索
      </Button>
      <Button variant="secondary" size="sm">
        <Mail className="mr-2 h-4 w-4" /> 发送
      </Button>
      <Button variant="ghost" size="icon">
        <Search className="h-4 w-4" />
      </Button>
    </div>
  );
}

render(<IconButton />);
`,
  },
  "loading-button": {
    title: "加载状态",
    code: `
import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";

function LoadingButton() {
  const [isLoading, setIsLoading] = useState(false);

  const handleClick = () => {
    setIsLoading(true);
    setTimeout(() => setIsLoading(false), 2000);
  };

  return (
    <div className="flex items-center gap-4">
      <Button disabled={isLoading} onClick={handleClick}>
        {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
        {isLoading ? "加载中..." : "点击加载"}
      </Button>
      <Button variant="outline" disabled>
        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        请稍等
      </Button>
    </div>
  );
}

render(<LoadingButton />);
`,
  },
  
  "block-button": {
    title: "块级按钮",
    code: `
import React from "react";
import { Button } from "@/components/ui/button";

function BlockButton() {
  return (
    <div className="space-y-4 w-full max-w-md">
      <Button className="w-full">块级按钮</Button>
      <Button variant="outline" className="w-full">块级线框按钮</Button>
      <Button variant="secondary" className="w-full">块级次要按钮</Button>
    </div>
  );
}

render(<BlockButton />);
`,
  },
  
  "button-group": {
    title: "按钮组",
    code: `
import React from "react";
import { Button } from "@/components/ui/button";
import { Copy, Settings, Download } from "lucide-react";

function ButtonGroup() {
  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-0">
        <Button variant="outline" className="rounded-r-none">上一页</Button>
        <Button variant="outline" className="rounded-none border-x-0">1</Button>
        <Button variant="outline" className="rounded-none">2</Button>
        <Button variant="outline" className="rounded-l-none">下一页</Button>
      </div>
      
      <div className="flex items-center space-x-0">
        <Button size="sm" variant="outline" className="rounded-r-none">
          <Copy className="h-4 w-4" />
        </Button>
        <Button size="sm" variant="outline" className="rounded-none border-x-0">
          <Download className="h-4 w-4" />
        </Button>
        <Button size="sm" variant="outline" className="rounded-l-none">
          <Settings className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}

render(<ButtonGroup />);
`,
  },
  
  "badge-button": {
    title: "带徽章的按钮",
    code: `
import React from "react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Bell } from "lucide-react";

function BadgeButton() {
  return (
    <div className="flex flex-wrap items-center gap-4">
      <div className="relative">
        <Button>
          通知
          <Badge className="ml-2 px-1.5" variant="destructive">5</Badge>
        </Button>
      </div>
      
      <div className="relative">
        <Button variant="outline">
          <Bell className="h-4 w-4 mr-2" />
          消息
          <Badge className="absolute -top-2 -right-2 px-1.5" variant="secondary">3</Badge>
        </Button>
      </div>
      
      <div className="relative">
        <Button variant="ghost" size="icon">
          <Bell className="h-4 w-4" />
          <Badge className="absolute -top-2 -right-2 w-4 h-4 p-0 flex items-center justify-center" variant="destructive">
            8
          </Badge>
        </Button>
      </div>
    </div>
  );
}

render(<BadgeButton />);
`,
  },
  
  "dropdown-button": {
    title: "下拉菜单按钮",
    code: `
import React from "react";
import { Button } from "@/components/ui/button";
import { ChevronDown } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

function DropdownButton() {
  return (
    <div className="flex flex-wrap items-center gap-4">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button>
            选项
            <ChevronDown className="ml-2 h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuItem>选项 1</DropdownMenuItem>
          <DropdownMenuItem>选项 2</DropdownMenuItem>
          <DropdownMenuItem>选项 3</DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline">
            更多操作
            <ChevronDown className="ml-2 h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuItem>编辑</DropdownMenuItem>
          <DropdownMenuItem>删除</DropdownMenuItem>
          <DropdownMenuItem>复制</DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}

render(<DropdownButton />);
`,
  },
  
  "interactive-button": {
    title: "交互式按钮",
    code: `
import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Copy } from "lucide-react";

function InteractiveButton() {
  const [copied, setCopied] = useState(false);
  const [clicked, setClicked] = useState(0);
  
  const handleCopy = () => {
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };
  
  const handleClick = () => {
    setClicked(prev => prev + 1);
  };
  
  return (
    <div className="flex flex-wrap items-center gap-4">
      <Button onClick={handleCopy} variant="outline" className="transition-all">
        {copied ? "已复制!" : "点击复制"}
        <Copy className={copied ? "ml-2 h-4 w-4 text-green-500" : "ml-2 h-4 w-4"} />
      </Button>
      
      <Button 
        onClick={handleClick}
        className={clicked % 5 === 0 && clicked !== 0 ? "animate-bounce" : ""}
        variant="secondary"
      >
        点击了 {clicked} 次
        {clicked % 5 === 0 && clicked !== 0 && " 🎉"}
      </Button>
    </div>
  );
}

render(<InteractiveButton />);
`,
  },
  
  // 数据表格示例
  "basic-table": {
    title: "基础数据表格",
    code: `
import React from "react";
import { 
  ColumnDef, 
  flexRender, 
  getCoreRowModel, 
  useReactTable,
  getPaginationRowModel,
} from "@tanstack/react-table";

import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

// 模拟数据
const data = [
  {
    id: "u-001",
    name: "张三",
    email: "<EMAIL>",
    role: "管理员",
    status: "活跃",
  },
  {
    id: "u-002",
    name: "李四",
    email: "<EMAIL>",
    role: "用户",
    status: "活跃",
  },
  {
    id: "u-003",
    name: "王五",
    email: "<EMAIL>",
    role: "编辑",
    status: "未活跃",
  },
  {
    id: "u-004",
    name: "赵六",
    email: "<EMAIL>",
    role: "用户",
    status: "待处理",
  },
  {
    id: "u-005",
    name: "钱七",
    email: "<EMAIL>",
    role: "编辑",
    status: "活跃",
  },
];

function BasicDataTable() {
  const columns = [
    {
      accessorKey: "name",
      header: "姓名",
    },
    {
      accessorKey: "email",
      header: "邮箱",
    },
    {
      accessorKey: "role",
      header: "角色",
    },
    {
      accessorKey: "status",
      header: "状态",
      cell: ({ row }) => {
        const status = row.getValue("status");
        return (
          <div className="flex items-center">
            <span
              className={\`mr-2 h-2 w-2 rounded-full \${
                status === "活跃" ? "bg-green-500" : 
                status === "未活跃" ? "bg-gray-400" : "bg-yellow-500"
              }\`}
            />
            {status}
          </div>
        );
      },
    },
  ];

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    initialState: {
      pagination: {
        pageSize: 3,
      },
    },
  });

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  无数据
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => table.previousPage()}
          disabled={!table.getCanPreviousPage()}
        >
          上一页
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => table.nextPage()}
          disabled={!table.getCanNextPage()}
        >
          下一页
        </Button>
      </div>
    </div>
  );
}

render(<BasicDataTable />);
`,
  },
  
  "advanced-table": {
    title: "高级数据表格",
    code: `
import React, { useState } from "react";
import { 
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender, 
  getCoreRowModel, 
  useReactTable,
  getPaginationRowModel,
  getSortedRowModel,
  getFilteredRowModel,
} from "@tanstack/react-table";
import { ArrowUpDown, ChevronDown, MoreHorizontal } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

// 模拟数据
const data = [
  {
    id: "u-001",
    name: "张三",
    email: "<EMAIL>",
    role: "管理员",
    status: "活跃",
    lastLogin: "2023-12-01 10:30",
  },
  {
    id: "u-002",
    name: "李四",
    email: "<EMAIL>",
    role: "用户",
    status: "活跃",
    lastLogin: "2023-12-03 14:20",
  },
  {
    id: "u-003",
    name: "王五",
    email: "<EMAIL>",
    role: "编辑",
    status: "未活跃",
    lastLogin: "2023-11-28 09:45",
  },
  {
    id: "u-004",
    name: "赵六",
    email: "<EMAIL>",
    role: "用户",
    status: "待处理",
    lastLogin: "2023-12-05 16:10",
  },
  {
    id: "u-005",
    name: "钱七",
    email: "<EMAIL>",
    role: "编辑",
    status: "活跃",
    lastLogin: "2023-12-04 11:25",
  },
];

function AdvancedDataTable() {
  const [sorting, setSorting] = useState([]);
  const [columnFilters, setColumnFilters] = useState([]);
  const [columnVisibility, setColumnVisibility] = useState({});
  const [rowSelection, setRowSelection] = useState({});

  const columns = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="全选"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="选择行"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "name",
      header: "姓名",
      cell: ({ row }) => <div>{row.getValue("name")}</div>,
    },
    {
      accessorKey: "email",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            邮箱
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        )
      },
      cell: ({ row }) => <div>{row.getValue("email")}</div>,
    },
    {
      accessorKey: "role",
      header: "角色",
      cell: ({ row }) => <div>{row.getValue("role")}</div>,
    },
    {
      accessorKey: "status",
      header: "状态",
      cell: ({ row }) => {
        const status = row.getValue("status");
        return (
          <div className="flex items-center">
            <span
              className={\`mr-2 h-2 w-2 rounded-full \${
                status === "活跃" ? "bg-green-500" : 
                status === "未活跃" ? "bg-gray-400" : "bg-yellow-500"
              }\`}
            />
            {status}
          </div>
        )
      },
    },
    {
      accessorKey: "lastLogin",
      header: "最后登录",
      cell: ({ row }) => <div>{row.getValue("lastLogin")}</div>,
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const user = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">打开菜单</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>操作</DropdownMenuLabel>
              <DropdownMenuItem
                onClick={() => navigator.clipboard.writeText(user.id)}
              >
                复制用户ID
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>查看详情</DropdownMenuItem>
              <DropdownMenuItem>编辑用户</DropdownMenuItem>
              <DropdownMenuItem>删除用户</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )
      },
    },
  ];

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
    initialState: {
      pagination: {
        pageSize: 5,
      },
    },
  });

  return (
    <div className="space-y-4">
      <div className="flex items-center py-4">
        <Input
          placeholder="按姓名筛选..."
          value={(table.getColumn("name")?.getFilterValue() as string) ?? ""}
          onChange={(event) =>
            table.getColumn("name")?.setFilterValue(event.target.value)
          }
          className="max-w-sm"
        />
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="ml-auto">
              列设置 <ChevronDown className="ml-2 h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {table
              .getAllColumns()
              .filter((column) => column.getCanHide())
              .map((column) => {
                // 获取列的中文标题
                let columnTitle = column.id;
                if (column.id === "name") columnTitle = "姓名";
                if (column.id === "email") columnTitle = "邮箱";
                if (column.id === "role") columnTitle = "角色";
                if (column.id === "status") columnTitle = "状态";
                if (column.id === "lastLogin") columnTitle = "最后登录";
                
                return (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    checked={column.getIsVisible()}
                    onCheckedChange={(value) =>
                      column.toggleVisibility(!!value)
                    }
                  >
                    {columnTitle}
                  </DropdownMenuCheckboxItem>
                )
              })}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  无数据
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-between">
        <div className="flex-1 text-sm text-muted-foreground">
          {table.getFilteredSelectedRowModel().rows.length} 项已选择，共 {table.getFilteredRowModel().rows.length} 项
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            上一页
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            下一页
          </Button>
        </div>
      </div>
    </div>
  );
}

render(<AdvancedDataTable />);
`,
  },
  
  "editable-table": {
    title: "可编辑数据表格",
    code: `
import React, { useState } from "react";
import { 
  flexRender, 
  getCoreRowModel, 
  useReactTable,
  getPaginationRowModel,
} from "@tanstack/react-table";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Pencil, Save, X } from "lucide-react";

// 模拟初始数据
const initialData = [
  { id: "1", name: "张三", email: "<EMAIL>", role: "管理员" },
  { id: "2", name: "李四", email: "<EMAIL>", role: "用户" },
  { id: "3", name: "王五", email: "<EMAIL>", role: "编辑" },
  { id: "4", name: "赵六", email: "<EMAIL>", role: "用户" },
];

function EditableDataTable() {
  const [data, setData] = useState([...initialData]);
  const [editingRow, setEditingRow] = useState(null);
  const [editedValues, setEditedValues] = useState({});

  // 开始编辑行
  const startEditing = (row) => {
    setEditingRow(row.id);
    setEditedValues({
      name: row.original.name,
      email: row.original.email,
      role: row.original.role,
    });
  };

  // 取消编辑
  const cancelEditing = () => {
    setEditingRow(null);
    setEditedValues({});
  };

  // 保存编辑
  const saveEditing = (id) => {
    setData(
      data.map((row) =>
        row.id === id
          ? {
              ...row,
              name: editedValues.name,
              email: editedValues.email,
              role: editedValues.role,
            }
          : row
      )
    );
    setEditingRow(null);
    setEditedValues({});
  };

  // 处理编辑字段的值变化
  const handleFieldChange = (field, value) => {
    setEditedValues({
      ...editedValues,
      [field]: value,
    });
  };

  const columns = [
    {
      accessorKey: "name",
      header: "姓名",
      cell: ({ row }) => {
        return editingRow === row.id ? (
          <Input
            value={editedValues.name}
            onChange={(e) => handleFieldChange("name", e.target.value)}
            className="h-8"
          />
        ) : (
          <div>{row.getValue("name")}</div>
        );
      },
    },
    {
      accessorKey: "email",
      header: "邮箱",
      cell: ({ row }) => {
        return editingRow === row.id ? (
          <Input
            value={editedValues.email}
            onChange={(e) => handleFieldChange("email", e.target.value)}
            className="h-8"
          />
        ) : (
          <div>{row.getValue("email")}</div>
        );
      },
    },
    {
      accessorKey: "role",
      header: "角色",
      cell: ({ row }) => {
        return editingRow === row.id ? (
          <Input
            value={editedValues.role}
            onChange={(e) => handleFieldChange("role", e.target.value)}
            className="h-8"
          />
        ) : (
          <div>{row.getValue("role")}</div>
        );
      },
    },
    {
      id: "actions",
      header: "操作",
      cell: ({ row }) => {
        return editingRow === row.id ? (
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => saveEditing(row.id)}
              className="h-8 w-8"
            >
              <Save className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={cancelEditing}
              className="h-8 w-8"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        ) : (
          <Button
            variant="ghost"
            size="icon"
            onClick={() => startEditing(row)}
            className="h-8 w-8"
          >
            <Pencil className="h-4 w-4" />
          </Button>
        );
      },
    },
  ];

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    initialState: {
      pagination: {
        pageSize: 4,
      },
    },
  });

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  无数据
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => table.previousPage()}
          disabled={!table.getCanPreviousPage()}
        >
          上一页
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => table.nextPage()}
          disabled={!table.getCanNextPage()}
        >
          下一页
        </Button>
      </div>
    </div>
  );
}

render(<EditableDataTable />);
`,
  },
  
  // 时间轴示例代码
  "basic-timeline": {
    title: "基础时间轴",
    code: `
import React from "react";
import { Timeline } from "@/components/common-custom/timeline";
import { CheckCircle, Clock, AlertCircle } from "lucide-react";

function BasicTimeline() {
  const timelineItems = [
    {
      id: "1",
      title: "项目创建",
      description: "项目初始化并设置基本参数",
      time: "2023-10-01",
      status: "success",
      icon: <CheckCircle className="h-4 w-4 text-white" />,
      content: (
        <div className="text-sm text-muted-foreground">
          由系统管理员创建
        </div>
      )
    },
    {
      id: "2",
      title: "需求分析",
      description: "收集并分析用户需求",
      time: "2023-10-05",
      status: "success",
      icon: <CheckCircle className="h-4 w-4 text-white" />,
      content: (
        <div className="text-sm text-muted-foreground">
          完成需求文档编写
        </div>
      )
    },
    {
      id: "3",
      title: "设计阶段",
      description: "UI设计和架构设计",
      time: "2023-10-10",
      status: "primary",
      icon: <Clock className="h-4 w-4 text-white" />,
      content: (
        <div className="text-sm text-muted-foreground">
          设计稿审核中
        </div>
      )
    },
    {
      id: "4",
      title: "开发阶段",
      description: "前后端功能开发",
      time: "预计 2023-10-20",
      status: "warning",
      icon: <AlertCircle className="h-4 w-4 text-white" />,
      content: (
        <div className="text-sm text-muted-foreground">
          等待设计稿完成
        </div>
      )
    }
  ];

  return <Timeline items={timelineItems} />;
}

render(<BasicTimeline />);
`,
  },
  
  "timeline-variants": {
    title: "时间轴样式变体",
    code: `
import React from "react";
import { Timeline } from "@/components/common-custom/timeline";
import { Star } from "lucide-react";

function TimelineVariants() {
  const timelineItems = [
    {
      id: "1",
      title: "创建项目",
      description: "项目初始化完成",
      time: "2023-10-01",
      status: "success"
    },
    {
      id: "2",
      title: "发布版本",
      description: "版本 1.0 发布",
      time: "2023-10-15",
      status: "success"
    },
    {
      id: "3",
      title: "功能更新",
      description: "新增核心功能",
      time: "2023-10-30",
      status: "primary"
    }
  ];

  return (
    <div className="space-y-8">
      <div>
        <h3 className="text-sm font-medium mb-2">虚线连接器</h3>
        <Timeline 
          items={timelineItems} 
          connectorStyle="dashed"
        />
      </div>
      
      <div>
        <h3 className="text-sm font-medium mb-2">点状连接器</h3>
        <Timeline 
          items={timelineItems} 
          connectorStyle="dotted"
          connectorColor="#6366f1"
        />
      </div>
      
      <div>
        <h3 className="text-sm font-medium mb-2">方形点标记</h3>
        <Timeline 
          items={timelineItems} 
          dotShape="square"
        />
      </div>
      
      <div>
        <h3 className="text-sm font-medium mb-2">自定义图标</h3>
        <Timeline 
          items={timelineItems.map(item => ({
            ...item,
            icon: <Star className="h-4 w-4 text-white" />
          }))} 
        />
      </div>
    </div>
  );
}

render(<TimelineVariants />);
`,
  },
  
  "activity-timeline": {
    title: "活动时间轴",
    code: `
import React from "react";
import { ActivityTimeline } from "@/components/common-custom/timeline";
import { FileText, MessageSquare, CheckCircle, AlertCircle } from "lucide-react";
import { Badge } from "@/components/ui/badge";

function ActivityTimelineExample() {
  const activities = [
    {
      id: "1",
      user: { name: "张三", avatar: "ZS" },
      action: "创建了任务",
      target: "首页设计",
      time: "1小时前",
      icon: <FileText className="h-4 w-4" />,
      status: "primary",
      content: (
        <div className="mt-1 text-sm bg-muted p-2 rounded-md">
          需要完成首页的响应式设计，包括桌面端和移动端
        </div>
      )
    },
    {
      id: "2",
      user: { name: "李四", avatar: "LS" },
      action: "评论了任务",
      target: "首页设计",
      time: "45分钟前",
      icon: <MessageSquare className="h-4 w-4" />,
      status: "primary",
      content: (
        <div className="mt-1 text-sm bg-muted p-2 rounded-md">
          建议增加暗色模式支持
        </div>
      )
    },
    {
      id: "3",
      user: { name: "王五", avatar: "WW" },
      action: "完成了任务",
      target: "登录页面",
      time: "30分钟前",
      icon: <CheckCircle className="h-4 w-4" />,
      status: "success"
    },
    {
      id: "4",
      user: { name: "系统", avatar: "SYS" },
      action: "部署了新版本",
      target: "v1.2.0",
      time: "10分钟前",
      icon: <AlertCircle className="h-4 w-4" />,
      status: "warning",
      content: (
        <div className="mt-1">
          <Badge variant="outline" className="mr-1">修复</Badge>
          <Badge variant="outline">性能优化</Badge>
        </div>
      )
    }
  ];

  return (
    <ActivityTimeline 
      activities={activities}
      showAvatar={true}
      hasMore={true}
      onLoadMore={() => console.log("加载更多")}
    />
  );
}

render(<ActivityTimelineExample />);
`,
  },
  
  "stage-timeline": {
    title: "阶段时间轴",
    code: `
import React, { useState } from "react";
import { StageTimeline } from "@/components/common-custom/timeline";
import { User, FileText, Calendar, Clock, CheckCircle } from "lucide-react";
import { Button } from "@/components/ui/button";

function StageTimelineExample() {
  const [currentStage, setCurrentStage] = useState("stage3");
  
  const stages = [
    {
      id: "stage1",
      name: "信息收集",
      description: "收集基本信息",
      status: "completed",
      icon: <User className="h-4 w-4" />
    },
    {
      id: "stage2",
      name: "需求确认",
      description: "确认项目需求和范围",
      status: "completed",
      icon: <FileText className="h-4 w-4" />
    },
    {
      id: "stage3",
      name: "方案设计",
      description: "设计解决方案",
      status: "current",
      icon: <Calendar className="h-4 w-4" />
    },
    {
      id: "stage4",
      name: "开发实施",
      description: "开发和测试",
      status: "pending",
      icon: <Clock className="h-4 w-4" />
    },
    {
      id: "stage5",
      name: "交付使用",
      description: "项目交付和验收",
      status: "pending",
      icon: <CheckCircle className="h-4 w-4" />
    }
  ];

  const handlePrevious = () => {
    const currentIndex = stages.findIndex(stage => stage.id === currentStage);
    if (currentIndex > 0) {
      setCurrentStage(stages[currentIndex - 1].id);
    }
  };

  const handleNext = () => {
    const currentIndex = stages.findIndex(stage => stage.id === currentStage);
    if (currentIndex < stages.length - 1) {
      setCurrentStage(stages[currentIndex + 1].id);
    }
  };

  return (
    <div>
      <StageTimeline 
        stages={stages}
        currentStage={currentStage}
        onChange={setCurrentStage}
      />
      <div className="mt-4">
        <Button variant="outline" size="sm" onClick={handlePrevious}>
          上一步
        </Button>
        <Button className="ml-2" size="sm" onClick={handleNext}>
          下一步
        </Button>
      </div>
    </div>
  );
}

render(<StageTimelineExample />);
`,
  },
  
  "horizontal-timeline": {
    title: "水平时间轴",
    code: `
import React from "react";
import { Timeline } from "@/components/common-custom/timeline";
import { Zap, ArrowRight } from "lucide-react";

function HorizontalTimeline() {
  const timelineItems = [
    {
      id: "1",
      title: "第一步",
      description: "创建项目",
      time: "1小时",
      status: "success",
      icon: <Zap className="h-4 w-4 text-white" />
    },
    {
      id: "2",
      title: "第二步",
      description: "配置环境",
      time: "2小时",
      status: "success",
      icon: <Zap className="h-4 w-4 text-white" />
    },
    {
      id: "3",
      title: "第三步",
      description: "编写代码",
      time: "8小时",
      status: "primary",
      icon: <ArrowRight className="h-4 w-4 text-white" />
    },
    {
      id: "4",
      title: "第四步",
      description: "测试部署",
      time: "4小时",
      status: "warning",
      icon: <ArrowRight className="h-4 w-4 text-white" />
    }
  ];

  return (
    <div className="w-full overflow-x-auto pb-4">
      <div className="min-w-max">
        <Timeline 
          items={timelineItems}
          mode="horizontal"
        />
      </div>
    </div>
  );
}

render(<HorizontalTimeline />);
`,
  },
  
  // 返回按钮示例
  "basic-back-button": {
    title: "基础返回按钮",
    code: `
import React from "react";
import { BackButton } from "@/components/common-custom/back-button";

function BasicBackButton() {
  return (
    <div className="flex flex-wrap items-center gap-4">
      <BackButton href="/" />
      <BackButton href="/" showText>返回首页</BackButton>
    </div>
  );
}

render(<BasicBackButton />);
`,
  },
  "size-back-button": {
    title: "按钮尺寸",
    code: `
import React from "react";
import { BackButton } from "@/components/common-custom/back-button";

function SizeBackButton() {
  return (
    <div className="flex flex-wrap items-center gap-4">
      <div className="flex items-center gap-2">
        <BackButton href="/" size="sm" showText>小尺寸</BackButton>
      </div>
      <div className="flex items-center gap-2">
        <BackButton href="/" size="md" showText>中尺寸</BackButton>
      </div>
      <div className="flex items-center gap-2">
        <BackButton href="/" size="lg" showText>大尺寸</BackButton>
      </div>
    </div>
  );
}

render(<SizeBackButton />);
`,
  },
  "rounded-back-button": {
    title: "圆角样式",
    code: `
import React from "react";
import { BackButton } from "@/components/common-custom/back-button";

function RoundedBackButton() {
  return (
    <div className="flex flex-wrap items-center gap-4">
      <BackButton href="/" rounded="default" showText>默认圆角</BackButton>
      <BackButton href="/" rounded="md" showText>中等圆角</BackButton>
      <BackButton href="/" rounded="full" showText>完全圆形</BackButton>
    </div>
  );
}

render(<RoundedBackButton />);
`,
  },
  "variant-back-button": {
    title: "变体样式",
    code: `
import React from "react";
import { BackButton } from "@/components/common-custom/back-button";

function VariantBackButton() {
  return (
    <div className="flex flex-wrap items-center gap-4">
      <BackButton href="/" variant="default" showText>默认样式</BackButton>
      <BackButton href="/" variant="ghost" showText>透明样式</BackButton>
      <BackButton href="/" variant="outline" showText>轮廓样式</BackButton>
    </div>
  );
}

render(<VariantBackButton />);
`,
  },
  "combined-back-button": {
    title: "组合用法",
    code: `
import React from "react";
import { BackButton } from "@/components/common-custom/back-button";

function CombinedBackButton() {
  return (
    <div className="flex flex-wrap items-center gap-4">
      <BackButton 
        href="/" 
        size="lg" 
        variant="outline" 
        rounded="md" 
        showText
      >
        返回上一页
      </BackButton>
      
      <BackButton 
        href="/" 
        size="sm" 
        variant="ghost" 
        rounded="full" 
      />
    </div>
  );
}

render(<CombinedBackButton />);
`,
  },
  
  // 面包屑导航示例
  "basic-breadcrumb": {
    title: "基础面包屑",
    code: `
import React from "react";
import { Breadcrumb } from "@/components/common-custom/breadcrumb";

function BasicBreadcrumb() {
  const breadcrumbItems = [
    { label: "首页", href: "/" },
    { label: "产品", href: "/products" },
    { label: "类别", href: "/products/categories" },
    { label: "电子产品", href: "/products/categories/electronics", current: true },
  ];

  return (
    <Breadcrumb items={breadcrumbItems} variant="standard" />
  );
}

render(<BasicBreadcrumb />);
`,
  },
  "icon-breadcrumb": {
    title: "带图标面包屑",
    code: `
import React from "react";
import { Breadcrumb } from "@/components/common-custom/breadcrumb";
import { Home, Users } from "lucide-react";

function IconBreadcrumb() {
  const breadcrumbItems = [
    { label: "首页", href: "/", icon: <Home className="h-4 w-4" /> },
    { label: "用户管理", href: "/users", icon: <Users className="h-4 w-4" /> },
    { label: "用户详情", href: "/users/profile", current: true },
  ];

  return (
    <Breadcrumb items={breadcrumbItems} variant="standard" />
  );
}

render(<IconBreadcrumb />);
`,
  },
  "collapsed-breadcrumb": {
    title: "折叠模式面包屑",
    code: `
import React from "react";
import { Breadcrumb } from "@/components/common-custom/breadcrumb";

function CollapsedBreadcrumb() {
  const breadcrumbItems = [
    { label: "首页", href: "/" },
    { label: "产品", href: "/products" },
    { label: "类别", href: "/products/categories" },
    { label: "电子产品", href: "/products/categories/electronics" },
    { label: "手机", href: "/products/categories/electronics/phones" },
    { label: "智能手机", href: "/products/categories/electronics/phones/smartphones" },
    { label: "iPhone", href: "/products/categories/electronics/phones/smartphones/iphone", current: true },
  ];

  return (
    <Breadcrumb 
      items={breadcrumbItems} 
      variant="collapsed" 
      maxItems={4}
    />
  );
}

render(<CollapsedBreadcrumb />);
`,
  },
  
  // 分页组件示例
  "basic-pagination": {
    title: "基础分页",
    code: `
import React, { useState } from "react";
import { Pagination } from "@/components/common-custom/pagination";

function BasicPagination() {
  const [pageIndex, setPageIndex] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  
  // 模拟总数据量
  const totalItems = 235;

  // 计算总页数
  const pageCount = Math.ceil(totalItems / pageSize);

  return (
    <div className="border rounded-md p-4">
      <div className="h-16 flex items-center justify-center bg-muted rounded-md mb-4">
        <p className="text-muted-foreground">
          当前页: {pageIndex + 1}, 每页显示: {pageSize} 条, 总计: {totalItems} 条
        </p>
      </div>
      
      <Pagination
        pageIndex={pageIndex}
        pageCount={pageCount}
        pageSize={pageSize}
        totalItems={totalItems}
        onPageChange={setPageIndex}
        onPageSizeChange={setPageSize}
      />
    </div>
  );
}

render(<BasicPagination />);
`,
  },
  "first-page": {
    title: "首页状态",
    code: `
import React from "react";
import { Pagination } from "@/components/common-custom/pagination";

function FirstPagePagination() {
  return (
    <Pagination
      pageIndex={0}
      pageCount={10}
      pageSize={10}
      totalItems={100}
      onPageChange={() => {}}
      onPageSizeChange={() => {}}
    />
  );
}

render(<FirstPagePagination />);
`,
  },
  "middle-page": {
    title: "中间页状态",
    code: `
import React from "react";
import { Pagination } from "@/components/common-custom/pagination";

function MiddlePagePagination() {
  return (
    <Pagination
      pageIndex={4}
      pageCount={10}
      pageSize={10}
      totalItems={100}
      onPageChange={() => {}}
      onPageSizeChange={() => {}}
    />
  );
}

render(<MiddlePagePagination />);
`,
  },
  "last-page": {
    title: "末页状态",
    code: `
import React from "react";
import { Pagination } from "@/components/common-custom/pagination";

function LastPagePagination() {
  return (
    <Pagination
      pageIndex={9}
      pageCount={10}
      pageSize={10}
      totalItems={100}
      onPageChange={() => {}}
      onPageSizeChange={() => {}}
    />
  );
}

render(<LastPagePagination />);
`,
  },
  "custom-page-size": {
    title: "自定义页码选项",
    code: `
import React from "react";
import { Pagination } from "@/components/common-custom/pagination";

function CustomPageSizePagination() {
  return (
    <Pagination
      pageIndex={0}
      pageCount={Math.ceil(100 / 15)}
      pageSize={15}
      totalItems={100}
      pageSizeOptions={[5, 15, 25, 50]}
      onPageChange={() => {}}
      onPageSizeChange={() => {}}
    />
  );
}

render(<CustomPageSizePagination />);
`,
  },
  // 通知组件示例
  "basic-notification": {
    title: "基础通知",
    code: `
import React from "react";
import { Button } from "@/components/ui/button";
import { showToast } from "@/components/common-custom/notification";
import { useToast } from "@/hooks/use-toast";

function BasicNotification() {
  const { toast } = useToast();
  
  const handleShowToast = (type) => {
    showToast(
      {
      type,
      message: \`这是一条\${
        type === "success" ? "成功" : 
        type === "error" ? "错误" : 
        type === "warning" ? "警告" : "信息"
      }通知\`
      },
      {
        show: ({ title, description, variant, duration }) => {
          toast({
            title,
            description,
            variant,
            duration,
          });
        }
      }
    );
  };

  return (
    <div className="flex flex-wrap gap-4 justify-center">
      <Button 
        onClick={() => handleShowToast("success")}
        variant="default"
      >
        成功通知
      </Button>
      <Button 
        onClick={() => handleShowToast("error")}
        variant="destructive"
      >
        错误通知
      </Button>
      <Button 
        onClick={() => handleShowToast("warning")}
        variant="outline"
      >
        警告通知
      </Button>
      <Button 
        onClick={() => handleShowToast("info")}
        variant="secondary"
      >
        信息通知
      </Button>
    </div>
  );
}

render(<BasicNotification />);
`,
  },
  "announcement-banner": {
    title: "公告横幅",
    code: `
import React from "react";
import { AnnouncementBanner } from "@/components/common-custom/notification";

function AnnouncementBannerExample() {
  return (
    <div className="flex flex-col gap-4">
      <AnnouncementBanner
        id="system-update"
        type="info"
        title="系统更新通知"
        content="系统将于今晚22:00-24:00进行升级维护，请提前做好准备工作。"
        date="2024-05-10 10:30"
        onClose={() => {}}
      />
      <AnnouncementBanner
        id="security-alert"
        type="warning"
        title="安全警告"
        content="检测到异常登录行为，请及时修改密码并开启双因素认证。"
        date="2024-05-09 15:45"
        onClose={() => {}}
      />
      <AnnouncementBanner
        id="success-operation"
        type="success"
        title="操作成功"
        content="您的订单已经成功提交，我们将尽快处理。"
        date="2024-05-08 09:15"
        onClose={() => {}}
      />
    </div>
  );
}

render(<AnnouncementBannerExample />);
`,
  },
  "notification-center": {
    title: "通知中心",
    code: `
import React, { useState } from "react";
import { NotificationCenter } from "@/components/common-custom/notification";
import { Button } from "@/components/ui/button";

function NotificationCenterExample() {
  const [notifications, setNotifications] = useState([
    {
      id: "1",
      title: "系统更新完成",
      message: "系统已更新到最新版本，新增多项功能优化。",
      date: "10分钟前",
      type: "info",
      read: false
    },
    {
      id: "2",
      title: "安全警告",
      message: "检测到异常登录行为，请及时修改密码。",
      date: "30分钟前",
      type: "warning",
      read: false
    },
    {
      id: "3",
      title: "订单已发货",
      message: "您的订单 #12345 已发货，预计3天内送达。",
      date: "2小时前",
      type: "success",
      read: true,
      actions: [
        {
          label: "查看订单",
          onClick: () => alert("查看订单详情"),
          primary: true
        }
      ]
    }
  ]);

  const handleMarkAsRead = (id) => {
    setNotifications(
      notifications.map(notification => 
        notification.id === id 
          ? { ...notification, read: true } 
          : notification
      )
    );
  };

  const handleMarkAllAsRead = () => {
    setNotifications(
      notifications.map(notification => ({ ...notification, read: true }))
    );
  };

  const handleClearAll = () => {
    setNotifications([]);
  };

  return (
    <div className="flex flex-col gap-4">
      <div className="max-w-md mx-auto">
        <NotificationCenter
          notifications={notifications}
          onNotificationClick={(notification) => alert(\`点击了: \${notification.title}\`)}
          onMarkAsRead={handleMarkAsRead}
          onMarkAllAsRead={handleMarkAllAsRead}
          onClearAll={handleClearAll}
        />
      </div>
    </div>
  );
}

render(<NotificationCenterExample />);
`,
  },
  "notification-settings": {
    title: "通知设置",
    code: `
import React, { useState } from "react";
import { NotificationSettings } from "@/components/common-custom/notification";
import { Mail, Bell, Smartphone, Settings } from "lucide-react";

function NotificationSettingsExample() {
  const [settings, setSettings] = useState([
    {
      id: "email",
      label: "电子邮件通知",
      description: "接收重要信息和更新",
      enabled: true,
      icon: Mail
    },
    {
      id: "push",
      label: "推送通知",
      description: "接收实时应用推送",
      enabled: true,
      icon: Bell
    },
    {
      id: "sms",
      label: "短信通知",
      description: "接收重要提醒和安全验证",
      enabled: false,
      icon: Smartphone
    },
    {
      id: "system",
      label: "系统通知",
      description: "接收系统更新和维护信息",
      enabled: true,
      icon: Settings
    }
  ]);

  const handleSave = (updatedSettings) => {
    setSettings(updatedSettings);
    alert("设置已保存");
  };

  return (
    <div className="max-w-md mx-auto">
      <NotificationSettings
        settings={settings}
        onSave={handleSave}
        onReset={() => setSettings([...settings])}
      />
    </div>
  );
}

render(<NotificationSettingsExample />);
`,
  },
  // 日历组件示例
  "basic-calendar": {
    title: "基础日历",
    code: `
import React from "react";
import { Calendar } from "@/components/common-custom/calendar";

class BasicCalendar extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      date: new Date()
    };
  }
  
  render() {
    return (
      <Calendar
        mode="single" 
        selected={this.state.date} 
        onSelect={(newDate) => this.setState({ date: newDate || new Date() })} 
        className="rounded-md border" 
      />
    );
  }
}

render(<BasicCalendar />);
`
  },
  "range-calendar": {
    title: "日期范围选择",
    code: `
import React from "react";
import { Calendar } from "@/components/common-custom/calendar";
import { addDays } from "date-fns";

class RangeCalendar extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      dateRange: {
        from: new Date(),
        to: addDays(new Date(), 7)
      }
    };
  }
  
  render() {
    return (
      <Calendar
        mode="range" 
        selected={this.state.dateRange} 
        onSelect={(newRange) => this.setState({ 
          dateRange: newRange || { from: new Date(), to: addDays(new Date(), 7) }
        })} 
        className="rounded-md border"
      />
    );
  }
}

render(<RangeCalendar />);
`
  },
  "event-calendar": {
    title: "事件日历",
    code: `
import React from "react";
import { Calendar } from "@/components/common-custom/calendar";
import { addDays } from "date-fns";

class EventCalendar extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      date: new Date()
    };
    
    this.events = [
      { date: new Date(), title: "团队会议", type: "meeting", description: "讨论项目进度" },
      { date: addDays(new Date(), 2), title: "项目截止", type: "deadline", description: "完成初步设计" },
      { date: addDays(new Date(), 4), title: "团建活动", type: "event", description: "团队聚餐" },
    ];
  }
  
  render() {
    return (
      <Calendar
        mode="single" 
        selected={this.state.date} 
        onSelect={(newDate) => this.setState({ date: newDate || new Date() })} 
        className="rounded-md border" 
        events={this.events}
        showEventDetails={true}
      />
    );
  }
}

render(<EventCalendar />);
`
  },
  "card-calendar": {
    title: "卡片式日历",
    code: `
import React from "react";
import { Calendar } from "@/components/common-custom/calendar";
import { addDays } from "date-fns";

class CardCalendar extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      date: new Date()
    };
    
    this.events = [
      { date: new Date(), title: "团队会议", type: "meeting", description: "讨论项目进度" },
      { date: addDays(new Date(), 2), title: "项目截止", type: "deadline", description: "完成初步设计" },
      { date: addDays(new Date(), 4), title: "团建活动", type: "event", description: "团队聚餐" },
    ];
  }
  
  render() {
    return (
      <Calendar
        mode="single" 
        selected={this.state.date} 
        onSelect={(newDate) => this.setState({ date: newDate || new Date() })}
        title="我的日程"
        withCard={true}
        events={this.events}
        showEventDetails={true}
      />
    );
  }
}

render(<CardCalendar />);
`
  },
  // 列表视图示例
  "message-list": {
    title: "消息列表",
    code: `
import React from "react"
import { MessageList } from "@/components/common-custom/list-view"

function MessageListExample() {
  const messages = [
    {
      id: 1,
      sender: "张三",
      avatar: "/placeholder-user.jpg",
      subject: "关于项目进度的讨论",
      preview: "我们需要加快进度，希望能在本周五之前完成第一阶段的开发工作...",
      time: "10:30",
      unread: true,
      important: true
    },
    {
      id: 2,
      sender: "李四",
      avatar: "/placeholder-user.jpg",
      subject: "设计稿确认",
      preview: "请查看最新的设计稿，并给出您的反馈意见。我们需要尽快确定最终方案。",
      time: "昨天",
      unread: false,
      important: false
    },
    {
      id: 3,
      sender: "王五",
      avatar: "/placeholder-user.jpg",
      subject: "会议安排",
      preview: "下周一上午10点将举行项目进展汇报会，请做好准备并准时参加。",
      time: "星期二",
      unread: false,
      important: false
    }
  ]
  
  return (
    <MessageList 
      title="消息列表"
      description="您的最新消息和对话"
      messages={messages}
      onMessageAction={(message) => console.log("消息操作:", message)}
    />
  )
}

render(<MessageListExample />)
`,
  },
  "notification-list": {
    title: "通知列表",
    code: `
import React from "react"
import { NotificationList } from "@/components/common-custom/list-view"

function NotificationListExample() {
  const notifications = [
    {
      id: 1,
      type: "system",
      title: "系统升级通知",
      message: "系统将于今晚22:00-24:00进行升级维护，请提前保存相关工作。",
      time: "10分钟前",
      read: false
    },
    {
      id: 2,
      type: "security",
      title: "安全提醒",
      message: "您的账户刚刚在新设备上登录，如非本人操作，请立即修改密码。",
      time: "1小时前",
      read: true
    },
    {
      id: 3,
      type: "order",
      title: "订单状态更新",
      message: "您的订单#12345已发货，预计3-5天送达。",
      time: "昨天",
      read: true
    }
  ]
  
  return (
    <NotificationList
      notifications={notifications}
      title="通知中心"
      description="系统通知和重要提醒"
      onNotificationClick={(notification) => console.log("点击通知:", notification)}
    />
  )
}

render(<NotificationListExample />)
`,
  },
  "comment-list": {
    title: "评论列表",
    code: `
import React from "react"
import { CommentList } from "@/components/common-custom/list-view"

function CommentListExample() {
  const comments = [
    {
      id: 1,
      author: "张三",
      avatar: "/placeholder-user.jpg",
      content: "这个设计方案非常棒，我很喜欢整体的布局和色彩搭配。",
      time: "2小时前",
      likes: 12,
      replies: 3,
      rating: 5
    },
    {
      id: 2,
      author: "李四",
      avatar: "/placeholder-user.jpg",
      content: "我认为导航栏的交互可以再优化一下，目前的用户体验不是很流畅。",
      time: "昨天",
      likes: 5,
      replies: 2,
      rating: 3
    }
  ]
  
  return (
    <CommentList
      title="评论列表"
      description="最新评论和反馈"
      comments={comments}
      onCommentLike={(comment) => console.log("点赞评论:", comment)}
      onCommentReply={(comment) => console.log("回复评论:", comment)}
      onCommentShare={(comment) => console.log("分享评论:", comment)}
    />
  )
}

render(<CommentListExample />)
`,
  },
  "user-list": {
    title: "用户列表",
    code: `
import React from "react"
import { UserList } from "@/components/common-custom/list-view"

function UserListExample() {
  const users = [
    {
      id: 1,
      name: "张三",
      avatar: "/placeholder-user.jpg",
      role: "前端开发工程师",
      department: "技术部",
      location: "北京",
      joinDate: "2022-05-15",
      status: "online",
      projects: 8
    },
    {
      id: 2,
      name: "李四",
      avatar: "/placeholder-user.jpg",
      role: "产品经理",
      department: "产品部",
      location: "上海",
      joinDate: "2021-10-20",
      status: "busy",
      projects: 12
    }
  ]
  
  return (
    <UserList
      title="用户列表"
      description="系统用户和成员"
      users={users}
      onUserMessage={(user) => console.log("发送消息给用户:", user)}
      onUserDetails={(user) => console.log("查看用户详情:", user)}
    />
  )
}

render(<UserListExample />)
`,
  },
  "list-view-controls": {
    title: "列表视图控件",
    code: `
import React, { useState } from "react"
import { ListViewControls } from "@/components/common-custom/list-view"

function ListViewControlsExample() {
  const [searchValue, setSearchValue] = useState("")
  const [currentView, setCurrentView] = useState("grid")
  
  const viewTypes = [
    { value: "grid", label: "网格视图" },
    { value: "list", label: "列表视图" },
    { value: "table", label: "表格视图" }
  ]
  
  const filters = [
    {
      id: "status",
      label: "状态",
      type: "select",
      options: [
        { label: "全部", value: "all" },
        { label: "活跃", value: "active" },
        { label: "已禁用", value: "disabled" }
      ],
      defaultValue: "all"
    },
    {
      id: "type",
      label: "类型",
      type: "radio",
      options: [
        { label: "全部", value: "all" },
        { label: "公开", value: "public" },
        { label: "私有", value: "private" }
      ],
      defaultValue: "all"
    },
    {
      id: "dateRange",
      label: "创建日期",
      type: "dateRange",
      placeholder: "选择日期范围"
    }
  ]
  
  return (
    <div className="w-full">
      <ListViewControls
        viewTypes={viewTypes}
        currentView={currentView}
        onViewChange={setCurrentView}
        searchValue={searchValue}
        onSearchChange={setSearchValue}
        filters={filters}
        onFilterChange={(id, value) => console.log("筛选器变更:", id, value)}
        onFilterApply={() => console.log("应用筛选器")}
        onFilterReset={() => console.log("重置筛选器")}
      />
      
      <div className="mt-4 p-6 border rounded-md">
        <div className="text-center text-muted-foreground">
          {currentView === "grid" ? "网格视图内容" : currentView === "list" ? "列表视图内容" : "表格视图内容"}
          {searchValue && <div className="mt-2">搜索: {searchValue}</div>}
        </div>
      </div>
    </div>
  )
}

render(<ListViewControlsExample />)
`,
  },
  "load-more-button": {
    title: "加载更多按钮",
    code: `
import React, { useState } from "react"
import { LoadMoreButton } from "@/components/common-custom/list-view"

function LoadMoreButtonExample() {
  const [loading, setLoading] = useState(false)
  
  const handleLoadMore = () => {
    setLoading(true)
    // 模拟加载数据
    setTimeout(() => {
      setLoading(false)
    }, 1500)
  }
  
  return (
    <div className="text-center">
      <div className="mb-4 p-4 border rounded-md">
        <p>列表内容会显示在这里...</p>
      </div>
      <LoadMoreButton
        onClick={handleLoadMore}
        loading={loading}
      />
    </div>
  )
}

render(<LoadMoreButtonExample />)
`,
  },
  // 表单示例
  "basic-form": {
    title: "基础表单",
    code: `
import React, { useState } from "react"
import { Form } from "@/components/common-custom/form"

function BasicFormExample() {
  const [formValues, setFormValues] = useState({
    name: "",
    email: "",
    department: "",
    bio: ""
  })
  
  const [formErrors, setFormErrors] = useState({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSuccess, setIsSuccess] = useState(false)
  
  const handleFieldChange = (name, value) => {
    setFormValues(prev => ({
      ...prev,
      [name]: value
    }))
    
    // 清除错误
    if (formErrors[name]) {
      setFormErrors(prev => {
        const newErrors = {...prev}
        delete newErrors[name]
        return newErrors
      })
    }
  }
  
  const handleSubmit = async (values) => {
    setIsSubmitting(true)
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      setIsSuccess(true)
      console.log("表单提交成功:", values)
      
      // 重置成功状态
      setTimeout(() => {
        setIsSuccess(false)
      }, 2000)
    } catch (error) {
      console.error(error)
    } finally {
      setIsSubmitting(false)
    }
  }
  
  // 表单字段配置
  const fields = [
    {
      name: "name",
      label: "姓名",
      type: "text",
      placeholder: "请输入您的姓名",
      required: true,
      description: "这将是您的显示名称"
    },
    {
      name: "email",
      label: "电子邮件",
      type: "email",
      placeholder: "<EMAIL>",
      required: true,
      description: "用于接收系统通知"
    },
    {
      name: "department",
      label: "部门",
      type: "select",
      placeholder: "请选择部门",
      required: true,
      options: [
        { label: "研发部", value: "engineering" },
        { label: "市场部", value: "marketing" },
        { label: "销售部", value: "sales" },
        { label: "客服部", value: "support" }
      ]
    },
    {
      name: "bio",
      label: "个人简介",
      type: "textarea",
      placeholder: "简单介绍一下自己...",
      required: false,
      description: "您可以介绍一下自己的专业背景和技能"
    }
  ]
  
  return (
    <Form
      title="个人信息"
      description="填写您的个人信息用于系统识别"
      fields={fields}
      values={formValues}
      errors={formErrors}
      onChange={handleFieldChange}
      onSubmit={handleSubmit}
      isLoading={isSubmitting}
      isSuccess={isSuccess}
      successMessage="提交成功!"
    />
  )
}

render(<BasicFormExample />)
`,
  },
  "section-form": {
    title: "分组表单",
    code: `
import React, { useState } from "react"
import { Form } from "@/components/common-custom/form"

function SectionFormExample() {
  const [formValues, setFormValues] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    address: "",
    city: "",
    state: "",
    zipCode: "",
    company: "",
    jobTitle: "",
    department: ""
  })
  
  const [formErrors, setFormErrors] = useState({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  const handleFieldChange = (name, value) => {
    setFormValues(prev => ({
      ...prev,
      [name]: value
    }))
    
    // 清除错误
    if (formErrors[name]) {
      setFormErrors(prev => {
        const newErrors = {...prev}
        delete newErrors[name]
        return newErrors
      })
    }
  }
  
  const handleSubmit = async (values) => {
    setIsSubmitting(true)
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      console.log("表单提交成功:", values)
    } catch (error) {
      console.error(error)
    } finally {
      setIsSubmitting(false)
    }
  }
  
  // 表单分组配置
  const sections = [
    {
      title: "个人信息",
      description: "您的基本个人信息",
      fields: [
        {
          name: "firstName",
          label: "名",
          type: "text",
          placeholder: "请输入名",
          required: true
        },
        {
          name: "lastName",
          label: "姓",
          type: "text",
          placeholder: "请输入姓",
          required: true
        },
        {
          name: "email",
          label: "电子邮件",
          type: "email",
          placeholder: "<EMAIL>",
          required: true
        },
        {
          name: "phone",
          label: "电话号码",
          type: "tel",
          placeholder: "请输入电话号码",
          required: false
        }
      ]
    },
    {
      title: "联系地址",
      description: "您的邮寄地址信息",
      fields: [
        {
          name: "address",
          label: "街道地址",
          type: "text",
          placeholder: "请输入街道地址",
          required: true
        },
        {
          name: "city",
          label: "城市",
          type: "text",
          placeholder: "请输入城市",
          required: true
        },
        {
          name: "state",
          label: "省份",
          type: "text",
          placeholder: "请输入省份",
          required: true
        },
        {
          name: "zipCode",
          label: "邮政编码",
          type: "text",
          placeholder: "请输入邮政编码",
          required: true
        }
      ]
    },
    {
      title: "工作信息",
      description: "您的职业相关信息",
      fields: [
        {
          name: "company",
          label: "公司名称",
          type: "text",
          placeholder: "请输入公司名称",
          required: false
        },
        {
          name: "jobTitle",
          label: "职位",
          type: "text",
          placeholder: "请输入职位",
          required: false
        },
        {
          name: "department",
          label: "部门",
          type: "select",
          placeholder: "请选择部门",
          required: false,
          options: [
            { label: "研发部", value: "engineering" },
            { label: "市场部", value: "marketing" },
            { label: "销售部", value: "sales" },
            { label: "客服部", value: "support" }
          ]
        }
      ]
    }
  ]
  
  return (
    <Form
      title="完整个人资料"
      description="请完善您的个人资料信息"
      sections={sections}
      values={formValues}
      errors={formErrors}
      onChange={handleFieldChange}
      onSubmit={handleSubmit}
      isLoading={isSubmitting}
    />
  )
}

render(<SectionFormExample />)
`,
  },
  "horizontal-form": {
    title: "水平布局表单",
    code: `
import React, { useState } from "react"
import { Form } from "@/components/common-custom/form"

function HorizontalFormExample() {
  const [formValues, setFormValues] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    bio: ""
  })
  
  const [formErrors, setFormErrors] = useState({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  const handleFieldChange = (name, value) => {
    setFormValues(prev => ({
      ...prev,
      [name]: value
    }))
    
    // 清除错误
    if (formErrors[name]) {
      setFormErrors(prev => {
        const newErrors = {...prev}
        delete newErrors[name]
        return newErrors
      })
    }
  }
  
  const handleSubmit = async (values) => {
    setIsSubmitting(true)
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      console.log("表单提交成功:", values)
    } catch (error) {
      console.error(error)
    } finally {
      setIsSubmitting(false)
    }
  }
  
  // 表单字段配置
  const fields = [
    {
      name: "firstName",
      label: "名",
      type: "text",
      placeholder: "请输入名",
      required: true
    },
    {
      name: "lastName",
      label: "姓",
      type: "text",
      placeholder: "请输入姓",
      required: true
    },
    {
      name: "email",
      label: "电子邮件",
      type: "email",
      placeholder: "<EMAIL>",
      required: true
    },
    {
      name: "phone",
      label: "电话号码",
      type: "tel",
      placeholder: "请输入电话号码",
      required: false
    },
    {
      name: "bio",
      label: "个人简介",
      type: "textarea",
      placeholder: "简单介绍一下自己...",
      required: false
    }
  ]
  
  return (
    <Form
      title="个人信息"
      description="填写您的个人信息用于系统识别"
      fields={fields}
      values={formValues}
      errors={formErrors}
      onChange={handleFieldChange}
      onSubmit={handleSubmit}
      isLoading={isSubmitting}
      layout="horizontal"
    />
  )
}

render(<HorizontalFormExample />)
`,
  },
  "form-dialog": {
    title: "表单对话框",
    code: `
import React, { useState } from "react"
import { Form } from "@/components/common-custom/form"
import { Button } from "@/components/ui/button"

function FormDialogExample() {
  const [dialogOpen, setDialogOpen] = useState(false)
  const [formValues, setFormValues] = useState({
    name: "",
    email: "",
    role: "",
    active: false,
    bio: ""
  })
  
  const [formErrors, setFormErrors] = useState({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  const handleFieldChange = (name, value) => {
    setFormValues(prev => ({
      ...prev,
      [name]: value
    }))
    
    // 清除错误
    if (formErrors[name]) {
      setFormErrors(prev => {
        const newErrors = {...prev}
        delete newErrors[name]
        return newErrors
      })
    }
  }
  
  const handleSubmit = async (values) => {
    setIsSubmitting(true)
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      console.log("表单提交成功:", values)
      setDialogOpen(false)
    } catch (error) {
      console.error(error)
    } finally {
      setIsSubmitting(false)
    }
  }
  
  // 表单字段配置
  const fields = [
    {
      name: "name",
      label: "姓名",
      type: "text",
      placeholder: "请输入姓名",
      required: true
    },
    {
      name: "email",
      label: "电子邮件",
      type: "email",
      placeholder: "<EMAIL>",
      required: true
    },
    {
      name: "role",
      label: "角色",
      type: "select",
      placeholder: "请选择角色",
      required: true,
      options: [
        { label: "管理员", value: "admin" },
        { label: "编辑", value: "editor" },
        { label: "用户", value: "user" }
      ]
    },
    {
      name: "active",
      label: "激活状态",
      type: "switch",
      description: "是否激活此用户"
    },
    {
      name: "bio",
      label: "个人简介",
      type: "textarea",
      placeholder: "简单介绍一下...",
      required: false
    }
  ]
  
  return (
    <div>
      <Button onClick={() => setDialogOpen(true)}>
        打开表单对话框
      </Button>
      
      <Form
        title="创建用户"
        description="添加新用户到系统"
        fields={fields}
        values={formValues}
        errors={formErrors}
        onChange={handleFieldChange}
        onSubmit={handleSubmit}
        isLoading={isSubmitting}
        isDialog={true}
        dialogOpen={dialogOpen}
        onDialogOpenChange={setDialogOpen}
        dialogMaxWidth="md"
      />
    </div>
  )
}

render(<FormDialogExample />)
`,
  },
  // 评分组件示例
  "basic-rating": {
    title: "基础评分",
    code: `
import React from "react";
import { Rating } from "@/components/common-custom/rating";

function BasicRatingExample() {
  const [value, setValue] = React.useState(3);
  
  return (
    <div className="space-y-6 w-full max-w-full">
      <div>
        <div className="text-sm font-medium mb-2">基本用法：</div>
        <Rating
          value={value}
          onChange={setValue}
          max={5}
        />
        <div className="mt-1 text-sm text-muted-foreground">
          当前评分：{value}
        </div>
      </div>
      
      <div>
        <div className="text-sm font-medium mb-2">只读模式：</div>
        <Rating
          value={4}
          readonly
          max={5}
        />
      </div>
      
      <div>
        <div className="text-sm font-medium mb-2">不同大小：</div>
        <div className="flex flex-col gap-3">
          <Rating
            value={value}
            onChange={setValue}
            size="sm"
            max={5}
          />
          <Rating
            value={value}
            onChange={setValue}
            size="md"
            max={5}
          />
          <Rating
            value={value}
            onChange={setValue}
            size="lg"
            max={5}
          />
        </div>
      </div>
      
      <div>
        <div className="text-sm font-medium mb-2">自定义图标：</div>
        <Rating
          value={value}
          onChange={setValue}
          max={5}
          icon="heart"
        />
      </div>
    </div>
  );
}

render(<BasicRatingExample />);
`,
  },
  "half-star-rating": {
    title: "半星评分",
    code: `
import React from "react";
import { Rating } from "@/components/common-custom/rating";

function HalfStarRatingExample() {
  const [value, setValue] = React.useState(2.5);
  
  return (
    <div className="w-full max-w-full">
      <div className="text-sm font-medium mb-2">半星模式：</div>
      <Rating
        value={value}
        onChange={setValue}
        max={5}
        allowHalf
      />
      <div className="mt-1 text-sm text-muted-foreground">
        当前评分：{value}
      </div>
    </div>
  );
}

render(<HalfStarRatingExample />);
`,
  },
  "rating-display": {
    title: "评分展示",
    code: `
import React from "react";
import { RatingDisplay } from "@/components/common-custom/rating";

function RatingDisplayExample() {
  return (
    <div className="space-y-6 w-full max-w-full">
      <div>
        <div className="text-sm font-medium mb-2">基本展示：</div>
        <RatingDisplay
          value={4.5}
          max={5}
        />
      </div>
      
      <div>
        <div className="text-sm font-medium mb-2">显示评分值：</div>
        <div className="flex flex-col gap-3">
          <div className="flex items-center gap-2">
            <RatingDisplay
              value={4.5}
              max={5}
              showValue
              valueFormat="decimal"
            />
            <span className="text-sm text-muted-foreground">小数形式</span>
          </div>
          
          <div className="flex items-center gap-2">
            <RatingDisplay
              value={4.5}
              max={5}
              showValue
              valueFormat="fraction"
            />
            <span className="text-sm text-muted-foreground">分数形式</span>
          </div>
          
          <div className="flex items-center gap-2">
            <RatingDisplay
              value={4.5}
              max={5}
              showValue
              valueFormat="percent"
            />
            <span className="text-sm text-muted-foreground">百分比形式</span>
          </div>
        </div>
      </div>
      
      <div>
        <div className="text-sm font-medium mb-2">带评分数量：</div>
        <RatingDisplay
          value={4.5}
          max={5}
          showValue
          count={126}
        />
      </div>
    </div>
  );
}

render(<RatingDisplayExample />);
`,
  },
  "rating-statistics": {
    title: "评分统计",
    code: `
import React from "react";
import { RatingStatistics } from "@/components/common-custom/rating";

function RatingStatisticsExample() {
  return (
    <div className="w-full max-w-full">
      <RatingStatistics
        distribution={[85, 32, 15, 8, 5]}
        average={4.2}
        count={145}
      />
    </div>
  );
}

render(<RatingStatisticsExample />);
`,
  },
  // 表单对话框示例
  "basic-form-dialog": {
    title: "基础表单对话框",
    code: `
import React, { useState } from "react"
import { FormDialog } from "@/components/common-custom/form"
import { Button } from "@/components/ui/button"
import { Plus } from "lucide-react"

function BasicFormDialogExample() {
  const [dialogOpen, setDialogOpen] = useState(false)
  const [formValues, setFormValues] = useState({
    name: "",
    email: "",
    role: "",
    active: false,
    bio: ""
  })
  
  const [formErrors, setFormErrors] = useState({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submittedData, setSubmittedData] = useState(null)
  
  const handleFieldChange = (name, value) => {
    setFormValues(prev => ({
      ...prev,
      [name]: value
    }))
    
    // 清除错误
    if (formErrors[name]) {
      setFormErrors(prev => {
        const newErrors = {...prev}
        delete newErrors[name]
        return newErrors
      })
    }
  }
  
  const handleSubmit = async (values) => {
    setIsSubmitting(true)
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      setSubmittedData(values)
      setDialogOpen(false)
      console.log("表单提交成功:", values)
    } catch (error) {
      console.error(error)
    } finally {
      setIsSubmitting(false)
    }
  }
  
  // 表单字段配置
  const fields = [
    {
      name: "name",
      label: "姓名",
      type: "text",
      placeholder: "请输入姓名",
      required: true
    },
    {
      name: "email",
      label: "电子邮件",
      type: "email",
      placeholder: "<EMAIL>",
      required: true
    },
    {
      name: "role",
      label: "角色",
      type: "select",
      placeholder: "请选择角色",
      required: true,
      options: [
        { label: "管理员", value: "admin" },
        { label: "编辑", value: "editor" },
        { label: "用户", value: "user" }
      ]
    },
    {
      name: "active",
      label: "激活状态",
      type: "switch",
      description: "是否激活此用户"
    },
    {
      name: "bio",
      label: "个人简介",
      type: "textarea",
      placeholder: "简单介绍一下...",
      required: false
    }
  ]
  
  return (
    <div className="space-y-4">
      <Button onClick={() => setDialogOpen(true)}>
        <Plus className="w-4 h-4 mr-2" />
        添加用户
      </Button>
      
      <FormDialog
        title="添加用户"
        description="添加新用户到系统"
        fields={fields}
        values={formValues}
        errors={formErrors}
        onChange={handleFieldChange}
        onSubmit={handleSubmit}
        isLoading={isSubmitting}
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        maxWidth="md"
      />
      
      {submittedData && (
        <div className="p-4 border rounded-md mt-4">
          <h4 className="font-medium mb-2">提交的数据</h4>
          <pre className="text-sm bg-muted p-2 rounded">
            {JSON.stringify(submittedData, null, 2)}
          </pre>
        </div>
      )}
    </div>
  )
}

render(<BasicFormDialogExample />)
`,
  },
  "confirm-dialog": {
    title: "确认对话框",
    code: `
import React, { useState } from "react"
import { FormDialog } from "@/components/common-custom/form"
import { Button } from "@/components/ui/button"
import { Trash2 } from "lucide-react"

function ConfirmDialogExample() {
  const [dialogOpen, setDialogOpen] = useState(false)
  const [formValues, setFormValues] = useState({
    confirm: false
  })
  
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [result, setResult] = useState(null)
  
  const handleFieldChange = (name, value) => {
    setFormValues(prev => ({
      ...prev,
      [name]: value
    }))
  }
  
  const handleSubmit = async (values) => {
    setIsSubmitting(true)
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      setResult({ success: true, message: "项目已删除" })
      setDialogOpen(false)
    } catch (error) {
      console.error(error)
    } finally {
      setIsSubmitting(false)
    }
  }
  
  // 表单字段配置
  const fields = [
    {
      name: "confirm",
      label: "确认删除",
      type: "checkbox",
      description: "我理解此操作不可撤销",
      required: true
    }
  ]
  
  return (
    <div className="space-y-4">
      <Button variant="destructive" onClick={() => setDialogOpen(true)}>
        <Trash2 className="w-4 h-4 mr-2" />
        删除项目
      </Button>
      
      <FormDialog
        title="确认删除"
        description="您确定要删除此项目吗？此操作不可撤销。"
        fields={fields}
        values={formValues}
        onChange={handleFieldChange}
        onSubmit={handleSubmit}
        isLoading={isSubmitting}
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        maxWidth="sm"
        submitLabel="删除"
        cancelLabel="取消"
      />
      
      {result && (
        <div className="p-4 border rounded-md mt-4">
          <div className="text-sm">
            {result.message}
          </div>
        </div>
      )}
    </div>
  )
}

render(<ConfirmDialogExample />)
`,
  },
  // 文件上传示例
  "basic-file-upload": {
    title: "基础文件上传",
    code: `
import React, { useState } from "react"
import { FileUpload } from "@/components/common-custom/file-upload"
import { UploadFile } from "@/types/file-upload"

function BasicFileUploadExample() {
  const [files, setFiles] = useState<UploadFile[]>([])

  const handleFilesAdded = (newFiles: File[]) => {
    // 转换为上传文件格式
    const uploadFiles = newFiles.map((file) => ({
      id: Math.random().toString(36).substring(2, 9),
      name: file.name,
      size: file.size,
      type: file.type,
      progress: 0,
      status: "uploading" as const,
      file,
    }))
    
    setFiles((prev) => [...prev, ...uploadFiles])
    
    // 模拟上传进度
    uploadFiles.forEach((file) => {
      const interval = setInterval(() => {
        setFiles((prevFiles) => {
          const fileIndex = prevFiles.findIndex((f) => f.id === file.id)
          if (fileIndex === -1) return prevFiles
          
          const updatedFiles = [...prevFiles]
          const currentFile = updatedFiles[fileIndex]
          
          if (currentFile.progress < 100) {
            updatedFiles[fileIndex] = {
              ...currentFile,
              progress: Math.min(currentFile.progress + 20, 100),
            }
          } else {
            updatedFiles[fileIndex] = {
              ...currentFile,
              status: "success",
              url: URL.createObjectURL(currentFile.file!),
            }
            clearInterval(interval)
          }
          
          return updatedFiles
        })
      }, 500)
    })
  }
  
  const handleRetry = (file: UploadFile) => {
    setFiles((prev) =>
      prev.map((f) =>
        f.id === file.id
          ? { ...f, progress: 0, status: "uploading" }
          : f
      )
    )
    
    // 模拟重新上传
    const interval = setInterval(() => {
      setFiles((prevFiles) => {
        const fileIndex = prevFiles.findIndex((f) => f.id === file.id)
        if (fileIndex === -1) return prevFiles
        
        const updatedFiles = [...prevFiles]
        const currentFile = updatedFiles[fileIndex]
        
        if (currentFile.progress < 100) {
          updatedFiles[fileIndex] = {
            ...currentFile,
            progress: Math.min(currentFile.progress + 20, 100),
          }
        } else {
          updatedFiles[fileIndex] = {
            ...currentFile,
            status: "success",
            url: URL.createObjectURL(currentFile.file!),
          }
          clearInterval(interval)
        }
        
        return updatedFiles
      })
    }, 500)
  }
  
  const handleRemove = (file: UploadFile) => {
    setFiles((prev) => prev.filter((f) => f.id !== file.id))
  }
  
  const handleView = (file: UploadFile) => {
    if (file.url) {
      window.open(file.url, "_blank")
    }
  }
  
  const handleDownload = (file: UploadFile) => {
    if (file.url) {
      const a = document.createElement("a")
      a.href = file.url
      a.download = file.name
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
    }
  }
  
  return (
    <FileUpload
      files={files}
      uploadAreaProps={{
        multiple: true,
        accept: "image/*,application/pdf",
        hint: "支持 JPG、PNG、PDF 等格式，单个文件不超过 10MB",
      }}
      onFilesAdded={handleFilesAdded}
      onRetry={handleRetry}
      onRemove={handleRemove}
      onView={handleView}
      onDownload={handleDownload}
    />
  )
}

render(<BasicFileUploadExample />)
`,
  },
  "custom-upload-area": {
    title: "自定义上传区域",
    code: `
import React, { useState } from "react"
import { FileUploadArea } from "@/components/common-custom/file-upload"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { CloudUpload } from "lucide-react"

function CustomUploadAreaExample() {
  const [files, setFiles] = useState<File[]>([])
  
  const handleFilesAdded = (newFiles: File[]) => {
    setFiles((prev) => [...prev, ...newFiles])
    console.log("已选择文件:", newFiles)
  }
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>自定义上传区域</CardTitle>
      </CardHeader>
      <CardContent>
        <FileUploadArea
          multiple={true}
          accept="image/*,application/pdf"
          prompt="点击或拖拽文件到此处"
          dragActivePrompt="松开鼠标上传文件"
          hint="最大支持5MB，允许JPG、PNG、PDF格式"
          icon={CloudUpload}
          buttonText="浏览文件"
          onChange={handleFilesAdded}
          className="bg-slate-50"
        />
        
        {files.length > 0 && (
          <div className="mt-4">
            <h4 className="font-medium mb-2">已选择 {files.length} 个文件:</h4>
            <ul className="list-disc pl-5">
              {files.map((file, index) => (
                <li key={index} className="text-sm">
                  {file.name} ({(file.size / 1024).toFixed(1)} KB)
                </li>
              ))}
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

render(<CustomUploadAreaExample />)
`,
  },
  // 搜索组件示例
  "basic-search": {
    title: "基础搜索",
    code: `
import React, { useState } from "react"
import { SearchInput } from "@/components/common-custom/search"
import { Search } from "lucide-react"

function BasicSearchExample() {
  const [searchTerm, setSearchTerm] = useState("")
  
  const handleSearch = () => {
    console.log("搜索:", searchTerm)
    // 执行搜索逻辑
  }
  
  return (
    <div className="space-y-4">
      <SearchInput
        value={searchTerm}
        onChange={setSearchTerm}
        placeholder="搜索..."
        onSubmit={handleSearch}
        showClear
        onClear={() => setSearchTerm("")}
        icon={Search}
      />
      
      {searchTerm && (
        <div className="p-4 border rounded-md">
          <p>搜索关键词: {searchTerm}</p>
        </div>
      )}
    </div>
  )
}

render(<BasicSearchExample />)
`,
  },
  "advanced-search": {
    title: "高级搜索",
    code: `
import React, { useState } from "react"
import { AdvancedSearch } from "@/components/common-custom/search"
import type { SearchFilter } from "@/types/search"

// 示例过滤器
const filters: SearchFilter[] = [
  {
    id: "department",
    label: "部门",
    value: "",
    type: "select",
    options: [
      { label: "技术部", value: "tech" },
      { label: "产品部", value: "product" },
      { label: "市场部", value: "marketing" }
    ]
  },
  {
    id: "status",
    label: "状态",
    value: "",
    type: "select",
    options: [
      { label: "活跃", value: "active" },
      { label: "未活跃", value: "inactive" }
    ]
  },
  {
    id: "joinDate",
    label: "入职日期",
    value: "",
    type: "date"
  },
  {
    id: "isAdmin",
    label: "管理员",
    value: false,
    type: "checkbox"
  }
]

function AdvancedSearchExample() {
  const [searchTerm, setSearchTerm] = useState("")
  const [searchFilters, setSearchFilters] = useState<SearchFilter[]>(filters)
  
  const handleSearch = (term: string, filters: SearchFilter[]) => {
    console.log("搜索:", term)
    console.log("过滤器:", filters)
    // 执行搜索逻辑
  }
  
  return (
    <AdvancedSearch
      value={searchTerm}
      onChange={setSearchTerm}
      placeholder="搜索用户、部门或角色..."
      filters={searchFilters}
      onFilterChange={setSearchFilters}
      onSearch={handleSearch}
      history={["张三", "管理员"]}
      showHistory={true}
    />
  )
}

render(<AdvancedSearchExample />)
`,
  },
  "global-search": {
    title: "全局搜索",
    code: `
import React, { useState } from "react"
import { Button } from "@/components/ui/button"
import { GlobalSearch } from "@/components/common-custom/search"
import type { SearchItem } from "@/types/search"
import { User, FileText, Settings } from "lucide-react"

// 示例搜索结果
const mockResults: SearchItem[] = [
  {
    id: 1,
    title: "张三",
    description: "前端开发工程师",
    icon: User,
    tags: ["开发", "前端"]
  },
  {
    id: 2,
    title: "李四",
    description: "产品经理",
    icon: User,
    tags: ["产品", "管理"]
  },
  {
    id: 3,
    title: "项目文档",
    description: "项目需求和设计文档",
    icon: FileText,
    tags: ["文档", "设计"]
  },
  {
    id: 4,
    title: "系统设置",
    description: "系统配置和参数设置",
    icon: Settings,
    tags: ["设置", "系统"]
  }
]

function GlobalSearchExample() {
  const [open, setOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [loading, setLoading] = useState(false)
  const [results, setResults] = useState<SearchItem[]>([])
  
  const handleSearch = (term: string) => {
    if (!term) return
    
    setLoading(true)
    // 模拟API请求
    setTimeout(() => {
      setResults(mockResults)
      setLoading(false)
    }, 500)
  }
  
  return (
    <>
      <Button onClick={() => setOpen(true)}>
        打开全局搜索
      </Button>
      
      <GlobalSearch
        open={open}
        onOpenChange={setOpen}
        value={searchTerm}
        onChange={(term) => {
          setSearchTerm(term)
          handleSearch(term)
        }}
        results={results}
        loading={loading}
        placeholder="搜索任何内容..."
      />
    </>
  )
}

render(<GlobalSearchExample />)
`,
  },
  // 筛选器示例
  "basic-filter": {
    title: "基础筛选器",
    code: `
import React, { useState } from "react"
import { Filter } from "@/components/common-custom/filter"
import { FilterItem } from "@/types/filter"

function BasicFilterExample() {
  // 筛选条件配置
  const filterItems: FilterItem[] = [
    {
      name: "status",
      label: "状态",
      type: "select",
      options: [
        { label: "全部", value: "all" },
        { label: "活跃", value: "active" },
        { label: "非活跃", value: "inactive" },
        { label: "已归档", value: "archived" }
      ],
      defaultValue: "all"
    },
    {
      name: "category",
      label: "分类",
      type: "select",
      options: [
        { label: "全部", value: "all" },
        { label: "电子产品", value: "electronics" },
        { label: "服装", value: "clothing" },
        { label: "家居", value: "home" },
        { label: "食品", value: "food" }
      ],
      defaultValue: "all"
    },
    {
      name: "dateRange",
      label: "日期范围",
      type: "date-range",
      defaultValue: { from: undefined, to: undefined }
    }
  ]
  
  // 筛选值状态
  const [filterValues, setFilterValues] = useState({
    status: "all",
    category: "all",
    dateRange: { from: undefined, to: undefined }
  })
  
  const handleFilterChange = (values) => {
    setFilterValues(values)
    console.log("筛选条件变更:", values)
  }
  
  const handleFilterSubmit = (values) => {
    console.log("提交筛选:", values)
    // 执行筛选逻辑
  }
  
  const handleFilterClear = () => {
    console.log("清空筛选")
    // 可以在这里执行额外的清空逻辑
  }
  
  return (
    <div className="space-y-4">
      <Filter
        filters={filterItems}
        values={filterValues}
        onChange={handleFilterChange}
        onSubmit={handleFilterSubmit}
        onClear={handleFilterClear}
        popover={true}
        popoverTitle="筛选条件"
      />
      
      {/* 显示当前筛选值 */}
      <div className="p-4 border rounded-md">
        <h4 className="font-medium mb-2">当前筛选条件</h4>
        <pre className="text-sm bg-muted p-2 rounded">
          {JSON.stringify(filterValues, null, 2)}
        </pre>
      </div>
    </div>
  )
}

render(<BasicFilterExample />)
`,
  },
  "inline-filter": {
    title: "内联筛选器",
    code: `
import React, { useState } from "react"
import { Filter } from "@/components/common-custom/filter"
import { FilterItem } from "@/types/filter"
import { Card } from "@/components/ui/card"

function InlineFilterExample() {
  // 筛选条件配置
  const filterItems: FilterItem[] = [
    {
      name: "status",
      label: "状态",
      type: "select",
      options: [
        { label: "全部", value: "all" },
        { label: "活跃", value: "active" },
        { label: "非活跃", value: "inactive" }
      ],
      defaultValue: "all"
    },
    {
      name: "price",
      label: "价格",
      type: "select",
      options: [
        { label: "全部", value: "all" },
        { label: "低于100", value: "lt100" },
        { label: "100-500", value: "100-500" },
        { label: "高于500", value: "gt500" }
      ],
      defaultValue: "all"
    },
    {
      name: "keyword",
      label: "关键词",
      type: "text",
      placeholder: "搜索关键词",
      defaultValue: ""
    }
  ]
  
  // 筛选值状态
  const [filterValues, setFilterValues] = useState({
    status: "all",
    price: "all",
    keyword: ""
  })
  
  const handleFilterChange = (values) => {
    setFilterValues(values)
    console.log("筛选条件变更:", values)
  }
  
  return (
    <Card className="p-4">
      <Filter
        filters={filterItems}
        values={filterValues}
        onChange={handleFilterChange}
        popover={false}
        className="flex flex-wrap gap-4"
      />
      
      {/* 显示当前筛选值 */}
      <div className="mt-4 p-4 border rounded-md">
        <h4 className="font-medium mb-2">当前筛选条件</h4>
        <pre className="text-sm bg-muted p-2 rounded">
          {JSON.stringify(filterValues, null, 2)}
        </pre>
      </div>
    </Card>
  )
}

render(<InlineFilterExample />)
`,
  },
};

export type ExampleKey = keyof typeof examples;
export default examples; 
"use client"

import { useEffect, useState } from "react"
import { PolarAngleAxis, PolarGrid, PolarRadiusAxis, Radar, RadarChart, ResponsiveContainer } from "recharts"

const data = [
  {
    subject: "效率",
    A: 87,
    fullMark: 100,
  },
  {
    subject: "协作",
    A: 78,
    fullMark: 100,
  },
  {
    subject: "创新",
    A: 82,
    fullMark: 100,
  },
  {
    subject: "质量",
    A: 90,
    fullMark: 100,
  },
  {
    subject: "速度",
    A: 85,
    fullMark: 100,
  },
  {
    subject: "满意度",
    A: 92,
    fullMark: 100,
  },
]

export function TeamPerformanceRadar() {
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
  }, [])

  if (!isMounted) {
    return <div className="h-[300px] flex items-center justify-center">加载中...</div>
  }

  return (
    <ResponsiveContainer width="100%" height={300}>
      <RadarChart cx="50%" cy="50%" outerRadius="80%" data={data}>
        <PolarGrid />
        <PolarAngleAxis dataKey="subject" tick={{ fill: "var(--muted-foreground)" }} />
        <PolarRadiusAxis angle={30} domain={[0, 100]} />
        <Radar name="团队表现" dataKey="A" stroke="hsl(var(--primary))" fill="hsl(var(--primary))" fillOpacity={0.2} />
      </RadarChart>
    </ResponsiveContainer>
  )
}
